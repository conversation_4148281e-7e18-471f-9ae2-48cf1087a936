<?php

namespace App\Models;

use App\Jobs\AdminActionLogJob;
use App\Models\AdminSubUser as AdminSubUserModel;
use Dcat\Admin\Form;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class AdminSubUser extends Model
{
	use HasDateTimeFormatter;
    use SoftDeletes;

    /**
     * 可分配的子账户角色
     */
    const MANAGEABLE_ROLE_IDS = [3, 5, 6, 7, 8];

    protected $table = 'admin_sub_users';

    /**
     * 获取所有子账户ID，包含自己
     *
     * @param $userId
     * @return array
     */
    public static function getAdminUids($userId): array
    {
        /** @var AdminUser $adminUser */
        $adminUser = AdminUser::query()->select("id", "parent_id")->find($userId);
        if ($adminUser->parent_id != 0) {
            return [$adminUser->id];
        } else {
            $subUserId = AdminUser::query()->where("parent_id", $adminUser->id)->where("status", 1)->pluck("id")->toArray();
            return array_merge([$userId], $subUserId);
        }
    }

    /**
     * 获取所有账户ID，包含自己以及同级别的子账号
     * @param $userId
     * @return array
     */
    public static function getALLAdminUids($userId): array
    {
        /** @var AdminUser $adminUser */
        $adminUser = AdminUser::query()->select("id", "parent_id")->find($userId);
        if ($adminUser->parent_id != 0) {
            $subUserId = AdminUser::query()->where("parent_id", $adminUser->parent_id)->where("status", 1)->pluck("id")->toArray();
            return array_merge([$adminUser->parent_id], $subUserId);
        } else {
            $subUserId = AdminUser::query()->where("parent_id", $adminUser->id)->where("status", 1)->pluck("id")->toArray();
            return array_merge([$userId], $subUserId);
        }
    }

    /**
     * 获取账号列表,带账号名称，格式是 ID=>用户名
     * @param $userId
     * @return array
     */
    public static function getAdminUsers($userId): array
    {
        /** @var AdminUser $adminUser */
        $adminUser = AdminUser::query()->select("id", "parent_id", "username")->find($userId);
        if ($adminUser->parent_id != 0) {
            return [$adminUser->id => $adminUser->username];
        } else {
            $subUser = AdminUser::query()->where("parent_id", $adminUser->id)->where("status", 1)->pluck("username", "id")->toArray();
            $subUser[$adminUser->id] = $adminUser->username;
            return $subUser;
        }
    }

    public function parentAdminInfo(): BelongsTo
    {
        return $this->BelongsTo(AdminUser::class, 'parent_id', 'id');
    }

    public function adminInfo(): BelongsTo
    {
        return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
    }

    /**
     * A user has and belongs to many roles.
     *
     * @return BelongsToMany
     */
    public function roles(): BelongsToMany
    {
        $pivotTable = config('admin.database.role_users_table');
        $relatedModel = config('admin.database.roles_model');

        return $this->belongsToMany($relatedModel, $pivotTable, 'user_id', 'role_id','admin_uid','id')->withTimestamps();
    }



    /**
     * 记录操作日志
     * @param Form $form
     * @return void
     */
    public static function logActivity(Form $form): void
    {
        /** @var AdminSubUserModel $model */
        $model = $form->model();
        $rolesName = $model->roles()
            ->pluck('name')
            ->toArray();
        $roleMsg = implode('，', $rolesName) ?? null;
        // 构建日志消息
        $action = $form->isCreating() ? 'create' : 'update';
        $actionMsg = $form->isCreating() ? '创建' : '修改';
        $recordId = $form->getKey(); // 主键ID
        $input = $form->input();
        $message = "ID：$recordId<br>
                共享落地页模板：{$input['tpl_ids']}<br>
                共享销售分组：{$input['ww_user_group_ids']}<br>
                权限：$roleMsg<br>
                操作：{$actionMsg}
                ";
        AdminActionLogJob::dispatch(
            'sub_user_' . $action,
            $recordId,
            AdminActionLog::ACTION_TYPE['账号'],
            $message,
            getIp(),
            \Dcat\Admin\Admin::user()->id, // 当前操作用户ID
        )->onQueue('admin_action_log_job');
    }

    /**
     * 同步用户角色权限
     * 保留超管分配的角色，合并当前选择的可管理角色
     * @param Form $form
     * @return void
     */
    public static function syncUserRoles(Form $form): void
    {
        /** @var AdminSubUserModel $model */
        $model = $form->model();
        // 修改前所有角色
        $currentRoles = $model
            ->roles()
            ->pluck('role_id')
            ->toArray();
        // 可管理角色
        $manageableRoleIs = self::MANAGEABLE_ROLE_IDS;
        // 超管分配的角色
        $superAdminRoleIs = array_diff($currentRoles, $manageableRoleIs);
        //选择的角色
        $chooseRoles = array_map('intval', explode(',', $form->roles));
        // 合并角色
        $finalRoles = array_merge($chooseRoles, $superAdminRoleIs);
        // 重新索引
        $finalRoles = array_unique($finalRoles);
        // 更新角色
        $model->roles()
            ->sync($finalRoles);
        // 删除字段
        $form->deleteInput('roles');
    }


}
