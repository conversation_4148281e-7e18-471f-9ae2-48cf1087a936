<?php

namespace App\Console\Commands\Tads\Account;

use App\Models\TencentAdAccount;
use App\Services\TenCentAd\OauthAccountService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class GetSceneSpecTags extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'GetSceneSpecTags';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        /**
         * 获取过往三天消耗较大的账户
         * 获取这些账户下面的所有素材，并且对获取素材对应的落地页，以及素材对应的数据
         */
        $accountVccIds = DB::table("ad_account_vcc_daily")->where("ad_cost", ">", "200000")->where('date', ">", date("Y-m-d", time() - 86400 * 7))->pluck("account_id");
        //$accountVccIds = [********];
        $accountInfos = TencentAdAccount::query()->whereIn("account_id", $accountVccIds)->limit(10)->get();
        /** @var TencentAdAccount $adAccount */
        $i        = 0;
        $sumCount = $accountInfos->count();
        $data = [];
        foreach ($accountInfos as $adAccount) {
            //完善有消耗的行业
            if (!$adAccount->system_industry_id) {
                $accountInfoResp               = OauthAccountService::advertiserGet($adAccount);
                $accountInfo                   = $accountInfoResp['data']['list'][0] ?? [];
                $adAccount->system_industry_id = $accountInfo['system_industry_id'] ?? "";
                $adAccount->mdm_name           = $accountInfo['mdm_name'] ?? "";
                $adAccount->agency_account_id  = $accountInfo['agency_account_id'] ?? "";
                $adAccount->operators          = json_encode($accountInfo['operators'] ?? []);
                $adAccount->memo               = $accountInfo['memo'] ?? "";
                $adAccount->save();
            }
            $this->info($adAccount->account_id);
            $i++;
            $this->info('进度:' . round($i / $sumCount * 100, 2) . '%');

            $types = ['WECHAT_POSITION', 'OFFICIAL_ACCOUNT_MEDIA_CATEGORY', 'MINI_PROGRAM_AND_MINI_GAME', 'PAY_SCENE', 'MOBILE_UNION_CATEGORY', 'WECHAT_CHANNELS_SCENE', 'PC_SCENE' ];
            foreach($types as $type){
                //获取所有素材
                $resp = $this->scene_spec_tags_get($adAccount,$type);
                $resp = json_decode($resp, true);
                if(isset($resp['data']['list'])){
                    foreach($resp['data']['list'] as $item){
                        $data[$item['id']] = $item;
                    }
                }
            }
            //$this->wechat_pages_get($adAccount);
        }
        Cache::store('redis')->set('tads_scene_spec_tags', json_encode($data));
        return Command::SUCCESS;
    }

    function scene_spec_tags_get(TencentAdAccount $adAccount,$type = 'WECHAT_POSITION')
    {
        $interface = 'scene_spec_tags/get';
        $url = 'https://api.e.qq.com/v3.0/' . $interface;

        $common_parameters = array (
            'access_token' => $adAccount->access_token,
            'timestamp' => time(),
            'nonce' => md5(uniqid('', true))
        );

        $parameters = array (
            'account_id' => $adAccount->account_id,
            'type' => $type,
        );

        $parameters = array_merge($common_parameters, $parameters);

        foreach ($parameters as $key => $value) {
            if (!is_string($value)) {
                $parameters[$key] = json_encode($value);
            }
        }

        $request_url = $url . '?' . http_build_query($parameters);

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $request_url);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 60);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $response = curl_exec($curl);
        if (curl_error($curl)) {
            $error_msg = curl_error($curl);
            $error_no = curl_errno($curl);
            curl_close($curl);
            throw new \Exception($error_msg, $error_no);
        }
        curl_close($curl);
        return $response;
    }
}
