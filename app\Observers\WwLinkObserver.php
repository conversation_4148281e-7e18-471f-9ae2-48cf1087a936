<?php

    namespace App\Observers;

    use App\Jobs\AdminActionLogJob;
    use App\Models\TencentAdAccount;
    use App\Models\AdminActionLog;
    use App\Models\WwLink;
    use Dcat\Admin\Admin;

    class WwLinkObserver
    {
        /**
         * Handle the WwLink "created" event.
         *
         * @param \App\Models\WwLink $wwLink
         * @return void
         */
        public function created(WwLink $wwLink)
        {
            //记录日志
            //$action, $relId, $actionType, $message, $ip, $adminUid
            AdminActionLogJob::dispatch(
                'create_ww_link',
                $wwLink->id,
                AdminActionLog::ACTION_TYPE['链接'],
                '创建ID「' . $wwLink->id . '」-备注「' . $wwLink->remark . '」投放链接。',
                getIp() ?? '',
               Admin::user() ? Admin::user()->id : -1
            )->onQueue('admin_action_log_job');

            TencentAdAccount::updateWwLinkRemark($wwLink);
        }

        /**
         * Handle the WwLink "updated" event.
         *
         * @param \App\Models\WwLink $wwLink
         * @return void
         */
        public function updated(WwLink $wwLink)
        {
            //记录日志
            $data = $wwLink->toArray();
            $updateString = '';
            foreach ($data as $key => $datum) {
                if (in_array($key, [
                    'admin_uid', 'deleted_at', 'long_press_action_type',
                    'link_ad_id','show_ww_user_policy','use_domain'
                ])) {
                    continue;
                }
                $updateString .= admin_trans('ww-link.fields.' . $key) . "：" . $datum . ";" . '<br/>';
            }
//            dd($updateString);
            AdminActionLogJob::dispatch(
                'update_ww_link',
                $wwLink->id,
                AdminActionLog::ACTION_TYPE['链接'],
                '更新「' . $wwLink->id . '」备注「' . $wwLink->remark . '」<br/>投放链接：' . $updateString,
                getIp() ?? '',
                Admin::user() ? Admin::user()->id : -1
            )->onQueue('admin_action_log_job');
            TencentAdAccount::updateWwLinkRemark($wwLink);
        }

        /**
         * Handle the WwLink "deleted" event.
         *
         * @param \App\Models\WwLink $wwLink
         * @return void
         */
        public function deleted(WwLink $wwLink)
        {

            //记录日志
            AdminActionLogJob::dispatch(
                'delete_ww_link',
                $wwLink->id,
                AdminActionLog::ACTION_TYPE['链接'],
                '删除「' . $wwLink->id . '」-备注「' . $wwLink->remark . '」投放链接。',
                getIp() ?? '',
                Admin::user() ? Admin::user()->id : -1
            )->onQueue('admin_action_log_job');
        }

        /**
         * Handle the WwLink "restored" event.
         *
         * @param \App\Models\WwLink $wwLink
         * @return void
         */
        public function restored(WwLink $wwLink)
        {
            //
        }

        /**
         * Handle the WwLink "force deleted" event.
         *
         * @param \App\Models\WwLink $wwLink
         * @return void
         */
        public function forceDeleted(WwLink $wwLink)
        {
            //
        }
    }
