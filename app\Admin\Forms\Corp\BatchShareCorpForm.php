<?php

namespace App\Admin\Forms\Corp;

use App\Models\AdminSubUser;
use App\Models\AdminSubUserAuthCorp;
use App\Models\AdminUser;
use App\Models\WwCorpInfo;
use App\Services\Tools\LogService;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class BatchShareCorpForm extends Form implements LazyRenderable
{
    use LazyWidget;

    // 处理请求
    public function handle(array $input)
    {
        LogService::inputLog('Tools','企微管理-批量共享', $input, Admin::user()->id, Admin::user()->username);
        if (!$input['id']) {
            return $this->response()->alert()->error('提示')->detail('请选择要共享的企业微信');
        }
        // id转化为数组
        $corpAuthIds = explode(',', $input['id']);

        //共享给子账号
        if($input['share_type'] == 0){

            if (!$input['sub_user_ids']) {
                return $this->response()->alert()->error('提示')->detail('请选择要共享的子账户');
            }
            $subUserIds = $input['sub_user_ids'];//选择的子账号ID

            foreach ($corpAuthIds as $corpAuthId) {

                $subUserAuthCorp = AdminSubUserAuthCorp::query()->find($corpAuthId);
                /** 验证共享企业访问权限  success为true时，data为corpInfo */
                $validateSubUserAuthCorp = AdminSubUserAuthCorp::validateSubUserAuthCorpAccess($subUserAuthCorp);
                if (!$validateSubUserAuthCorp['success']) {
                    return $this->response()->alert()->error('提示')->detail($validateSubUserAuthCorp['message']);
                }
                $corpInfo = $validateSubUserAuthCorp['data'];

                foreach ($subUserIds as $adminUserId) {
                    AdminSubUserAuthCorp::addCorpRecord($adminUserId, $corpInfo,$subUserAuthCorp->auto_label);
                }
            }
        }


        //如果是共享给其他智投账号
        if($input['share_type'] == 1){
            if(!$input['encrypt_admin_account']){
                return $this->response()->alert()->error('提示')->detail('请输入账号或账号标识');
            }
            foreach ($corpAuthIds as $corpAuthId) {
                $subUserAuthCorp = AdminSubUserAuthCorp::query()->find($corpAuthId);

                if (!AdminUser::isAdmin($subUserAuthCorp) && !Admin::user()->isRole("wop")) {
                    return $this->response()->alert()->error('提示')->detail('无操作权限');
                }
                $corpInfo = WwCorpInfo::query()->find($subUserAuthCorp->corp_id);
                if (!$corpInfo) {
                    return $this->response()->alert()->error('提示')->detail('共享的企微不存在，请刷新后重试-2');
                }
                //获取要共享的用户信息
                $encryptAdminAccount = $input['encrypt_admin_account'];

                $adminUser = AdminUser::query()->where('encrypt_id', $encryptAdminAccount)->first();
                if (!$adminUser) {
                    $adminUser = AdminUser::query()->where('username', $encryptAdminAccount)->first();
                }
                if (!$adminUser) {
                    return $this->response()->alert()->error('提示')->detail('账号不存在');
                }
                AdminSubUserAuthCorp::addCorpRecord($adminUser->id, $corpInfo);
            }

        }

        return $this->response()->alert()->success('提示')->detail('共享成功')->refresh();
    }

    public function form()
    {

        $this->radio('share_type','共享给谁')
            ->when(0,function (Form $form){
                if (AdminUser::isSystemOp()) {
                    $this->multipleSelect('sub_user_ids', '选择子账号')->options(AdminUser::query()->pluck("username", "id"));
                } else {
                    $this->multipleSelect('sub_user_ids', '选择子账号')->options(AdminSubUser::getAdminUsers(Admin::user()->id));
                }
            })
            ->when(1,function (Form $form){
                $this->text('encrypt_admin_account', '智投账号或智投账号标识')->placeholder('请输入智投账号或智投账号标识');
            })
            ->options([0 => '共享给子账号', 1 => '共享给其他智投方舟账号'])
            ->default(0)
            ->required();
        $this->hidden('id')->value($this->payload['ids'] ?? '');
        $this->confirm('确认提交？');
    }

}
