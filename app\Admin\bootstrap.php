<?php

use App\Admin\Extensions\Column\ClickCopy;
use App\Admin\Extensions\Filter\DateRange;
use App\Exceptions\PermissionException;
use App\Models\Announcement;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid\Column;
use Dcat\Admin\Grid\Filter;
use Dcat\Admin\Http\Auth\Permission;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Support\Helper;
use Dcat\Admin\Layout\Navbar;
use Illuminate\Redis\Connections\PhpRedisConnection;
use Illuminate\Support\Facades\Redis;

/**
 * Dcat-admin - admin builder based on Laravel.
 * <AUTHOR> <https://github.com/jqhph>
 *
 * Bootstraper for Admin.
 *
 * Here you can remove builtin form field:
 *
 * extend custom field:
 * Dcat\Admin\Form::extend('php', PHPEditor::class);
 * Dcat\Admin\Grid\Column::extend('php', PHPEditor::class);
 * Dcat\Admin\Grid\Filter::extend('php', PHPEditor::class);
 *
 * Or require js and css assets:
 * Admin::css('/packages/prettydocs/css/styles.css');
 * Admin::js('/packages/prettydocs/js/main.js');
 *
 */

//主题
Admin::css('/css/theme.css');

// 美化版消息提示样式
Admin::css('/css/toastr-enhancement.css');

// 美化版模态框样式
Admin::css('/css/modal-enhancement.css');

// 美化版SweetAlert2样式
Admin::css('/css/swal2-enhancement.css');

// 美化版Alert样式
Admin::css('/css/alert-enhancement.css');

//Column拓展点击复制
Column::extend('ClickCopy', ClickCopy::class);
//Filter拓展日期范围
Filter::extend('dateRange', DateRange::class);

Admin::navbar(function (Navbar $navbar) {
    if (!Admin::user()) {
        return false;
    }
    $AdminUserId = Admin::user()->id;
    // 获取最新的5条公告数据
    $announcements = Announcement::getAnnouncement();
    /** @var PhpRedisConnection $redis */
    try {
        $redis = Redis::connection('announcement');
    } catch (Exception $e) {
        Log::error("公告Redis错误".$e->getMessage());
        return false;
    }
    $start = $redis->sismember('NotReadAnnouncement', $AdminUserId);
    $navbar->right(view('announcements.index',
        [
            'ip' => getIp(),
            'announcements' => $announcements,
            'start' => $start,
            'admin_id' => $AdminUserId
        ]
    ));
    return true;
});

// 修复水平菜单遮挡菜单标题问题
Admin::script(
    <<<JS
$(document).ready(function() {
    // 定义更新padding-top的函数
    function updatePaddingTop() {
        var \$sidebar = $('.main-horizontal-sidebar.sidebar-dark-white');
        var \$pjaxContainer = $('#pjax-container');

        // 检查元素是否存在
        if (\$sidebar.length && \$pjaxContainer.length) {
            var sidebarHeight = \$sidebar.outerHeight(); // 使用outerHeight()包含边框和padding
            \$pjaxContainer.css('padding-top', sidebarHeight + 20 +'px');
            \$pjaxContainer.css('padding-bottom',  70 +'px');
            // 调试信息（可选）
            console.log('Updated pjax-container padding-top to:', sidebarHeight + 20 + 'px');
        } else {
            if (!\$sidebar.length) console.warn('Sidebar element not found');
            if (!\$pjaxContainer.length) console.warn('pjax-container element not found');
        }
    }

    // 初始设置
    updatePaddingTop();

    // 添加窗口resize事件监听（可选）
    $(window).on('resize', function() {
        updatePaddingTop();
    });

    // 如果使用PJAX，可能需要监听内容加载事件（可选）
    $(document).on('pjax:complete', function() {
        updatePaddingTop();
    });
});
JS
);
//Admin::css('css/custom-form.css');
//// 提意见
//Admin::script(<<<JS
//    $(document).ready(function() {
//        // 检查是否在登录后的后台页面（排除登录页）
//        if (window.location.pathname !== '/admin/auth/login' &&
//            document.body.classList.contains('sidebar-mini')) {
//
//            $('body').append(`
//                <a href="/feedback"
//                   class="feedback-float-btn"
//                   style="position: fixed;
//                          right: 20px;
//                          bottom: 20px;
//                          z-index: 9999;
//                          padding: 10px 15px;
//                          background: #409EFF;
//                          color: white;
//                          border-radius: 4px;
//                          box-shadow: 0 2px 10px rgba(0,0,0,0.2);
//                          transition: all 0.3s;">
//                    <i class="fa fa-commenting"></i> 提建议
//                </a>
//            `);
//        }
//    });
//JS
//);

Permission::registerErrorHandler(function () {
    if (Helper::isAjaxRequest()) {
        $response = response()->json([
            'message' => trans('admin.deny'),
            'status' => false,
        ], 403);
    } else{
        $response = response(
            Content::make()->withError(trans('admin.deny')), 403
        );
    }
    throw new PermissionException($response);
});