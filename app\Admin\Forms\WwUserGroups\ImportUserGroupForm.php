<?php

namespace App\Admin\Forms\WwUserGroups;

use App\Models\AdminSubUserAuthCorp;
use App\Models\WwUser;
use App\Models\WwUsersGroup;
use App\Models\WwUsersGroupsRel;
use App\Services\Corp\WwCorpApiService;
use App\Services\Tools\LogService;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Exception;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Throwable;

class ImportUserGroupForm extends Form implements LazyRenderable
{
    use LazyWidget;

    private int $adminUid;

    /**
     * @param array $input
     * @return JsonResponse
     * @throws Throwable
     */
    public function handle(array $input): JsonResponse
    {
        $adminUid = Admin::user()->id ?? null;
        $this->adminUid = $adminUid;
        LogService::inputLog('Tools', '销售分组-导入复制销售分组', $input, $adminUid, Admin::user()->username ?? null);
        //分享码
        $code = $input['UserGroupCode'] ?? null;
        $parentId = Admin::user()->parent_id ?? null;
        /** 判断是否是主账号 */
        if ($parentId != 0) {
            return $this->response()->alert()->error('提示')->detail('主账号才可以进行分享复制');
        }

        /** 验证并解析分享码 */
        $shareCodeResult = $this->validateAndParseShareCode($code);
        // 如果$shareCodeResult['error']等于true,提示错误信息
        if ($shareCodeResult['error']) {
            return $this->response()->alert()->error('提示')->detail($shareCodeResult['message']);
        }
        // 解密后数据
        $groupData = $shareCodeResult['data'];
        // 获取源分组ID
        $groupId = $groupData['groupId'] ?? null;
        // 查询分组信息是否存在
        $groupInfo = $this->getGroupInfo($groupId);

        // 不存在返回-分组不存在
        if (!$groupInfo) {
            return $this->response()->alert()->error('提示')->detail('分组不存在');
        }

        // 获取源分组下的销售列表【id】
        $wwUserIds = json_decode($groupInfo->ww_users_list, true);
        if (empty($wwUserIds)) {
            return $this->response()->alert()->error('提示')->detail('该分组下无销售');
        }


        // 查询源分组下的销售
        $wwUsers = WwUser::query()->whereIn('id', $wwUserIds)->get();
        // 查询源分组下的销售 -> 获取 corp_id 并去重
        $corpIds = $wwUsers->pluck('corp_id')->unique()->toArray();
        /** 判断是否配置重复导入销售 */
        $repeatStatus = WwUser::getRepeatImportStatus($this->adminUid);
        /** 检查是否有重复的销售 */
        $checkRepeatUser = $this->checkRepeatUser($repeatStatus, $wwUsers);
        if (!$checkRepeatUser) {
            return $this->response()->alert()->error('提示')->detail('不能重复导入销售');
        }
        // 查询当前登录用户已授权 分组下销售所属的企业微信
        $AuthCorpData = AdminSubUserAuthCorp::query()
            ->where('admin_uid', $adminUid)
            ->whereIn('corp_id', $corpIds)
            ->get()
            ->toArray();
        // 查询当前登录用户已授权 分组下销售所属的企业微信 - 提取corp_id字段
        $corpIdsColumn = array_column($AuthCorpData, 'corp_id');

        // 根据销售所属企业微信 和 当前登录已授权的企微信,取差集 - 需要授权的企业微信【corp_id】
        $unauthorizedCorpIds = array_diff($corpIds, $corpIdsColumn);
        //开启事务
        DB::beginTransaction();
        // 授权当前用户所有未授权的 corp_id
        if (!empty($unauthorizedCorpIds)) {
            return $this->response()->alert()->error("导入分组失败，请授权销售所属企微");
        }

        // 新建一个分组,名称根据$input获取，其他信息为源分组的信息
        $newGroup = $this->createNewGroup($input['title'], $groupInfo['type']);
        // 获取新建分组的id
        $newGroupId = $newGroup->id;

        // 存储新的corp_id和id映射。键值对:corpId = corp_auth_id
        $CorpAuthData = $this->getCorpAuthMap($corpIds);

        try {
            // 新复制销售的ID列表
            $WwUsersList = [];
            // 把新复制的销售和新建的分组。在WwUsersGroupsRel的关联上
            $newGroupList = [];
            // 把源分组的销售转数组
            $wwUsers = $wwUsers->toArray();
            foreach ($wwUsers as $wwUser) {
                // 如果该销售的corp_id，在$CorpAuthData里有对应的键
                if (isset($CorpAuthData[$wwUser['corp_id']])) {
                    // 更新corp_auth_id 为 $CorpAuthData里对应的键的值
                    $wwUser['corp_auth_id'] = $CorpAuthData[$wwUser['corp_id']];
                }
                // 更新admin_uid为当前登录用户ID
                $wwUser['admin_uid'] = $adminUid;
                $wwUser['created_at'] = date('Y-m-d H:i:s');
                $wwUser['updated_at'] = date('Y-m-d H:i:s');
                $wwUser['today_show_count'] = 0;
                $wwUser['today_add_count'] = 0;
                // 表示该销售为复制销售
                $wwUser['is_copy'] = 1;
                $wwUser['have_qrcode'] = -1;
                // 删除 获客助手信息
                $wwUser['cus_acq_link_id'] = null;
                $wwUser['cus_acq_link_name'] = null;
                $wwUser['cus_acq_link_url'] = null;
                $wwUser['cus_acq_link_skip_verify'] = null;
                //删除 二维码
                $wwUser['qrcode'] = null;
                $wwUser['qrcode_config_id'] = null;
                // 删除id
                unset($wwUser['id']);
                // 新增-返回id
                $newWwUserId = WwUser::query()->insertGetId($wwUser);
                // 将新增用户的ID 存到要新增到WwUsersGroupsRel的变量里
                $newGroupList[] = [
                    'ww_user_id' => $newWwUserId,
                    'ww_group_id' => $newGroupId,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ];
                $upDateWwUser = WwUser::query()->where('id', $newWwUserId)->first();
                //更新获客助手信息
                try {
                    $this->updateCuaAcq($upDateWwUser);
                } catch (Exception) {
                    DB::rollBack();
                    return $this->response()->alert()->error("导入分组失败，联系管理员-4");
                }
                //更新二维码
                try {
                    $result = $this->updateQrcode($upDateWwUser);
                } catch (Exception) {
                    DB::rollBack();
                    return $this->response()->alert()->error("导入分组失败，联系管理员-5");
                }
                if (!isset($result['config_id'])) {
                    DB::rollBack();
                    return $this->response()->error($upDateWwUser->user_id . '导入失败，' . (isset($result['errmsg']) ? explode(",", WwUser::getCusAcqLinkErrString($result['errmsg']))[0] : ""));
                }
                // 将新增的用户id 追加到新增用户列表
                $WwUsersList[] = $newWwUserId;
            }
            if (!empty($WwUsersList)) {
                //更新到wr_ww_users_groups表里 - 直接更新,防止定时任务未执行，查看不了组队销售
                WwUsersGroup::query()->where("id", $newGroupId)
                    ->update(
                        [
                            'ww_users_list' => json_encode($WwUsersList),
                            'ww_users_count' => count($WwUsersList),
                        ]
                    );
            }
            //新增到wr_ww_users_groups_rel表
            WwUsersGroupsRel::query()->insert($newGroupList);
            DB::commit();
        } catch (Exception) {
            DB::rollBack();
            return $this->response()->alert()->error("导入分组失败，联系管理员-3");
        }
        //

        // 业务逻辑后续处理...
        return $this->response()->success('操作成功')->refresh();
    }

    /**
     * 验证并解析分享码
     * @param String $code 分享码
     * @return array 返回解析结果数组
     */
    private function validateAndParseShareCode(string $code): array
    {
        if (empty($code)) {
            return ['error' => true, 'message' => '请填写销售分组代码'];
        }
        try {
            $decrypted = Crypt::decryptString($code);
            $groupData = json_decode($decrypted, true);
        } catch (Exception) {
            return ['error' => true, 'message' => '分享码无效'];
        }

        if (empty($groupData['expires_at']) || $groupData['expires_at'] < time()) {
            return ['error' => true, 'message' => '分享码已过期'];
        }

        return ['error' => false, 'data' => $groupData];
    }

    /**
     * 获取分组信息
     * @param String $groupId 分组ID
     * @return Object
     */
    private function getGroupInfo(string $groupId): object
    {
        return WwUsersGroup::query()
            ->where("id", $groupId)
            ->first();
    }

    /**
     * 判断是否有重复的销售
     * @param bool $repeatStatus 是否可以重复导入销售
     * @param Object $wwUsers 用户模型
     * @return bool
     */
    private function checkRepeatUser(bool $repeatStatus, object $wwUsers): bool
    {
        if (!$repeatStatus) {
            $extractedData = $wwUsers->map(function ($user) {
                return [
                    'corp_id' => $user->corp_id,
                    'user_id' => $user->user_id,
                    'open_user_id' => $user->open_user_id,
                ];
            })->values()->toArray();
            foreach ($extractedData as $data) {
                $findUser = WwUser::query()->where($data)->where('admin_uid', $this->adminUid)->first();
                if (!empty($findUser)) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 创建销售分组
     * @param string $title
     * @param int $type
     * @return WwUsersGroup
     */
    private function createNewGroup(string $title, int $type): WwUsersGroup
    {
        $newGroup = new WwUsersGroup();
        $newGroup->admin_uid = $this->adminUid;
        $newGroup->title = $title;
        $newGroup->type = $type;
        $newGroup->save();
        return $newGroup;
    }

    /**
     * 获取授权表[wr_admin_sub_user_auth_corps]的corp_id和id的映射
     * @param array $corpIds 所有销售的 corpId
     * @return array $corpAuthMap[corp_id => id]
     */
    private function getCorpAuthMap(array $corpIds): array
    {

        $authCorpData = AdminSubUserAuthCorp::query()
            ->where('admin_uid', $this->adminUid)
            ->whereIn('corp_id', $corpIds)
            ->get();

        $corpAuthMap = [];
        foreach ($authCorpData as $record) {
            $corpAuthMap[$record->corp_id] = $record->id;
        }

        return $corpAuthMap;
    }

    /**
     * 更新获客助手信息
     * @param Object $wwUser 用户
     * @return void
     */
    private function updateCuaAcq(object $wwUser): void
    {
        /** @var WwUser $wwUser */
        $linkData = WwCorpApiService::create_link($wwUser->corpInfo, $wwUser);
        if ($linkData['errcode'] != 0) { // 创建失败
            $wwUser['cus_acq_link_status'] = 0;
            $wwUser['cus_acq_link_status_message'] = $linkData['errmsg'];
        } else { // 创建成功
            $wwUser['cus_acq_link_status'] = 1;
            $wwUser['cus_acq_link_status_message'] = $linkData['errmsg'];
            $wwUser['cus_acq_link_id'] = $linkData['link']['link_id'];
            $wwUser['cus_acq_link_name'] = $linkData['link']['link_name'];
            $wwUser['cus_acq_link_url'] = $linkData['link']['url'];
            $wwUser['cus_acq_link_skip_verify'] = 1;
        }
        $wwUser->save();
    }

    /**
     * 更新二维码信息
     * @param Object $wwUser 用户
     * @return array 错误结果
     */
    private function updateQrcode(object $wwUser): array
    {
        /** @var WwUser $wwUser */
        $contactWay = WwCorpApiService::add_contact_way($wwUser->corpInfo, $wwUser->open_user_id, true, getLinkState());
        if (isset($contactWay['config_id'])) {
            $wwUser->qrcode = $contactWay['qr_code'];
            $wwUser->qrcode_config_id = $contactWay['config_id'];
            $wwUser->save();
            return [
                'config_id' => $contactWay['config_id'],
            ];
        } else {
            return [
                'errmsg' => $contactWay['errmsg'] ?? "",
            ];
        }
    }

    public function form(): void
    {
        $this->text('UserGroupCode', '销售分组代码')->required();
        $this->text('title', '分组名称')->required();
    }

}
