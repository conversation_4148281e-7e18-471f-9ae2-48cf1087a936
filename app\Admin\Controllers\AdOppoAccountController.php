<?php /** @noinspection PhpInconsistentReturnPointsInspection */

namespace App\Admin\Controllers;

use Admin;
use App\Admin\Extensions\Grid\Exporters\XlsxWriterExporter;
use App\Admin\Repositories\AdOppoAccount;
use App\Models\AdminSubUser;
use App\Models\AdminUser;
use App\Services\Tools\UtilsService;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Show;
use Dcat\Admin\Widgets\Alert;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Request as RequestFacade;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Session;
use Vtiful\Kernel\Excel;

/**
 * @property int $id
 * @property     $input
 */
class AdOppoAccountController extends AdminController
{


    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {

        return Grid::make(new AdOppoAccount(['adminInfo']), function (Grid $grid) {
            $grid->disableViewButton();
            $grid->model()->orderByDesc("id");
            if (!AdminUser::isSystemOp()) { //如果不是系统角色
                if (Admin::user()->isRole('customer')) { //如果是“客户”的角色 也就是主账号
                    $grid->model()->whereIn("admin_uid", AdminSubUser::getAdminUids(Admin::user()->id));//如果是主账号，则获取所有子账号的id，包括自己
                    $grid->column('归属账号')->display(function () {
                        if (Admin::user()->id == $this->admin_uid) { //如果是自己创建的则直接显示主账号
                            return '主账号';
                        } else {
                            $username = Admin::user()->where('id', $this->admin_uid)->value('username');
                            $username = str_replace(Admin::user()->username, '', $username);
                            return $username;
                        }
                    });
                } else { //如果是客户运营的角色 也就是子账号
                    $grid->model()->where("admin_uid", Admin::user()->id);
                }
            }

            /** 对话框新增 */
            $grid->disableCreateButton();
            $grid->tools(function (Grid\Tools $tools) {
                $className = collect(RequestFacade::segments())->last();
                $tools->append(UtilsService::dialogForm('添加',RequestFacade::url().'/create',"create-{$className}"));
            });

            $grid->column('id');
            $grid->column('用户名称')->display(function () {
                return $this->adminInfo->username ?? '';
            });
            $grid->column('account_name');
//                $grid->column('corporation_name');
            $grid->column('owner_id');
            $grid->column('created_at');
            $grid->column('updated_at');


           $grid->export();


            $grid->filter(function (Grid\Filter $filter) use ($grid) {
                $filter->expand();
                $filter->panel();
                $filter->equal("owner_id")->width(2);
                $filter->like("account_name")->width(2);
                //所有带「所属账户」的列表，主账户都增加对应筛选
                if (Admin::user()->parent_id == 0) { //主账号
                    $adminIds = AdminSubUser::getALLAdminUids(Admin::user()->id);
                    $filter->equal("admin_uid", '操作账号')
                        ->select(AdminUser::query()
                            ->whereIn("id", $adminIds)
                            ->pluck("username", 'id')
                            ->toArray())->width(2);
                }
                $filter->equal("created_at")->date()->width(2);
            });
        });
    }

    public function destroy($id)
    {
        $data = \App\Models\AdOppoAccount::query()->whereIn("id", explode(",", $id))->get();
        foreach ($data as $datum) {
            if (!AdminUser::isAdmin($datum)) {
                return $this->form()->response()->error("无权限操作");
            }
        }
        return $this->form()->destroy($id);
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new AdOppoAccount(), function (Form $form) {
            if (!$form->isCreating() && !AdminUser::isAdmin($form->model()) && !AdminUser::isSystemOp()) {
                $form->display('N')->value("无数据");
            } else {
                $form->display('id');
                $form->hidden('id');
                $form->hidden('admin_uid');
                $form->text('owner_id')->required();
                $form->text('account_name')->required();
                $form->text('api_id')->required();
                $form->text('api_key')->required();
                $form->saving(function (Form $form) {
                    $form->admin_uid = Admin::user()->id;
                    $check = \App\Models\AdOppoAccount::query()->where('owner_id', $form->input('owner_id'))->first();
                    if ($form->isCreating()) {
                        if ($check) {
                            return $form->response()->alert()->error('提示')->detail('账户已存在-1');
                        }
                    }
                    if ($check && $check['id'] != $form->input('id')) {
                        return $form->response()->alert()->error('提示')->detail('账户已存在-2');
                    }

                });
                $form->disableViewButton();
                $form->disableViewCheck();
                $form->disableEditingCheck();
            }
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new AdOppoAccount(), function (Show $show) {
            if (!AdminUser::isAdmin($show->model())) {
                $show->field('N')->value("无数据");
            } else {
                $show->field('id');
                $show->field('corporation_name');
                $show->field('account_id');
            }
        });
    }

    public function getAdAccount(Request $request): LengthAwarePaginator
    {
        $keyword = $request->get("q", 0);
        return \App\Models\AdOppoAccount::query()
            ->where(function ($query) use ($keyword) {
                $query->where('owner_id', 'like', "{$keyword}%")->orWhere("account_name", 'like', "{$keyword}%");
            })
            ->where('admin_uid', Admin::user()->id)
            ->select('owner_id as id', DB::raw("concat(owner_id,'-',account_name) as text"))
            ->paginate(null, ['owner_id as id', 'owner_id as text']);
        // 限制返回数量
    }

}
