<?php

namespace App\Jobs;

use App\Models\LinkViewRecord;
use App\Models\WwLink;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AskDataJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $askData;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($askData)
    {

        $this->askData = $askData;
    }

    /**
     * 防止任务重叠。 expireAfter 设置锁的最大持有时间（防止任务崩溃导致死锁）
     *
//     * @return array
//     */
//    public function middleware()
//    {
//        $vid = $this->formData['vid'] ?? '';
//        if (empty($vid)) {
//            return [];
//        }
//        return [(new WithoutOverlapping($vid))->releaseAfter(10)->expireAfter(15)];
//    }
    /**
     * Execute the job.
     *
     * @return mixed
     */
    public function handle()
    {
        $askData = $this->askData;
        $adminUid = 0;
        $wwLinkId = 0;
        $area = '';
        $ip = $askData['ip'] ?? '';
        if($askData['vid'] && $askData['vid'] > 0) {
            $linkViewRecord = LinkViewRecord::query()
                ->select('id','ww_link_id','admin_uid','area','ip')
                ->where('id',$askData['vid'])
                ->first();
            $adminUid = $linkViewRecord->admin_uid ?? 0;
            $wwLinkId = $linkViewRecord->ww_link_id ?? 0;
            $area = $linkViewRecord->area ?? '';
            $ip = $linkViewRecord->ip ?? '';
        }
        $where = [
            'admin_uid' => $adminUid,
            'ip' => $ip,
            'vid' => $askData['vid'],
        ];


        $exists = DB::table('ask_data')->where($where)->exists();
        if(!$exists) {
            $tplId = WwLink::query()->where('id',$wwLinkId)->value('tpl_id');
            $insert = [
                'admin_uid' => $adminUid,
                'vid' => $askData['vid'] ?? 0,
                'ww_link_id' => $wwLinkId,
                'tpl_id' => $tplId ?? 0,
                'ask_content' => json_encode($askData['ask_content'], JSON_UNESCAPED_UNICODE),
//                'ask_content' => $askData['ask_content'],
                'ip' => $ip,
                'area' => $area,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ];
            DB::table('ask_data')->insert($insert);
        }
        return true;
    }
}
