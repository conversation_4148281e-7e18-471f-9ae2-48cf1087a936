<?php

	namespace App\Admin\Actions\Grid\wwLink;

	use App\Jobs\AdminActionLogJob;
    use App\Models\AdminActionLog;
    use App\Models\WwLink;
    use App\Models\WwLinkConvTad;
    use Dcat\Admin\Actions\Response;
    use Dcat\Admin\Admin;
    use Dcat\Admin\Grid\RowAction;
    use Dcat\Admin\Traits\HasPermissions;
    use Illuminate\Contracts\Auth\Authenticatable;
    use Illuminate\Database\Eloquent\Model;
    use Illuminate\Http\Request;
    use Illuminate\Support\Facades\Log;

    class FlushWwLinkTadsConvCount extends RowAction
	{
		/**
		 * @return string
		 */
		protected $title = '<i class="feather icon-upload-cloud" style="margin:0 10px 0 10px">刷新回传</i>';

		/**
		 * Handle the action request.
		 *
		 * @param Request $request
		 *
		 * @return Response
		 */
		public function handle(Request $request)
		{
			$id = $this->getKey();
			/** @var WwLink $wwLink */
			$wwLink = WwLink::query()->find($id);
			if (!$wwLink) {
				return $this->response()->error('记录未找到，请刷新页面后尝试');
			}
            //获取今天的计数
            $wwLinkConvTads = WwLinkConvTad::query()->where("ww_link_id", $wwLink->id)->where("date", date("Y-m-d"))->first();
            if (!$wwLinkConvTads) {
                $wwLinkConvTads                  = new WwLinkConvTad();
                $wwLinkConvTads->ww_link_id      = $wwLink->id;
                $wwLinkConvTads->date            = date("Y-m-d");
            }
            $wwLinkConvTads->real_conv_count = 0;
            $wwLinkConvTads->real_tads_count = 0;
            $wwLinkConvTads->save();
            $adminUser = Admin::user();
            Log::info('用户ID：' . $adminUser->id . '，账号：' . $adminUser->username . '，刷新了回传，投放链接ID：' . $id . '，账户ID：' .$wwLink->account_id);
            AdminActionLogJob::dispatch(
                'flush_link_tads_conv_count',
                $id,
                AdminActionLog::ACTION_TYPE['链接'],
                "刷新回传：「" . Admin::user()->username . '」，投放链接ID：' . $id . '，账户ID：' . $wwLink->account_id,
                getIp(),
                $adminUser->id
            );
			return $this->response()->success('刷新完成，请观察后续数据')->refresh();
		}

		/**
		 * @return string|array|void
		 */
		public function confirm()
		{
			return ['确认刷新回传?', '刷新回传后，系统按比例上报会重新计数计算'];
		}

		/**
		 * @param Model|Authenticatable|HasPermissions|null $user
		 *
		 * @return bool
		 */
		protected function authorize($user): bool
		{
			return true;
		}

		/**
		 * @return array
		 */
		protected function parameters()
		{
			return [
				//	         'id' => $this->row->id
			];
		}
	}
