<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class CorpLicenseOrders extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'corp_license_orders';


    public function adminInfo(): BelongsTo
    {
        return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
    }

    public function corpInfo(): BelongsTo
    {
        return $this->belongsTo(WwCorpInfo::class, 'corp_id','id');
    }

    public function activeCode(): HasMany
    {
        return $this->hasMany(CorpLicenseActiveCode::class, 'order_id');
    }
}
