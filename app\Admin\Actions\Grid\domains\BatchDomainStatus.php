<?php

namespace App\Admin\Actions\Grid\domains;

use App\Admin\Extensions\Grid\BatchActionPlus;
use App\Admin\Forms\Domains\BatchDomainStatusForm;

class BatchDomainStatus extends BatchActionPlus
{
    /**
     * @return string
     */
    public $title = '批量修改状态';
    public $buttonText = '<button class="btn btn-primary ww_user_batch_btn"><i class="feather icon-refresh-ccw"></i>&nbsp&nbsp<span class="selected"></span>批量修改状态</button>';

    public function form(): BatchDomainStatusForm
    {
        return BatchDomainStatusForm::make();
    }
}
