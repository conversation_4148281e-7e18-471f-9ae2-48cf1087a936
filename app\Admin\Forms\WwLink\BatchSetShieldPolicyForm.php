<?php

namespace App\Admin\Forms\WwLink;

use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Models\AdminSubUser;
use App\Models\AdminUser;
use App\Models\ShieldPolicy;
use App\Models\WwLink;
use App\Services\Tools\LogService;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class BatchSetShieldPolicyForm extends Form implements LazyRenderable
{
    use LazyWidget;

    // 处理请求
    public function handle(array $input)
    {
        LogService::inputLog('Tools','ww_link-链接列表-屏蔽规则', $input, Admin::user()->id, Admin::user()->username);
        // id转化为数组
        $id = explode(',', $input['id'] ?? null);
        if (!$id) {
            return $this->response()->alert()->error('提示')->detail('参数错误，请刷新页面后重试-2。');
        }

        $wwLik = WwLink::query()->find($id);
        if ($wwLik->isEmpty()) {
            return $this->response()->alert()->error('提示')->detail('页面不存在。');
        }

        foreach ($wwLik as $key => $value) {
            if (!AdminUser::isAdmin($value)) {
                return $this->response()->alert()->error('提示')->detail('异常操作，请刷新页面后重试-1。');
            }

            $value->shield_policy_id = $input['shield_policy_id'];
            $value->save();
            AdminActionLogJob::dispatch(
                'batch_set_ww_link_shield_policy',
                $value->id,
                AdminActionLog::ACTION_TYPE['链接'],
                '批量配置企微链接屏蔽规则，账户ID：「' . $value->account_id . '」，屏蔽规则ID：' . $value->shield_policy_id ,
                getIp(),
                Admin::user()->id
            )->onQueue('admin_action_log_job');
        }
        return $this->response()->alert()->success('提示')->success('异常操作，请刷新页面后重试-1。')->refresh();
    }

    public function form()
    {
        $shieldPolicy = ShieldPolicy::query()->whereIn("admin_uid", AdminSubUser::getALLAdminUids(Admin::user()->id))->pluck("name", "id")->toArray();
        $this->select('shield_policy_id', '屏蔽规则')->options($shieldPolicy)->required();
        $this->hidden('id')->attribute('id', 'reset-shield_policy_id');
    }

    // 返回表单数据，如不需要可以删除此方法
    public function default()
    {
        return [
            'password' => '',
            'password_confirm' => '',
        ];
    }
}
