<?php

	namespace App\Models;

	use Dcat\Admin\Traits\HasDateTimeFormatter;
	use Illuminate\Database\Eloquent\Model;
	use Illuminate\Database\Eloquent\SoftDeletes;

	/**
	 * @property mixed $qrcode
	 * @property mixed $admin_uid
	 * @property mixed $ww_app_id
	 * @property mixed|string $expires_at
	 */
	class WwAppAdminQrcode extends Model
	{
		use HasDateTimeFormatter;
		use SoftDeletes;

		protected $table = 'ww_app_admin_qrcode';

	}
