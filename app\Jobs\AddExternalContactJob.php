<?php

    namespace App\Jobs;

    use App\Jobs\TencentAd\TrackClickSyncAddRecordJob;
    use App\Models\AdminSubUserAuthCorp;
    use App\Models\LinkViewRecord;
    use App\Models\LinkViewRecordDelete;
    use App\Models\WwCorpInfo;
    use App\Models\WwUser;
    use App\Models\WwUserAddRecord;
    use App\Models\WwUserAddRecordEx;
    use App\Services\Corp\WwCorpApiService;
    use App\Services\NotifySendService;
    use Illuminate\Bus\Queueable;
    use Illuminate\Contracts\Queue\ShouldBeUnique;
    use Illuminate\Contracts\Queue\ShouldQueue;
    use Illuminate\Foundation\Bus\Dispatchable;
    use Illuminate\Queue\InteractsWithQueue;
    use Illuminate\Queue\Middleware\WithoutOverlapping;
    use Illuminate\Queue\SerializesModels;
    use Illuminate\Support\Facades\DB;
    use Illuminate\Support\Facades\Log;

    /**
     *企微回调-添加外部联系人队列
     *
     */
    class AddExternalContactJob implements ShouldQueue
    {
        use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

        protected $message;


        /**
         * Create a new job instance.
         *
         * @return void
         */
        public function __construct($message)
        {
            //
            $this->message = $message;
        }

        /**
         * 防止任务重叠。 expireAfter 设置锁的最大持有时间（防止任务崩溃导致死锁）
         *
         * @return array
         */
        public function middleware()
        {
            $state = $this->message['State'] ?? "";
            if (empty($state)) {
                return [];
            }
            return [(new WithoutOverlapping($state))->releaseAfter(10)->expireAfter(15)];
        }

        /**
         * Execute the job.
         *
         * @return mixed
         */
        public function handle()
        {
            //

             Log::info("企微回调-添加外部联系人-队列开始执行");
            $message = $this->message;
            //			Log::info('change_external_contact - add_external_contact - MESSAGE：' . json_encode($message));
            // 获取State的点击记录
            /**
             * 获取企业微信的State，给用户展示二维码或者获客链接的时候，我们系统加上的唯一标识，用于溯源这条数据是通过哪个链接，哪个viewRecord进来的
             */
            $state = $message['State'] ?? "";


            /** @var WwCorpInfo $corpInfo */
            $corpInfo = WwCorpInfo::getCorpInfoFromWwCallback($message);
            if (!$corpInfo) {
                NotifySendService::sendWorkWeixinForError("[企微回调][好友变更][严重错误] 回调无法获取到相关的企业信息，请立即查看，相关数据如下：" . json_encode($message));
                return false;
            }
            /**
             * 这里是判断了state的格式，智投2.0系统的前缀统一zt_t2，不是这个前缀的，说明不是通过新系统来的，不用处理，单独记录一下即可
             */
            if (!str_contains($state, 'zt_t2')) {
                // 这段意思是如果是销售删除的，那么我们要恢复一下
                $addRecords = WwUserAddRecord::query()->where([
                    'follow_user_id' => $message['UserID'],
                    'external_userid' => $message['ExternalUserID'],
                ])->get();
                /** @var WwUserAddRecord $addRecord */
                foreach ($addRecords as $addRecord) {
                    $addRecord->is_delete = 0;
                    $addRecord->save();
                }
                if ($addRecords->isEmpty()) {
                    $obj = new WwUserAddRecordEx();
                    $obj->message = json_encode($message, JSON_UNESCAPED_UNICODE);

                    $exUserInfo = WwCorpApiService::externalcontactGet($corpInfo, $message['ExternalUserID']);
                    if (isset($exUserInfo['errcode']) && $exUserInfo['errcode'] == 0) {
                        $obj->name = $exUserInfo['external_contact']['name'];
                        $obj->external_userid = $exUserInfo['external_contact']['external_userid'];
                        $obj->ex_user_info = json_encode($exUserInfo, JSON_UNESCAPED_UNICODE);
                    }
                    $obj->save();
                }
                NotifySendService::sendWorkWeixinForError('这里是判断了state的格式，智投2.0系统的前缀统一zt_t2，不是这个前缀的，说明不是通过新系统来的，不用处理，单独记录一下即可');
                return false;
            }
            /**
             * 通过State，溯源查一下，这次进粉的访问记录，访问记录包含了其他的关键信息，如链接ID等
             */
            /** @var LinkViewRecord $linkViewRecord */
            $linkViewRecord = LinkViewRecord::query()->where("state", $state)->first();
            if(!$linkViewRecord){
                $linkViewRecord = LinkViewRecordDelete::query()->where("state", $state)->first();
            }
            if (!$linkViewRecord) {
                NotifySendService::sendWorkWeixinForError("[企微回调][好友变更][严重错误] 回调的添加记录，无法获取到LinkView记录，请立即查看，相关数据如下：" . json_encode($message));
                return false;
            }
            /**
             * 获取这次访问记录，当时展示的销售信息，一般这个销售信息就是本次回调的销售信息，但是这里没有判断，后续需要优化一下
             * TODO 判断一下企业微信回调的进粉销售与展示的销售是否一致，一般情况下都是一致的，这里单独判断一下，以防异常
             */
            $viewWwUserInfo = $linkViewRecord->wwUserInfoWithTrashed;

            /**
             * 通过企业微信的API，获取外部联系人的信息
             */
            $exUserInfo = WwCorpApiService::externalcontactGet($corpInfo, $message['ExternalUserID']);
            Log::info('通过企业微信的API，获取外部联系人的信息');
            Log::info(json_encode($exUserInfo, JSON_UNESCAPED_UNICODE));
            if (isset($exUserInfo['errcode']) && $exUserInfo['errcode'] == 0) {
                // 获取用户信息
                $addRecord = WwUserAddRecord::query()->where([
                    'admin_uid' => $linkViewRecord->admin_uid,
                    'ww_link_id' => $linkViewRecord->ww_link_id,
                    'external_userid' => $message['ExternalUserID'],
                    'ww_user_id' => $viewWwUserInfo->id
                ])->first();
                // 如果信息不存在，那么创建一条新的记录
                if (!$addRecord) {
                    $addRecord = new WwUserAddRecord();
                    $addRecord->admin_uid = $linkViewRecord->admin_uid;
                    $addRecord->tpl_type = $linkViewRecord->tpl_type;
                    $addRecord->need_shield = $linkViewRecord->need_shield;
                    $addRecord->corp_id = $viewWwUserInfo->corp_id;
                    $addRecord->ww_user_id = $linkViewRecord->ww_user_id;
                    $addRecord->link_view_record_id = $linkViewRecord->id;
                    $addRecord->user_id = $linkViewRecord->user_id;
                    $addRecord->ww_link_id = $linkViewRecord->ww_link_id;
                    $addRecord->click_id = $linkViewRecord->click_id;
                    $addRecord->page_type = $linkViewRecord->page_type;
                    $addRecord->ip = $linkViewRecord->ip;
                    $addRecord->prov = $linkViewRecord->prov;
                    $addRecord->city = $linkViewRecord->city;
                    $addRecord->district = $linkViewRecord->district;
                    $addRecord->area = $linkViewRecord->area;
                    $addRecord->ua = $linkViewRecord->ua;
                    $addRecord->view_count = $linkViewRecord->view_count;
                    $addRecord->link_view_created_at = $linkViewRecord->created_at;
                    $addRecord->date = date("Y-m-d");
                    $addRecord->state = $state;
                    $addRecord->external_userid = $exUserInfo['external_contact']['external_userid'];
                    $addRecord->name = $exUserInfo['external_contact']['name'];
                    $addRecord->avatar = $exUserInfo['external_contact']['avatar'] ?? "";
                    $addRecord->type = $exUserInfo['external_contact']['type'];
                    $addRecord->gender = $exUserInfo['external_contact']['gender'] ?? "";
                    $addRecord->follow_user_id = $viewWwUserInfo->open_user_id;

                    $addRecord->ocpx_result = '[]';
                    $addRecord->label = '[]';

                    $addRecord->trace_id = '';
                    $otherParam = json_decode($linkViewRecord->other,true);
                    if(isset($otherParam['traceid'])){
                        $addRecord->trace_id = $otherParam['traceid'];
                    }

                    $addRecord->ww_user_group_id = $linkViewRecord->ww_user_group_id;
                    if ($linkViewRecord->wwUserGroup) {
                        $addRecord->ww_user_group_name = $linkViewRecord->wwUserGroup->title;
                    }
                    $addRecord->ww_user_wind_label = $viewWwUserInfo->wind_label;
                    $addRecord->save();
                    // 今日进粉数+1
                    $viewWwUserInfo->today_add_count++;
                    $viewWwUserInfo->save();

                    /** 发送欢迎语 */
                    if ($viewWwUserInfo->welcome_message) {
                        if(isset($message['WelcomeCode'])){
                            $params = WwUser::getWelcomeMessageParams($message['WelcomeCode'], $viewWwUserInfo->welcome_message);
                            $response = WwCorpApiService::sendWelcomeMsg($corpInfo,$params);
                            if(!isset($response) || $response['errcode'] != 0){
                                /** 如果错误码是41051设置错误消息 否则返回原来响应值 */
                                $errorMessage = $response['errmsg'] == 41051 ? "已经开始的聊天的客户不能发送欢迎语" : $response['errmsg'];
                                NotifySendService::sendWorkWeixinForError('进粉-发送欢迎语失败，进粉ID记录：' . $addRecord->id . '，错误码：' . $response['errcode'] . '，失败原因：' . $errorMessage ?? '');
                            }else{
                                NotifySendService::sendWorkWeixinForError('进粉-发送欢迎语成功，进粉ID记录：' . $addRecord->id . '，响应码：' . $response['errcode']);
                            }
                        }
                    }

                    // 只有第一次进粉才进行OCPX的上报 ,调用OCPX的上报队列
                    $action = 'add_external_contact';
                    OcpxSubmitJob::dispatch($addRecord,$action)->onQueue('ocpx_submit');
                    //进粉记录同步点击监测记录队列
                    TrackClickSyncAddRecordJob::dispatch($addRecord)->onQueue('track_click_sync_add_record');
                }
                // 判断最新的跟进信息，follow_user是外部联系人添加的企业微信销售，比如小张（外部联系人）添加了公司的A、B两个销售，那么follow_user中会有2条记录，一般都是1条，因为一般只添加这个企业微信下的1个销售
                foreach ($exUserInfo['follow_user'] as $followUser) {
                    if (!isset($followUser['userid'])) {
                        continue;
                    }
                    // 因为follow_user有可能是多条， 这里做一个判断，只处理当前进粉的销售对应的信息
                    if ($followUser['userid'] == $viewWwUserInfo->open_user_id || $followUser['userid'] == $viewWwUserInfo->user_id) {
                        if ($followUser['userid'] == $viewWwUserInfo->user_id) {
                            $addRecord->follow_user_id = $viewWwUserInfo->user_id;
                        }
                        $addRecord->follow_user_remark = $followUser['remark'] ?? "";
                        $addRecord->follow_user_add_way = $followUser['add_way'] ?? "";
                        $addRecord->follow_user_created_at = date("Y-m-d H:i:s", $followUser['createtime']);

                        $remarkMobileSys = json_decode($addRecord->follow_user_remark_mobiles_sys, true);
                        if (!$remarkMobileSys) {
                            $remarkMobileSys = [];
                        }
                        $remarkMobileSys = array_merge($remarkMobileSys, ($followUser['remark_mobiles'] ?? []));
                        $patternForPhone = '/1[3456789]\d{9}/'; // pattern for chinese mobile phone，移动电话（手机）的正则表达式
                        preg_match($patternForPhone, $addRecord->follow_user_remark, $phones);
                        if (isset($phones[0])) {
                            $remarkMobileSys[] = $phones[0];
                        }
                        $addRecord->follow_user_remark_mobiles_sys = json_encode($remarkMobileSys);
                        $addRecord->follow_user_remark_mobiles = json_encode($followUser['remark_mobiles'] ?? []);
                        $addRecord->follow_user_remark_tags = json_encode($followUser['tags'] ?? []);
                        if (!empty($followUser['tags'])) {
                            $addRecord->follow_user_remark_tags_string = implode(",", array_column($followUser['tags'], "tag_name"));
                        }
                        $addRecord->external_contact_created_at = date("Y-m-d H:i:s", $followUser['createtime']);
                    }
                }
                $addRecord->is_delete = 0;
                $addRecord->save();

                $linkViewRecord->add_ww_user_id = $addRecord->ww_user_id;
                $linkViewRecord->add_time = date("Y-m-d H:i:s", time());
                $linkViewRecord->save();

                // 调用打标签队列
                SetTagsJob::dispatch($message, $addRecord, $corpInfo, $viewWwUserInfo->id)->onQueue('set_tag');
                return true;
            } else {
                NotifySendService::sendWorkWeixinForError("[企微回调][好友变更][严重错误] 回调无法获取到外部联系人信息，请立即查看，相关数据如下：" . json_encode([$message, $exUserInfo]));
                return false;
            }
        }


    }
