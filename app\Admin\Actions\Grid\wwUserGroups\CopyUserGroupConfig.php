<?php

	namespace App\Admin\Actions\Grid\wwUserGroups;

    use Dcat\Admin\Actions\Response;
    use Dcat\Admin\Admin;
    use Dcat\Admin\Grid\RowAction;
    use Dcat\Admin\Traits\HasPermissions;
    use Illuminate\Contracts\Auth\Authenticatable;
    use Illuminate\Database\Eloquent\Model;
    use Illuminate\Http\Request;
    use Illuminate\Support\Facades\Crypt;

    class CopyUserGroupConfig extends RowAction
	{

		/**
		 * @return string
		 */
		protected $title = '<i class="feather icon-copy" style="margin:0 10px 0 10px">&nbsp;&nbsp;复制分组</i>';

		/**
		 * Handle the action request.
		 *
		 * @param Request $request
		 *
		 * @return Response
		 */
        public function handle(Request $request)
        {
            $userId = Admin::user()->id;
            $groupId = $this->getKey();
            $payload = [
                'groupId' => $groupId,
                'expires_at' => time() + 3600,
            ];
            $expiresAt = date('Y-m-d H:i:s', $payload['expires_at']);
            $encrypted = Crypt::encryptString(json_encode($payload));
            return $this->response()->alert()->info('分组代码')->detail('<b>有效期至:'.$expiresAt.'</b><br><br><b>'.$encrypted.'</b>');
        }


		/**
		 * @return string|array|void
		 */
		public function confirm()
		{
//
		}

		/**
		 * @param Model|Authenticatable|HasPermissions|null $user
		 *
		 * @return bool
		 */
		protected function authorize($user): bool
		{
			return true;
		}

		/**
		 * @return array
		 */
		protected function parameters()
		{
			return [
				//	         'id' => $this->row->id
			];
		}
	}
