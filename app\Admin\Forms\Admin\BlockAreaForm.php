<?php

    namespace App\Admin\Forms\Admin;

    use Admin;
use App\Models\AdminUser;
use App\Models\Area;
use App\Models\TencentAdAccount;
    use App\Models\WwLink;
use App\Services\Tools\UtilsService;
use Dcat\Admin\Contracts\LazyRenderable;
    use Dcat\Admin\Traits\LazyWidget;
    use Dcat\Admin\Widgets\Form;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

    class BlockAreaForm extends Form implements LazyRenderable
    {
        use LazyWidget;

        public function handle(array $input)
        {
            if (!empty($input['error_msg'])) {
                return $this->response()->error($input['error_msg'])->refresh();
            }
            $adminUser = AdminUser::query()->find($input['admin_user_id']);
            if ($adminUser) {

                // 1. 刷新缓存
                // 2. 获取当前用户的所有子用户
                // 3. 更新所有子用户的屏蔽区域
                $input['block_area'] = array_filter($input['block_area']);
                $areaNames = Area::query()->whereIn('id', $input['block_area'])->pluck('name')->toArray();
                $adminUserIds = $adminUser->getAllChildrenIds();
                foreach ($adminUserIds as $adminUserId) {
                    $key = "admin_black_areas_{$adminUserId}";
                    Cache::store('redis')->put($key, $areaNames);
                    // 为每个用户生成一个独立的缓存键
                }


                AdminUser::query()->whereIn('id', $adminUserIds)->update(['block_area' => $input['block_area']]);
                return $this->response()->success("操作完成")->refresh();
            } else {
                return $this->response()->error("请刷新后重试")->refresh();
            }
        }

        public function form()
        {
            $this->hidden("admin_user_id");
            $adminUser = AdminUser::query()->find($this->payload['admin_user_id']);
            if ($adminUser) {
                $this->html('<div id="search_block_area"></div>', '搜索选择地域');
                /** 搜搜节点组件 */
                UtilsService::searchJsTree('search_block_area','block_area');
                $areaData = Area::query()->select("id", "name", "pid as parent_id", "id as order")->whereIn("level", [1, 2])->get()->toArray();
                $this->tree('block_area', '屏蔽区域')
                    ->nodes($areaData) // 设置所有节点
                    ->expand(false)
                    ->treeState(false) # 允许单独选择父节点
                    ->customFormat(function ($v) { // 格式化外部注入的值
                        if (!$v) {
                            return [];
                        }
                        return $v;
                    });
            } else {
                $errMsg = '请刷新后重试';
                $this->display("提示")->value($errMsg);
                $this->hidden('error_msg')->value($errMsg);
            }
        }

        public function default()
        {
            return [
                // 展示上个页面传递过来的值
                'admin_user_id' => $this->payload['admin_user_id'] ?? '',
                'block_area' => AdminUser::query()->find($this->payload['admin_user_id'])->block_area ?? []
            ];
        }
    }
