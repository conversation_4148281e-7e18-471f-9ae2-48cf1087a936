<?php

namespace App\Admin\Forms\WwUser;

use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Models\AdminUser;
use App\Models\WwUser;
use App\Services\Tools\LogService;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class BatchSetWindLabelForm extends Form implements LazyRenderable
{
    use LazyWidget;

    // 处理请求
    public function handle(array $input)
    {
        LogService::inputLog('Tools','销售客服管理-配置智投标签', $input, Admin::user()->id, Admin::user()->username);
        if (!$input['id']) {
            return $this->response()->alert()->error('提示')->detail('请选择销售。');
        }
        // id转化为数组
        $id = explode(',', $input['id']);
        if (!$id) {
            return $this->response()->alert()->error('提示')->detail('参数错误。');
        }
        $wwUsers = WwUser::query()->find($id);

        if ($wwUsers->isEmpty()) {
            return $this->response()->alert()->error('提示')->detail('销售不存在。');
        }
        /** @var WwUser $wwUser */
        foreach ($wwUsers as $wwUser) {
            if (!AdminUser::isAdmin($wwUser)) {
                return $this->response()->alert()->error('提示')->detail('无操作权限。');
            }
            $wwUser->wind_label = $input['wind_label'];
            $wwUser->save();

            //添加操作日志队列
            AdminActionLogJob::dispatch(
                'batch_set_wind_label',
                $wwUser->id,
                AdminActionLog::ACTION_TYPE['销售'],
                '【' .$wwUser->name . '】设置智投方舟标签：' . $input['wind_label'],
                getIp(),
                Admin::user()->id
            )->onQueue('admin_action_log_job');
        }
        return $this->response()->alert()->success('提示')->detail('配置成功')->refresh();
    }

    public function form()
    {
        $this->text('wind_label', '标签');
        $this->hidden('id')->value($this->payload['ids'] ?? ''); // 批量操作时使用BatchActionPlus 就可以$this->payload['ids'] 获取批量选中的ids
        $this->confirm('确认提交？');
    }

    // 返回表单数据，如不需要可以删除此方法
    public function default()
    {
        return [
            'password' => '',
            'password_confirm' => '',
        ];
    }
}
