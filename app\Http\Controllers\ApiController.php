<?php

namespace App\Http\Controllers;

use App\Jobs\AskDataJob;
use App\Jobs\FormDataJob;
use App\Services\CacheService;
use App\Services\SmsServices;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ApiController
{


    /**
     * 获取表单提交数据
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function formSubmit(Request $request)
    {
        $data = $request->all();
//        Log::info('表单提交：' . json_encode($data, JSON_UNESCAPED_UNICODE));
        $lockKey = $data['vid'] .  '_form_' . getIp();
        $lock = CacheService::requestLock($lockKey);
        if(!$lock){
            return response()->json(['code' => 1, 'data' => [], 'message' => '您手速太快了'],JSON_UNESCAPED_UNICODE);
        }

        if(!$data['phone']){
            return response()->json(['code' => 1, 'data' => [], 'message' => '请输入手机号'],JSON_UNESCAPED_UNICODE);
        }
        if(!$data['name']){
            return response()->json(['code' => 1, 'data' => [], 'message' => '请输入姓名'],JSON_UNESCAPED_UNICODE);
        }
//        $where = [
//            'name' => $formData['name'] ?? '',
//            'phone' => $formData['phone'] ?? '',
//            'ip' => $formData['ip'] ?? '',
//            'vid' => $formData['vid'],
//        ];
//        $check = DB::table('form_data')->where($where)->first();
//        if($check) {
//            return response()->json(['code' => 1, 'data' => [], 'message' => '您已经提交'],JSON_UNESCAPED_UNICODE);
//        }

        //短信验证码
//        if(!$data['code']){
//            return response()->json(['code' => 1, 'data' => [], 'message' => '请输入验证码'],JSON_UNESCAPED_UNICODE);
//        }
//        $checkCode = SmsServices::verifyCode($data['phone'],$data['code']);
//        if(!$checkCode){
//            return response()->json(['code' => 0, 'data' => [], 'message' => '验证码错误'],JSON_UNESCAPED_UNICODE);
//        }
        $data['ip'] = getIp();
        FormDataJob::dispatch($data)->onQueue('save_form_data');
        return response()->json(['code' => 0, 'data' => [], 'message' => '提交成功'],JSON_UNESCAPED_UNICODE);
    }


    /**
     * 获取问答页提交数据
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function askSubmit(Request $request)
    {
        $data = $request->all();
//        Log::info('提交问答数据：' . json_encode($data, JSON_UNESCAPED_UNICODE));
        $lockKey = $data['vid'] .  '_ask_' . getIp();
        $lock = CacheService::requestLock($lockKey,5);
        if(!$lock){
            return response()->json(['code' => 1, 'data' => [], 'message' => '您手速太快了'],JSON_UNESCAPED_UNICODE);
        }
        $data['ip'] = getIp();
        AskDataJob::dispatch($data)->onQueue('save_ask_data');
        return response()->json(['code' => 0, 'data' => [], 'message' => '提交成功'],JSON_UNESCAPED_UNICODE);
    }

    /**
     * 发送验证码
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|void
     */
    public function sendCode(Request $request){

        $phone = $request->input('phone');
        if(empty($phone)){
            return response()->json(['code' => 1, 'data' => [], 'message' => '请填写手机号'],JSON_UNESCAPED_UNICODE);
        }
        $sendCode = SmsServices::sendCode($phone);
        return response()->json(['code' => $sendCode['code'], 'data' => [], 'message' => $sendCode['message']],JSON_UNESCAPED_UNICODE);

    }

}


