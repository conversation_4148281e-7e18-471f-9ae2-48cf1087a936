<?php

namespace App\Admin\Actions\Grid\wwUser;

use App\Admin\Extensions\Grid\BatchActionPlus;
use App\Admin\Forms\WwUser\BatchSetGroupForm;

class BatchSetGroup extends BatchActionPlus
{
    /**
     * @return string
     */
    public $buttonText = '<button class="btn btn-primary ww_user_batch_btn"><i class="fa fa-group"></i><span class="selected"></span>&nbsp;分组</button>';
    
    public $title = '分组';

    public function form(): BatchSetGroupForm
    {
        // 表单渲染时
        return BatchSetGroupForm::make();
    }
}
