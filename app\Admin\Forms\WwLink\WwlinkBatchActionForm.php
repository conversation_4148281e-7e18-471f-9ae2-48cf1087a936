<?php

namespace App\Admin\Forms\WwLink;

use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Models\AdminDomain;
use App\Models\AdminSubUser;
use App\Models\AdminUser;
use App\Models\ShieldPolicy;
use App\Models\WwLink;
use App\Models\WwTpl;
use App\Models\WwUsersGroup;
use App\Services\Tools\LogService;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class WwlinkBatchActionForm extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input): JsonResponse
    {
        /** @var AdminUser $adminUser */
        $adminUser = Admin::user();
        /** 记录输入日志，便于审计和排查问题 */
        LogService::inputLog('Tools', 'ww_link-链接列表-批量配置', $input, $adminUser->id, $adminUser->username);

        /** 提取批量操作 ID */
        $ids = explode(',', $input['id'] ?? null);
        if (!$ids) {
            return $this->response()->alert()->error('提示')->detail('请选择需要操作的投放链接。');
        }

        /**
         * 配置每种批量操作类型所需校验字段
         * ```
         * [
         *      操作类型 => [[字段1, 字段2...], 提示语, 是否使用 empty() 判断（可选）]
         * ]
         * ```
         */
        $validationRules = [
            '0' => [['tpl_id'], '请选择落地页'],
            '1' => [['ww_user_group_id'], '请选销售分组。'],
            '2' => [['shield_policy_id'], '请选屏蔽规则。'],
            '3' => [['is_open'], '请选择状态。', true], // true 代表用 empty 判断
            '4' => [['ww_label'], '请填写企微标签。'],
            '5' => [['conv_count', 'tads_count'], '请填写上报比例。'],
            '6' => [['use_domain'], '请填写默认域名。'],
            '7' => [['need_shield'], '请选择屏蔽状态。', true] // true 代表用 empty 判断

        ];
        /** 当前选择的操作类型 */
        $type = $input['batch_action_type'] ?? '';

        /** 通用校验逻辑（基于配置）  */
        if (isset($validationRules[$type])) {   // 判断当前操作类型是否有配置校验规则
            // 使用 array_pad 补齐配置项，确保总是有3个值（字段数组、错误提示、是否用 empty 判断）
            [$fields, $message, $useEmpty] = array_pad($validationRules[$type], 3, false);

            // 遍历当前配置需要校验的字段
            foreach ($fields as $field) {
                // 获取字段对应的输入值，未设置则为 null
                $value = $input[$field] ?? null;
                /**
                 * 判断字段值是否为空
                 * - 如果 $useEmpty 为 true，则使用 empty() 判断（适用于 '0' 也算空的情况）
                 * - 否则使用 !$value 判断（适用于普通非空判断）
                 */
                if ($useEmpty ? empty($value) : !$value) {
                    return $this->response()->alert()->error('提示')->detail($message);
                }
            }
        }
        /** @var JsonResponse|true $resourceValidationResult 资源权限验证 */
        $resourceValidationResult = $this->validateResourcePermissions($input, $adminUser->id);

        /** 返回错误信息 */
        if ($resourceValidationResult !== true) {
            return $resourceValidationResult;
        }
        /** 构造批量更新的数据结构 */
        $batchUpdateData = match ($type) {
            '0' => ['tpl_id' => $input['tpl_id'], 'tpl_type' => 1],
            '1' => ['ww_user_group_id' => $input['ww_user_group_id']],
            '2' => ['shield_policy_id' => $input['shield_policy_id']],
            '3' => ['is_open' => $input['is_open'] == 'open' ? 1 : 0],
            '4' => ['ww_label' => $input['ww_label']],
            '5' => ['conv_count' => $input['conv_count'], 'tads_count' => $input['tads_count']],
            '6' => ['use_domain' => $input['use_domain']],
            '7' => ['need_shield' => $input['need_shield'] == 'yes' ? 1 : 0],
        };

        /** @var WwLink $wwLinks 获取链接对象，后面验证权限 & 生成日志 */
        $wwLinks = WwLink::query()->find($ids);

        /** 获取链接对象，后面验证权限 & 生成日志 */
        foreach ($wwLinks as $wwLink) {
            if (!AdminUser::isAdmin($wwLink)) {
                return $this->response()->alert()->error('提示')->detail('异常操作，请刷新页面后重试。');
            }
        }

        /** 所有链接权限通过，开始记录操作日志（一个链接一条日志） */
        foreach ($wwLinks as $wwLink) {
            /** 生成日志消息 */
            $actionLogMessage = match ($type) {
                '0' => '批量设置落地页，账户ID：「' . $wwLink->account_id . '」，页面ID：' . $input['tpl_id'],
                '1' => '批量配置投放销售分组，账户ID：「' . $wwLink->account_id . '」，分组ID：' . $input['ww_user_group_id'],
                '2' => '批量配置屏蔽规则，账户ID：「' . $wwLink->account_id . '」，屏蔽规则ID：' . $input['shield_policy_id'],
                '3' => '投放链接ID【' . $wwLink->id . '】，修改状态为：' . ($input['is_open'] == 'open' ? '开启' : '关闭'),
                '4' => '批量配置企微链接企微标签，账户ID：「' . $wwLink->account_id . '」，企微标签：' . $input['ww_label'],
                '5' => '批量设置上报比例，账户ID：「' . $wwLink->account_id . '」，转化：' . $input['conv_count'] . '，上报：' . $input['tads_count'],
                '6' => "批量设置默认域名，链接ID：「" . $wwLink->id . '」，设置为：' . $input['use_domain'],
                '7' => '批量设置屏蔽状态，账户ID：「' . $wwLink->account_id . '」，屏蔽状态：' . ($input['need_shield'] == 'yes' ? '是' : '否'),

            };

            /** 派发操作日志 Job（异步写日志队列） */
            AdminActionLogJob::dispatch(
                'update_ww_link',
                $wwLink->id,
                AdminActionLog::ACTION_TYPE['链接'],
                $actionLogMessage,
                getIp(),
                $adminUser->id
            )->onQueue('admin_action_log_job');
        }
        /** 最终批量更新数据库字段 */
        WwLink::query()->whereIn('id', $ids)->update($batchUpdateData);
        return $this->response()->alert()->success('提示')->detail('配置成功')->refresh();
    }

    /**
     * 验证资源权限的统一方法
     *
     * @param array $input 输入数据
     * @param mixed $authorizedAdminUids 管理员ID
     * @return JsonResponse|true 验证失败返回错误响应，成功返回true
     */
    public function validateResourcePermissions(array $input, mixed $authorizedAdminUids): bool|JsonResponse
    {
        /** @var string $actionType 获取当前批量操作类型 */
        $actionType = $input['batch_action_type'] ?? '';

        /** @var array $resourceValidations 定义需要验证资源权限的操作类型配置 */
        $resourceValidations = [
            '0' => [
                'model' => WwTpl::class,
                'field' => 'tpl_id',
                'error' => '落地页不存在或无权限访问'
            ],
            '1' => [
                'model' => WwUsersGroup::class,
                'field' => 'ww_user_group_id',
                'error' => '销售分组不存在或无权限访问'
            ],
            '2' => [
                'model' => ShieldPolicy::class,
                'field' => 'shield_policy_id',
                'error' => '屏蔽规则不存在或无权限访问'
            ]
        ];

        /** 检查当前操作类型是否需要验证资源权限 */
        if (isset($resourceValidations[$actionType])) {

            /** @var array $config 获取当前操作类型的验证配置 */
            $config = $resourceValidations[$actionType];

            /** @var string $resourceId 从输入数据中获取资源ID */
            $resourceId = $input[$config['field']] ?? null;

            /** 如果没有资源ID，跳过验证（前面的字段验证已经处理过） */
            if (!$resourceId) {
                return true; // 如果没有资源ID，跳过验证（前面已经有字段验证）
            }

            /** @var WwTpl|WwUsersGroup|ShieldPolicy $modelClass 获取模型类名并设置类型提示，便于IDE识别 */
            $modelClass = $config['model'];

            /** 查询资源是否存在且当前用户有权限访问 */
            $resource = $modelClass::query()
                ->where('admin_uid', $authorizedAdminUids)
                ->where('id', $resourceId)
                ->first();

            if (!$resource) {
                return $this->response()->alert()->error('提示')->detail($config['error']);
            }

        }
        return true;
    }
    public function form(): void
    {
        /** @var AdminUser $adminUser */
        $adminUser = Admin::user();
        $this->hidden('id')->value($this->payload['ids'] ?? ''); // 批量操作的链接ID
        $batchActionType = [
            0 => '落地页',
            1 => '投放销售分组',
            2 => '屏蔽规则',
            3 => '状态',
            4 => '企微标签',
            5 => '上报比例',
            6 => '默认域名',
            7 => '屏蔽状态'
        ];
        $this->radio('batch_action_type', '批量配置类型')
            ->when(0, function () use ($adminUser) {
                $tplList = WwTpl::getAdTplList();
                $this->select('tpl_id', '投放页面')->options($tplList);
            })
            ->when(1, function () use ($adminUser) {
                $this->select('ww_user_group_id', '投放销售分组')->options(WwUsersGroup::getMyAdGroupList());//修改为展示授权分组以及自身分组
            })
            ->when(2, function () use ($adminUser) {
                $shieldPolicy = ShieldPolicy::query()->whereIn("admin_uid", AdminSubUser::getALLAdminUids($adminUser->id))->pluck("name", "id")->toArray();
                $this->select('shield_policy_id', '屏蔽规则')->options($shieldPolicy);
            })
            ->when(3, function () use ($adminUser) {
                $this->select('is_open','状态')->options(['open' => '打开','close' => '关闭'])->default(0);
            })
            ->when(4, function () use ($adminUser) {
                $this->textarea("ww_label","标签")->placeholder("一行一个，可以配置多个，需要确保配置的标签在企业微信后台标签库有配置，例如：" . PHP_EOL . "标签1" . PHP_EOL . "标签2");
            })
            ->when(5, function () use ($adminUser) {
                $this->number('conv_count', '转化')->min(1)->default(1);
                $this->number('tads_count', '上报')->min(1)->default(1)->help("建议使用默认配置，系统将根据此配置比例进行优化。例如：2转化1上报=50%回传，1转化1上报=100%回传。<br/>");
            })
            ->when(6, function () use ($adminUser) {
                $adminDomain = AdminDomain::query()->whereIn('admin_uid', AdminSubUser::getAdminUids($adminUser->id))->pluck('domain', 'domain')->toArray();
                $this->select('use_domain', '域名')->options($adminDomain);
            })
            ->when(7, function () use ($adminUser) {
                $this->radio('need_shield')->options(['no' => '否', 'yes' => '是']);
            })
            ->options($batchActionType)->required();
        $this->confirm('确认提交？');
    }
}
