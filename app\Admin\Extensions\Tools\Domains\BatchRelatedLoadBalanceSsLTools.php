<?php

namespace App\Admin\Extensions\Tools\Domains;

use Admin;
use App\Admin\Extensions\Grid\BatchActionPlus;
use App\Models\AdminDomain;
use App\Models\AdminUser;
use App\Services\AlibabaCloudService;
use App\Services\NotifySendService;
use Dcat\Admin\Grid\BatchAction;
use Illuminate\Http\Request;

class BatchRelatedLoadBalanceSsLTools extends BatchActionPlus
{
    public $title = '<button class="btn btn-primary" style="margin-left: 5px"><i class="feather icon-minimize-2"></i>&nbsp;&nbsp;关联证书到负载均衡</button>';

    protected $listenerId = 'lsn-muwx0iancv5jwv6bb5';

    // 处理请求
    public function handle(Request $request)
    {
        $keys = $this->getKey();

        $domains = AdminDomain::query()
//            ->where('listener_status','noAssocia')
//            ->where('upload_status',1)
            ->whereIn('id', $keys)
            ->get();
        if ($domains->isEmpty()) {
            return $this->response()->alert()->error('提示')->detail('域名不存在');
        }
        foreach ($domains as $domain) {
            if($domain->upload_status != 1){
                return $this->response()->alert()->error('提示')->detail('证书未上传到阿里云');
            }
            if($domain->listener_status == 'Associated'){
                return $this->response()->alert()->error('提示')->detail('该域名的证书已关联到负载均衡：'.$domain->domain);
            }
            if($domain->listener_status == 'Associating'){
                return $this->response()->alert()->error('提示')->detail('该域名的证书正在关联中：'.$domain->domain);
            }
        }
        $ali_cert_id = array_column($domains->toArray(), 'ali_cert_id');

        $response = AlibabaCloudService::associateCertificates($this->listenerId, $ali_cert_id);
        if ($response['code'] == 200) {
            AdminDomain::query()->whereIn('id', $keys)->update(['listener_status' => 'Associating']);
            /** 查询不携带证书参数返回总数量 */
            $listenerTotalCount = AlibabaCloudService::listListenerCertificates($this->listenerId, []);
            /** 如果有就提示当前数量 */
            if ($listenerTotalCount['success']) {
                /** 消息内容 */
                $listenerTotalCountMessage = '负载均衡:'.$this->listenerId.'数量:'.
                    $listenerTotalCount['data']['TotalCount'] .'个';
                /** 超过260报警企微通知 */
                if ($listenerTotalCount['data']['TotalCount'] >= 260) {
                    /** 获取所有域名ID */
                    $doMainIds = array_column($domains->toArray(), 'id');
                    NotifySendService::sendWorkWeixinForError('负载均衡实例提醒：'.$this->listenerId.PHP_EOL.
                        '阿里证书ID：'.implode(",",$ali_cert_id).PHP_EOL.
                        '后台域名ID：'.implode(",",$doMainIds).PHP_EOL.
                        '数量超过260，请创建新实例 '.PHP_EOL.'当前'.$this->listenerId.'证书数量：'.$listenerTotalCount['data']['TotalCount']);
                }
            }else{
                /** 显示错误代码方便排查问题 */
                $listenerTotalCountMessage = '<br>负载均衡数量查询失败,联系系统管理员<br>'.$listenerTotalCount['errors']['requestId'];
            }
        }
        /** @var AdminUser $adminUser */
        $adminUser = Admin::user();
        /** 发送消息到运营群 */
        NotifySendService::sendWopForMessage(
            title: "域名管理-关联证书通知",
            contentLines:[
                "用户ID" => $adminUser->id,
                "用户姓名" => $adminUser->username,
                "操作域名ID" => implode(',',$keys),
                "域名证书ID" => implode(',',$ali_cert_id),
                "侦听器总计数" => ($listenerTotalCountMessage ?? "侦听器总计数获取失败"),
                "操作时间" => date("Y-m-d H:i:s")
            ]
        );
        return $this->response()->alert()->success('提示')->detail(
            $response['message'] ?? '关联失败'.'<br>'.
            ($listenerTotalCountMessage ?? "侦听器总计数获取失败")
        )->refresh();
    }

    public function confirm()
    {
        return ['提示','确认要关联证书到负载均衡？操作前，请先确保证书已经上传成功。'];
    }
    // 设置请求参数
    public function parameters()
    {

    }

}
