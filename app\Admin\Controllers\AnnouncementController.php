<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\Announcement;
use App\Services\Tools\UtilsService;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Models\Administrator;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Redis\Connections\PhpRedisConnection;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Request;

class AnnouncementController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Announcement(), function (Grid $grid) {
            /** 对话框新增 */
            $grid->disableCreateButton();
            $grid->tools(function (Grid\Tools $tools) {
                $className = collect(Request::segments())->last();
                $tools->append(UtilsService::dialogForm('添加',Request::url().'/create',"create-{$className}"));
            });
            $grid->column('id')->sortable();
            $grid->column('title');
            $grid->column('overview');
            $grid->column('content');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
            $grid->disableViewButton();
//            $grid->disableActions();
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Announcement(), function (Show $show) {
            $show->field('id');
            $show->field('title');
            $show->field('overview');
            $show->field('content');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Announcement(), function (Form $form) {
            $form->display('id');
            $form->text('title')->required();
            $form->text('overview')->required();
            $form->textarea('content')->required();

            $form->display('created_at');
            $form->display('updated_at');
            $form->saved(function (Form $form) {
                //更新未读公告
                /** @var PhpRedisConnection $redis */
                $redis = Redis::connection('announcement');
                $data = Administrator::query()->pluck('id')->toArray();
                $redis->sAdd('NotReadAnnouncement', ...$data);
            });
            $form->disableEditingCheck();
            $form->disableViewButton();
        });
    }
}
