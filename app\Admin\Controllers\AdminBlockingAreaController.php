<?php

namespace App\Admin\Controllers;

use App\Admin\Forms\Admin\BlockAreaForm;
use App\Models\AdminUser;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Contracts\View\View;

class AdminBlockingAreaController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(AdminUser::class, function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->disableDeleteButton();
            $grid->column('id', 'ID')
                ->sortable();
            $grid->column('username')
                ->tree(true);
            $grid->column('name');
            $grid->column('404区域')
                ->display('设置')
                ->label()
                ->modal(function (Grid\Displayers\Modal $modal) {
                    // 标题
                    $modal->title('设置404区域');
                    // 自定义图标
                    $modal->icon('');//feather icon-check-circle
                    $modal->xl();
                    // 传递当前行字段值
                    $data = [
                        'admin_user_id' => $this->getKey()
                    ];
                    return BlockAreaForm::make()
                        ->payload($data);
                });
            $grid->column('created_at');
            $grid->column('updated_at')
                ->sortable();

            $grid->quickSearch(['id', 'name', 'username']);
            $grid->disableViewButton();
            $grid->disableBatchActions();
            $grid->disableActions();
        });
    }


    protected function form(): View
    {
        abort(404,'页面不存在');
    }

    protected function detail(): View
    {
        abort(404,'页面不存在');
    }
}
