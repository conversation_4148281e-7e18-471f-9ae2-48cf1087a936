<?php

namespace App\Console\Commands\Job;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FailedJobsRetry extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'Job:FailedJobsRetry';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '失败的队列重试';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {


//        $command = 'php artisan queue:retry all';
//         system($command);
//        return Command::SUCCESS;
        $failedJobs = DB::table('failed_jobs')
            ->get()
            ->toArray();
        if (!empty($failedJobs)) {
            foreach ($failedJobs as $failedJob) {
                if(str_contains($failedJob->queue,'new_import_ww_user')){
                   continue;
                }
                if(str_contains($failedJob->queue,'export_task')){
                    continue;
                }
                $command = 'php artisan queue:retry ' . $failedJob->uuid;
                $output = system($command);
                Log::info('执行失败的队列重试:' . $command . ' ，结果:' . $output);
            }
        }
    }

}
