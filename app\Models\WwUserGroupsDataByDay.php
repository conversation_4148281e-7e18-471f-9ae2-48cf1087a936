<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

/**
 * @property mixed            $date
 * @property int|mixed|string $ww_user_group_id
 */
class WwUserGroupsDataByDay extends Model
{
	use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'ww_user_groups_data_by_days';

	public function groupInfo (): BelongsTo
	{
		return $this->BelongsTo(WwUsersGroup::class, 'ww_user_group_id', 'id');
	}
}
