<?php

namespace App\Admin\Extensions\Tools\WwUserAddRecord;

use App\Models\AdminUser;
use App\Models\ShieldIp;
use App\Models\WechatUser;
use App\Models\WwUserAddRecord;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid\BatchAction;

class WwUsersToBlack extends BatchAction
{
    protected $title = '<button class="btn btn-primary ww_user_batch_btn" style="margin-left: 5px"><i class="fa fa-user-times"></i>&nbsp&nbsp<span class="selected"></span>拉黑</button>';

    /**
     * 确认弹窗信息
     * @return string
     */
    public function confirm(): string
    {
	    return '您确定要拉黑吗';
    }

    // 处理请求
    public function handle()
    {
        // 获取页面的分组
        $keys = $this->getKey();
		if(!$keys){
            return $this->response()->alert()->error('提示')->detail('请选择要拉黑的加粉明细。');
        }
		foreach($keys as $id){


			$addRecord = WwUserAddRecord::query()->find($id);
			if(!$addRecord){
                return $this->response()->alert()->error('提示')->detail('加粉明细不存在，请刷新页面后重试。');
			}
            if(!AdminUser::isAdmin($addRecord)){
                return $this->response()->error("无权限操作");
            }
            $wechatUser = WechatUser::query()->where("id",$addRecord->user_id)->first();
			if(!$wechatUser){
                return $this->response()->alert()->error('提示')->detail("ID为：【{$id}】的加粉明细溯源失败，请联系运营。");
			}
			$wechatUser->is_black = - Admin::user()->id;
			$wechatUser->save();

            //同步拉黑IP
            $shieldIpModel = new ShieldIp();
            $checkShieldIp = $shieldIpModel->where('ip',$addRecord->ip)->where('admin_uid',Admin::user()->id)->first();
            if(!$checkShieldIp){
                $shieldIpModel->admin_uid = Admin::user()->id;
                $shieldIpModel->ip = $addRecord->ip;
                $shieldIpModel->remark = Admin::user()->username . '用户通过进粉记录拉黑';
                $shieldIpModel->save();
            }
		}
        return $this->response()->alert()->success('提示')->detail('拉黑成功。');
    }

}
