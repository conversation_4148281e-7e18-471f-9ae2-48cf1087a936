<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Grid\adminUser\BatchSetAuditOcpx;
use App\Admin\Actions\Grid\adminUser\BatchSetRobotNotice;
use App\Admin\Actions\Grid\adminUser\BatchSetWwUserOfflineCheck;
use App\Admin\Actions\Grid\adminUser\BatchUpdateShowWwUserPolicy;
use App\Models\AdminRoles;
use App\Models\AdminRoleUser;
use App\Models\AdminUser;
use App\Models\TencentAdAccount;
use App\Models\WwAppList;
use App\Models\WwUsersDataByDay;
use App\Services\Tools\UtilsService;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Show;

class AdminUserMessageRobotsController extends AdminController
{

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid(): Grid
    {
        return Grid::make(new AdminUser(), function (Grid $grid) {
            $grid->model()->orderByDesc("id");
            $grid->column('id')->sortable();
            $grid->column('id', '用户ID');
            $grid->column('username', '用户名称');
            if(Admin::user()->id == 1){
                $grid->column('登陆TA的账号')->display(function () {
                    $adminUid = encrypt($this->id);
                    $url = "/ztfz/switch-user/{$adminUid}";
                    $userName = addslashes($this->name); // 安全处理

                    return <<<HTML
<button class="btn btn-sm btn-pure btn-info"
        title="在新窗口登录此账户"
        onclick="
            Dcat.confirm(
                '提示',
                '确认切换到【 {$userName}】 ？',
                function() {
                    const newWindow = window.open('{$url}', '_blank');
                    newWindow.focus();
                    Dcat.NP.done(); // 确保进度条正常关闭
                }
            )">
    <i class="feather icon-external-link"></i> 登录
</button>
HTML;
                })->width(100);

            }

            $grid->column('parent_id', '主账号')->display(function ($parent_id) {
                if($parent_id){
                    return  "<span style='color: red'>否</span>";
                }
                return  "<span style='color: green'>是</span>";
            });
            $grid->column('wechat', '企微机器人')->display(function ($wechat) {
                return $wechat ? "<span style='color: #0B8B06'>已配置</span>" : "<span style='color:peru'>未配置</span>";
            });
            $grid->column('dingding', '钉钉机器人')->display(function ($dingding) {
                return $dingding ? "<span style='color: #0B8B06'>已配置</span>" : "<span style='color: peru'>未配置</span>";
            });
            $grid->column('feishu', '飞书机器人')->display(function ($feishu) {
                return $feishu ? "<span style='color: #0B8B06'>已配置</span>" : "<span style='color:peru'>未配置</span>";
            });

            $grid->column('repeat_ww_link', '重复创建投放链接')
                ->using([0 => '关闭', 1 => '开启'])
                ->dot([0 => 'danger', 1 => 'success'], 'primary');

            $grid->column('today_add_record', '进粉记录默认当天')
                ->using([0 => '关闭', 1 => '开启'])
                ->dot([0 => 'danger', 1 => 'success'], 'primary');

            $grid->column('audit_ocpx', '屏蔽进粉手动上报')
                ->using([0 => '关闭', 1 => '开启'])
                ->dot([0 => 'danger', 1 => 'success'], 'primary');

            $grid->column('ww_user_offline_check', '销售批量下线检查')
                ->using([0 => '关闭', 1 => '开启'])
                ->dot([0 => 'danger', 1 => 'success'], 'primary');

            $grid->column('show_ww_user_policy', '展示销售策略')->select(AdminUser::SHOW_WW_USER_POLICY);

            $grid->tools([
                new BatchUpdateShowWwUserPolicy(), //批量配置去重策略
                new BatchSetRobotNotice(), //批量配置机器人
                new BatchSetAuditOcpx(), //批量配置屏蔽进粉手动上报
                new BatchSetWwUserOfflineCheck() //批量配置销售下线检查
            ]);
            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                $filter->like('username', '用户')->width(2);
            });
            $grid->disableViewButton();
            $grid->disableCreateButton();
            $grid->disableDeleteButton();
            $grid->disableBatchDelete();
//            $grid->disableActions();
//            $grid->disableRowSelector();
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id): Show
    {
        return Show::make($id, new WwUsersDataByDay(), function (Show $show) {
            if (!AdminUser::isAdmin($show->model())) {
                $show->field('N')->value("无数据");
            } else {
                $show->field('id');
            }
            $show->disableDeleteButton();
            $show->disableEditButton();
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form(): Form
    {
        return Form::make(new AdminUser(), function (Form $form) {
            $form->display('id');
            $form->display('username');
            $form->url('wechat', '企微机器人链接');
            $form->url('dingding', '钉钉机器人链接');
            $form->url('feishu', '飞书机器人链接');
            $form->switch('repeat_ww_link', '同一广告账户重复创建投放链接')->options([1 => '是', 0 => '否'])->default(1);
            $form->switch('today_add_record', '进粉记录默认当天')->options([1 => '是', 0 => '否'])->default(1);
            $form->select('show_ww_user_policy', '展示销售策略')->options(AdminUser::SHOW_WW_USER_POLICY)->default(0);
            $form->switch('ad_tools_role', '配置广告工具角色')->help('开启后，左侧菜单栏将会显示【广告工具】菜单');
//            $form->switch('set_miaohu_auth', '配置智投方舟授权')
//                ->help('开启后，企微管理页面将会显示【智投方舟】扫码授权按钮。关闭则不显示。');
            $form->switch('audit_ocpx', '屏蔽进粉手动上报')->help('开启后，屏蔽页进粉可以手动上报。');
            $form->switch('ww_user_offline_check', '销售批量下线检查')->help('开启后，销售批量下线时，会判断否是某些分组内的最后一个在线销售。');
            $form->disableViewButton();
            $form->disableDeleteButton();
            $form->disableResetButton();
            $form->disableViewButton();
            $form->disableViewCheck();
            $form->disableEditingCheck();
//                $form->display('created_at');
//                $form->display('updated_at');

        })->saving(function (Form $form) {
            $adminUids = AdminUser::getAllAdminUidsByAdminUid($form->model()->id);
            $roleId = AdminRoles::query()->where('slug', 'tencentAdTools')->value('id');
            if ($form->input('ad_tools_role')) {
                $check = AdminRoleUser::query()->where('role_id', $roleId)->where('user_id', $form->model()->id)->first();
                if (!$check) {
                    $insert = [
                        'role_id' => $roleId,
                        'user_id' => $form->model()->id,
                        'created_at' => date('Y-m-d H:i:s', time()),
                        'updated_at' => date('Y-m-d H:i:s', time()),
                    ];
                    AdminRoleUser::query()->insert($insert);
                    if($adminUids){
                        TencentAdAccount::query()->whereIn('admin_uid',$adminUids)->update(['monitor_status' => 1]);
                    }
                }
            } else {
                AdminRoleUser::query()->where('role_id', $roleId)->where('user_id', $form->model()->id)->delete();
                TencentAdAccount::query()->whereIn('admin_uid',$adminUids)->update(['monitor_status' => 0]);
            }

            //配置智投方舟
//            $checkSetMiaohuAuth = WwAppList::query()
//                ->withTrashed()
//                ->where('is_3rd',0)
//                ->where('admin_uid', $form->model()->id)
//                ->first();
//            if ($form->input('set_miaohu_auth')) {
//                if (!$checkSetMiaohuAuth) {
//                    $app = WwAppList::query()->first()->toArray();
//                    if ($app) {
//                        unset($app['id'], $app['admin_uid']);
//                        $app['admin_uid'] = $form->model()->id;
//                        $app['created_at'] = date('Y-m-d H:i:s');
//                        $app['updated_at'] = date('Y-m-d H:i:s');
//                        WwAppList::query()->insert($app);
//                    }
//                } else if ($checkSetMiaohuAuth && $checkSetMiaohuAuth->deleted_at) {
//                    $checkSetMiaohuAuth->deleted_at = null;
//                    $checkSetMiaohuAuth->save();
//                }
//            } else {
//                if ($checkSetMiaohuAuth && !$checkSetMiaohuAuth->deleted_at) {
//                    $checkSetMiaohuAuth->deleted_at = date('Y-m-d H:i:s', time());
//                    $checkSetMiaohuAuth->save();
//                }
//            }
        });
    }

    public function destroy($id)
    {
        return $this->form()->response()->error("无权限操作");
    }

}
