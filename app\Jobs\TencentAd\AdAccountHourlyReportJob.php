<?php

namespace App\Jobs\TencentAd;

use App\Jobs\Middleware\RateLimitedJob;
use App\Models\TencentAdAccount;
use App\Models\LinkClickRecord;
use App\Models\LinkViewRecord;
use App\Models\WwLink;
use App\Models\WwUserAddRecord;
use App\Services\TenCentAd\DataService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AdAccountHourlyReportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected TencentAdAccount $accountInfo;
    protected $startDate;
    protected $endDate;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($accountInfo,$startDate,$endDate)
    {
        $this->accountInfo = $accountInfo;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
    }

    /**
     * 获取任务时，应该通过的中间件。
     *
     * @return array
     */
    public function middleware()
    {
        return [new RateLimitedJob()];
    }


    /**
     * Execute the job.
     *
     * @return mixed
     */
    public function handle()
    {

        $hourlyReportList = DataService::hourlyReports($this->accountInfo,$this->startDate,$this->endDate); //获取接口数据

        if (!$hourlyReportList) {
//            Log::info($this->accountInfo->account_id . '：无数据');
            return false;
        }
//        Log::info(json_encode($hourlyReportList));
        $thisHour = date('H');//获取当前小时

        $adminUid = $this->accountInfo->admin_uid;
        $accountId = $this->accountInfo->account_id;
        $date = date('Y-m-d');
        $addRecordCount = 0;//进粉数
        $openCount = 0;     //开口数
        $jumpPvCount = 0;   //二跳pv数
        $jumpUvCount = 0;   //二跳uv数

        $hourData = [];
        foreach ($hourlyReportList as $hourlyReport) {
            $hourData[$hourlyReport['hour']] = $hourlyReport;
        }

        for ($i = 0; $i < 24; $i++) {
            if (!isset($hourData[$i])) {
                $hourData[$i] = [
                    "account_id" => $accountId,
                    "view_count" => 0,
                    "valid_click_count" => 0,
                    "ad_cost" => 0,
                    "conversions_count" => 0,
                    "conversions_rate" => 0,
                    "hour" => $i,
                    "conversions_cost" => 0,
                    "add_count" => 0,
                    "open_count" => 0,
                ];
            }
        }

        foreach ($hourData as $key => $hourlyReport) {
            $hour = $hourlyReport['hour'];
            $where = [
                'admin_uid' => $adminUid,
                'account_id' => $accountId,
                'hour' => $hour,
                'date' => $date
            ];

            $wwLinkIds = WwLink::query()
                ->select('id', 'account_id')
                ->where('account_id', $accountId)
                ->where('admin_uid', $adminUid)
                ->pluck('id')
                ->toArray();

            if ($wwLinkIds) {
                // 首先判断循环得到数据的时间，与当前时间，是否匹配，如匹配（循环的小时是当前小时，或当前小时的上一个小时，如果是 0 点，仅统计 0 点的数据即可）
                if($thisHour == 0){
                    $sysDataHourRange = [$thisHour];
                }else{
                    $sysDataHourRange = [$thisHour-1,$thisHour];
                }

//                if(in_array($hour,$sysDataHourRange)){
//                    Log::info("统计的时间是：".$hour);
                    $dateRange = [
                        $date . ' ' . $hour . ':00:00',
                        $date . ' ' . $hour . ':59:59'
                    ];
                    $addRecordCount = WwUserAddRecord::query()
                        ->where("admin_uid", $adminUid)
                        ->whereIn('ww_link_id', $wwLinkIds)
                        ->whereBetween('external_contact_created_at', $dateRange)
                        ->count();
                    $openCount = WwUserAddRecord::query()
                        ->where("admin_uid", $adminUid)
                        ->whereIn('ww_link_id', $wwLinkIds)
                        ->whereBetween('external_contact_created_at', $dateRange)
                        ->where('customer_start_chat', 1)
                        ->count();

                    $jumpUvCount = LinkClickRecord::query()
                        ->where("admin_uid", $adminUid)
                        ->whereIn('ww_link_id', $wwLinkIds)
                        ->whereBetween('created_at', $dateRange)
                        ->count();
                    $jumpPvCount = LinkViewRecord::query()
                        ->where("admin_uid", $adminUid)
                        ->whereIn('ww_link_id', $wwLinkIds)
                        ->whereBetween('created_at', $dateRange)
                        ->count();
//                }
            }
            //查询数据库是否存在
            $checkHourlyReport = DB::table('ad_account_monitor')->where($where)->first();
            if (!$checkHourlyReport) {
                $insertData = [
                    'admin_uid' => $adminUid,
                    'account_id' => $accountId,
                    'view_count' => $hourlyReport['view_count'] ?? 0,
                    'valid_click_count' => $hourlyReport['valid_click_count'] ?? 0,
                    'ad_cost' => $hourlyReport['cost'] ?? 0,
                    'conversions_count' => $hourlyReport['conversions_count'] ?? 0,
                    'conversions_rate' => $hourlyReport['conversions_rate'] ?? 0,
                    'conversions_cost' => $hourlyReport['conversions_cost'] ?? 0,
                    'jump_pv_count' => $jumpPvCount,
                    'jump_uv_count' => $jumpUvCount,
                    'add_count' => $addRecordCount,
                    'open_count' => $openCount,
                    'hour' => $hour,
                    'date' => $date,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                DB::table('ad_account_monitor')->insert($insertData);

            } else {
                $updateData = [
                    'view_count' => $hourlyReport['view_count'] ?? 0,
                    'valid_click_count' => $hourlyReport['valid_click_count'] ?? 0,
                    'ad_cost' => $hourlyReport['cost'] ?? 0,
                    'conversions_count' => $hourlyReport['conversions_count'] ?? 0,
                    'conversions_rate' => $hourlyReport['conversions_rate'] ?? 0,
                    'conversions_cost' => $hourlyReport['conversions_cost'] ?? 0,
                    'jump_pv_count' => $jumpPvCount,
                    'jump_uv_count' => $jumpUvCount,
                    'add_count' => $addRecordCount,
                    'open_count' => $openCount,
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                DB::table('ad_account_monitor')->where('id',$checkHourlyReport->id)->update($updateData);
//
            }
        }

        return true;
    }
}
