<?php

namespace App\Admin\Controllers;

use Admin;
use App\Admin\Forms\TencentAd\AdTracClickConfigByTypeForm;
use App\Admin\Forms\TencentAd\TrackClickConfigImportForm;
use App\Admin\Repositories\TenCentAdTrackClickConfig;
use App\Models\TencentAdAccount;
use App\Models\AdminSubUser;
use App\Models\AdminUser;
use App\Services\TenCentAd\AdTrackClickService;
use App\Services\Tools\UtilsService;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Show;
use Dcat\Admin\Widgets\Modal;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request as FacadesRequest;
use Illuminate\Support\Facades\Log;
use App\Models\TenCentAdTrackClickConfig as TenCentAdTrackClickConfigModel;


/**
 * @property int $id
 * @property     $input
 */
class TenCentAdTrackClickConfigController extends AdminController
{

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {

        return Grid::make(new TenCentAdTrackClickConfig(['adminInfo', 'adAccountInfo']), function (Grid $grid) {
            /** 对话框新增 */
            $grid->disableCreateButton();
            $grid->tools(function (Grid\Tools $tools) {
                $className = collect(FacadesRequest::segments())->last();
                $tools->append(UtilsService::dialogForm('新增',FacadesRequest::url().'/create',"create-{$className}"));
            });
            $grid->disableViewButton();
            $grid->disableDeleteButton();
            $grid->disableActions();
//            $grid->disableBatchDelete();
//            $grid->disableCreateButton();
            if(AdminUser::isSystemOp()){
                $grid->model()->orderByDesc("id");
            }else{
                if (Admin::user()->isRole('customer')) { //如果是“客户”的角色 也就是主账号
                    $grid->model()->whereIn("admin_uid", AdminSubUser::getAdminUids(Admin::user()->id));//如果是主账号，则获取所有子账号的id，包括自己
                    $grid->column('归属账号')->display(function () {
                        if (Admin::user()->id == $this->admin_uid) { //如果是自己创建的则直接显示主账号
                            return '主账号';
                        } else {
                            $username = Admin::user()->where('id', $this->admin_uid)->value('username');
                            $username = str_replace(Admin::user()->username, '', $username);
                            return $username;
                        }
                    });
                } else { //如果是客户运营的角色 也就是子账号
                    $grid->model()->where("admin_uid", Admin::user()->id);
                }
            }


            // 在工具栏添加操作按钮，用于上传Excel文件及下载导入模板
            $grid->tools(function (Grid\Tools $tools) {
                $tools->append(Modal::make()
                    // 大号弹窗
                    ->lg()
                    // 弹窗标题
                    ->title('上传账户ID文件')
                    // 按钮
                    ->button('<button class="btn btn-primary"><i class="feather icon-upload"></i> 导入账户ID配置</button>')
                    // 弹窗内容
                    ->body(TrackClickConfigImportForm::make()));

            });

            $modal = Modal::make()
                ->lg()
                ->title('根据用户账号配置')
                ->body(AdTracClickConfigByTypeForm::make())
                ->button('<button class="btn btn-primary float-right" style="margin-left: 5px"><i class="feather icon-user"></i>&nbsp&nbsp根据用户账号配置</button>');
            $grid->tools($modal);

            $grid->column('id');
            $grid->column('adminInfo.username', '用户')->copyable();
            $grid->column('feedback_name')->copyable();
            $grid->column('account_id')->copyable();
            $grid->column('adAccountInfo.account_name', '广告账户名称')->copyable();
            $grid->column('adAccountInfo.corporation_name', '企业主体名称')->copyable();
            $grid->column('second_category_type', '营销载体类型')->display(function ($second_category_type) {
               return TenCentAdTrackClickConfigModel::CATEGORY_TYPE[$second_category_type];
            });
            $grid->column('updated_at');
            $grid->filter(function (Grid\Filter $filter) use ($grid) {
                $filter->expand();
                $filter->panel();
                $filter->equal("account_id")->width(2);
                $filter->like("adAccountInfo.account_name",'广告账户名称')->width(2);
                $filter->like("adAccountInfo.corporation_name",'企业主体名称')->width(2);
                $filter->like("feedback_name")->width(2);
                $filter->like("adminInfo.username", '用户')->width(2);
                $filter->equal("second_category_type",'营销载体类型')->select(\App\Models\TenCentAdTrackClickConfig::CATEGORY_TYPE)->width(2);
                $filter->equal("updated_at")->datetime()->width(2);
            });
        });
    }

    public function destroy($id)
    {
        $data = TenCentAdTrackClickConfigModel::query()->whereIn("id", explode(",", $id))->get();
        foreach ($data as $datum) {
            if (!AdminUser::isAdmin($datum)) {
                return $this->form()->response()->error("无权限操作");
            }
        }
        return $this->form()->destroy($id);
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new TenCentAdTrackClickConfig(), function (Form $form) {
            if (!$form->isCreating() && !AdminUser::isAdmin($form->model()) && !AdminUser::isSystemOp()) {
                $form->display('N')->value("无数据");
            } else {
                $form->display('id');
                $form->hidden('id');
                $form->hidden('feedback_id');
                $form->hidden('admin_uid');
                $form->hidden('feedback_name');
                $form->select('account_id', '广告账户')
                    ->options(function ($keyword) { // 异步搜索回调
                        return TencentAdAccount::query()
                            ->where('account_id', 'like', "{$keyword}%")
                            ->pluck('account_id', 'account_id')
                            ->take(5); // 限制返回数量
                    })
                    ->ajax(admin_route('api.tencent-ad-account'))->required(); // 启用异步搜索
                $form->select('second_category_type','营销载体类型')->help('默认为WEB类型，如客户有其他类型需求，则选择对应的营销载体类型')->options(TenCentAdTrackClickConfigModel::CATEGORY_TYPE)->required()->default('WEB');
                $form->saving(function (Form $form) {
                    $accountInfo = TencentAdAccount::query()->where('account_id', $form->input('account_id'))->first();
                    if (!$accountInfo) {
                        return $form->response()->alert()->error('提示')->detail('账户不存在');
                    }
                    $form->admin_uid = $accountInfo->admin_uid;
                    $checkConfig = TenCentAdTrackClickConfigModel::query()
                        ->where('account_id', $form->input('account_id'))
                        ->where('second_category_type', $form->input('second_category_type'))
                        ->first();
                    if ($form->isCreating()) {
                        if ($checkConfig) {
                            return $form->response()->alert()->error('提示')->detail('该账户已配置监测组链接');
                        }
                    }
                }
                );
                $form->saved(function (Form $form, $result) {
                    if ($form->isCreating()) {
                        $insertId = $result;//新增的主键ID
                        $configInfo = TenCentAdTrackClickConfigModel::query()->where('id', $insertId)->first();
                        $accountInfo = TencentAdAccount::query()->where('account_id', $form->input('account_id'))->first();
                        $feedbackName = 'ZTFZ-DN-智投方舟-' . $form->input('account_id');
                        $apiRes = AdTrackClickService::addConfig($accountInfo, $feedbackName,$form->input('second_category_type'));

                        if (!$apiRes || !isset($apiRes['code']) || $apiRes['code'] != 0) {
                            $configInfo->delete();
                            return $form->response()->alert()->error('提示')->detail($apiRes['message_cn'] ?? '创建失败，请稍后重试');
                        }
                        $feedback_id = $apiRes['data']['feedback_info']['feedback_id'] ?? '';
                        if(!$feedback_id){
                            $configInfo->delete();
                            return $form->response()->alert()->error('提示')->detail('创建失败，缺少feedback_id，请稍后重试');
                        }
                        TenCentAdTrackClickConfigModel::query()->where('id', $insertId)->update([
                            'feedback_id' => $feedback_id,
                            'feedback_name' => $feedbackName,
                        ]);
                    }
                });
                $form->disableViewButton();
                $form->disableViewCheck();
                $form->disableEditingCheck();
                $form->disableDeleteButton();
            }
        });
    }


    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new TenCentAdTrackClickConfig(), function (Show $show) {
            if (!AdminUser::isAdmin($show->model())) {
                $show->field('N')->value("无数据");
            } else {
                $show->field('id');
                $show->field('feedback_name');
                $show->field('account_id');
            }
        });
    }

    public function getAdTrackAdminUser(Request $request)
    {
        $keyword = $request->get("q", 0);
        return TencentAdAccount::query()->whereIn('admin_uid', $keyword)
            ->get(['account_name', DB::raw('account_name as text')])
            ->unique('account_name');

    }
}
