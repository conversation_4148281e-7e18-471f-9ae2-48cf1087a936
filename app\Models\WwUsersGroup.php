<?php

	namespace App\Models;

	use Dcat\Admin\Admin;
    use Dcat\Admin\Traits\HasDateTimeFormatter;
    use Illuminate\Database\Eloquent\Model;
    use Illuminate\Database\Eloquent\Relations\BelongsTo;
    use Illuminate\Database\Eloquent\Relations\BelongsToMany;
    use Illuminate\Database\Eloquent\SoftDeletes;
    use Laravel\Scout\Searchable;

    /**
	 * @property mixed $wwUsers
	 * @property mixed $title
	 * @property mixed $ww_users_count
	 * @property mixed $id
     * @property mixed $admin_uid
     * @property int|mixed $type
     */
	class WwUsersGroup extends Model
	{
		use HasDateTimeFormatter;
		use SoftDeletes;
        use Searchable;

		protected $table = 'ww_users_groups';


        /**
         * 获取与模型关联的索引的名称。
         *
         * @return string
         */
        public function searchableAs()
        {
            return 'ww_users_groups_index';
        }
        /**
         * 获取模型的可索引的数据。
         *
         * @return array
         */
        public function toSearchableArray()
        {
            $array = $this->toArray();

            // 自定义数据数组...

            return $array;
        }


        public static function get(){

        }

		public function adminInfo(): BelongsTo
		{
			return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
		}

        /**获取分组下全部销售
         * @return BelongsToMany
         */
		public function wwUsers(): BelongsToMany
		{
			return $this->belongsToMany(WwUser::class, WwUsersGroupsRel::class, 'ww_group_id', 'ww_user_id')->withTimestamps();
		}

        /**
         * 获取分组下在线销售
         * @return BelongsToMany
         */
        public function onlineWwUsers()
        {
            return $this->belongsToMany(WwUser::class, WwUsersGroupsRel::class, 'ww_group_id', 'ww_user_id')->where('online_status',1)->withTimestamps();
        }

        public static function getMyAllSubGroupList(): array
        {
            $wwUsersGroup = WwUsersGroup::query()->with("adminInfo")->whereIn("admin_uid", AdminSubUser::getAdminUids(Admin::user()->id))->get();
            $return = [];
            //获取主账号昵称
            /** @var AdminUser $adminUser */
            $adminUser = AdminUser::query()->find(Admin::user()->id);
            if($adminUser->parent_id){
                $adminUser = AdminUser::query()->find($adminUser->parent_id);
            }
            foreach($wwUsersGroup as $item){
                $username = str_replace($adminUser->username,"",$item->adminInfo->username);
                $username = empty($username)?"主账号":$username;
                $return[$item->id] = $username."「".$item->title."」";
            }
            return $return;
        }

        public static function getMyAdGroupList(): array
        {
            if (AdminUser::isSystemOp()) {//如果是管理员，那么显示全部分组
                return WwUsersGroup::query()->whereIn("type", [1,-1])->pluck("title", "id")->toArray();
            } else {
                /** @var AdminUser $adminUser */
                $adminUser = Admin::user();
                if (Admin::user()->isRole('customer')) { //如果是主账户，显示自身以及子账户全部模板
                    return WwUsersGroup::query()->whereIn("type", [1,-1])->whereIn("admin_uid", AdminSubUser::getALLAdminUids($adminUser->id))->pluck("title", "id")->toArray();
                } else {
                    //如果是子账户，显示自身模板，以及父级授权展示的模板
                    $wwUserGroupData = AdminSubUser::query()->where('admin_uid', $adminUser->id)->value('ww_user_group_ids');
                    $wwUserGroupIds = json_decode($wwUserGroupData, true);
                    if (!$wwUserGroupIds) $wwUserGroupIds = [];
                    if (in_array(-1, $wwUserGroupIds)) {
                        //判断授权是否是-1，代表全部，如果是-1，那么展示主账户全部+自己的全部
                        return WwUsersGroup::query()->whereIn("type", [1,-1])->whereIn("admin_uid", [$adminUser->id, $adminUser->parent_id])->pluck("title", "id")->toArray();
                    } else {
                        //如果授权不是全部，那么展示授权的ID，以及自己的全部
                        return WwUsersGroup::query()->whereIn("type", [1,-1])->where(function ($query) use ($wwUserGroupIds, $adminUser) {
                            $query->whereIn('id', $wwUserGroupIds)
                                ->orWhere("admin_uid", $adminUser->id);
                        })->pluck("title", "id")->toArray();
                    }
                }
            }
        }

        /**
         * 获取分组名称
         * @param $groupId
         * @return mixed[]
         */
        public static function getGroupTitle($groupId){
            if(is_array($groupId)){
              return  self::query()->whereIn('id', $groupId)->pluck('title')->toArray();
            }
            return self::query()->where('id', $groupId)->pluck('title')->toArray();
        }

        /**
         * 根据admin_uid获取分组
         * @param $adminUid
         * @return \Illuminate\Database\Eloquent\Builder[]|\Illuminate\Database\Eloquent\Collection
         */
        public static function getGroupListByAdminUid($adminUid)
        {
            return self::query()->where("admin_uid", $adminUid)->select("title as name", 'id')->get();
        }

        /**
         * 获取所有分组。自建跟主账号分享的
         * @param $adminUid
         * @return \Illuminate\Support\Collection
         */
        public static function getAllGroupList($adminUid)
        {
            $groupIds = self::query()->where("admin_uid", $adminUid)->pluck('id')->toArray();
            $shareGroupIds = AdminSubUser::query()->where("admin_uid", $adminUid)->value('ww_user_group_ids');
            if($shareGroupIds){
                $shareGroupIds = json_decode($shareGroupIds,true);
                $groupIds = array_merge($groupIds,$shareGroupIds);
            }
            $groupList = self::query()->whereIn("id", $groupIds)->pluck("title", 'id');
            return $groupList;
        }
	}
