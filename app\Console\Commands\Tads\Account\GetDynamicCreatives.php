<?php

namespace App\Console\Commands\Tads\Account;

use App\Models\AdAccountDynamicCreative;
use App\Models\TencentAdAccount;
use App\Services\TenCentAd\OauthAccountService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class GetDynamicCreatives extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'GetDynamicCreatives';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        /**
         * 获取过往三天消耗较大的账户
         * 获取这些账户下面的所有素材，并且对获取素材对应的落地页，以及素材对应的数据
         */
        $accountVccIds = DB::table("ad_account_vcc_daily")->where("ad_cost", ">", "200000")->where('date', ">", date("Y-m-d", time() - 86400 * 7))->pluck("account_id");
        //$accountVccIds = [********,********];
        $accountInfos = TencentAdAccount::query()->whereIn("account_id", $accountVccIds)->get();
        /** @var TencentAdAccount $adAccount */
        $i = 0;
        $sumCount = $accountInfos->count();
        foreach ($accountInfos as $adAccount) {
            //完善有消耗的行业
            if (!$adAccount->system_industry_id) {
                $accountInfoResp               = OauthAccountService::advertiserGet($adAccount);
                $accountInfo                   = $accountInfoResp['data']['list'][0] ?? [];
                $adAccount->system_industry_id = $accountInfo['system_industry_id'] ?? "";
                $adAccount->mdm_name           = $accountInfo['mdm_name'] ?? "";
                $adAccount->agency_account_id  = $accountInfo['agency_account_id'] ?? "";
                $adAccount->operators          = json_encode($accountInfo['operators']??[]);
                $adAccount->memo               = $accountInfo['memo'] ?? "";
                $adAccount->save();
            }
            $this->info($adAccount->account_id);
            $i++;
            $this->info('进度:'.round($i/$sumCount*100,2).'%');
            //获取所有素材
            $this->getDynamicCreatives($adAccount,time()-86400*2);
        }
        return Command::SUCCESS;
    }

    public function getDynamicCreatives(TencentAdAccount $accountInfo,$last_modified_time='',$cursor = '',$isDelete = false)
    {
        //$this->info("页码：".$cursor);
        //$this->info("是否删除：".$isDelete);
        $interface = 'dynamic_creatives/get';
        $url       = 'https://api.e.qq.com/v3.0/' . $interface;

        $common_parameters = array(
            'access_token' => $accountInfo->access_token,
            'timestamp'    => time(),
            'nonce'        => md5(uniqid('', true))
        );

        $parameters = [
            'account_id'      => $accountInfo->account_id,
            'fields'          =>
                [
                    'adgroup_id',
                    'dynamic_creative_id',
                    'dynamic_creative_name',
                    'creative_template_id',
                    'delivery_mode',
                    'dynamic_creative_type',
                    'creative_components',
                    'impression_tracking_url',
                    'click_tracking_url',
                    'program_creative_info',
                    'page_track_url',
                    'configured_status',
                    'is_deleted',
                    'created_time',
                    'last_modified_time',
                    'marketing_asset_verification',
                    'creative_set_approval_status',
                    'source',
                    'asset_inconsistent_status',
                ],
            'page_size'       => 100,//000,
            'cursor'          => $cursor,
            'is_deleted'=>$isDelete,
            'pagination_mode' => 'PAGINATION_MODE_CURSOR',
        ];
        $parameters['filtering'][] = [
            'field' => 'last_modified_time',
            'operator' => 'GREATER_EQUALS',
            'values' => [
                (string)$last_modified_time
            ]
        ];

        $parameters = array_merge($common_parameters, $parameters);

        foreach ($parameters as $key => $value) {
            if (!is_string($value)) {
                $parameters[$key] = json_encode($value);
            }
        }

        $request_url = $url . '?' . http_build_query($parameters);

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $request_url);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 60);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $response = curl_exec($curl);
        if (curl_error($curl)) {
            $error_msg = curl_error($curl);
            $error_no  = curl_errno($curl);
            curl_close($curl);
            throw new \Exception($error_msg, $error_no);
        }
        curl_close($curl);
        //$this->info($response);
        $data = (json_decode($response, true));
        if(isset($data['data']['list'])){
            foreach($data['data']['list'] as $datum){
                $adDc = AdAccountDynamicCreative::query()->where("dynamic_creative_id",$datum['dynamic_creative_id'])->first();
                if(!$adDc){
                    $adDc = new AdAccountDynamicCreative();
                }
                $adDc->admin_uid = $accountInfo->admin_uid;
                $adDc->account_id = $accountInfo->account_id;
                $adDc->system_industry_id = $accountInfo->system_industry_id;
                $adDc->adgroup_id = $datum['adgroup_id'] ?? "";
                $adDc->dynamic_creative_id = $datum['dynamic_creative_id'] ?? "";
                $adDc->dynamic_creative_name = $datum['dynamic_creative_name'] ?? "";
                $adDc->creative_template_id = $datum['creative_template_id'] ?? "";
                $adDc->delivery_mode = $datum['delivery_mode'] ?? "";
                $adDc->dynamic_creative_type = $datum['dynamic_creative_type'] ?? "";
                $adDc->creative_components = isset($datum['creative_components'])?json_encode($datum['creative_components']):"[]";
                $adDc->impression_tracking_url = $datum['impression_tracking_url'] ?? "";
                $adDc->click_tracking_url = $datum['click_tracking_url'] ?? "";
                $adDc->program_creative_info = isset($datum['program_creative_info'])?json_encode($datum['program_creative_info']):"[]";
                $adDc->page_track_url = $datum['page_track_url'] ?? "";
                $adDc->configured_status = $datum['configured_status'] ?? "";
                $adDc->is_deleted = $datum['is_deleted'] ?? "";
                $adDc->created_time = $datum['created_time'] ?date("Y-m-d H:i:s",$datum['created_time']): "";
                $adDc->last_modified_time = $datum['last_modified_time'] ?date("Y-m-d H:i:s",$datum['last_modified_time']): "";
                $adDc->marketing_asset_verification = isset($datum['marketing_asset_verification'])?json_encode($datum['marketing_asset_verification']):"[]";
                $adDc->creative_set_approval_status = $datum['creative_set_approval_status'] ?? "";
                $adDc->source = $datum['source'] ?? "";
                $adDc->asset_inconsistent_status = $datum['asset_inconsistent_status'] ?? "";
                $adDc->save();
            }
        }
        if(!empty($data['data']['cursor_page_info']['next_cursor'])){
            $this->info("翻页".time());
            return $this->getDynamicCreatives($accountInfo,$last_modified_time,$data['data']['cursor_page_info']['next_cursor'],$isDelete);
        }
        return true;
    }
}
