<?php

namespace App\Admin\Forms\AdminActionLog;

use Admin;
use App\Models\AdminUser;
use Dcat\Admin\Widgets\Form;

class ActionLogTableSelectorForm extends Form
{
    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        $url = admin_url("action_logs");
        $param = [];

        if (!empty($input['table_id'])) {
            $param['table_id'] = $input['table_id'];
        }

        return $this
            ->response()
            ->success('切换成功.')
            ->redirect($url . "?" . http_build_query($param));
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        $this->disableResetButton();
        $this->disableSubmitButton();

        $this->column(6, function (Form $form) {
            // 只有管理员才能看到表选择器
            if (AdminUser::isSystemOp()) {
                $form->select("table_id", '操作日志表')->options([
                    '0' => '当前用户默认表',
                    '1' => '操作日志表1',
                    '2' => '操作日志表2',
                    '3' => '操作日志表3',
                    '4' => '操作日志表4',
                    '5' => '操作日志表5',
                    '6' => '操作日志表6',
                    '7' => '操作日志表7',
                    '8' => '操作日志表8',
                    '9' => '操作日志表9',
                    '10' => '操作日志表10',
                ]);
            }
        });

        $this->column(3, function (Form $form) {
            $form->html("<button type=\"submit\" class=\"btn btn-primary pull-left\" style='margin-top: 4px'><i class=\"feather icon-search\"></i> 查看</button>");
        });
    }

    /**
     * The data of the form.
     *
     * @return array
     */
    public function default()
    {
        return [
            'table_id' => request('table_id', ''),
        ];
    }
}
