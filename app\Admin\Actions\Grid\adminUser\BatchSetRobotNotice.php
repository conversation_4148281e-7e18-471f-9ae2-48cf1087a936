<?php

namespace App\Admin\Actions\Grid\adminUser;

use App\Admin\Extensions\Grid\BatchActionPlus;
use App\Admin\Forms\Admin\BatchSetRobotNoticeForm;

class BatchSetRobotNotice extends BatchActionPlus
{
    /**
     * @return string
     */
    public $title = '配置机器人报警';
    public $buttonText = '<button class="btn btn-primary ww_user_batch_btn"><i class="fa fa-android"></i>&nbsp&nbsp<span class="selected"></span>配置机器人报警</button>';


    public function form(): BatchSetRobotNoticeForm
    {
        return BatchSetRobotNoticeForm::make();
    }
}
