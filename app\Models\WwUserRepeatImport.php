<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;


class WwUserRepeatImport extends Model
{
    use HasDateTimeFormatter;
//    use SoftDeletes;

    protected $table = 'ww_user_repeat_import';

    public function adminInfo(): BelongsTo
    {
        return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
    }
}

