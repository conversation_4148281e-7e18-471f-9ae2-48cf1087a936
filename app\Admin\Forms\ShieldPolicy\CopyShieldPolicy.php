<?php

    namespace App\Admin\Forms\ShieldPolicy;

    use Admin;
    use App\Models\AdminSubUser;
    use App\Models\AdminUser;
    use App\Models\ShieldPolicy;
    use App\Models\TencentAdAccount;
    use App\Models\WwTpl;
    use App\Models\WwUsersGroup;
    use App\Services\Tools\LogService;
    use Dcat\Admin\Contracts\LazyRenderable;
    use Dcat\Admin\Traits\LazyWidget;
    use Dcat\Admin\Widgets\Form;

    class CopyShieldPolicy extends Form implements LazyRenderable
    {
        use LazyWidget;

        public function handle(array $input)
        {
            LogService::inputLog('Tools','屏蔽规则-复制', $input, Admin::user()->id, Admin::user()->username);
            if (!empty($input['error_msg'])) {
                return $this->response()->error($input['error_msg'])->refresh();
            }
            /** @var ShieldPolicy $shieldPolicy */
            $shieldPolicy = ShieldPolicy::query()->find($this->payload['policy_id']);
            if (AdminUser::isAdmin($shieldPolicy)) {
                $newShieldPolicy = $shieldPolicy->replicate();
                $newShieldPolicy->name = $input['name'];
                $newShieldPolicy->mdm = $input['mdm'];
                $newShieldPolicy->product = $input['product'];
                $newShieldPolicy->tpl_audit_id = $input['tpl_audit_id'];
                $newShieldPolicy->ww_user_group_id = $input['ww_user_group_id'];
                $newShieldPolicy->save();
                return $this->response()->success("复制完成")->refresh();
			} else {
                return $this->response()->error("未找到策略，请刷新后重试")->refresh();
            }
        }

        public function form()
        {
            $this->hidden("policy_id");
            /** @var ShieldPolicy $shieldPolicy */
            $shieldPolicy = ShieldPolicy::query()->find($this->payload['policy_id']);
            if (AdminUser::isAdmin($shieldPolicy)) {
                $this->text('name')->required();
                $this->multipleSelect('mdm')->options(TencentAdAccount::query()->where('admin_uid', \Dcat\Admin\Admin::user()->id)->groupBy("corporation_name")->pluck("corporation_name", "corporation_name"))->help("用于新建投放链接的时候，自动匹配投放主体的屏蔽规则使用，留空代表适用所有主体")->saveAsJson();
                $this->multipleSelect('product')->options(WwTpl::query()->whereIn("admin_uid", AdminSubUser::getALLAdminUids(Admin::user()->id))->groupBy("product")->pluck("product", "product"))->help("用于新建投放链接的时候，自动匹配同落地页产品的屏蔽规则使用，留空代表适用所有产品")->saveAsJson();

                $tplList = WwTpl::query()->where("type", 0)->where('admin_uid', \Dcat\Admin\Admin::user()->id)->pluck("name", "id");
                $this->select("tpl_audit_id")->default($shieldPolicy->tpl_audit_id)->options($tplList)->required()->help("符合屏蔽规则的异常流量，会展示该页面");

                $wwUsersGroup = WwUsersGroup::query()->where("admin_uid", Admin::user()->id)->pluck("title", 'id');
                $this->select("ww_user_group_id")->default($shieldPolicy->ww_user_group_id)->options($wwUsersGroup)->required()->help("符合屏蔽规则的异常流量，会展示该规则的接粉销售");

            } else {
                $errMsg = '未找到策略，请刷新后重试';
                $this->display("提示")->value($errMsg);
                $this->hidden('error_msg')->value($errMsg);
            }
        }

        public function default()
        {
            return [
                // 展示上个页面传递过来的值
                'policy_id' => $this->payload['policy_id'] ?? '',
            ];
        }
    }
