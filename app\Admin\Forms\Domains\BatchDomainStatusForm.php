<?php

namespace App\Admin\Forms\Domains;

use Admin;
use App\Models\AdminDomain;
use App\Services\Tools\LogService;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class BatchDomainStatusForm extends Form implements LazyRenderable
{
    use LazyWidget;

    // 处理请求
    public function handle(array $input)
    {
        LogService::inputLog('Tools','域名管理-批量配置', $input, Admin::user()->id, Admin::user()->username);
        // id转化为数组
        $id = explode(',', $input['id'] ?? null);
        if (!$id) {
            return $this->response()->alert()->error('提示')->detail('参数错误。');
        }

        $wwLik = AdminDomain::query()->find($id);
        if ($wwLik->isEmpty()) {
            return $this->response()->alert()->error('提示')->detail('域名不存在。');
        }

        foreach ($wwLik as $key => $value) {

            $value->status = $input['status'];
            $value->save();
        }
        return $this->response()->alert()->success('提示')->detail('修改状态成功。')->refresh();

    }

    public function form()
    {
        $this->radio('status', '状态')->options([0 => '不可用', 1 => '可用'])->required();//修改为展示授权分组以及自身分组
        $this->hidden('id')->value($this->payload['ids'] ?? ''); // 获取传递的ID
    }

    // 返回表单数据，如不需要可以删除此方法
    public function default()
    {
        return [
            'password' => '',
            'password_confirm' => '',
        ];
    }
}
