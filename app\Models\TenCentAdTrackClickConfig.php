<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;


class TenCentAdTrackClickConfig extends Model
{
    use HasDateTimeFormatter;

    protected $table = 'ad_track_click_config';

    const SALT = 'zt-datanexus_shuipojieshuishabi666';

    public function adminInfo(): BelongsTo
    {
        return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
    }

    public function adAccountInfo(): BelongsTo
    {
        return $this->BelongsTo(TencentAdAccount::class, 'account_id', 'account_id');
    }

    CONST CATEGORY_TYPE = [
        'WEB' => 'WEB',
//        'ANDROID' => '安卓',
//        'IOS' => 'IOS',
//        'QUICK_APP' => '快应用',
        'WECHAT_OFFICIAL_ACCOUNT' => '微信公众号',
        'WECHAT_CHANNELS' => '微信视频号',
        'WECHAT_MINI_PROGRAM' => '微信小程序',
        'WECOM' => '企业微信',
//        'WECHAT_MINI_GAME' => '微信小游戏',
//        'QQ_MINI_GAME' => 'QQ小游戏',
    ];
}
