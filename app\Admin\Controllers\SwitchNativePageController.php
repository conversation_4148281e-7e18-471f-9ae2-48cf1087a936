<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\SwitchNativePage;
use App\Models\AdAccount;
use App\Models\SwitchNativePage as SwitchNativePageModel;
use App\Models\AdminUser;
use App\Models\TencentAdAccount;
use App\Services\Tools\UtilsService;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Card;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;

class SwitchNativePageController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new SwitchNativePage(['adminInfo']), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->tools(function (Grid\Tools $tools) {
                $className = collect(Request::segments())->last();
                $tools->append(UtilsService::dialogForm('添加', Request::url() . '/create', "create-{$className}"));
            });
            $grid->model()->orderBy('id', 'desc');
            $grid->column('id')->sortable();
            $grid->column('admin_uid', '用户ID');
            $grid->column("adminInfo.username", "用户");
            $grid->column('status', '状态')->using(SwitchNativePageModel::STATUS)->label([
                0 => 'default',
                1 => 'warning',
                2 => 'success',
                3 => 'warning',
                4 => 'danger',
            ]);
            $grid->column('account_id', '账户')
                ->display('账户') // 设置按钮名称
                ->expand(function () {
                    // 返回显示的详情
                    // 这里返回 content 字段内容，并用 Card 包裹起来
                    $card = new Card(null, $this->account_id);

                    return "<div style='padding:10px 10px 0'>$card</div>";
                });
            $grid->column('账户数量')->display(function () {
                return sizeof(explode(',',$this->account_id));
            });
            $grid->column('result_json', '结果')
                ->display('结果') // 设置按钮名称
                ->expand(function () {
                    // 返回显示的详情
                    // 这里返回 content 字段内容，并用 Card 包裹起来
                    $card = new Card(null, $this->result_json);

                    return "<div style='padding:10px 10px 0'>$card</div>";
                });
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) use ($grid) {
                $filter->panel('查询');
                $filter->expand();
                $adminUidListName = AdminUser::query()->where("parent_id", 0)->pluck("username", "id");
                $filter->equal('admin_uid', '用户')->select($adminUidListName)->loads(['corp_id', 'ww_group_id'], ['api/getCorpList', 'api/getGroupList'])->width(2);
                $filter->equal("status", '状态')->select(SwitchNativePageModel::STATUS)->width(2);
            });
            $grid->disableViewButton();
            $grid->disableBatchActions();
        });
    }


    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new SwitchNativePage(), function (Form $form) {
            $form->display('id');
            $form->hidden('id');

            $adminUsers = AdminUser::query()->where("parent_id", 0)->pluck("username", "id");
            $form->select('admin_uid', '用户')
                ->options($adminUsers)
                ->help('此处选择的是主账号')
                ->required();
//            $form->multipleSelect('account_id', '广告账户ID')->saveAsJson()->required();
            $form->textarea('account_id', '广告账户ID')
                ->required();
//                ->help('英文逗号隔开，不需要换行，例如：********,********');
            $form->radio('status', '状态')->options(SwitchNativePageModel::STATUS)->default(0)->help('修改为【等待切换】状态，任务会重新执行');
            $form->disableViewButton();
            $form->disableViewCheck();
            $form->disableDeleteButton();

        })->saving(function (Form $form) {
            $accountIds =  explode(PHP_EOL, $form->input('account_id'));
            $accountIds = array_map('trim', $accountIds);
            // 或者更精确地移除特定字符
            $accountIds = array_map(function($item) {
                return str_replace(["\r", "\n"], '', $item);
            }, $accountIds);

            foreach ($accountIds as $accountId) {
                $adminUids = AdminUser::getAllAdminUidsByAdminUid($form->input('admin_uid'));
                $check = TencentAdAccount::query()
                    ->whereIn('admin_uid',$adminUids)
                    ->where('account_id',$accountId)
                    ->exists();
                if (!$check) {
                    return $form->response()->alert()->error('提示')->detail('账户ID：【' . $accountId . '】不存在，请跟客户确认是否已授权。');
                }
            }
            $form->account_id = implode(',', $accountIds);

        })->confirm('确认提交吗？');
    }
}
