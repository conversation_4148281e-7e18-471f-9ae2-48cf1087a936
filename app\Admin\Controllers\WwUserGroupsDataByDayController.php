<?php

    namespace App\Admin\Controllers;

    use App\Models\AdminSubUser;
    use App\Models\AdminUser;
    use App\Models\WwUserGroupsDataByDay;
    use App\Models\WwUsersGroup;
    use App\Services\Tools\UtilsService;
    use Dcat\Admin\Admin;
    use Dcat\Admin\Form;
    use Dcat\Admin\Grid;
    use Dcat\Admin\Http\Controllers\AdminController;
    use Dcat\Admin\Show;

    /**
     * @property $admin_uid
     * @property $ww_user_group_id
     */
    class WwUserGroupsDataByDayController extends AdminController
    {
        /**
         * Make a grid builder.
         *
         * @return Grid
         */
        protected function grid()
        {
            return Grid::make(new WwUserGroupsDataByDay(), function (Grid $grid) {
                $grid->paginate(AdminUser::getPaginate());
                if (Admin::user()->id !== 1) {
//                $grid->model()->where("admin_uid", Admin::user()->id);
                    if (Admin::user()->isRole('customer')) { //如果是“客户”的角色 也就是主账号
                        $grid->model()->whereIn("admin_uid", AdminSubUser::getAdminUids(Admin::user()->id));//如果是主账号，则获取所有子账号的id，包括自己
                        $grid->column('归属账号')->display(function () {
                            if (Admin::user()->id == $this->admin_uid) { //如果是自己创建的则直接显示主账号
                                return '主账号';
                            } else {
                                $username = Admin::user()->where('id', $this->admin_uid)->value('username');
                                $username = str_replace(Admin::user()->username, '', $username);
                                return $username;
                            }
                        });

                    } else { //如果是客户运营的角色 也就是子账号
                        $grid->model()->where("admin_uid", Admin::user()->id)->orderByDesc("id");
                    }
                }
                $grid->export()->rows(function ($rows) {
                    /** @var AdminUser $adminUser */
                    $adminUser = Admin::user();
                    foreach ($rows as $key => $row) {
                        if ($adminUser->id == $row->admin_uid) {
                            $rows[$key]['所属账号'] = '主账号';
                        } else {
                            $username  = $row->adminInfo ? $row->adminInfo->username : '';
                            $rows[$key]['所属账号'] = str_replace($adminUser->username, '', $username);
                        }
                        $rows[$key]['groupInfo.title'] = WwUsersGroup::query()->where("id", $row->ww_user_group_id)->value("title");
                    }
                    return $rows;
                })->chunkSize(2000);
                $grid->model()->orderByDesc("date");
                $grid->disableCreateButton();
                $grid->disableBatchActions();
                $grid->disableActions();
                $grid->showColumnSelector();
                $grid->column('id')->sortable();
                $grid->column('groupInfo.title', '分组')->display(function () {
                    return WwUsersGroup::query()->where("id", $this->ww_user_group_id)->value("title");
                });
                $grid->column('date');
                $grid->column('add_count')->sortable()->display(function ($value) {
                    return UtilsService::beautifyNumber($value, 'minimal', 'success', ['tooltip' => '新增客户数量']);
                });
                $grid->column('cus_del_count')->sortable()->display(function ($value) {
                    return UtilsService::beautifyNumber($value, 'box', 'warning', ['tooltip' => '客户删除数量']);
                });
                $grid->column('ww_del_count', '企微删除')->sortable()->display(function ($value) {
                    return UtilsService::beautifyNumber($value, 'badge', 'danger', ['tooltip' => '企微删除数量']);
                });
                $grid->column('pure_add_count', '净增数量')->sortable()->display(function ($value) {
                    return UtilsService::beautifyNumber($value, 'pill', 'info', ['tooltip' => '净增客户数量']);
                });
                $grid->column('customer_start_chat_count')->sortable()->display(function ($value) {
                    return UtilsService::beautifyNumber($value, 'icon', 'info', [
                        'tooltip' => '客户主动发起聊天的数量',
                        'icon' => 'feather icon-message-circle'
                    ]);
                });
                $grid->column('updated_at')->sortable();

                $grid->filter(function (Grid\Filter $filter) {
                    $filter->expand();
                    $filter->panel();
                    //所有带「所属账户」的列表，主账户都增加对应筛选
                    if (Admin::user()->parent_id == 0) { //主账号
                        $adminIds = AdminSubUser::getALLAdminUids(Admin::user()->id);
                        $filter->equal("admin_uid", '操作账号')->select(AdminUser::query()->whereIn("id", $adminIds)->pluck("username", 'id')->toArray())->width(2);
                    }
                    $filter->in('ww_user_group_id', '分组')->multipleSelect(WwUsersGroup::getMyAdGroupList())->width(2);
                    $filter->equal("date")->date()->width(2);
                });
            });
        }

        /**
         * Make a show builder.
         *
         * @param mixed $id
         *
         * @return Show
         */
        protected function detail($id)
        {
            return Show::make($id, new WwUserGroupsDataByDay(), function (Show $show) {
//            $show->field('id');
//            $show->field('ww_user_group_id');
//            $show->field('date');
//            $show->field('add_count');
//            $show->field('cus_del_count');
//            $show->field('ww_del_count');
//            $show->field('pure_add_count');
//            $show->field('customer_start_chat_count');
//            $show->field('corp_id');
//            $show->field('admin_uid');
//            $show->field('ocpx_count');
//            $show->field('ocpx_x_count');
//            $show->field('ocpx_json');
//            $show->field('created_at');
//            $show->field('updated_at');
                if (!AdminUser::isAdmin($show->model())) {
                    $show->field('N')->value("无数据");
                } else {
                    $show->field('id');
                }
            });
        }

        /**
         * Make a form builder.
         *
         * @return Form
         */
        protected function form()
        {
            return Form::make(new WwUserGroupsDataByDay(), function (Form $form) {
                if (!$form->isCreating() && !AdminUser::isAdmin($form->model())) {
                    $form->display('N')->value("无数据");
                } else {
                    $form->display('id');
                }
            });
        }

        public function destroy($id)
        {
            return $this->form()->response()->error("无权限操作");
        }
    }
