<?php

	namespace App\Models;

	use Dcat\Admin\Traits\HasDateTimeFormatter;
	use Illuminate\Database\Eloquent\Model;
    use Illuminate\Database\Eloquent\Relations\BelongsTo;
    use Illuminate\Database\Eloquent\SoftDeletes;

	/**
	 * @property mixed        $access_token
	 * @property mixed        $data_source_id
	 * @property mixed        $account_id
	 * @property mixed|string $refresh_token_expires_in
	 * @property mixed|string $access_token_expires_in
	 * @property mixed        $refresh_token
	 * @property false|string $scope_list
	 * @property mixed        $login_name
	 * @property mixed        $account_name
	 * @property mixed        $role_type
	 * @property mixed        $account_type
	 * @property mixed        $wechat_account_id
	 * @property mixed        $account_role_type
	 * @property mixed|string $memo
	 * @property mixed        $operators
	 * @property mixed        $agency_account_id
	 * @property mixed        $mdm_name
	 * @property mixed        $system_industry_id
	 * @property mixed        $certification_image
	 * @property mixed|string $corporate_image_logo
	 * @property mixed|string $corporate_image_name
	 * @property mixed        $corporation_name
	 * @property mixed        $admin_uid
	 */
	class TencentAdAccount extends Model
	{
		use HasDateTimeFormatter;
		use SoftDeletes;

		protected $table = 'ad_account';

        public function adminInfo(): BelongsTo
        {
            return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
        }

        public function industryInfo(): BelongsTo
        {
            return $this->BelongsTo(TencentAdAccountIndustry::class, 'system_industry_id', 'system_industry_id');
        }

        /**
         * 同步投放链接备注到腾讯广告账户
         * @param $wwLink
         * @return true
         */
        public static function updateWwLinkRemark($wwLink)
        {
            $where = [
                'account_id' => $wwLink->account_id,
                'admin_uid' => $wwLink->admin_uid,
            ];
            if($wwLink->media_type == '腾讯广告'){
                self::query()->where($where)->update(['ww_link_remark' => $wwLink->remark]);
            }
            return true;
        }
	}
