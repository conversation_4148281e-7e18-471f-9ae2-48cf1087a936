<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;


class IpInfect extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'ip_infect';


    const STATUS = [
        0 => '关闭',
        1 => '开启',
    ];

    public function adminInfo(): BelongsTo
    {
        return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
    }


}
