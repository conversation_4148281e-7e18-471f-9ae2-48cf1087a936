<?php

    namespace App\Admin\Controllers;

    use App\Admin\Repositories\WhiteUserList;
    use App\Models\AdminUser;
    use App\Services\Tools\QrcodeService;
    use Dcat\Admin\Admin;
    use Dcat\Admin\Form;
    use Dcat\Admin\Grid;
    use Dcat\Admin\Http\Controllers\AdminController;
    use Dcat\Admin\Show;
    use Dcat\Admin\Widgets\Modal;
    use Illuminate\Support\Facades\Log;
    use Illuminate\Support\Facades\Request;

    class WhiteUserListController extends AdminController
    {
        /**
         * Make a grid builder.
         *
         * @return Grid
         */
        protected function grid()
        {
            return Grid::make(new WhiteUserList(), function (Grid $grid) {
                $grid->paginate(AdminUser::getPaginate());
                $grid->disableEditButton();
                $grid->disableViewButton();
                $grid->disableCreateButton();
                if(AdminUser::isSystemOp()){
                    $grid->model()->orderByDesc("id");
                }else{
                    $grid->model()->where("admin_uid", Admin::user()->id)->orderByDesc("id");
                }
                /** @var AdminUser $adminUser */
                $adminUser = AdminUser::query()->find(Admin::user()->id);
                if (empty($adminUser->white_qrcode)) {
                    if (empty($adminUser->encrypt_id)) {
                        $adminUser->encrypt_id = md5($adminUser->id . "_wra_shuipojieshuishabi");
                        $adminUser->save();
                    }
                    $whiteUrl = "https://" . Request::getHost() . route('addWhite', [], false) . "?" . http_build_query(['aeid' => $adminUser->encrypt_id]);
                    $imgPath = QrcodeService::createQrcodeFile('white_qrcode_' . $adminUser->id, $whiteUrl);
                    $adminUser->white_qrcode = $imgPath;
                    $adminUser->save();
                } else {
                    $imgPath = $adminUser->white_qrcode;
                }
                $modal = Modal::make()
                    ->sm()
                    ->title('添加白名单')
                    ->body('<img style="width:100%" src="' . $imgPath . '">')
                    ->button('<button class="btn btn-primary disable-outline float-right" style="color: white;margin-left: 5px"><i class="fa  fa-qrcode"></i>&nbsp;&nbsp;添加白名单</button>');
                $grid->tools($modal);

                $grid->column('id')->sortable();
                $grid->column('md5_id');
                $grid->column('name')->editable();
                $grid->column('updated_at')->sortable();

//                $grid->quickSearch("name");
                if(AdminUser::isSystemOp()){
                    $grid->filter(function (Grid\Filter $filter) use ($grid)  {
                        $filter->panel('查询');
                        $filter->expand();
                        $adminUidListName = AdminUser::query()->pluck("username", "id");
                        $filter->equal('admin_uid','用户')->select($adminUidListName)->width(2);
                        $filter->equal('ip')->width(2);
                        $filter->equal('wechat_user_id','用户ID')->width(2);
                        $filter->equal('md5_id','用户标识')->width(2);
                    });
                }
            });
        }

        /**
         * Make a show builder.
         *
         * @param mixed $id
         *
         * @return Show
         */
        protected function detail($id)
        {
            return Show::make($id, new WhiteUserList(), function (Show $show) {
                if (!AdminUser::isAdmin($show->model())) {
                    $show->field('N')->value("无数据");
                } else {
                    $show->field('id');
                    $show->field('md5_id');
                    $show->field('created_at');
                    $show->field('updated_at');
                }
            });
        }

        /**
         * Make a form builder.
         *
         * @return Form
         */
        protected function form()
        {
            return Form::make(new WhiteUserList(), function (Form $form) {
                if (!$form->isCreating() && !AdminUser::isAdmin($form->model())) {
                    $form->display('N')->value("无数据");
                } else {
                    $form->display('id');
                    $form->text('md5_id');
                    $form->text('openid');
                    $form->text('name');
                    $form->text('wechat_user_id');
                    $form->text('admin_uid');

                    $form->display('created_at');
                    $form->display('updated_at');
                }
            });
        }

        public function destroy($id)
        {
            $data = \App\Models\WhiteUserList::query()->whereIn("id", explode(",", $id))->get();
            foreach ($data as $datum) {
                if (!AdminUser::isAdmin($datum)) {
                    return $this->form()->response()->error("无权限操作");
                }
            }
            return $this->form()->destroy($id);
        }
    }
