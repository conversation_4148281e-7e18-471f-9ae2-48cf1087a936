<?php

namespace App\Admin\Controllers;

use Admin;
use App\Admin\Renderable\AdAccountMonitor\AdAccountHourlyMonitorRecord;
use App\Admin\Repositories\AdAccountMonitorCount;
use App\Models\AdminSubUser;
use App\Models\AdminUser;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Show;

class AdAccountMonitorCountController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new AdAccountMonitorCount(['adminInfo', 'tencentAdAccountInfo']), function (Grid $grid) {
            $grid->paginate(AdminUser::getPaginate());
            // 开启字段选择器功能
            $grid->scrollbarX();
            $grid->showColumnSelector();
            $grid->disableActions();
            $grid->disableCreateButton();
            $grid->disableBatchDelete();
//            $grid->disableRowSelector();
//            $grid->export();
//
            $grid->export()->rows(function ($rows) {
                foreach ($rows as $index => &$row) {
                    $rows[$index]['adminInfo.username'] = $row->adminInfo->username ?? '';
                    $row['ad_cost'] = round($row['ad_cost'] / 100, 2);
                    $row['conversions_cost'] = round($row['conversions_cost'] / 100, 2);
                    $rows[$index]['tencentAdAccountInfo.ww_link_remark'] = $row->tencentAdAccountInfo->ww_link_remark;
                    $rows[$index]['tencentAdAccountInfo.account_name'] = $row->tencentAdAccountInfo->account_name ?? $row->tencentAdAccountInfo->corporation_name;
                    unset($rows[$index]['查看时报']);
                }
                return $rows;
            });
            $grid->model()->orderByDesc('date')->orderByDesc('id');
            if (!AdminUser::isSystemOp()) {
                $grid->model()->whereIn("admin_uid", AdminSubUser::getAdminUids(Admin::user()->id));
            }

            $grid->column('id')->sortable();
//            $grid->column('adminInfo.id','用户ID')->sortable();
            $grid->column('adminInfo.username', '用户');
            $grid->column('account_id', '广告账户ID');
            $grid->column('tencentAdAccountInfo.account_name', '广告账户');
            $grid->column('tencentAdAccountInfo.ww_link_remark', '备注');
            $grid->column("date", '日期');
            $grid->column("view_count", '曝光量')->sortable();
            $grid->column("valid_click_count", '点击量')->sortable();
            $grid->column("ad_cost", '消耗')->display(function ($ad_cost) {
                $ad_cost = "¥" . round($ad_cost / 100, 2);
                return $ad_cost;
            })->sortable();
            $grid->column("conversions_count", '转化数')->sortable();
            $grid->column("conversions_rate", '转化率')->display(function ($conversions_cost) {
               return $conversions_cost . '%';
            })->sortable();
            $grid->column("conversions_cost", '转化成本')->display(function ($conversions_cost) {
                return "¥" . round($conversions_cost / 100, 2);
            })->sortable();
            $grid->column("jump_pv_count", '二跳PV数')->sortable();
            // $grid->column("jump_uv_count",'二跳UV数');
            $grid->column("add_count", '进粉数')->sortable()->help('包含投放页和屏蔽页的进粉');
//            $grid->column("actual_add_count", '实际进粉数')->sortable()->help('投放页的进粉数');
            $grid->column("add_count_cost", '实际进粉成本')->display(function ($add_count_cost){
                if($this->add_count_cost == 0){
                    return "¥" . 0;
                }
                return $add_count_cost;
            })->help('只统计投放页面进粉')->sortable();
            $grid->column("open_count", '开口数')->sortable();
//            $grid->column("actual_add_count", '实际开口数')->sortable()->help('投放页的进粉开口');
//            $grid->column("open_count_cost", '实际开口成本');
           $grid->column('查看时报')->display('查看时报')->modal(function ($modal) {
               // 设置弹窗标题
               $modal->title('查看时报');
               $modal->xl();
               return AdAccountHourlyMonitorRecord::make(['id' => $this->id]);
           });
//             $grid->column('查看时报')->display('查看时报')->modal(function ($modal) {
//                 // 设置弹窗标题
//                 $modal->title('查看时报 ');
//                 $modal->xl();
//                 return AccountMonitorHourly::make($this->id);
//             });
//            $grid->column('created_at')->sortable();
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                $filter->equal('admin_uid', "用户")->select(AdminSubUser::getAdminUsers(Admin::user()->id))->width(2);
//                $accountList = AdAccount::query()->whereIn("admin_uid", AdminSubUser::getAdminUids(Admin::user()->id))->pluck("account_name", "account_id");
//                $filter->equal('account_id', "广告账户")->select($accountList)->width(2);

//                $filter->where('account_name', function ($query) {
//                    $query->where('account_name', 'like', "{$this->input}%");
//                }, "广告账户")->width(2);
                $filter->equal('account_id','账户ID')->width(2);
                $filter->between("date",'日期')->date()->width(4);

//                $filter->where('account_id', function ($query) {
//                    if (AdminUser::isSystemOp()) {
//                        $accountInfo = AdAccount::query()->where('account_name', 'like', "{$this->input}%")->pluck('account_id');
//                    } else {
//                        $accountInfo = AdAccount::query()->whereIn("admin_uid", AdminSubUser::getAdminUids(Admin::user()->id))->where('account_name', 'like', "{$this->input}%")->pluck('account_id');
//                    }
//                    $query->whereIn("account_id", $accountInfo);
//                }, '广告账户')->width(2);

                $filter->between("updated_at")->datetime()->width(4);
            });

//            $grid->header(function ($collection) use ($grid) {
//                $query = \App\Models\TencentAdAccountMonitorCount::query();
//
//                // 拿到表格筛选 where 条件数组进行遍历
//                $grid->model()->getQueries()->unique()->each(function ($value) use (&$query) {
//                    if (in_array($value['method'], ['paginate', 'get', 'orderBy', 'orderByDesc'], true)) {
//                        return;
//                    }
//
//                    $query = call_user_func_array([$query, $value['method']], $value['arguments'] ?? []);
//                });
//
//                // 查出统计数据
//                $ad_cost = $query->sum('ad_cost');//消耗
//                $ad_cost = round($ad_cost / 100, 2);
//                $view_count = $query->sum('view_count');//曝光量
//                $valid_click_count = $query->sum('valid_click_count');//点击量
//                $conversions_count = $query->sum('conversions_count');//转化数
//                $conversions_cost = $query->sum('conversions_cost');//转化成本
//                $conversions_cost = round($conversions_cost / 100, 2);
//                $add_count = $query->sum('add_count');//进粉数
//                $open_count = $query->sum('open_count');//开口数
//                $add_count_cost = $query->sum('add_count_cost');//实际进粉成本
//                return "
//                <div style='padding: 10px;'>
//                        消耗合计 ： {$ad_cost}，
//                        曝光量合计：{$view_count}，
//                        点击量合计：{$valid_click_count}，
//                        转化数合计：{$conversions_count}，
//                        转化成本合计：{$conversions_cost}，
//                        进粉数合计：{$add_count}，
//                        开口数合计：{$open_count}，
//                        实际进粉成本合计：{$add_count_cost}，
//                </div>";
//            });
        });

    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new AdAccountMonitorCount(), function (Show $show) {
            if (!AdminUser::isAdmin($show->model())) {
                $show->field('N')->value("无数据");
            } else {
                $show->field('id');
            }
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new AdAccountMonitorCount(), function (Form $form) {
            if (!$form->isCreating() && !AdminUser::isAdmin($form->model())) {
                $form->display('N')->value("无数据");
            } else {
                $form->display('id');
            }
            $form->disableEditingCheck();
            $form->disableViewCheck();
            $form->disableCreatingCheck();
        });
    }

    public function destroy($id)
    {
        return $this->form()->response()->error("无权限操作");
    }
}
