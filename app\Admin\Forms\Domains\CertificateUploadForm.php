<?php

namespace App\Admin\Forms\Domains;

use Admin;
use App\Models\AdminDomain;
use App\Models\AdminDomainCert;
use App\Services\AlibabaCloudService;
use App\Services\NotifySendService;
use Dcat\Admin\Widgets\Form;

class CertificateUploadForm extends Form
{
    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        $pem = $input['pem'];
        $key = $input['key'];
        $domain = $input['domain'];
        if (empty($pem) || empty($key)) {
            return $this->response()->alert()->error('错误')->detail('填写证书内容');
        }
        /** 通过pem解析证书域名 */
        $parsingCertificate = AlibabaCloudService::DescribeForParsingCertificate($pem);
        if (!$parsingCertificate['success']) {
            return $this->response()->alert()->error('提示')->detail('证书错误：' . $parsingCertificate['message']);
        }
        /** 证书域名列表 */
        $parsingCertificateData = $parsingCertificate['data'];
        if (!AdminDomain::isDomainMatched($domain, $parsingCertificateData)) {
            return $this->response()->alert()->error('提示')->detail('证书域名和输入的域名不匹配<br>===证书域名===<br>'.
                implode('<br>', $parsingCertificateData)
            );
        }
        // 处理域名格式，如果是纯域名则添加https://前缀
        if (!preg_match('/^https?:\/\//', $domain)) {
            $domain = 'https://' . $domain;
        }
        //检查是否存在
        $check = AdminDomain::query()->where('domain', $domain)->first();
        if ($check) {
            return $this->response()->alert()->error('提示')->detail('该域名已存在：' . $input['domain']);
        }
        $adminDomainId = AdminDomain::query()->insertGetId([
            'admin_uid' => Admin::user()->id,
            'upload_status' => 0,
            'status' => 0,
            'domain' => $domain,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ]);
        AdminDomainCert::query()->insert([
            'admin_uid' => Admin::user()->id,
            'domain_id' => $adminDomainId,
            'key' => $key,
            'pem' => $pem,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ]);
        NotifySendService::sendWopForMessage(
            title: "客户添加域名通知",
            contentLines:[
                "用户ID" => Admin::user()->id,
                "用户姓名" => Admin::user()->username,
                "域名ID" => $adminDomainId,
                "域名信息" => $input['domain'],
                "提交时间" => date("Y-m-d H:i:s")
            ]
        );
        return $this->response()->alert()->success('提交成功')->detail('等待管理员上传')->refresh();
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        $this->html(
            <<<HTML
<div class="alert alert-info alert-dismissable">
            <h4> 提示</h4>
           请上传【Nginx】格式的证书 域名不添加https前缀<br/>
          上传前请确保域名已经解析成功，并且添加的域名与证书匹配。
</div>
HTML
        );
        $this->text('domain', '域名')->help('请填写域名，如：xxx.abc.com，不需要填写https://，上传前请确保域名已经解析成功。')->required();
        $this->textarea('pem', '证书(PEM格式)')->help('后缀为.pem、.crt、.PEM、.CRT的文件。')->required();
        $this->textarea('key', '密钥(KEY)')->help('后缀为.key、.KEY的文件。')->required();

    }

}
