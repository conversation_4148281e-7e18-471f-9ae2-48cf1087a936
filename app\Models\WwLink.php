<?php

    namespace App\Models;

    use Dcat\Admin\Traits\HasDateTimeFormatter;
    use Illuminate\Database\Eloquent\Model;
    use Illuminate\Database\Eloquent\Relations\BelongsTo;
    use Illuminate\Database\Eloquent\SoftDeletes;
    use Illuminate\Support\Facades\Cache;

    /**
     * @property int $account_id
     * @property int $id
     * @property int $link_ad_id
     * @property mixed $remark
     * @property mixed $need_shield
     * @property WwTpl $tplInfo
     * @property mixed $media_type
     * @property mixed $admin_uid
     * @property AdminUser $adminInfo
     * @property ShieldPolicy $shieldPolicy
     * @property mixed $is_open
     * @property mixed $add_succeeded_action_type
     * @property mixed $huawei_secret_key
     * @property mixed $tads_count
     * @property mixed $conv_count
     * @property mixed $ww_label
     * @property mixed $customer_start_chat_action_type
     * @property mixed $ww_user_group_id
     * @property mixed $tpl_type
     * @property mixed $tpl_link
     * @property WwUsersGroup $wwUserGroup
     * @property TencentAdAccount $adAccountInfo
     * @property mixed $show_ww_user_policy
     * @property int|mixed $tpl_id
     * @property mixed|string $shield_policy_id
     */
    class WwLink extends Model
    {
        use HasDateTimeFormatter;
        use SoftDeletes;

        const MEDIA_TYPE = [
            '腾讯广告' => '腾讯广告',
            'OPPO广告' => 'OPPO广告',
//            'VIVO广告' => 'VIVO广告',
            '优酷广告' => '优酷广告',
            '爱奇艺广告' => '爱奇艺广告',
            '巨量广告' => '巨量广告',
            '快手广告' => '快手广告',
            '华为广告' => '华为广告',
//            '喜马拉雅广告' => '喜马拉雅广告',

        ];
        protected $table = 'ww_link';

        public function tplInfo(): BelongsTo
        {
            return $this->BelongsTo(WwTpl::class, 'tpl_id', 'id');
        }

        public function adminInfo(): BelongsTo
        {
            return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
        }
        public function adAccountInfo(): BelongsTo
        {
            return $this->BelongsTo(TencentAdAccount::class, 'account_id', 'account_id');
        }

        public function wwUserGroup(): BelongsTo
        {
            return $this->BelongsTo(WwUsersGroup::class, 'ww_user_group_id', 'id');
        }

        public function shieldPolicy(): BelongsTo
        {
            return $this->BelongsTo(ShieldPolicy::class, 'shield_policy_id', 'id');
        }

        public static function getWwLinkInfoFromRedis($id){
            $redisKey = 'ww_link_info_id_'.$id;
            $wwLink = Cache::store('redis')->get($redisKey);
            if(!empty($wwLink)){
                return json_decode($wwLink,true);
            }
            $wwLink = self::withTrashed()->with("adAccountInfo","tplInfo","wwUserGroup")->find($id);
            Cache::store('redis')->set($redisKey, json_encode($wwLink),300);
            if($wwLink){
                return $wwLink->toArray();
            }
            return [];
        }

        public static function getShowWwUserPolicy(WwLink $wwLink){
            if($wwLink->show_ww_user_policy){
                return $wwLink->show_ww_user_policy;
            }
            $redisKey = 'show_ww_user_policy'.$wwLink->admin_uid;
            $adminShowPId = Cache::store('redis')->get($redisKey);
            if($adminShowPId){
                return $adminShowPId;
            }
            /** @var AdminUser $adminInfo */
            $adminInfo = AdminUser::query()->find($wwLink->admin_uid);
            if($adminInfo && $adminInfo->show_ww_user_policy){
                Cache::store('redis')->set($redisKey, $adminInfo->show_ww_user_policy,240);
                return $adminInfo->show_ww_user_policy;
            }
            return 0;
        }

        /**
         * @param string $mediaType 媒体类型
         * @param string $host 域名
         * @param mixed  $accountId 账户ID
         * @return string
         */
        public static function getLinkUrl(string $mediaType, string $host, mixed $accountId): string
        {
            if (empty($host)) {
                return '未配置域名';
            }
            return match ($mediaType) {
                '腾讯广告' => $host . "/wec?adt=" . $accountId,
                '巨量广告' => $host . "/ordinary?adt=" . $accountId . "&accountid=__ADVERTISER_ID__&aid=__AID__&cid=__CID__&clickid=__CLICK_ID__&promotioni=__PROMOTION_ID__&projectid=__PROJECT_ID__",
                'VIVO广告' => $host . "/ordinary?adt=" . $accountId . "&requestId=__REQUESTID__&creativeId=__ADID__",
                '优酷广告' => $host . "/ordinary?adt=" . $accountId . "&trackid=__TRACKID__&creativeid=__CREATIVEID__",
                '喜马拉雅广告' => $host . "/ordinary?adt=" . $accountId . "&callback=_CALLBACK_URL_",
                '爱奇艺广告' => $host . "/ordinary?adt=" . $accountId . "&oaid=__OAID__&mac=__MAC__&os=__OS__&idfa=__IDFA__&androidid=__ANDROIDID__&imei=__IMEI__",
                default => $host . "/ordinary?adt=" . $accountId
            };
        }

        /**
         *  获取指定管理员的跳转404区域名称数组
         * @param string $adminUid 媒体类型
         * @return array
         */
        public static function getLinkAdminBlackAreas($adminUid): array
        {
            $key = 'admin_black_areas_' . $adminUid;
            $blackAreas = Cache::store('redis')->get($key);
            if (!$blackAreas) {
                $blockAreaIds = AdminUser::query()->find($adminUid)->block_area ?? [];
                $blackAreas = Area::query()->whereIn('id', $blockAreaIds)->pluck('name')->toArray();
                Cache::store('redis')->set($key, $blackAreas);
            }
            return $blackAreas ?? [];

        }
    }
