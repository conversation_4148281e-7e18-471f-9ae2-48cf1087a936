<?php

namespace App\Admin\Forms\WwUser;

use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Models\AdminSubUserAuthCorp;
use App\Models\AdminUser;
use App\Models\CorpLabels;
use App\Models\WwUser;
use App\Services\Tools\LogService;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class BatchSetWwCorpLabelForm extends Form implements LazyRenderable
{
    use LazyWidget;

    // 处理请求
    public function handle(array $input)
    {
        LogService::inputLog('Tools','销售客服管理-配置企微标签', $input, Admin::user()->id, Admin::user()->username);
        //获取id
        if (!$input['id']) {
            return $this->response()->alert()->error('提示')->detail('请选择销售客服');
        }
        // id转化为数组
        $id = explode(',', $input['id']);
        if (!$id) {
            return $this->response()->alert()->error('提示')->detail('参数错误');
        }
        //查询销售是否存在
        $wwUsers = WwUser::query()
            ->whereIn('id', $id)
            ->get();
        if ($wwUsers->isEmpty()) {
            return $this->response()->alert()->error('提示')->detail('选择的销售不存在');
        }
        //获取所有销售的corp_auth_id，进行去重
        $uniqueCorpAuthId = $wwUsers->pluck('corp_auth_id')->unique();
        //判断去重后是否等于1（所有销售都是同一corp_auth_id）
        $uniqueCorpIdAuthCount = $uniqueCorpAuthId->count() === 1;
        //如果是false,非同一企业
        if (!$uniqueCorpIdAuthCount) {
            return $this->response()->alert()->error('提示')->detail('所选的销售非同一企业');
        }
        //判断销售的corp_auth_id和批量配置标签的corp_auth_id是否一致
        if ($uniqueCorpAuthId[0] != $input['corp_auth_id']) {
            return $this->response()->alert()->error('提示')->detail('所选的销售企微主体不一致');
        }
//        dd('ok');
        //获取增加企微标签
        $addCorpLabel = json_decode($input['add_corp_label'], true);
        //获取移除企微标签
        $removeCorpLabel = json_decode($input['remove_corp_label'], true);

        //判断是不是无操作
        if (!$addCorpLabel && !$removeCorpLabel) {
            return $this->response()->alert()->error('提示')->detail('请选择要增加或移除的标签');
        }

        //获取企微标签
        $corpAuthRecord = AdminSubUserAuthCorp::query()->where('id', $input['corp_auth_id'])->first();
        $tagData = CorpLabels::getCorpLabelsByCorpAuth($corpAuthRecord);

        // 检查两个数组的交集
        $repeatCorpLabel = array_intersect($addCorpLabel, $removeCorpLabel);
        // 如果交集不为空，说明有重复值
        if (!empty($repeatCorpLabel)) {
            return $this->response()->alert()->error('提示')->detail('增加标签和移除标签中存在相同的标签');
        }

        /** @var WwUser $wwUser */
        foreach ($wwUsers as $wwUser) {
            if (!AdminUser::isAdmin($wwUser)) {
                return $this->response()->alert()->error('提示')->detail('无操作权限');
            }

            //获取老标签
            $oldCorpLabel = json_decode($wwUser->ww_corp_label_ids, true);
            $oldCorpLabelNames = json_decode($wwUser->ww_corp_label_names, true);
            //如果有老的标签，获取一下差集
            if ($oldCorpLabel) {
                //增加企微标签如果有内容，取差集，合并差集
                if ($addCorpLabel) {
                    $diff = array_diff($addCorpLabel, $oldCorpLabel);
                    $oldCorpLabel = array_merge($oldCorpLabel, $diff);
                }
                //移除企微标签，如果有内容 取交集，取差集。
                if ($removeCorpLabel) {
                    $intersection = array_intersect($removeCorpLabel, $oldCorpLabel);
                    $oldCorpLabel = array_diff($oldCorpLabel, $intersection);
                }
            } else {
                //如果没有就只获取添加的增加标签
                if ($addCorpLabel) {
                    $oldCorpLabel = $addCorpLabel;
                }
            }

            $corpLabelNames = [];
            if ($oldCorpLabel) {
                foreach ($oldCorpLabel as $tagId) {
                    if (isset($tagData[$tagId])) {
                        $corpLabelNames[] = $tagData[$tagId];
                    }
                }
            }
            $wwUser->ww_corp_label_ids = json_encode($oldCorpLabel);
            $wwUser->ww_corp_label_names = json_encode($corpLabelNames, JSON_UNESCAPED_UNICODE);
            $wwUser->save();

            //处理操作日志
            //本次下拉框选择增加的标签
            $input_add_corp_label_names = [];
            if ($addCorpLabel) {
                foreach ($addCorpLabel as $tagId) {
                    if (isset($tagData[$tagId])) {
                        $input_add_corp_label_names[] = $tagData[$tagId];
                    }
                }
            }
            //本次下拉框选择移除的标签
            $input_remove_corp_label_names = [];
            if ($removeCorpLabel) {
                foreach ($removeCorpLabel as $tagId) {
                    if (isset($tagData[$tagId])) {
                        $input_remove_corp_label_names[] = $tagData[$tagId];
                    }
                }
            }
            $input_add_corp_label_names = implode('、', $input_add_corp_label_names);
            $input_remove_corp_label_names = implode('、', $input_remove_corp_label_names);
            $result = implode('、', json_decode($wwUser->ww_corp_label_names, true));
            if (!$input_add_corp_label_names) {
                $input_add_corp_label_names = '';
            }
            if (!$input_remove_corp_label_names) {
                $input_remove_corp_label_names = '';
            }
            if (!$result) {
                $result = '';
            }
            $oldCorpLabelNames = implode('、', $oldCorpLabelNames);
            //添加操作日志队列
            AdminActionLogJob::dispatch(
                'batch_set_corp_label',
                $wwUser->id,
                AdminActionLog::ACTION_TYPE['销售'],
                '「' . $wwUser->name . '」批量设置企微标签：增加【' . $input_add_corp_label_names . '】 ，移除【' . $input_remove_corp_label_names . '】 ，更新前【' . $oldCorpLabelNames . '】，更新后【' . $result . '】',
                getIp(),
                Admin::user()->id,
            )->onQueue('admin_action_log_job');
        }
        return $this->response()->alert()->success('提示')->detail('配置成功')->refresh();
    }

    public function form()
    {
        $this->html(
            <<<HTML
<div class="alert alert-info alert-dismissable">
            <h4><i class="fa fa-info"></i>&nbsp; 提示</h4>
        所选销售必须为同一企业微信下。<br>并且选择企业微信与销售的企微必须保持一致。
</div>
HTML
        );

        $corpAuthRecord = AdminSubUserAuthCorp::getCorpAuthListBuyAdminUid(Admin::user()->id);
        $this->hidden('id')->value($this->payload['ids'] ?? ''); // 批量操作时使用BatchActionPlus 就可以$this->payload['ids'] 获取批量选中的ids
        $this->select("corp_auth_id", "企业微信")
            ->options($corpAuthRecord)
//            ->loads(['add_corp_label', 'remove_corp_label'], ['/api/getLocalWwCorpLabel', '/api/getLocalWwCorpLabel'])
            ->required();



        //优化企微标签筛选
        $this->multipleSelect("add_corp_label", "增加企微标签")
            ->ajax("/api/asyncGetWwCorpLabel") // 启用异步搜
            ->saveAsJson()
            ->help('如未找到标签，可以到企微管理->点击【同步企微标签】按钮进行同步。')
            ->placeholder('请输入标签名称搜索。');;

        $this->multipleSelect("remove_corp_label", "移除企微标签")
            ->ajax("/api/asyncGetWwCorpLabel") // 启用异步搜
            ->saveAsJson()
            ->help('如未找到标签，可以到企微管理->点击【同步企微标签】按钮进行同步。')
            ->placeholder('请输入标签名称搜索。');;
        // 添加JavaScript代码实现动态更新ajax URL
        Admin::script(<<<JS
            // 监听企业微信选择变化
            $('select[name="corp_auth_id"]').on('change', function() {
                var corpAuthId = $(this).val();
                var \$addLabelSelect = $('select[name="add_corp_label[]"]');
                 var \$removeLabelSelect = $('select[name="remove_corp_label[]"]');

                if (corpAuthId) {
                    // 重新初始化select2并设置新的ajax URL
                    \$addLabelSelect.select2('destroy').select2({
                        ajax: {
                            url: 'api/asyncGetWwCorpLabel?corp_auth_id=' + corpAuthId,
                            dataType: 'json',
                            delay: 250,
                            data: function (params) {
                                return {
                                    q: params.term,
                                    page: params.page
                                };
                            },
                            processResults: function (data, params) {
                                params.page = params.page || 1;
                                return {
                                    results: data.data,
                                    pagination: {
                                        more: data.pagination && data.pagination.more
                                    }
                                };
                            }
                        },
                        multiple: true,
                        placeholder: '请选择...',
                        allowClear: true
                    });

                    // 清空当前选中的值
                    \$addLabelSelect.val(null).trigger('change');
                } else {
                    // 如果没有选择企业微信，则清空标签选择
                    \$addLabelSelect.val(null).trigger('change');
                }

                 if (corpAuthId) {
                    // 重新初始化select2并设置新的ajax URL
                    \$removeLabelSelect.select2('destroy').select2({
                        ajax: {
                            url: 'api/asyncGetWwCorpLabel?corp_auth_id=' + corpAuthId,
                            dataType: 'json',
                            delay: 250,
                            data: function (params) {
                                return {
                                    q: params.term,
                                    page: params.page
                                };
                            },
                            processResults: function (data, params) {
                                params.page = params.page || 1;
                                return {
                                    results: data.data,
                                    pagination: {
                                        more: data.pagination && data.pagination.more
                                    }
                                };
                            }
                        },
                        multiple: true,
                        placeholder: '请选择...',
                        allowClear: true
                    });

                    // 清空当前选中的值
                    \$removeLabelSelect.val(null).trigger('change');
                } else {
                    // 如果没有选择企业微信，则清空标签选择
                    \$removeLabelSelect.val(null).trigger('change');
                }
            });
JS
        );
    }


}
