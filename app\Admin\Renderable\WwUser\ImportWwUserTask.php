<?php

namespace App\Admin\Renderable\WwUser;

use App\Models\AdminSubUser;
use App\Models\AdminSubUserAuthCorp;
use App\Models\AdminUser;
use App\Models\WwCorpInfo;
use App\Models\WwUsersImportTask;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid;
use Dcat\Admin\Grid\LazyRenderable;
use Dcat\Admin\Widgets\Table;
use Illuminate\Support\Facades\DB;

class ImportWwUserTask extends LazyRenderable
{
    public function grid(): Grid
    {
        return Grid::make(new WwUsersImportTask(), function (Grid $grid) {
            $grid->disableFilterButton();
            $grid->showRefreshButton();
            $grid->model()->orderByDesc("id");
            if (!AdminUser::isSystemOp()) {
                $grid->model()->whereIn("admin_uid", AdminSubUser::getAdminUids(Admin::user()->id));
            }
            $grid->column('id');
            $statusArr = [0 => '等待导入', 1 => '导入中', 2 => '成功', 3 => '部分成功', 4 => '失败'];
            $grid->column('status', '状态')->display(function ($value) use ($statusArr) {
                if ($value <= 1) {
                    $count = DB::table("ww_users_import_task_sub_log")->where("task_id", $this->id)->count();
                    $fai_count = DB::table("ww_users_import_task_sub_log")
                        ->where("task_id", $this->id)
                        ->where("ww_user_id", 0)
//                            ->orWhereNull("user_name")
                        ->count();
                    if ($count == 0) {
                        $status = 0;
                    } elseif ($count < $this->total) {
                        $status = 1;
                    } else {
                        if ($fai_count > 0) {
                            if ($fai_count == $this->total) {
                                $status = 4;
                            } else {
                                $status = 3;
                            }
                        } else {
                            $status = 2;
                        }
                    }
                    DB::table("ww_users_import_task")->where("id", $this->id)->update([
                        'status' => $status
                    ]);
                } else {
                    $status = $value;
                }
                return $statusArr[$status] ?? "";
            });
            $grid->column('admin_uid', '用户')->display(function () {
                return AdminUser::query()->where("id", $this->admin_uid)->value("username");
            });
            $grid->column('corp_id', '企业名称')->display(function () {
                return WwCorpInfo::query()->where("id", $this->corp_id)->value("corp_name");
            });
            $grid->column('total', '总数');
            $grid->column('imported_suc', '导入成功')->display(function () {
                $count = DB::table("ww_users_import_task_sub_log")
                    ->where("task_id", $this->id)
                    ->where("ww_user_id", '<>', 0)
                    ->get()
                    ->groupBy('user_id');
                return count($count->toArray());
            });
            $grid->column('imported_fai', '导入失败')->display(function () {
                return DB::table("ww_users_import_task_sub_log")->where("task_id", $this->id)->where("ww_user_id", 0)->count();
            });
            $grid->column('result', '导入结果')->display("展开")->expand(function () {
                $data = DB::table("ww_users_import_task_sub_log")->where("task_id", $this->id)->select("id", "user_id", "user_name", "desc")->get()->toArray();
                return Table::make(["ID", "销售账号ID", "销售名称", "结果"], $data);
            });
            // $grid->column("fai_info","失败信息")->display(function(){
            //     $data = DB::table("ww_users_import_task_sub_log")->where("task_id", $this->id)->where("ww_user_id", 0)->pluck("user_id", "user_name", )->toArray();
            //     $idString = [];
            //     $nameString = [];
            //     foreach($data as $id=>$name){
            //         $idString[] = $id;
            //         $nameString[] = $name;
            //     }
            //     return implode(PHP_EOL,$idString) .PHP_EOL.implode(PHP_EOL,$nameString);
            // })->ClickCopy(0,"复制错误信息");
            $grid->column('created_at');


            $grid->paginate(10);
            $grid->disableActions();
            $grid->disableRowSelector();

            $grid->filter(function (Grid\Filter $filter) use ($statusArr) {
                $corpAuthList = AdminSubUserAuthCorp::query()->with("corpInfo")->where("admin_uid", Admin::user()->id)->orderByDesc("id")->get()->toArray();
                $corpIds = array_column($corpAuthList, 'corp_id');
                $corp_list = WwCorpInfo::query()->whereIn('id', $corpIds)->pluck('corp_name', 'id');
                $filter->equal('corp_id', '企业名称')->select($corp_list)->width(4);
                $filter->equal('status', '状态')->select($statusArr)->width(4);
            });
        });
    }
}
