<?php
	
	namespace App\Models;
	
	use Dcat\Admin\Traits\HasDateTimeFormatter;
	use Illuminate\Database\Eloquent\Model;
	use Illuminate\Database\Eloquent\SoftDeletes;
	
	/**
	 * @property mixed        $id
	 * @property mixed|string $md5_id
	 * @property mixed|string $suite_id
	 * @property mixed|string $open_userid
	 * @property mixed|string $parents
	 * @property mixed|string $device_id
	 * @property mixed        $userid
	 * @property mixed|string $corp_id
	 */
	class Corp3rdViewUser extends Model
	{
		use HasDateTimeFormatter;
		use SoftDeletes;
		
		protected $table = 'corp_3rd_view_users';
		
	}
