<?php

namespace App\Admin\Forms\WwUser;

use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Models\AdminUser;
use App\Models\WwUser;
use App\Models\WwUsersGroup;
use App\Models\WwUsersGroupsRel;
use App\Services\Tools\LogService;
use App\Services\Tools\UtilsService;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class BatchSetGroupForm extends Form implements LazyRenderable
{
    use LazyWidget;

    // 处理请求
    public function handle(array $input)
    {
        LogService::inputLog('Tools','销售客服管理-增减分组', $input, Admin::user()->id, Admin::user()->username);
        if (!$input['id']) {
            return $this->response()->alert()->error('提示')->detail('请选择销售。');
        }
        // id转化为数组
        $id = explode(',', $input['id']);
        if (!$id) {
            return $this->response()->alert()->error('提示')->detail('参数错误。');
        }
        $wwUsers = WwUser::query()->find($id);
        if ($wwUsers->isEmpty()) {
            return $this->response()->alert()->error('提示')->detail('销售不存在。');
        }
        /** @var WwUser $wwUser */
        foreach ($wwUsers as $wwUser) {
            if (!AdminUser::isAdmin($wwUser)) {
                return $this->response()->alert()->error('提示')->detail('无操作权限。');
            }
            if (!empty($input['del_group_id'])) {
                //获取分组名称
                $delGroupTitles = WwUsersGroup::getGroupTitle($input['group_id']);
                foreach ($input['del_group_id'] as $gid) {
                    $lastGroupRecord = WwUsersGroupsRel::withTrashed()->where("ww_user_id", $wwUser->id)->where("ww_group_id", $gid)->first();
                    $lastGroupRecord?->forceDelete();

                    AdminActionLogJob::dispatch(
                        'batch_set_group',
                        $wwUser->id,
                        AdminActionLog::ACTION_TYPE['销售'],
                        '【' . $wwUser->name . '】，移除分组：' . implode('，', $delGroupTitles),
                        getIp(),
                        Admin::user()->id
                    )->onQueue('admin_action_log_job');
                }
            }
            if (!empty($input['group_id'])) {
                //获取分组名称
                $addGroupTitles = WwUsersGroup::getGroupTitle($input['group_id']);
                //创建销售与分组关联数据
                WwUsersGroupsRel::createWwGroupRel($wwUser,$input['group_id']);
                //添加操作日志队列
                AdminActionLogJob::dispatch(
                    'batch_set_group',
                    $wwUser->id,
                    AdminActionLog::ACTION_TYPE['销售'],
                    '【' . $wwUser->name . '】，新增分组：' . implode('，', $addGroupTitles),
                    getIp(),
                    Admin::user()->id
                )->onQueue('admin_action_log_job');
            }
        }
        //增加分组 该用户下的所有销售0
        if (!empty($input['group_id'])) {
            WwUser::reSetTodayShowCount(Admin::user()->id);
        }
        return $this->response()->alert()->success('提示')->detail('设置成功。')->refresh();
    }

    public function form()
    {


//        $groupList = [];
//        if (AdminUser::isSystemOp()) {
//            $groupList = WwUsersGroup::query()->orderBy('id', 'desc')->select("title as name", 'id')->get();
//        }
//        if (Admin::user()->isRole('customer')) { //如果是“客户”的角色 也就是主账号
//            $groupList = WwUsersGroup::query()->whereIn("admin_uid", AdminSubUser::getAdminUids(Admin::user()->id))->select("title as name", 'id')->get();
//        }
//        if (Admin::user()->isRole('customer_op') || Admin::user()->isRole('sub_user_ww')) { //如果是客户运营的角色 也就是子账号
//            $groupList = WwUsersGroup::query()->where("admin_uid", Admin::user()->id)->select("title as name", 'id')->get();
//        }

//        $this->html('<div id="search_group_id"></div>', '快捷选择增加分组');
        /** 搜搜节点组件 */
//        UtilsService::searchJsTree('search_group_id','group_id',' ',' ');
        $group = WwUsersGroup::query()->where("admin_uid", Admin::user()->id)->pluck("title", "id");
        $this->listbox('group_id','新增分组')->options($group);
//        $this->tree('group_id', '新增分组')
//            ->nodes($groupList)
//            ->customFormat(function ($v) {
//                if (!$v) {
//                    return [];
//                }
//
//                // 这一步非常重要，需要把数据库中查出来的二维数组转化成一维数组
//                return array_column($v, 'id');
//            });

//        $this->html('<div id="search_del_group_id"></div>', '快捷选择移除分组');
        /** 搜搜节点组件 */
//        UtilsService::searchJsTree('search_del_group_id','del_group_id',' ',' ');
        $this->listbox('del_group_id','移除分组')->options($group);
//        $this->tree('del_group_id', '移除分组')
//            ->nodes($groupList)
//            ->customFormat(function ($v) {
//                if (!$v) {
//                    return [];
//                }
//
//                // 这一步非常重要，需要把数据库中查出来的二维数组转化成一维数组
//                return array_column($v, 'id');
//            });
        $this->hidden('id')->value($this->payload['ids'] ?? ''); // 批量操作时使用BatchActionPlus 就可以$this->payload['ids'] 获取批量选中的ids
        $this->confirm('确认提交？');
    }

    // 返回表单数据，如不需要可以删除此方法
    public function default()
    {
        return [
            'password' => '',
            'password_confirm' => '',
        ];
    }
}
