<?php

	namespace App\Admin\Forms\WwUser;

	use App\Jobs\AdminActionLogJob;
    use App\Models\AdminActionLog;
	use App\Models\AdminSubUserAuthCorp;
	use App\Models\WwAppList;
	use App\Models\WwUser;
	use App\Models\WwUsersGroup;
	use App\Models\WwUsersGroupsRel;
    use App\Services\Corp\WwCorpApiService;
    use App\Services\Tools\LogService;
    use Dcat\Admin\Admin;
	use Dcat\Admin\Widgets\Form;
	use Illuminate\Support\Facades\DB;

	class ImportWwUserGroupFromOwner extends Form
	{
		/**
		 * Handle the form request.
		 *
		 * @param array $input
		 *
		 * @return mixed
		 */
		public function handle(array $input)
		{
            LogService::inputLog('Tools','销售客服管理-导入销售群-【群主ID】', $input, Admin::user()->id, Admin::user()->username);

            $corp_auth_id = $input['corp_auth_id'];
			/** @var AdminSubUserAuthCorp $corpAuthRecord */
			$corpAuthRecord = AdminSubUserAuthCorp::query()->with("corpInfo")->find($corp_auth_id);
			if (!$corpAuthRecord || !$corpAuthRecord->corpInfo) {
				return $this->response()->withValidation([
					'corp_auth_id' => '请检查企业微信是否已授权'
				])->error('请检查企业微信是否已授权');
			}

			$userId = explode(PHP_EOL, $input['user_id_list']);
			foreach ($userId as $key => $value) {
				$userId[$key] = trim($value);
			}
			//转换ID
			$userOpenIds = WwCorpApiService::userid_to_openuserid($corpAuthRecord->corpInfo, $userId);

			if (!isset($userOpenIds['open_userid_list'])) {
				return $this->response()->withValidation([
					'corp_auth_id' => '请检查企业微信是否已授权'
				])->error('请检查企业微信是否已授权');
			}
			if (count($userOpenIds['open_userid_list']) < count($userId)) {
				$errorOpenUserId = array_diff($userId, array_column($userOpenIds['open_userid_list'], 'userid'));
				return $this->response()
					->withValidation([
						'user_id_list' => "「" . implode(",", $errorOpenUserId) . "」" . '，ID有误，请检查ID是否正确，或用户是否在可见范围内'
					])
					->error(implode(",", $errorOpenUserId) . '，ID有误，请检查ID是否正确，或用户是否在可见范围内');
			}
			/** @var WwAppList $wwApp */
			$wwApp = WwAppList::query()->where("suite_id", $corpAuthRecord->corpInfo->suite_id)->first();
			if (!$wwApp) {
				return $this->response()->error('导入失败，企微应用不存在，请联系管理员');
			}

			//获取群列表
			$groupList = WwCorpApiService::group_chat_list($corpAuthRecord->corpInfo, array_column($userOpenIds['open_userid_list'], 'open_userid'));
			if ($groupList['errcode'] != 0) {
				return $this->response()->error('导入失败，群主名下未查询到群信息，如有疑问，请联系管理员');
			}

			DB::beginTransaction();
			foreach ($groupList['group_chat_list'] as $key => $datum) {
				$getChat = WwCorpApiService::group_chat_get($corpAuthRecord->corpInfo, $datum['chat_id']);
				if ($getChat['errcode'] != 0) {
					continue;
				}
				$wwUser = WwUser::query()->where([
					"admin_uid"    => Admin::user()->id,
					'corp_id'      => $corpAuthRecord->corpInfo->id,
					"corp_auth_id" => $corpAuthRecord->id,
					'open_user_id' => $datum['chat_id'],
					'type'         => 2
				])->first();
				if (!$wwUser) {
					$wwUser = new WwUser();
				}

				//基础信息
				$wwUser->admin_uid    = Admin::user()->id;
				$wwUser->ww_app_id    = $wwApp->id;
				$wwUser->corp_id      = $corpAuthRecord->corpInfo->id;
				$wwUser->corp_auth_id = $corpAuthRecord->id;
				$wwUser->type         = 2;
				$wwUser->open_user_id = $datum['chat_id'];
				$wwUser->user_id      = '';

				$wwUser->add_method        = 2;
				$wwUser->name              = !empty($getChat['group_chat']['name']) ? $getChat['group_chat']['name'] : "未命名";
				$wwUser->alias             = "";
				$wwUser->cus_acq_link_name = $getChat['group_chat']['owner'];
				$wwUser->group_member_list = count($getChat['group_chat']['member_list']);

				$wwUser->save();
				if (empty($wwUser->qrcode_config_id)) {
					$contactWay = WwCorpApiService::group_chat_add_join_way($corpAuthRecord->corpInfo, $datum['chat_id'], getLinkState());
					if (isset($contactWay['config_id'])) {
						$wwUser->qrcode_config_id = $contactWay['config_id'];
					} else {
						DB::rollBack();
						return $this->response()->error($datum['chat_id'] . '导入失败，请联系智投运营为您排查开通。' . (isset($contactWay['errmsg']) ? explode(",", $contactWay['errmsg'])[0] : ""));
					}
				}
				if (empty($wwUser->qrcode)) {
					$joinWayData = WwCorpApiService::group_chat_get_join_way($corpAuthRecord->corpInfo, $wwUser->qrcode_config_id);
					if (isset($joinWayData['join_way']['qr_code'])) {
						$wwUser->qrcode = $joinWayData['join_way']['qr_code'];
					} else {
						DB::rollBack();
						return $this->response()->error($datum['chat_id'] . '导入失败，' . (isset($joinWayData['errmsg']) ? explode(",", $joinWayData['errmsg'])[0] : ""));
					}
				}

				$wwUser->weight     = $input['weight'];
				$wwUser->wind_label = $input['label'] ?? "";

				$wwUser->cus_acq_link_status         = 0;
				$wwUser->cus_acq_link_status_message = '群聊不支持';

				$wwUser->auto_status_config = $input['auto_status_config'];
				$wwUser->up_time            = $input['up_time'];
				$wwUser->down_time          = $input['down_time'];
				$wwUser->down_add_count     = $input['down_add_count'];
				$wwUser->subscribe          = 1;
				$wwUser->save();

                //处理分组数据
				if (!empty($input['group_id'])) {
					WwUsersGroupsRel::createWwGroupRel($wwUser, $input['group_id']);
				}

                //添加操作日志队列
                AdminActionLogJob::dispatch(
                    'create_ww_user_group',
                    $wwUser->id,
                    AdminActionLog::ACTION_TYPE['销售'],
                    '导入「' . $wwUser->id . '」-「' . $wwUser->name . '」销售群。',
                    getIp(),
                    Admin::user()->id,
                )->onQueue('admin_action_log_job');
			}
			DB::commit();
            return $this->response()->alert()->success('提示')->detail('导入成功。')->refresh();
		}

		/**
		 * Build a form here.
		 */
		public function form()
		{
            $corpAuthRecord = AdminSubUserAuthCorp::getCorpAuthListBuyAdminUid(Admin::user()->id);
			$this->select("corp_auth_id", "企业微信")->options($corpAuthRecord);
			$this->textarea("user_id_list", '群主账号ID')->placeholder("一行一个，例如\r\nXXXXXX1\r\nXXXXXX2")->help("一行一个，可以批量添加，账号ID方式-企业微信后台-通讯录-选择企业-右侧账号ID")->required();
			$this->multipleSelect('group_id', '分组')->options(WwUsersGroup::query()->where("admin_uid", Admin::user()->id)->pluck("title", 'id'))->help("导入后，销售将自动进入该分组，如无请先创建");
			$this->text("label", "智投管理标签")->help("该标签为智投后台批量管理销售客服使用的标签，可通过标签批量配置客服，不是企业微信后台的进粉标签");
			$this->radio('auto_status_config', '自动上下线')->options([
				0 => '关闭',
				1 => '按时间段',
				2 => '按加粉量',
			])->when([1, 2], function ($form) {
				$this->time('up_time', '上线时间');
			})->when([1], function ($form) {
				$form->time('down_time', '下线时间');
			})->when([2], function ($form) {
				$form->number('down_add_count', '下线粉丝量')->help("当日加够数量大于等于改数量，将会自动下线该销售");
			})->default(0);
			$this->number('weight', '权重')->help("可以配置1-10的数字，数字越大，该销售展示概率越高")->default(1);
		}

		/**
		 * The data of the form.
		 *
		 * @return array
		 */
		public function default()
		{
			return [

			];
		}
	}

