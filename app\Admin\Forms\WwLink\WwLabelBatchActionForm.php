<?php

namespace App\Admin\Forms\WwLink;

use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Models\AdminUser;
use App\Models\WwLink;
use App\Services\Tools\LogService;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class WwLabelBatchActionForm extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input)
    {
        LogService::inputLog('Tools','ww_link-链接列表-企微标签', $input, Admin::user()->id, Admin::user()->username);

        // id转化为数组
        $id = explode(',', $input['id'] ?? null);
        if (!$id) {
            return $this->response()->alert()->error('提示')->detail('请选择需要操作的数据。');
        }
        $wwLink = WwLink::query()->find($id);
        if ($wwLink->isEmpty()) {
            return $this->response()->alert()->error('提示')->detail('页面不存在。');
        }
        foreach ($wwLink as $key => $value) {
            if (!AdminUser::isAdmin($value)) {
                return $this->response()->alert()->error('提示')->detail('异常操作，请刷新页面后重试。');
            }
            $value->ww_label = $input['ww_label'];
            $value->save();
            AdminActionLogJob::dispatch(
                'batch_set_ww_link_ww_label',
                $value->id,
                AdminActionLog::ACTION_TYPE['链接'],
                '批量配置企微链接企微标签，账户ID：「' . $value->account_id . '」，企微标签：' . $value->ww_label ,
                getIp(),
                Admin::user()->id
            )->onQueue('admin_action_log_job');
        }
        return $this->response()->alert()->success('提示')->detail('批量操作成功')->refresh();
    }

    public function form()
    {
        $this->textarea("ww_label","标签")->placeholder("一行一个，可以配置多个，需要确保配置的标签在企业微信后台标签库有配置，例如：" . PHP_EOL . "标签1" . PHP_EOL . "标签2");
        $this->hidden('id')->attribute('id', 'reset-ww_link_label_id');
    }
}
