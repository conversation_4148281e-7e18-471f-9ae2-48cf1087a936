<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;

use Illuminate\Database\Eloquent\Model;

/**
 * @property mixed|string       $adx_realtime_type
 * @property mixed|string       $enable_steady_exploration
 * @property mixed|string       $custom_cost_roi_cap
 * @property mixed|string       $enable_breakthrough_siteset
 * @property mixed|string       $cost_guarantee_money
 * @property mixed|string       $cost_guarantee_status
 * @property mixed|string       $aoi_optimization_strategy
 * @property mixed|string       $custom_cost_cap
 * @property mixed|string       $cost_constraint_scene
 * @property mixed|string       $mpa_spec
 * @property mixed|string       $auto_acquisition_status
 * @property mixed|string       $conversion_name
 * @property mixed|string       $forward_link_assist
 * @property mixed|string       $ecom_pkam_switch
 * @property mixed|string       $priority_site_set
 * @property mixed|string       $exploration_strategy
 * @property mixed|string       $marketing_scene
 * @property mixed|string       $material_package_id
 * @property mixed|string       $promoted_asset_type
 * @property mixed|string       $marketing_asset_id
 * @property mixed|string       $search_expansion_switch
 * @property mixed|string       $negative_word_cnt
 * @property mixed|string       $flow_optimization_enabled
 * @property mixed|string       $marketing_target_ext
 * @property mixed|string       $bid_scene
 * @property mixed|string       $data_model_version
 * @property mixed|string       $auto_derived_landing_page_switch
 * @property mixed|string       $search_expand_targeting_switch
 * @property mixed|string       $auto_derived_creative_enabled
 * @property mixed|string       $smart_cost_cap
 * @property mixed|string       $smart_bid_type
 * @property mixed|string       $auto_acquisition_budget
 * @property mixed|string       $auto_acquisition_enabled
 * @property mixed|string       $bid_mode
 * @property mixed|string       $deep_conversion_worth_rate
 * @property mixed|string       $deep_conversion_behavior_bid
 * @property mixed|string       $conversion_id
 * @property mixed|string       $deep_conversion_spec
 * @property mixed|string       $user_action_sets
 * @property mixed|string       $scene_spec
 * @property mixed|string       $daily_budget
 * @property mixed|string       $site_set
 * @property mixed|string       $automatic_site_enabled
 * @property mixed|string       $time_series
 * @property mixed|string       $optimization_goal
 * @property mixed|string       $bid_amount
 * @property mixed|string       $first_day_begin_time
 * @property mixed|string       $end_date
 * @property mixed|string       $begin_date
 * @property mixed|string       $marketing_target_id
 * @property mixed|string       $marketing_target_detail
 * @property mixed|string       $marketing_target_type
 * @property mixed|string       $marketing_sub_goal
 * @property mixed|string       $marketing_goal
 * @property mixed|string       $adgroup_name
 * @property mixed|string       $system_status
 * @property mixed|string       $is_deleted
 * @property mixed|string       $last_modified_time
 * @property mixed|string       $created_time
 * @property mixed|string       $configured_status
 * @property mixed|string       $targeting_translation
 * @property mixed|string       $targeting
 * @property mixed|string       $adgroup_id
 * @property mixed              $admin_uid
 * @property mixed|string       $account_id
 * @property mixed              $system_industry_id
 * @property false|mixed|string $marketing_carrier_detail
 * @property mixed|string       $marketing_carrier_type
 */
class AdAccountAdGroup extends Model
{
    use HasDateTimeFormatter;

    protected $table = 'ad_account_ad_groups';

    const SITE_SET = [
        'SITE_SET_MOBILE_UNION'     => '优量汇',
        'SITE_SET_KUAISHOU'         => '快手',
        'SITE_SET_WECHAT'           => '微信公众号与小程序',
        'SITE_SET_MOBILE_INNER'     => 'QQ、腾讯看点、腾讯音乐 (待废弃)',
        'SITE_SET_TENCENT_NEWS'     => '腾讯新闻',
        'SITE_SET_TENCENT_VIDEO'    => '腾讯视频',
        'SITE_SET_MOBILE_YYB'       => '应用宝',
        'SITE_SET_PCQQ'             => 'QQ、QQ 空间、腾讯音乐、PC 版位',
        'SITE_SET_KANDIAN'          => 'QQ 浏览器（原腾讯看点）',
        'SITE_SET_QQ_MUSIC_GAME'    => 'QQ、腾讯音乐及游戏',
        'SITE_SET_MOMENTS'          => '微信朋友圈',
        'SITE_SET_MINI_GAME_WECHAT' => '微信生态内的小游戏场景',
        'SITE_SET_MINI_GAME_QQ'     => '手机 QQ 生态内的小游戏场景',
        'SITE_SET_MOBILE_GAME'      => '集合腾讯游戏和优量汇联盟生态的手机端游戏',
        'SITE_SET_QQSHOPPING'       => 'QQ 购物',
        'SITE_SET_CHANNELS'         => '微信视频号',
        'SITE_SET_WECHAT_PLUGIN'    => '微信新闻插件',
        'SITE_SET_SEARCH_SCENE'     => '搜索场景',
    ];

    const  marketing_goal = [
        'MARKETING_GOAL_UNKNOWN'                   => '未知',
        'MARKETING_GOAL_USER_GROWTH'               => '用户增长',
        'MARKETING_GOAL_PRODUCT_SALES'             => '商品销售',
        'MARKETING_GOAL_LEAD_RETENTION'            => '线索留资',
        'MARKETING_GOAL_BRAND_PROMOTION'           => '品牌宣传',
        'MARKETING_GOAL_INCREASE_FANS_INTERACTION' => '加粉互动',
    ];

    const  marketing_sub_goal     = [
        'MARKETING_SUB_GOAL_UNKNOWN'                              => '未知',
        'MARKETING_SUB_GOAL_NEW_GAME_RESERVE'                     => '新游预约',
        'MARKETING_SUB_GOAL_NEW_GAME_TEST'                        => '新游测试',
        'MARKETING_SUB_GOAL_NEW_GAME_LAUNCH'                      => '新游首发',
        'MARKETING_SUB_GOAL_PLATEAU_PHASE_LAUNCH'                 => '平推期投放',
        'MARKETING_SUB_GOAL_MINI_GAME_NEW_CUSTOMER_GROWTH'        => '新客增长',
        'MARKETING_SUB_GOAL_MINI_GAME_RETURN_CUSTOMER_ENGAGEMENT' => '回流触达',
        'MARKETING_SUB_GOAL_APP_ACQUISITION'                      => '应用拉新',
        'MARKETING_SUB_GOAL_APP_ACTIVATION'                       => '应用拉活',
        'MARKETING_SUB_GOAL_NOT_INSTALL_USER'                     => '未安装用户',
        'MARKETING_SUB_GOAL_PRE_INSTALL_USER'                     => '预安装用户',
        'MARKETING_SUB_GOAL_UNLOADED_USER'                        => '已卸载用户',
        'MARKETING_SUB_GOAL_SHORT_INACTIVE_USER'                  => '短期未活跃用户',
        'MARKETING_SUB_GOAL_LONG_INACTIVE_USER'                   => '长期未活跃用户',
        'MARKETING_SUB_GOAL_GAME_VERSION_UPGRADE'                 => '版本更新',
    ];
    const  marketing_carrier_type = [
        'MARKETING_CARRIER_TYPE_UNKNOWN'                          => '未知',
        'MARKETING_CARRIER_TYPE_APP_ANDROID'                      => 'Android应用',
        'MARKETING_CARRIER_TYPE_APP_IOS'                          => 'IOS应用',
        'MARKETING_CARRIER_TYPE_WECHAT_OFFICIAL_ACCOUNT'          => '微信公众号',
        'MARKETING_CARRIER_TYPE_JUMP_PAGE'                        => '页面跳转',
        'MARKETING_CARRIER_TYPE_WECHAT_MINI_GAME'                 => '微信小游戏',
        'MARKETING_CARRIER_TYPE_WECHAT_CHANNELS_LIVE'             => '视频号直播',
        'MARKETING_CARRIER_TYPE_WECHAT_CHANNELS'                  => '视频号动态',
        'MARKETING_CARRIER_TYPE_WECHAT_CHANNELS_LIVE_RESERVATION' => '视频号直播预约',
        'MARKETING_CARRIER_TYPE_MINI_PROGRAM_WECHAT'              => '小程序',
        'MARKETING_CARRIER_TYPE_APP_QUICK_APP'                    => '快应用',
        'MARKETING_CARRIER_TYPE_PC_GAME'                          => 'PC游戏',
        'MARKETING_CARRIER_TYPE_QQ_MINI_GAME'                     => 'QQ小游戏',
        'MARKETING_CARRIER_TYPE_APP_HARMONY'                      => '鸿蒙应用',
    ];

    const MARKETING_TARGET_TYPE = [
        'MARKETING_TARGET_TYPE_APP_ANDROID'                      => 'Android应用',
        'MARKETING_TARGET_TYPE_APP_IOS'                          => 'iOS应用',
        'MARKETING_TARGET_TYPE_WECHAT_OFFICIAL_ACCOUNT'          => '微信公众号',
        'MARKETING_TARGET_TYPE_PRODUCT'                          => '教育产品',
        'MARKETING_TARGET_TYPE_TRAFFIC'                          => '汽车商品',
        'MARKETING_TARGET_TYPE_LOCAL_STORE'                      => '门店',
        'MARKETING_TARGET_TYPE_WECHAT_MINI_GAME'                 => '微信小游戏',
        'MARKETING_TARGET_TYPE_CONSUMER_PRODUCT'                 => '商品',
        'MARKETING_TARGET_TYPE_WECHAT_CHANNELS'                  => '视频号账号',
        'MARKETING_TARGET_TYPE_WECHAT_CHANNELS_LIVE'             => '视频号直播',
        'MARKETING_TARGET_TYPE_WECHAT_CHANNELS_LIVE_RESERVATION' => '视频号直播预约',
        'MARKETING_TARGET_TYPE_MINI_PROGRAM_WECHAT'              => '微信小程序',
        'MARKETING_TARGET_TYPE_APP_QUICK_APP'                    => '快应用',
        'MARKETING_TARGET_TYPE_CONSUME_MEDICAL'                  => '消费医疗',
        'MARKETING_TARGET_TYPE_COMPREHENSIVE_HOUSEKEEPING'       => '综合家政产品',
        'MARKETING_TARGET_TYPE_FICTION'                          => '小说',
        'MARKETING_TARGET_TYPE_SHORT_DRAMA'                      => '短剧',
        'MARKETING_TARGET_TYPE_AUDIOVISUAL_ENTERTAINMENT'        => '影音文娱产品',
        'MARKETING_TARGET_TYPE_BEAUTY_AND_PERSONAL_CARE'         => '丽人产品',
        'MARKETING_TARGET_TYPE_WEDDING_AND_PORTRAIT_PHOTOGRAPHY' => '婚恋摄影产品',
        'MARKETING_TARGET_TYPE_FRANCHISE_BRAND'                  => '招商加盟',
        'MARKETING_TARGET_TYPE_ENTERPRISE_SERVICES'              => '商务服务',
        'MARKETING_TARGET_TYPE_EXHIBITION_BOOTH_DESIGN'          => '布景会展',
        'MARKETING_TARGET_TYPE_INSURANCE'                        => '保险产品',
        'MARKETING_TARGET_TYPE_BANK'                             => '银行产品',
        'MARKETING_TARGET_TYPE_CREDIT'                           => '信贷产品',
        'MARKETING_TARGET_TYPE_INVESTMENT_CONSULTING'            => '投顾产品',
        'MARKETING_TARGET_TYPE_REAL_ESTATE'                      => '房产商品',
        'MARKETING_TARGET_TYPE_TELECOMMUNICATIONS_OPERATOR'      => '运营商产品',
        'MARKETING_TARGET_TYPE_TOURIST_ATTRACTIONS_TICKETS'      => '景点门票产品',
        'MARKETING_TARGET_TYPE_RENOVATION_SERVICES'              => '装修服务',
        'MARKETING_TARGET_TYPE_FURNITURE_AND_BUILDING_MATERIALS' => '家具建材',
        'MARKETING_TARGET_TYPE_EXHIBITION_SALES'                 => '展会卖场',
        'MARKETING_TARGET_TYPE_MEDICINE_INDUSTRY_COMMERCIAL'     => '两品一械商品',
        'MARKETING_TARGET_TYPE_FINANCE'                          => '金融产品',
        'MARKETING_TARGET_TYPE_LOCAL_STORE_PACKAGE'              => '门店包',
        'MARKETING_TARGET_TYPE_CATERING_AND_LEISURE'             => '餐饮休娱产品',
        'MARKETING_TARGET_TYPE_CHAIN_RESTAURANT'                 => '连锁餐饮产品',
        'MARKETING_TARGET_TYPE_COMMODITY_SET'                    => '商品集合',
        'MARKETING_TARGET_TYPE_TOURIST_TRAVEL_ROUTE'             => '旅行路线产品',
        'MARKETING_TARGET_TYPE_TOURIST_CRUISE_LINE'              => '邮轮航线产品',
        'MARKETING_TARGET_TYPE_TOURIST_HOTEL_SERVICE'            => '酒店服务产品',
        'MARKETING_TARGET_TYPE_TOURIST_AIRLINE_TICKETS'          => '航司票务产品',
        'MARKETING_TARGET_TYPE_LOCAL_STORE_COMBINE_WITH_PRODUCT' => '门店产品组合',
        'MARKETING_TARGET_TYPE_ACTIVITY'                         => '活动',
        'MARKETING_TARGET_TYPE_STORE'                            => '平台店铺',
        'MARKETING_TARGET_TYPE_MINI_GAME_QQ'                     => 'QQ小游戏',
        'MARKETING_TARGET_TYPE_PC_GAME'                          => 'PC游戏',
        'MARKETING_TARGET_TYPE_WECHAT_WORK'                      => '企业微信',
        'MARKETING_TARGET_TYPE_LIVE_STREAM_ROOM'                 => '引流直播间',
        'MARKETING_TARGET_TYPE_PERSONAL_STORE'                   => '个人店铺',
        'MARKETING_TARGET_TYPE_PLATFORM_CHANNEL'                 => '平台频道',
        'MARKETING_TARGET_TYPE_TWO_WHEEL_VEHICLE'                => '二轮车商品',
        'MARKETING_TARGET_TYPE_GOVERNMENT_AFFAIRS'               => '政务',
        'MARKETING_TARGET_TYPE_CAR_ECOLOGY'                      => '汽车生态',
        'MARKETING_TARGET_TYPE_WECHAT_STORE_PRODUCT'             => '微信小店商品',
        'MARKETING_TARGET_TYPE_APP_HARMONY'                      => '鸿蒙应用',
        'MARKETING_TARGET_TYPE_WECHAT_STORE_PRODUCT_SET'         => '微信小店商品集合',
    ];
}
