<?php

namespace App\Admin\Extensions\Tools\WwUserAddRecord;

use Dcat\Admin\Grid\Tools\AbstractTool;

class AddRecordExportTool extends AbstractTool
{
    /**
     * 按钮样式定义
     *
     * @var string
     */
    protected $style = 'btn btn-primary dropdown-toggle btn-outline';

    /**
     * 渲染工具
     *
     * @return string
     */
    public function render()
    {
        $exportUrl = admin_url('ww_user_add_record/xls-export');

        return <<<HTML
<div class="btn-group dropdown" style="margin-right:3px">
    <button type="button" class="btn btn-primary dropdown-toggle btn-outline" data-toggle="dropdown">
        <i class="feather icon-download"></i>
        <span class="d-none d-sm-inline">&nbsp;导出&nbsp;</span>
        <span class="caret"></span>
        <span class="sr-only"></span>
    </button>
    <ul class="dropdown-menu" role="menu" style="left: 0px; right: inherit;">
        <li class="dropdown-item">
            <a href="{$exportUrl}?_export_=all" target="_blank">全部</a>
        </li>
        <li class="dropdown-item">
            <a href="{$exportUrl}?_export_=page:1" target="_blank">当前页</a>
        </li>
        <li class="dropdown-item">
            <a href="javascript:void(0)" class="export-selected" data-url="{$exportUrl}">选择的行</a>
        </li>
    </ul>
</div>

<script>
$(document).ready(function() {
    // 恢复位置
    $('div.btn-group.dropdown').filter(function () {
        return $(this).text().includes('导出');
    }).appendTo('div.pull-right[data-responsive-table-toolbar="grid-table"]');

    // 处理选择行导出
    $('.export-selected').off('click').on('click', function() {
        var url = $(this).data('url');
        var selected = [];

        // 获取选中的行
        $('.grid-row-checkbox:checked').each(function() {
            var id = $(this).data('id');
            if (id) {
                selected.push(id);
            }
        });

        if (selected.length === 0) {
            Dcat.swal.warning('请至少选择一行数据');
            return;
        }

        // 构建导出URL
        var exportUrl = url + '?_export_=selected:__rows__&__rows__=' + selected.join(',');
        window.open(exportUrl, '_blank');
    });
});
</script>
HTML;
    }
}
