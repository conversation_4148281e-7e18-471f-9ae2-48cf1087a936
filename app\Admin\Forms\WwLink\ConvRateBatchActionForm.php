<?php

namespace App\Admin\Forms\WwLink;

use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Models\AdminUser;
use App\Models\WwLink;
use App\Models\WwLinkConvTad;
use App\Services\Tools\LogService;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class ConvRateBatchActionForm extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input)
    {
        LogService::inputLog('Tools','ww_link-链接列表-回传比例', $input, Admin::user()->id, Admin::user()->username);

        // id转化为数组
        $id = explode(',', $input['id'] ?? null);
        if (!$id) {
            return $this->response()->alert()->error('提示')->detail('请选择需要操作的数据。');
        }
        $wwLink = WwLink::query()->find($id);
        if ($wwLink->isEmpty()) {
            return $this->response()->alert()->error('提示')->detail('页面不存在。');
        }
        foreach ($wwLink as $value) {
            if (!AdminUser::isAdmin($value)) {
                return $this->response()->alert()->error('提示')->detail('异常操作，请刷新页面后重试。');
            }
            $value->conv_count = $input['conv_count'];
            $value->tads_count = $input['tads_count'];
            $value->save();
            //获取今天的计数
            $wwLinkConvTads = WwLinkConvTad::query()->where("ww_link_id", $value->id)->where("date", date("Y-m-d"))->first();
            if (!$wwLinkConvTads) {
                $wwLinkConvTads                  = new WwLinkConvTad();
                $wwLinkConvTads->ww_link_id      = $value->id;
                $wwLinkConvTads->date            = date("Y-m-d");
            }
            $wwLinkConvTads->real_conv_count = 0;
            $wwLinkConvTads->real_tads_count = 0;
            $wwLinkConvTads->save();
            AdminActionLogJob::dispatch(
                'batch_set_link_rate',
                $value->id,
                AdminActionLog::ACTION_TYPE['链接'],
                '批量设置回传比例，账户ID：「' . $value->account_id . '」，转化：' . $input['conv_count'] . '，上报：' . $input['tads_count'],
                getIp(),
                Admin::user()->id
            )->onQueue('admin_action_log_job');

        }
        return $this->response()->alert()->success('提示')->detail('配置成功')->refresh();
    }

    public function form()
    {
        $this->hidden('id')->attribute('id', 'reset-ww_link_conv_rate_id');
        $this->number('conv_count', '转化')->min(1)->default(1)->required();
        $this->number('tads_count', '上报')->min(1)->default(1)->required()->help("该配置为优化使用，系统将根据您配置的比例进行广告效果优化，建议使用默认配置【1转化1上报】，请谨慎使用。<br/>配置规则如下：1转化1上报=100%回传，2转化1上报=50%回传，10转化7上报=70%上传，依此类推");
    }
}
