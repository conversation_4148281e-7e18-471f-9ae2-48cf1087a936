<?php

namespace App\Http\Controllers;

use App\Http\Controllers\WorkWeixin3rd\AuthController;
use App\Http\Controllers\WorkWeixin3rd\ChangeExternalChat;
use App\Http\Controllers\WorkWeixin3rd\ChangeExternalContact;
use App\Http\Controllers\WorkWeixin3rd\CustomerAcquisition;
use App\Http\Controllers\WorkWeixin3rd\Subscribe;
use App\Models\WwAppList;
use EasyWeChat\Kernel\Exceptions\BadRequestException;
use EasyWeChat\Kernel\Exceptions\InvalidArgumentException;
use EasyWeChat\Kernel\Exceptions\RuntimeException;
use EasyWeChat\OpenWork\Application;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Psr\Http\Message\ResponseInterface;
use ReflectionException;
use Throwable;

class WorkWeixin3rdController
{
    /**
     * @param Request $request
     * @return ResponseInterface
     * @throws InvalidArgumentException
     * @throws BadRequestException
     * @throws RuntimeException
     * @throws ReflectionException
     * @throws Throwable
     */
    public function notify(Request $request): ResponseInterface
    {
        $sid                 = $request->get("sid");
        Log::info("3rdTicketNotify回调：sid：" . $sid);
        Log::info(json_encode($request->all()));

        $server              = self::getApp('', $sid)->getServer();
        $providerAccessToken = self::getApp('', $sid)->getProviderAccessToken();


        $providerAccessToken->getToken(); // string
        Log::info('获取->providerAccessToken : ' . $providerAccessToken->getToken());
        $param = $request->all();
        /**
         * 企业微信授权应用、取消授权等授权类的回调
         */
        //$server->with(AuthController::class);
        $server->with(function($message, \Closure $next) use ( $param ){
            Log::info('企业微信授权应用、取消授权等授权类的回调');
            $logObj = new AuthController();// 自建应用授权
            $logObj->handler($message,$param );
            return $next($message);
        });
        /**
         * 外部联系人变更，包含新增、编辑、外部联系人删除销售、销售删除外部联系人等 外部联系人类回调
         */
        //$server->with(ChangeExternalContact::class);
        $server->with(function($message, \Closure $next) use ( $param ){
            Log::info('外部联系人变更，包含新增、编辑、外部联系人删除销售、销售删除外部联系人等 外部联系人类回调');
            $logObj = new ChangeExternalContact();// 更新外部联系人
            $logObj->handler($message,$param );
            return $next($message);
        });
        /**
         * 群变更
         */
        $server->with(function($message, \Closure $next) use ( $param ){
            Log::info('群变更');
            $logObj = new ChangeExternalChat();// 群变更
            $logObj->handler($message,$param );
            return $next($message);
        });
        /**
         * 获客助手类回调，包含获客助手异常、被删除、或外部联系人开口说话的回调
         */
        //$server->with(CustomerAcquisition::class);
        $server->with(function($message, \Closure $next) use ( $param ){
            Log::info('获客助手类回调，包含获客助手异常、被删除、或外部联系人开口说话的回调');
            $logObj = new CustomerAcquisition();// 获客助手的回调
            $logObj->handler($message,$param );
            return $next($message);
        });
        /**
         * 企微应用的可见范围类的回调
         */
        //$server->with(Subscribe::class);
        $server->with(function($message, \Closure $next) use ( $param ){
            Log::info('可见范围的回调');
            $logObj = new Subscribe();// 可见范围的回调
            $logObj->handler($message,$param );
            return $next($message);
        });
        return $server->serve();
    }

    /**
     * @return Application
     * @throws InvalidArgumentException
     */
    public static function getApp($suite_id = '', $sid = ''): Application
    {
        //这里需要根据GET参数获取不同的账户ID
        if (!empty($sid)) {
            Log::info('sid不为空 : ' . $sid);
            /** @var WwAppList $wwApp */
            $wwApp = WwAppList::query()->where("md5_id", $sid)->first();
            if ($wwApp) {
                $config = [
                    'corp_id'         => $wwApp->corp_id,
                    'provider_secret' => $wwApp->provider_secret,
                    'suite_id'        => $wwApp->suite_id,
                    'suite_secret'    => $wwApp->suite_secret,
                    'token'           => $wwApp->token,
                    'aes_key'         => $wwApp->aes_key,
                ];
            }
        }
        if (!empty($suite_id)) {
            Log::info('suite_id : ' . $suite_id);
            /** @var WwAppList $wwApp */
            $wwApp = WwAppList::query()->where("suite_id", $suite_id)->first();
            if ($wwApp) {
                $config = [
                    'corp_id'         => $wwApp->corp_id,
                    'provider_secret' => $wwApp->provider_secret,
                    'suite_id'        => $wwApp->suite_id,
                    'suite_secret'    => $wwApp->suite_secret,
                    'token'           => $wwApp->token,
                    'aes_key'         => $wwApp->aes_key,
                ];
            }
        }
        if (!isset($config)) {
            Log::info('config不存在');
            $config = [
                'corp_id'         => 'ww1cf6aa7e1132b4f6',
                'provider_secret' => 'XbV8AdHl6_3DIKcBByf5df6-uFDGzuXB7rfI49ceyGcsZzh3MQHuh8i6EwkUG3DR',
                'suite_id'        => 'ww8f0203dd6465fd0e',
                'suite_secret'    => '22_XZ2dYqebaQN8bidw8ymNrfX4B0qn-eYBC4J6SX9g',
                'token'           => 'oqggjwd8olryHJa72mWXI1bb',
                'aes_key'         => 'HQ7UmZERrkOSz8apiP74XudBaoQaYOKt88Q9cghot5o',
            ];
        }
        $config['http'] = [
            'throw'   => false, // 状态码非 200、300 时是否抛出异常，默认为开启
            'timeout' => 5.0,
            //					'retry' => true, // 使用默认重试配置
            'retry'   => [
                // 仅以下状态码重试
                'status_codes' => [429, 500],
                // 最大重试次数
                'max_retries'  => 3,
                // 请求间隔 (毫秒)
                'delay'        => 1000,
                // 如果设置，每次重试的等待时间都会增加这个系数
                // (例如. 首次:1000ms; 第二次: 3 * 1000ms; etc.)
                'multiplier'   => 3
            ],
        ];

        $app = new Application($config);
        $app->setCache(Cache::store("redis"));
        return $app;
    }

}


