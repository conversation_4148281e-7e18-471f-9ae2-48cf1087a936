<?php

namespace App\Admin\Forms\Admin;

use App\Models\AdminUser;
use App\Models\IpInfect;
use App\Services\CacheService;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class BatchSetIpInFectStatusForm extends Form implements LazyRenderable
{
    use LazyWidget;

    // 处理请求
    public function handle(array $input)
    {
        // id转化为数组
        $id = explode(',', $input['id'] ?? null);
        if (!$id) {
            return $this->response()->error('参数错误');
        }

        $ipInfect = IpInfect::query()->find($id);
        if ($ipInfect->isEmpty()) {
            return $this->response()->error('配置不存在');
        }
        $allConfigAdminUies = IpInfect::query()->where('status',0)->pluck('admin_uid')->toArray();
        foreach ($ipInfect as $key => $value) {

            $value->status = $input['status'];
            $value->save();
            $adminUid = $value->admin_uid;
            $adminUser = AdminUser::query()->where("id", $adminUid)->first();
            if($adminUser->parent_id == 0){ //如果是主账号
                $adminUidsList = AdminUser::getAdminUidsByParentId([$adminUid]);
            }else{ //如果是自账号
                $adminUidsList = [$adminUid];
            }
            if ($input['status'] == 0) { //关闭 就是往集合里新增admin_uid
                CacheService::setIpInfectData($adminUidsList);
            } else { //开启 就是把admin_uid 从集合中移除
                if($allConfigAdminUies){ //获取所有配置的admin_uid
                    if(in_array($adminUid, $allConfigAdminUies)){ //如果当前用户ID在所有配置的admin_uid 数组中，则从要删除的的数组中排除
                        unset($adminUidsList[$adminUid]);
                    }
                }
                CacheService::deleteInfectData($adminUidsList);
            }
        }
        return $this->response()->success('操作成功')->refresh();
    }

    public function form()
    {
        $this->radio('status', '状态')->options([0 => '关闭', 1 => '开启'])->required();
        $this->hidden('id')->value($this->payload['ids'] ?? ''); // 获取传递的ID
    }

    // 返回表单数据，如不需要可以删除此方法
    public function default()
    {
        return [
            'password' => '',
            'password_confirm' => '',
        ];
    }
}
