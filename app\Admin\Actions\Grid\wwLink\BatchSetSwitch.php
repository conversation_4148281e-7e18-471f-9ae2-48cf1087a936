<?php

namespace App\Admin\Actions\Grid\wwLink;

use App\Admin\Forms\WwLink\BatchSetSwitchForm;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\BatchAction;
use Dcat\Admin\Widgets\Modal;
use Illuminate\Http\Request;

class BatchSetSwitch extends BatchAction
{
    protected $title = '<button class="btn ww_page_batch_btn btn-primary"><i class="fa fa-toggle-on"></i>&nbsp&nbsp<span class="selected"></span>启用状态</button>';

    public function handle(Request $request): Response
    {
        return $this->response()
            ->success('Processed successfully: '.json_encode($this->getKey()))
            ->redirect('/');
    }

    public function render()
    {
        // 实例化表单类
        $form = BatchSetSwitchForm::make();

        return Modal::make()
            ->lg()
            ->title('配置链接启用状态')
            // 因为此处使用了表单异步加载功能，所以一定要用 onLoad 方法
            // 如果是非异步方式加载表单，则需要改成 onShow 方法
            ->onLoad($this->getModalScript())
            ->button($this->title)
            ->body($form);
    }

    protected function getModalScript()
    {
        $warning = __('请选择记录');
        // 弹窗显示后往隐藏的id表单中写入批量选中的行ID
        return <<<JS
		// 获取选中的ID数组
		var key = {$this->getSelectedKeysScript()}
		if (key.length === 0) {
		$('.modal').modal('hide');
		        Dcat.swal.warning('{$warning}');
		        return false;
	    }
		$('#reset-batch_link_switch_id').val(key);
JS;
    }

}
