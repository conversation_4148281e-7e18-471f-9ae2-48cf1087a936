<?php

namespace App\Console\Commands\WwUser;

use App\Models\WwUser;
use Illuminate\Console\Command;

class ResetTodayShowCount extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'WwUser:ResetTodayShowCount';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        //每天凌晨重置today_show_count

        WwUser::query()->update(['today_show_count' => 0,'today_add_count' => 0]);
        return Command::SUCCESS;
    }

}
