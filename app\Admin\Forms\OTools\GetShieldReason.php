<?php /** @noinspection SqlNoDataSourceInspection */

	namespace App\Admin\Forms\OTools;

	use Admin;
    use App\Models\AdminUser;
    use App\Services\Tools\LogService;
    use Dcat\Admin\Widgets\Form;
    use Dcat\EasyExcel\Excel;
    use Illuminate\Support\Facades\DB;
    use Illuminate\Support\Facades\Storage;

    class GetShieldReason extends Form
	{
		/**
		 * Handle the form request.
		 *
		 * @param array $input
		 *
		 * @return mixed
		 */
		public function handle(array $input)
		{
            LogService::inputLog('Tools','运营工具箱-获取屏蔽原因', $input, Admin::user()->id, Admin::user()->username);
			if (isset($input['admin_user_id'])) {
				$sql = 'SELECT l.account_id,r.id,r.user_id,r.ww_link_id,r.need_shield,r.admin_uid,r.page_type,r.shield_reason,r.area,r.ip,r.accuracy,r.add_count, r.view_count, r.vc_count, r.is_black, r.is_white, r.is_deny_ip, r.is_mobile, r.is_wechat, r.is_ad_user, r.is_pre_view, r.is_area, r.xt_status,r.ww_user_id FROM wr_link_view_record r INNER JOIN wr_ww_link l ON r.ww_link_id = l.id WHERE r.id IN ( SELECT link_view_record_id FROM wr_ww_user_add_record WHERE page_type = "audit" and admin_uid  =' . $input['admin_user_id'] . '  AND created_at >= "' . $input['date'] . ' 00:00:00" AND created_at <= "' . $input['date'] . ' 23:59:59") ';
			} else {
                if(!is_numeric($input['ww_user_id'])){
                    return $this->response()->alert()->error('提示')->detail('销售的ID必须为数字类型');
                }
				$sql = 'SELECT l.account_id,r.id,r.user_id,r.ww_link_id,r.need_shield,r.admin_uid,r.page_type,r.shield_reason,r.area,r.ip,r.accuracy,r.add_count, r.view_count, r.vc_count, r.is_black, r.is_white, r.is_deny_ip, r.is_mobile, r.is_wechat, r.is_ad_user, r.is_pre_view, r.is_area, r.xt_status,r.ww_user_id FROM wr_link_view_record r INNER JOIN wr_ww_link l ON r.ww_link_id = l.id WHERE r.id IN ( SELECT link_view_record_id FROM wr_ww_user_add_record WHERE  page_type = "audit" and ww_user_id  in (' . $input['ww_user_id'] . ' )  AND created_at >= "' . $input['date'] . ' 00:00:00" AND created_at <= "' . $input['date'] . ' 23:59:59") ';
			}
			$data   = DB::select($sql);
			if (empty($data)) {
				return $this->response()->error('没有查询到数据');
			}
			$export = [];
			foreach ($data as $datum) {
				$item     = [
					'账户ID'       => $datum->account_id,
					'访问记录'     => $datum->id,
					'链接ID'       => $datum->ww_link_id,
					'是否需要屏蔽' => ($datum->need_shield) ? "是" : "否",
					'管理员ID'     => $datum->admin_uid,
					'页面类型'     => ($datum->page_type == 'audit') ? "屏蔽" : "投放",
					'屏蔽原因'     => $datum->shield_reason,
					'地域'         => $datum->area,
					'IP'           => $datum->ip,
					'定位精确度'   => $datum->accuracy,
					'总添加次数'   => $datum->add_count,
					'本次访问次数' => $datum->view_count,
					'总访问次数'   => $datum->vc_count,
					'黑名单'       => $datum->is_black,
					'白名单'       => $datum->is_white,
					'风险IP'       => $datum->is_deny_ip,
					'手机访问'     => $datum->is_mobile,
					'微信访问'     => $datum->is_wechat,
					'广告用户'     => $datum->is_ad_user,
					'预览'         => $datum->is_pre_view,
					'是否屏蔽地域' => $datum->is_area,
					'XT检测'       => $datum->xt_status,
					'用户ID' => $datum->user_id,
					'销售ID' => $datum->ww_user_id,
				];
				$export[] = $item;
			}
			$disk = Storage::disk('oss');
			$file = "GetShieldReason/" . (empty($input['ww_user_id']) ? $input['admin_user_id'] : $input['ww_user_id']) . "-" . date("YmdHis") . '.xlsx';
			Excel::export($export)->disk($disk)->store($file);
			return $this
				->response()
				->success('获取成功')
				->download(env('CDN_URL') . '/' . $file);
		}

		/**
		 * Build a form here.
		 */
		public function form()
		{
			$this->select("admin_user_id", "管理员ID")->options(AdminUser::query()->pluck("username", "id"))->help("和销售ID二选一，如果都配置，会选择第一种");
			$this->text("ww_user_id", "销售ID")->help('请填写数字类型的ID：例如10086');

			// 日期选择器 判断边界bug  修复方案 强制在顶部弹出
			// public/vendor/dcat-admin/dcat/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.min.js
			$this->date('date', '天')->options([
				'widgetPositioning' => [    
					'vertical' => 'top'
				]
			])->required();
		}
	}
