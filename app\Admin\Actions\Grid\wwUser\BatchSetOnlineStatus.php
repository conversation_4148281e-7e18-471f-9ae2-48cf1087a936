<?php

namespace App\Admin\Actions\Grid\wwUser;

use App\Admin\Extensions\Grid\BatchActionPlus;
use App\Admin\Forms\WwUser\BatchSetOnlieStatusForm;

class BatchSetOnlineStatus extends BatchActionPlus
{
    /**
     * @return string
     */
    public $title = '批量上下线';

    public $buttonText = '<button class="btn btn-primary"><i class="fa fa-toggle-on"></i><span class="selected"></span>&nbsp;批量上下线</button>';

    public function form(): BatchSetOnlieStatusForm
    {
        
        $form = BatchSetOnlieStatusForm::make();
        // 表单渲染时
        return $form;
    }
}
