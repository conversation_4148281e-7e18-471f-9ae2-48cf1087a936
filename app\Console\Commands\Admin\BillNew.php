<?php

namespace App\Console\Commands\Admin;

use App\Models\AdminBillNew;
use App\Models\AdminUser;
use App\Models\LinkViewRecord;
use App\Models\WwLink;
use App\Models\WwUserAddRecord;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class Bill<PERSON>ew extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'Admin:BillNew';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';


    const ADMIN_IDS = [
    ];

    // 内部链接 + 开启屏蔽单价 0.5元
    const INTERNAL_SHIELD_UNIT_PRICE = 0.5;

    // 内部链接 + 关闭屏蔽单价 0.2元
    const INTERNAL_NO_SHIELD_UNIT_PRICE = 0.2;

    // 外部链接 + 开启屏蔽单价 0.5元
    const EXTERNAL_SHIELD_UNIT_PRICE = 0.05;

    // 外部链接 + 关闭屏蔽单价 0.2元
    const EXTERNAL_NO_SHIELD_UNIT_PRICE = 0.02;

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $deBugArr   = [];
        $adminData  = $this->getAdminData();
        $startDate  = strtotime(date("Y-m-d", time() - 86400 * 15));
        $today      = strtotime(date("Y-m-d", time()));
        $dates      = [];
        for ($d = $startDate; $d <= $today; $d += 86400) {
            $dates[] = date("Y-m-d", $d);
        }
        $totalSteps = count($dates) * count($adminData);
        $this->output->progressStart($totalSteps);

        foreach ($dates as $date) {
            foreach ($adminData as $adminDatum) {
                if (strtotime(date("Y-m-d", strtotime($adminDatum['created_at']))) > strtotime($date)) {
                    $this->output->progressAdvance();
                    continue;
                }
                $where     = [
                    'admin_uid' => $adminDatum['admin_uid'],
                    'date'      => $date
                ];
                $adminBill = AdminBillNew::query()->where($where)->first();
                if (!$adminBill) {
                    $adminBill            = new AdminBillNew();
                    $adminBill->admin_uid = $adminDatum['admin_uid'];
                    $adminBill->date      = $date;
                    $adminBill->add_price = 0;
                }

                // 计算广告费用
                $this->calculationAdPrice($adminBill, $adminDatum, $date);

                // 计算服务费
                $this->calculationServicePrice($adminBill, $adminDatum['sub_admin_uid'] ?? [], $date);

                $adminBill->save();
                $deBugArr[] = [
                    'admin_uid' => $adminBill->admin_uid,
                    'date'      => $adminBill->date,
                    'internal_shield_count' => $adminBill->internal_shield_count,
                    'internal_no_shield_count' => $adminBill->internal_no_shield_count,
                    'internal_shield_price' => $adminBill->internal_shield_price / 100,
                    'internal_no_shield_price' => $adminBill->internal_no_shield_price / 100,
                    'external_shield_count' => $adminBill->external_shield_count,
                    'external_no_shield_count' => $adminBill->external_no_shield_count,
                    'external_shield_price' => $adminBill->external_shield_price / 100,
                    'external_no_shield_price' => $adminBill->external_no_shield_price / 100
                ];
                $this->output->progressAdvance();
            }
        }
        $this->output->progressFinish();
        $this->info("账单数据统计完成");
        $this->table(
            ['admin_uid', '日期', '内部链接屏蔽数量', '内部链接非屏蔽数量', '内部链接屏蔽总价', '内部链接非屏蔽总价',
                '外部链接屏蔽数量', '外部链接非屏蔽数量', '外部链接屏蔽总价', '外部链接非屏蔽总价'],
            $deBugArr
        );
        return Command::SUCCESS;
    }

    protected function calculationAdPrice(AdminBillNew $adminBill, array $adminDatum, string $date): void
    {
        $noShieldWwLinks    = WwLink::withTrashed()
            ->whereIn("admin_uid", $adminDatum['sub_admin_uid'])
            ->where("need_shield", 0)
            ->select("id", "account_id")
            ->get();
        $noShieldAccountIds = [];
        if (!$noShieldWwLinks->isEmpty()) {
            //校验一下，是否只有非屏蔽的，如果有屏蔽的链接，那么从非屏蔽的里面剔除掉
            foreach ($noShieldWwLinks as $wwLink) {
                if (!WwLink::query()->where("account_id", $wwLink->account_id)->where("need_shield", 1)->exists()) {
                    $noShieldAccountIds[] = $wwLink->account_id;
                }
            }
        }

        //sum_ad_cost 查询名下所有的账户消耗，真实的应该是，非屏蔽的点击量*单价 + 屏蔽的账户总消耗*比率
        // 2024-03-23 修改为只查询使用屏蔽的账户消耗 -
        $vccData = DB::table("ad_account_vcc_daily")
            ->whereIn("admin_uid", $adminDatum['sub_admin_uid'])
            ->where("date", $date)
            ->whereNotIn("account_id", $noShieldAccountIds)
            ->select(
                DB::raw("sum(ad_cost) as sum_ad_cost"),
                DB::raw("sum(view_count) as sum_ad_view_count"),
                DB::raw("sum(valid_click_count) as sum_ad_valid_click_count"),
                DB::raw("sum(conversions_count) as sum_ad_conversions_count")
            )
            ->first();
        $adminBill->sum_ad_cost     = $vccData->sum_ad_cost ?? 0;
        $sumAllVccData              = DB::table("ad_account_vcc_daily")
            ->whereIn("admin_uid", $adminDatum['sub_admin_uid'])
            ->where("date", $date)
            ->select(DB::raw("sum(ad_cost) as sum_all_ad_cost"))
            ->first();
        $adminBill->sum_all_ad_cost = $sumAllVccData->sum_all_ad_cost ?? 0;
        //查询是否有配置，如果没有配置，按照如下计算
        $ad_price_rate = 0;
        if ($adminDatum['ad_price_rate'] > 0) {
            $ad_price_rate = $adminDatum['ad_price_rate'] / 100;
        }

        $adminBill->sum_ad_view_count        = $vccData->sum_ad_view_count ?? 0;
        $adminBill->sum_ad_valid_click_count = $vccData->sum_ad_valid_click_count ?? 0;
        $adminBill->sum_ad_conversions_count = $vccData->sum_ad_conversions_count ?? 0;
        $adminBill->ad_price_rate            = $ad_price_rate;
        $adminBill->ad_price                 = round($adminBill->sum_ad_cost * $ad_price_rate);
    }


    /**
     * 计算软件服务费
     *
     * 计费规则：
     * 1. 内部链接 & 开启屏蔽：每个进粉（ww_user_add_record表）0.5元
     * 2. 内部链接 & 关闭屏蔽：每个进粉（ww_user_add_record表）0.2元
     * 3. 外部链接 & 开启屏蔽：按访问量（link_view_record表）0.05元
     * 4. 外部链接 & 关闭屏蔽：按访问量（link_view_record表）0.02元
     *
     * @param AdminBillNew $adminBill 账单对象
     * @param array $subAdminUids
     * @param string $date
     * @return void
     */
    protected function calculationServicePrice(AdminBillNew &$adminBill, array $subAdminUids, string $date): void
    {
        $start = Carbon::parse($date)->startOfDay()->toDateTimeString();
        $end   = Carbon::parse($date)->endOfDay()->toDateTimeString();

        // 内部链接计费
        $this->fillBillData(
            $adminBill,
            WwUserAddRecord::query()
                ->whereIn('admin_uid', $subAdminUids)
                ->where('date', $date)
                ->where('tpl_type', 1),
            'internal',
            self::INTERNAL_SHIELD_UNIT_PRICE * 100,
            self::INTERNAL_NO_SHIELD_UNIT_PRICE * 100
        );

        // 外部链接计费
        $this->fillBillData(
            $adminBill,
            LinkViewRecord::query()
                ->whereIn('admin_uid', $subAdminUids)
                ->whereBetween('created_at', [$start, $end])
                ->where('tpl_type', 2),
            'external',
            self::EXTERNAL_SHIELD_UNIT_PRICE * 100,
            self::EXTERNAL_NO_SHIELD_UNIT_PRICE * 100
        );
    }

    /**
     * 获取管理员数据
     *
     * @return array
     */
    protected function getAdminData(): array
    {
        /** @var AdminUser[] $adminUsers */
        $adminUsers = AdminUser::query()
            //  自定义 IDs
            ->when(count(self::ADMIN_IDS) > 0, function ($query) {
                return $query->whereIn("id", self::ADMIN_IDS);
            })
            ->where("parent_id", "<>", 1)
            ->get();
        $adminData = [];
        foreach ($adminUsers as $adminUser) {
            if (!$adminUser->parent_id) {
                $adminData[$adminUser->id]['admin_uid']            = $adminUser->id;
                $adminData[$adminUser->id]['ad_price_rate']        = $adminUser->ad_price_rate;
                $adminData[$adminUser->id]['created_at']           = $adminUser->created_at->toDatetimeString();
                $adminData[$adminUser->id]['username']             = $adminUser->username;
                $adminData[$adminUser->id]['sub_admin_uid'][]      = $adminUser->id;
                $adminData[$adminUser->id]['sub_admin_username'][] = $adminUser->username;
            } else {
                $adminData[$adminUser->parent_id]['sub_admin_uid'][]      = $adminUser->id;
                $adminData[$adminUser->parent_id]['sub_admin_username'][] = $adminUser->username;
            }
        }

        return $adminData;
    }

    /**
     * 统一填充账单数据
     */
    private function fillBillData(
        AdminBillNew &$adminBill,
                     $query,
        string $type,
        float $shieldUnitPrice,
        float $noShieldUnitPrice
    ): void {
        $selectRaw = sprintf(
            "need_shield, COUNT(*) as add_count, CASE need_shield WHEN 1 THEN COUNT(*) * %f ELSE COUNT(*) * %f END as total_price",
            $shieldUnitPrice,
            $noShieldUnitPrice
        );
        $dataList = $query->selectRaw($selectRaw)->groupBy('need_shield')->get();

        // 默认值
        $adminBill->{$type . '_shield_unit_price'} = $shieldUnitPrice;
        $adminBill->{$type . '_no_shield_unit_price'} = $noShieldUnitPrice;
        $adminBill->{$type . '_shield_count'} = 0;
        $adminBill->{$type . '_no_shield_count'} = 0;
        $adminBill->{$type . '_shield_price'} = 0;
        $adminBill->{$type . '_no_shield_price'} = 0;
        foreach ($dataList as $data) {
            if ($data->need_shield == 1) {
                $adminBill->{$type . '_shield_count'} = (int)$data->add_count;
                $adminBill->{$type . '_shield_price'} = (float)$data->total_price;
            } else {
                $adminBill->{$type . '_no_shield_count'} = (int)$data->add_count;
                $adminBill->{$type . '_no_shield_price'} = (float)$data->total_price;
            }
        }
    }
}
