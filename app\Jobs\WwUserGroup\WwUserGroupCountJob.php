<?php

namespace App\Jobs\WwUserGroup;

use App\Models\WwUser;
use App\Models\WwUsersGroupsCount;
use App\Models\WwUsersGroupsRel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;


class WwUserGroupCountJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $group;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($group)
    {
        $this->group = $group;
    }

    public function middleware()
    {
        $groupId = $this->group->id ?? 0;
        if (empty($groupId)) {
            return [];
        }
        return [(new WithoutOverlapping($groupId))->releaseAfter(10)->expireAfter(15)];
    }


    /**
     * Execute the job.
     *
     * @return mixed
     */
    public function handle()
    {
        $allOnlineUserIds = WwUser::query()
            ->where('online_status', 1)
            ->whereNull('deleted_at')
            ->pluck('id')
            ->toArray();
        $group = $this->group;
        try {
            $wwUserIds = WwUsersGroupsRel::query()->where('ww_group_id', $group->id)->pluck('ww_user_id')->toArray();
            $wwUserIds = WwUser::query()->whereIn('id', $wwUserIds)->whereNull('deleted_at')->pluck('id')->toArray();

            $onlineWwUsers = array_intersect($wwUserIds, $allOnlineUserIds);
            $group->ww_users_count = count($wwUserIds);
            $group->ww_users_online_count = count(array_values($onlineWwUsers));
            $group->ww_users_list = json_encode($wwUserIds);
            $group->save();
            $countList = [
                'ww_group_id' => $group->id,
                'ww_users' => json_encode($wwUserIds),
                'online_ww_users' => json_encode(array_values($onlineWwUsers)),
                'created_at' => now(),
                'updated_at' => now()
            ];
            if (!empty($countList)) {
                WwUsersGroupsCount::query()->insert($countList);
            }
            return true;
        } catch (\Exception $exception) {
            Log::error('统计销售分组销售数据队列执行失败：' . $exception->getMessage());
        }

        return true;
    }


}
