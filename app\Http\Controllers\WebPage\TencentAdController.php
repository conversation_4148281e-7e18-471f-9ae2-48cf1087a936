<?php

    namespace App\Http\Controllers\WebPage;

    use App\Models\AdminUser;
    use App\Models\Area;
    use App\Models\LinkClickRecord;
    use App\Models\LinkViewRecord;
    use App\Models\ShieldIp;
    use App\Models\ShieldPolicy;
    use App\Models\WechatUser;
    use App\Models\WhiteUserList;
    use App\Models\WwLink;
    use App\Models\WwUser;
    use App\Services\CacheService;
    use App\Services\Location\IpLocation;
    use App\Services\NotifySendService;
    use App\Services\System\SpeedLogService;
    use Illuminate\Http\Request;
    use Illuminate\Support\Facades\Cache;
    use Illuminate\Support\Facades\Log;
    use Illuminate\Support\Facades\Redirect;

    class TencentAdController extends Controller
    {
        public function shieldCheck(Request $request)
        {
            // Log::info("SC-".request()->headers->get('referer'));
            SpeedLogService::info( "00008");// 性能日志，单独做记录使用
            echo "<script>
        function onBridgeReady() {
            WeixinJSBridge.call('hideOptionMenu');
        }

        if (typeof WeixinJSBridge == 'undefined') {
            if (document.addEventListener) {
                document.addEventListener('WeixinJSBridgeReady', onBridgeReady, false);
            } else if (document.attachEvent) {
                document.attachEvent('WeixinJSBridgeReady', onBridgeReady);
                document.attachEvent('onWeixinJSBridgeReady', onBridgeReady);
            }
        } else {
            onBridgeReady();
        }
    </script>";
            SpeedLogService::info( "00009");// 性能日志，单独做记录使用
            // 查询页面是否存在
            $linkAdId = $request->get("adt", "");
            if (empty($linkAdId)) {
                return view('errors.system-error', [
                    'message' => '系统错误-1'
                ]);
            }
            /** @var WwLink $wwLink */
            $wwLink = WwLink::query()->where("link_ad_id", $linkAdId)->first();
            if (!$wwLink) {
                return view('errors.system-error', [
                    'message' => '系统错误-2'
                ]);
            }

            $uuid = $request->get("uuid");
            if (empty($uuid)) {
                return view('errors.system-error', [
                    'message' => '系统错误-4'
                ]);
            }
            /** @var WechatUser $wechatUser */
            $wechatUser = WechatUser::query()->where("md5_id", $uuid)->first();

            if (!$wechatUser) {
                return view('errors.system-error', [
                    'message' => '系统错误-5'
                ]);
            }
            SpeedLogService::info( "00010");// 性能日志，单独做记录使用
            // 获取广告跟踪ID
            $clickId = $this->getClickId($request, $wwLink);

            // 新建访问记录
            $where = [
                'user_id' => $wechatUser->id,
                'ww_link_id' => $wwLink->id,
            ];
            /** @var LinkClickRecord $linkClickRecord */
            $linkClickRecord = LinkClickRecord::query()->where($where)->first();
            SpeedLogService::info( "00011");// 性能日志，单独做记录使用
            if (!$linkClickRecord) {
                $linkClickRecord = new LinkClickRecord();
                $linkClickRecord->user_id = $wechatUser->id;// 用户ID
                $linkClickRecord->ww_link_id = $wwLink->id; // 投放链接ID
                $linkClickRecord->view_count = 0;
                $linkClickRecord->account_id = $wwLink->account_id;// 腾讯广告广告账户ID
                $linkClickRecord->admin_uid = $wwLink->admin_uid;  // 后台用户ID
                $linkClickRecord->save();
                $wechatUser->vc_count++; // 访问次数
                $wechatUser->save();
            }
            $linkClickRecord->view_count++;
            $linkClickRecord->save();
            SpeedLogService::info( "00012");// 性能日志，单独做记录使用
            // 记录每次进来的单独日志
            $linkViewRecord = new LinkViewRecord();
            $linkViewRecord->link_click_record_id = $linkClickRecord->id;// 用户UV_ID 也就是上面第70行的$linkClickRecord的主键ID
            $linkViewRecord->user_id = $wechatUser->id;                  // 用户ID
            $linkViewRecord->ww_link_id = $wwLink->id;                   // 投放链接ID
            $linkViewRecord->admin_uid = $wwLink->admin_uid;             // 创建投放链接得后台用户admin的ID
            $linkViewRecord->need_shield = $wwLink->need_shield;         //  是否需要屏蔽 0否 1是
            $linkViewRecord->tpl_type = $wwLink->tpl_type;               //1 智投页面 2 外部链接
            $linkViewRecord->click_id = $clickId;                        // 广告跟踪ID
            $linkViewRecord->ua = $_SERVER['HTTP_USER_AGENT'] ?? "";
            $linkViewRecord->referer = $_SERVER['HTTP_REFERER'] ?? "";
            $linkViewRecord->other = json_encode($request->all(), JSON_UNESCAPED_UNICODE);
            $linkViewRecord->ip = getIp();


            $data = IpLocation::get($linkViewRecord->ip);// 进行ip定位

            $linkViewRecord->accuracy = $data['accuracy'] ?? ""; // IP定位精度
            $linkViewRecord->owner = $data['owner'] ?? "";       //
            $linkViewRecord->isp = $data['isp'] ?? "";
            $linkViewRecord->prov = $data['prov'] ?? "";        // 省
            $linkViewRecord->city = $data['city'] ?? "";        // 市
            $linkViewRecord->district = $data['district'] ?? "";// 区、县
            if (empty($linkViewRecord->prov)) {
                $linkViewRecord->area = $linkViewRecord->district;
            } else {
                $linkViewRecord->area = implode("", [
                    $linkViewRecord->prov, $linkViewRecord->city
                ]);
            }
            $linkViewRecord->add_count = $wechatUser->add_count;
            $linkViewRecord->vc_count = $wechatUser->vc_count;

            $linkViewRecord->save();
            SpeedLogService::info( "00013");// 性能日志，单独做记录使用
            // 检测是否开启屏蔽，如果未开启，直接跳转到投放页面
            if (!$wwLink->need_shield) {
                // 跳转到投放页面
                $linkViewRecord->page_type = 'page';
                $linkViewRecord->save();
                return $this->toAdPage($wwLink, $linkViewRecord, $wechatUser);
            }

            // 需要屏蔽，进行校验，先获取校验规则
            $shieldPolicy = $wwLink->shieldPolicy;
            if (!$shieldPolicy) {
                return view('errors.system-error', [
                    'message' => '系统错误-8'
                ]);
            }

            // 系统默认检测 - xToken检测
            $linkViewRecord->xt_status = 1;          // xt状态-1 正常 2超次 3超时 4不存在
            $xt = $request->get("xt", "");
            if (empty($xt)) {
                $errMessage = makeErrorLog('[屏蔽提醒][xToken][不存在]', ['view_record' => $linkViewRecord]);
                NotifySendService::sendWorkWeixinForError("[屏蔽提醒][xToken][不存在]" . $errMessage);

                $wechatUser->is_black = 11;
                $wechatUser->save();

                ShieldIp::create(1,$linkViewRecord->ip,'无 XT 访问');

                $linkViewRecord->xt_status = 4;
            }
            $xtToken = Cache::store('redis')->get($xt, '-100');

            if ($xtToken >= 3) {
                $linkViewRecord->xt_status = 2;                                                  // 超次
                return $this->toAuditPage($wwLink, $shieldPolicy, $linkViewRecord, $wechatUser); // 一个链接只允许使用三次
            }
            SpeedLogService::info( "00014");// 性能日志，单独做记录使用
            // 初始值有可能是0
            if ($xtToken == -100) {
                if (isset($_GET['xt'])) {
                    unset($_GET['xt']);
                }
                $linkViewRecord->xt_status = 3;
            } else {
                Cache::store('redis')->set($xt, ++$xtToken, 600);
            }
            SpeedLogService::info( "00015");// 性能日志，单独做记录使用
            // 检测是否开启开关 如果is_black > 0 则都是黑名单用户
            $isOpen = $wwLink->is_open;
            if (!$isOpen) {
                $wechatUser->is_black = 4;
                $wechatUser->save();
            }

            // 判断直接点击链接的用户，参数小于或等于3的人
            if (count($request->all()) <= 3) {
                $wechatUser->is_black = 19;
                $wechatUser->save();

                ShieldIp::create(1,$linkViewRecord->ip,'参数小于或等于3');
            }
            // 判断广告环境
            $adEnvStatus = $this->checkAdEnv($request, $clickId, $wwLink);
            $linkViewRecord->is_ad_user = $adEnvStatus;
            if ($adEnvStatus == 2) { // 表示预览
                $wechatUser->is_black = 2;
                $wechatUser->save();

                ShieldIp::create(1,$linkViewRecord->ip,'预览');

                $linkViewRecord->is_pre_view = 1;
            } else {
                $linkViewRecord->is_pre_view = 0;
            }

            //判断是否是专门狙击使用的
            $bState = $request->get("ad_type",'');
            if(!empty($bState)){
                $wechatUser->is_black = 3;
                $wechatUser->save();

                ShieldIp::create(1,$linkViewRecord->ip,'ad_type');

                $linkViewRecord->is_pre_view = 3;
            }
            // 判断设备环境
            $linkViewRecord->is_mobile = (int)self::isMobile();
            $linkViewRecord->is_wechat = (int)self::isWechat();
            SpeedLogService::info( "00016");// 性能日志，单独做记录使用
            // 判断IP是否风险,0无风险，1自有文件风险IP 2自有数据库风险IP 3自己拉黑的IP 4共享IP数据库
            $isDenyIp = (int)self::isDenyIp($linkViewRecord->ip);
            if (!$isDenyIp) {// 2自有数据库风险IP
                $shieldIps = ShieldIp::query()->where("admin_uid", 1)->where("ip", $linkViewRecord->ip)->first();
                if ($shieldIps) {
                    $isDenyIp = 2;
                }
            }
            if (!$isDenyIp) {// 3自己拉黑的IP
                $adminUids = [$wwLink->admin_uid];
                if ($wwLink->adminInfo) {
                    $adminUids[] = $wwLink->adminInfo->parent_id;
                }
                $shieldIps = ShieldIp::query()->whereIn("admin_uid", $adminUids)->where("ip", $linkViewRecord->ip)->first();
                if ($shieldIps) {
                    $isDenyIp = 3;
                }
            }
            if (!$isDenyIp) {// 4共享IP数据库
                $shieldIps = ShieldIp::query()->where("ip", $linkViewRecord->ip)->first();
                if ($shieldIps) {
                    $isDenyIp = 4;
                }
            }
            if(in_array($isDenyIp,[1,2]) && $wechatUser->is_black == 0){
                // 如果是屏蔽 IP 进入的，自动拉黑
                $wechatUser->is_black = 23;
                $wechatUser->save();
            }
            $linkViewRecord->is_deny_ip = $isDenyIp;
            SpeedLogService::info( "00017");// 性能日志，单独做记录使用
            // 判断是否屏蔽地域
            $linkViewRecord->is_area = 0;
            $blackArea = json_decode($shieldPolicy->block_area, true);
            if (!empty($blackArea)) {
                $areaList = Area::query()->whereIn("id", $blackArea)->pluck("name");
                foreach ($areaList as $area) {
                    if (str_contains($linkViewRecord->area, $area)) {
                        $linkViewRecord->is_area = 1;
                    }
                }
            }
            SpeedLogService::info( "00018");// 性能日志，单独做记录使用
            // 单独狙击的用户
            if (isset($param['ad_type']) && $param['ad_type'] == 'add') {
                // 如果是屏蔽链接进入的，将这个人的信息加入黑名单
                $wechatUser->is_black = 3;
                $wechatUser->save();

                ShieldIp::create(1,$linkViewRecord->ip,'ad_type');
            }

            $linkViewRecord->is_black = $wechatUser->is_black;

            // 暂时关闭 IP 传染的黑名单用户
            $adminUidsLists = CacheService::getIpInfectData();
            if(!empty($adminUidsLists)){
                if(in_array($linkViewRecord->admin_uid,$adminUidsLists)){
                    if ($wechatUser->is_black === 23) {
                        $linkViewRecord->is_black = 0;
                    }
                }
            }

            // 白名单 1是 0否
            if (WhiteUserList::getIsWhite($linkViewRecord->admin_uid, $wechatUser->openid, getIp(), $wwLink->media_type)) {
                $linkViewRecord->is_white = 1;
            } else {
                $linkViewRecord->is_white = 0;
            }


            $linkViewRecord->view_count = $linkClickRecord->view_count;
            $linkViewRecord->save();

            $linkViewRecord->page_type = 'page';
            $linkViewRecord->shield_reason = '';

            if ($linkViewRecord->xt_status !== 1) {
                $linkViewRecord->page_type = 'audit';
                $linkViewRecord->shield_reason = 'xt';
            }

            if ($linkViewRecord->is_area) {
                $linkViewRecord->page_type = 'audit';
                $linkViewRecord->shield_reason = 'area';
            }

            if ($shieldPolicy->ad_env && !$linkViewRecord->is_ad_user) {
                $linkViewRecord->page_type = 'audit';
                $linkViewRecord->shield_reason = 'ad_env';
            }
            if ($shieldPolicy->view_count > 0 && $shieldPolicy->view_count < $linkViewRecord->view_count) {
                $linkViewRecord->page_type = 'audit';
                $linkViewRecord->shield_reason = 'view_count';
            }
            // 判断IP是否风险,0无风险，1自有文件风险IP 2自有数据库风险IP 3自己拉黑的IP 4共享IP数据库
            if ($shieldPolicy->default_block_ip && in_array($linkViewRecord->is_deny_ip, [1, 2, 3])) {
                $linkViewRecord->page_type = 'audit';
                $linkViewRecord->shield_reason = 'default_block_ip';
            }
            if ($shieldPolicy->share_block_ip && $linkViewRecord->is_deny_ip == 4) {
                $linkViewRecord->page_type = 'audit';
                $linkViewRecord->shield_reason = 'share_block_ip';
            }
            // 正数是系统判断的黑名单
            if ($shieldPolicy->default_block_user && ($linkViewRecord->is_black > 0 || $linkViewRecord->is_black == -$wwLink->admin_uid)) {
                $linkViewRecord->page_type = 'audit';
                $linkViewRecord->shield_reason = 'default_block_user';
            }
            // 负数是客户自己拉黑的黑名单
            if ($shieldPolicy->share_block_user && $linkViewRecord->is_black < 0) {
                $linkViewRecord->page_type = 'audit';
                $linkViewRecord->shield_reason = 'share_block_user';
            }
            if ($shieldPolicy->is_mobile && !$linkViewRecord->is_mobile) {
                $linkViewRecord->page_type = 'audit';
                $linkViewRecord->shield_reason = 'is_mobile';
                // 如果不是手机访问的，直接把这个人拉黑掉
                $wechatUser->is_black = 24;
                $wechatUser->save();

                //如果不是手机访问，还在给他的 IP 拉黑
                ShieldIp::create(1,$linkViewRecord->ip,'非手机访问');
            }
            if ($shieldPolicy->add_count > 0 && $shieldPolicy->add_count < $linkViewRecord->add_count) {
                $linkViewRecord->page_type = 'audit';
                $linkViewRecord->shield_reason = 'add_count';
            }
            if ($shieldPolicy->vc_count > 0 && $shieldPolicy->vc_count < $linkViewRecord->vc_count) {
                $linkViewRecord->page_type = 'audit';
                $linkViewRecord->shield_reason = 'vc_count';
            }
            if ($linkViewRecord->is_white === 1 && $isOpen) {
                $linkViewRecord->page_type = 'page';
                $linkViewRecord->shield_reason = 'white_user';
            }
            SpeedLogService::info( "00019");// 性能日志，单独做记录使用
            $linkViewRecord->save();
            SpeedLogService::info( "00020");// 性能日志，单独做记录使用
            // 检查页面类型是否为审核页面
            if ($linkViewRecord->page_type == 'audit') {
//                SpeedLogService::info("00021");//性能日志，单独做记录使用
                // 如果是审核页面，调用 toAuditPage 方法跳转到审核页面
                return $this->toAuditPage($wwLink, $shieldPolicy, $linkViewRecord, $wechatUser);
            } else {
//                SpeedLogService::info("00022");//性能日志，单独做记录使用
                // 如果不是审核页面，调用 toAdPage 方法跳转到广告页面
                return $this->toAdPage($wwLink, $linkViewRecord, $wechatUser);
            }

        }

        public function getClickId(Request $request, WwLink $wwLink)
        {
            $param = $request->all();
            $clickId = '';
            switch ($wwLink->media_type) {
                case "腾讯广告":
                    // 获取广告ID
                    $clickId = $param['trace_id'] ?? "";
                    if (empty($clickId)) {
                        $clickId = $param['gdt_vid'] ?? "";
                    }
                    if (empty($clickId)) {
                        $clickId = $param['qz_gdt'] ?? "";
                    }
                    if (empty($clickId)) {
                        $clickId = $param['click_id'] ?? "";
                    }
                    break;
                case '巨量广告':
                    $clickId = $param['clickid'] ?? "";
                    if($clickId == '__CLICK_ID__'){
                        $clickId = '';
                    }
                    break;
                case '华为广告':
                case '快手广告':
                    $clickId = $param['callback'] ?? "";
                    break;
                case '爱奇艺广告':
                    $clickId = $param['impress_id'] ?? "";
                    break;
                case 'OPPO广告':
//                    Log::info('OPPO广告获取链接携带参数：' . json_encode($param));
                    $tid = rawurlencode($request->input('tid'));
//                    Log::info('OPPO广告获取链接携带tid：' . $tid . ' ，投放链接ID：' . $wwLink);
                    if(empty($tid) || empty($param['pageId']) || empty($param['lbid'])){
                        $clickId = '';
                    }else{
                        $clickId = [
                            'tid'    => $tid,
                            'pageId' => $param['pageId'] ?? '',
                            'lbid'   => $param['lbid'] ?? '',
                        ];
                        $clickId = json_encode($clickId);
                    }
                    break;
                case 'VIVO广告':
//                    Log::info('VIVO广告投放链接ID：' . $wwLink->id  . '，VIVO广告获取页面参数' . json_encode($param));
                    if(empty($param['requestid']) || empty($param['adid'])){ //adid 其实就是creativeId
                        $clickId = '';
                    }else{
                        $clickId = [
                            'requestId'  => $param['requestid'],
                            'creativeId' => $param['adid'],
                        ];
                        $clickId = json_encode($clickId,JSON_UNESCAPED_UNICODE);
                    }
                    break;
                case '优酷广告':
//                    Log::info('优酷广告-投放链接ID：' . $wwLink->id  . '，优酷广告获取页面参数' . json_encode($param));
                    if(empty($param['trackid']) || empty($param['creativeid'])){
                        $clickId = '';
                    }else{
                        $clickId = [
                            'trackid'  => $param['trackid'],
                            'creativeid' => $param['creativeid'],
                        ];
                        $clickId = json_encode($clickId,JSON_UNESCAPED_UNICODE);
                    }
                    break;
                case '喜马拉雅广告':
                    Log::info('喜马拉雅广告-投放链接ID：' . $wwLink->id  . '，喜马拉雅落地页页参数' . json_encode($param));
                    if(empty($param['callback'])){
                        $clickId = '';
                    }else{
                        $clickId = $param['callback'];
                    }
                    break;
            }
            return $clickId;
        }


        /**
         * 跳转到投放页
         * @param WwLink $wwLink
         * @param LinkViewRecord $linkViewRecord
         * @param WechatUser $wechatUser
         * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|\Illuminate\Http\RedirectResponse|void
         */
        public function toAdPage(WwLink $wwLink, LinkViewRecord $linkViewRecord, WechatUser $wechatUser)
        {
            if ($wwLink->tpl_type == 2) {
                if ($wwLink->tpl_link) {
                    if (str_contains($wwLink->tpl_link, "http")) {
                        $toUrl = $wwLink->tpl_link;
                    } else {
                        $toUrl = "https://" . $wwLink->tpl_link;
                    }
                    if (str_contains($toUrl, "?")) {
                        $toUrl .= "&" . http_build_query($_GET);
                    } else {
                        $toUrl .= "?" . http_build_query($_GET);
                    }
                    return Redirect::to($toUrl);

                } else {
                    return view('errors.system-error', [
                        'message' => '系统错误-82'
                    ]);// 未配置投放链接
                }
            }
            SpeedLogService::info("00026");//性能日志，单独做记录使用
            if (!$wwLink->tplInfo || !$wwLink->tplInfo->view_file) {
                return view('errors.system-error', [
                    'message' => '系统错误-3'
                ]);
            }
            // 寻找销售
            $wwUserData = WwUser::getUserFromGroup($wwLink, $wwLink->ww_user_group_id, $linkViewRecord);
            SpeedLogService::info("00027");//性能日志，单独做记录使用
            if (!$wwUserData) {
                $errMessage = makeErrorLog('[广告页面][投放销售][不存在] ' . "「" . $wwLink->adminInfo->username . "」用户的「" . $wwLink->id . "」链接，账户ID「" . $wwLink->account_id . "」，投放未配置销售，投放分组是「" . (($wwLink->wwUserGroup)?$wwLink->wwUserGroup->title:$wwLink->ww_user_group_id) . "」", ['view_record_id' => $linkViewRecord->id]);

                $adminUser = AdminUser::query()->where("id", $wwLink->admin_uid)->first();
                $wwUserGroupTitle = $wwLink->wwUserGroup->title ?? $wwLink->ww_user_group_id;
                $robotsMessage = '[广告页面][投放销售][不存在] ' . "「" . $wwLink->adminInfo->username . "」用户的「" . $wwLink->id . "」链接，账户ID「" . $wwLink->account_id . "」，投放未配置销售，投放分组是「" . $wwUserGroupTitle . "」";

                //河南界凡 投放分组未配置销售，不发送机器人消息
                if($wwLink && $wwLink->ww_user_group_id && $wwLink->ww_user_group_id != 1966){
                    NotifySendService::sendCustomerForMessageByRobots($adminUser, $robotsMessage);
                    NotifySendService::sendWorkWeixinForError("[广告页面][投放销售][不存在] " . "「" . $wwLink->adminInfo->username . "」用户的「" . $wwLink->id . "」链接，账户ID「" . $wwLink->account_id . "」，投放未配置销售，投放分组是「" . (($wwLink->wwUserGroup)?$wwLink->wwUserGroup->title:$wwLink->ww_user_group_id) . "」" . $errMessage);
                }
                $wwUserData = [
                    'add_method' => '',
                    'id' => '',
                    'ww_user_name' => '',
                    'qrcode_id' => '',
                    'qrcode_url' => '',
                    'cus_acq_link' => '',
                    'state' => '',
//                    'vid' => -1,
                    'vid' => $linkViewRecord->id ?? -1,
                    'corp_id' => 0,
                    'corp_auth_id' => 0,
                    'admin_uid' => 0,
                ];
            } else {
                if (empty($wwUserData['qrcode_url']) && empty($wwUserData['cus_acq_link'])) {
                    $errMessage = makeErrorLog('[广告页面][投放销售][无添加方式] ' . "「" . $wwLink->adminInfo->username . "」用户的「" . $wwLink->id . "」链接，账户ID「" . $wwLink->account_id . "」，投放销售无添加方式，投放分组是「" . $wwLink->wwUserGroup->title . "」", ['view_record_id' => $linkViewRecord->id]);
                    NotifySendService::sendWorkWeixinForError("[广告页面][投放销售][无添加方式] " . "「" . $wwLink->adminInfo->username . "」用户的「" . $wwLink->id . "」链接，账户ID「" . $wwLink->account_id . "」，投放销售无添加方式，投放分组是「" . $wwLink->wwUserGroup->title . "」" . $errMessage);
                }
                $linkViewRecord->ww_user_id = $wwUserData['id'];
                $linkViewRecord->ww_user_group_id = $wwLink->ww_user_group_id;
                $linkViewRecord->ww_user_name = $wwUserData['ww_user_name'];
                $linkViewRecord->qrcode_id = $wwUserData['qrcode_id'];
                $linkViewRecord->state = $wwUserData['state'];

                //2025-05-29 新增corp_id 和 corp_auth_id
                $linkViewRecord->corp_id = $wwUserData['corp_id'] ?? 0;  // 企微ID
                $linkViewRecord->corp_auth_id = $wwUserData['corp_auth_id'] ?? 0;  // 企微授权ID

                $linkViewRecord->save();
            }
//            dd($wwLink->tplInfo->view_file);
            SpeedLogService::info("00028");//性能日志，单独做记录使用
            return view($wwLink->tplInfo->view_file, $wwUserData);
        }

        /**
         * 跳转屏蔽页
         * @param WwLink $wwLink
         * @param ShieldPolicy $shieldPolicy
         * @param LinkViewRecord $linkViewRecord
         * @param WechatUser $wechatUser
         * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|\Illuminate\Http\RedirectResponse|void
         */
        public function toAuditPage(WwLink $wwLink, ShieldPolicy $shieldPolicy, LinkViewRecord $linkViewRecord, WechatUser $wechatUser)
        {
            try {
                //配置跳转404页面
                $blackAreas = WwLink::getLinkAdminBlackAreas($wwLink->admin_uid);
                if(!empty($blackAreas)){
//                    foreach ($blackAreas as $area) {
//                        if (str_contains($linkViewRecord->area, $area)) {
//                            // 如果是黑名单区域，直接跳转到404页面
//                            return Redirect::to("https://404.vleads.cn/" . md5(time()));
//                        }
//                    }
                    $pattern = '/(' . implode('|', array_map('preg_quote', $blackAreas)) . ')/';

                    if (preg_match($pattern, $linkViewRecord->area)) {
                        return Redirect::to("http://404.vleads.cn/" . md5(time()));
                    }
                }


            } catch (\Exception $exception) {

            }
            SpeedLogService::info("00023");//性能日志，单独做记录使用
            if (!$shieldPolicy->wwTpl || !$shieldPolicy->wwTpl->view_file) {
                return view('errors.system-error', [
                    'message' => '系统错误-7'
                ]);
            }
            // 寻找销售，查看该用户是否有展示过销售，如展示过，仅展示当前
            $wwUserData = WwUser::getShieldUserFromGroup($wwLink, $shieldPolicy->ww_user_group_id,$linkViewRecord);
            SpeedLogService::info("00024");//性能日志，单独做记录使用
            if (!$wwUserData) {
                $errMessage = makeErrorLog('[广告页面][屏蔽销售][不存在] ' . "「" . $wwLink->adminInfo->username . "」用户的「" . $wwLink->id . "」链接，账户ID「" . $wwLink->account_id . "」，屏蔽页面未配置销售，屏蔽策略是「" . $wwLink->shieldPolicy->name . "」,屏蔽分组ID是「" . $wwLink->shieldPolicy->wwUserGroup->id . "」,屏蔽分组名称是「" . $wwLink->shieldPolicy->wwUserGroup->title . "」", ['view_record_id' => $linkViewRecord->id]);
                NotifySendService::sendWorkWeixinForError("[广告页面][屏蔽销售][不存在] " . "「" . $wwLink->adminInfo->username . "」用户的「" . $wwLink->id . "」链接，账户ID「" . $wwLink->account_id . "」，屏蔽页面未配置销售，屏蔽策略是「" . $wwLink->shieldPolicy->name . "」,屏蔽分组ID是「" . $wwLink->shieldPolicy->wwUserGroup->id . "」,屏蔽分组名称是「" . $wwLink->shieldPolicy->wwUserGroup->title . "」" . $errMessage);
                $adminUser = AdminUser::query()->where("id", $wwLink->admin_uid)->first();
                $robotsMessage = '[广告页面][屏蔽销售][不存在] ' . "「" . $wwLink->adminInfo->username . "」用户的「" . $wwLink->id . "」链接，账户ID「" . $wwLink->account_id . "」，屏蔽页面未配置销售，屏蔽策略是「" . $wwLink->shieldPolicy->name . "」,屏蔽分组ID是「" . $wwLink->shieldPolicy->wwUserGroup->id . "」,屏蔽分组名称是「" . $wwLink->shieldPolicy->wwUserGroup->title . "」";

                //此处配置 屏蔽分组未配置销售，不发送机器人消息
                if($wwLink->shieldPolicy && $wwLink->shieldPolicy->wwUserGroup && $wwLink->shieldPolicy->wwUserGroup->id != 3729){
                    NotifySendService::sendCustomerForMessageByRobots($adminUser, $robotsMessage);
                }

                $wwUserData = [
                    'add_method' => '',
                    'id' => '',
                    'ww_user_name' => '',
                    'qrcode_id' => '',
                    'qrcode_url' => '',
                    'cus_acq_link' => '',
                    'state' => '',
//                    'vid' => -1,
                    'vid' => $linkViewRecord->id ?? -2,
                    'admin_uid' => 0,
                ];
            } else {
                if (empty($wwUserData['qrcode_url']) && empty($wwUserData['cus_acq_link'])) {
                    $errMessage = makeErrorLog('[广告页面][屏蔽销售][无添加方式] ' . "「" . $wwLink->adminInfo->username . "」用户的「" . $wwLink->id . "」链接，账户ID「" . $wwLink->account_id . "」，屏蔽页面销售无有效添加方式，屏蔽策略是「" . $wwLink->shieldPolicy->name . "」,屏蔽分组是「" . $wwLink->shieldPolicy->wwUserGroup->title . "」", ['view_record_id' => $linkViewRecord->id]);
                    NotifySendService::sendWorkWeixinForError("[广告页面][屏蔽销售][无添加方式] " . "「" . $wwLink->adminInfo->username . "」用户的「" . $wwLink->id . "」链接，账户ID「" . $wwLink->account_id . "」，屏蔽页面销售无有效添加方式，屏蔽策略是「" . $wwLink->shieldPolicy->name . "」,屏蔽分组是「" . $wwLink->shieldPolicy->wwUserGroup->title . "」" . $errMessage);
                }
                $linkViewRecord->ww_user_id = $wwUserData['id'];
                $linkViewRecord->ww_user_group_id = $shieldPolicy->ww_user_group_id;
                $linkViewRecord->ww_user_name = $wwUserData['ww_user_name'];
                $linkViewRecord->qrcode_id = $wwUserData['qrcode_id'];
                $linkViewRecord->state = $wwUserData['state'];

                $linkViewRecord->corp_id = $wwUserData['corp_id'] ?? 0;  // 企微ID
                $linkViewRecord->corp_auth_id = $wwUserData['corp_auth_id'] ?? 0;  // 企微授权ID

                $linkViewRecord->save();
            }
            SpeedLogService::info("00025");//性能日志，单独做记录使用
            return view($shieldPolicy->wwTpl->view_file, $wwUserData);
        }

        private function checkAdEnv(Request $request, mixed $clickId, WwLink $wwLink): int
        {
            // 校验是否广告用户
            if (!empty($clickId)) {
                $get = http_build_query($request->all());
                if (str_contains($get, "1888888887")) {
                    return 2;// 2 表示预览
                }
                return 1;// 1 标识正常广告用户
            } else {
                return 0;// 0 标识不是广告用户
            }
        }
    }
