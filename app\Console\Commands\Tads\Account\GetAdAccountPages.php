<?php

namespace App\Console\Commands\Tads\Account;

use App\Models\TencentAdAccount;
use App\Models\AdAccountPage;
use App\Services\TenCentAd\OauthAccountService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class GetAdAccountPages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'GetAdAccountPages';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        /**
         * 获取过往三天消耗较大的账户
         * 获取这些账户下面的所有素材，并且对获取素材对应的落地页，以及素材对应的数据
         */
        $accountVccIds = DB::table("ad_account_vcc_daily")->where("ad_cost", ">", "50000")->where('date', ">", date("Y-m-d", time() - 86400 * 7))->pluck("account_id");
        //$accountVccIds = [********,********];
        $accountInfos = TencentAdAccount::query()->whereIn("account_id", $accountVccIds)->get();
        /** @var TencentAdAccount $adAccount */
        $i        = 0;
        $sumCount = $accountInfos->count();
        foreach ($accountInfos as $adAccount) {
            //完善有消耗的行业
            if (!$adAccount->system_industry_id) {
                $accountInfoResp               = OauthAccountService::advertiserGet($adAccount);
                $accountInfo                   = $accountInfoResp['data']['list'][0] ?? [];
                $adAccount->system_industry_id = $accountInfo['system_industry_id'] ?? "";
                $adAccount->mdm_name           = $accountInfo['mdm_name'] ?? "";
                $adAccount->agency_account_id  = $accountInfo['agency_account_id'] ?? "";
                $adAccount->operators          = json_encode($accountInfo['operators'] ?? []);
                $adAccount->memo               = $accountInfo['memo'] ?? "";
                $adAccount->save();
            }
            $this->info($adAccount->account_id);
            $i++;
            $this->info('进度:' . round($i / $sumCount * 100, 2) . '%');
            //获取所有素材
            $this->pages_get($adAccount);
            //$this->wechat_pages_get($adAccount);
        }
        return Command::SUCCESS;
    }

    function pages_get(TencentAdAccount $adAccount, $page = 1)
    {
        $interface = 'pages/get';
        $url       = 'https://api.e.qq.com/v3.0/' . $interface;

        $common_parameters = array(
            'access_token' => $adAccount->access_token,
            'timestamp'    => time(),
            'nonce'        => md5(uniqid('', true))
        );

        $parameters = array(
            'account_id' => $adAccount->account_id,
            'page'       => $page,
            'filtering'  =>
                [
                    [
                        'field'    => 'page_type',
                        'values'   =>
                            [
                                'PAGE_TYPE_OFFICIAL',
                            ],
                        'operator' => 'EQUALS',
                    ],
                ],
            'page_size'  => 100,
        );

        $parameters = array_merge($common_parameters, $parameters);

        foreach ($parameters as $key => $value) {
            if (!is_string($value)) {
                $parameters[$key] = json_encode($value);
            }
        }

        $request_url = $url . '?' . http_build_query($parameters);

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $request_url);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 60);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $response = curl_exec($curl);
        if (curl_error($curl)) {
            $error_msg = curl_error($curl);
            $error_no  = curl_errno($curl);
            curl_close($curl);
            throw new \Exception($error_msg, $error_no);
        }
        curl_close($curl);
        $data = json_decode($response, true);
        if(isset($data['data']['list'])){
            foreach ($data['data']['list'] as $item){
                $pages = AdAccountPage::query()->where("page_id")->first();
                if(!$pages){
                    $pages = new AdAccountPage();
                }
                $pages->account_id = $adAccount->account_id;
                $pages->page_type = $item['page_type']?? "";
                $pages->page_id = $item['page_id']?? "";
                $pages->page_name = $item['page_name']?? "";
                $pages->page_url = $item['page_url']?? "";
                $pages->page_status = $item['page_status']?? "";
                $pages->owner_account_id = $item['owner_account_id']?? "";
                $pages->created_time = isset($item['created_time'])?date("Y-m-d H:i:s",$item['created_time']):"";
                $pages->last_modified_time = isset($item['last_modified_time'])?date("Y-m-d H:i:s",$item['last_modified_time']):"";
                $pages->disable_code = $item['disable_code']?? "";
                $pages->disable_message = $item['disable_message']?? "";
                $pages->save();
            }
        }
        return $response;
    }

    function wechat_pages_get($accountInfo)
    {
        $interface = 'wechat_pages/get';
        $url       = 'https://api.e.qq.com/v1.1/' . $interface;

        $common_parameters = array(
            'access_token' => $accountInfo->access_token,
            'timestamp'    => time(),
            'nonce'        => md5(uniqid('', true)),
            'fields'       => [
                'page_name',
                'created_time',
                'page_template_id',
                'page_elements_spec_list',
                'share_content_spec',
                'canvas_type',
                'page_status'
            ]
        );

        $parameters = array(
            'account_id' => $accountInfo->account_id,
            'filtering'  => [],
            'page'       => 1,
            'page_size'  => 10,
        );

        $parameters = array_merge($common_parameters, $parameters);

        foreach ($parameters as $key => $value) {
            if (!is_string($value)) {
                $parameters[$key] = json_encode($value);
            }
        }

        $request_url = $url . '?' . http_build_query($parameters);

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $request_url);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 60);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $response = curl_exec($curl);
        if (curl_error($curl)) {
            $error_msg = curl_error($curl);
            $error_no  = curl_errno($curl);
            curl_close($curl);
            throw new \Exception($error_msg, $error_no);
        }
        curl_close($curl);
        $data = json_decode($response, true);
        dd($data);
        return $response;
    }
}
