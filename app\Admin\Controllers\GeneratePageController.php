<?php

namespace App\Admin\Controllers;

use Admin;
use App\Admin\Forms\GeneratePage\CopyPageByAdminUser;
use App\Admin\Repositories\GeneratePage;
use App\Models\AdminUser;
use App\Services\GeneratePageService;
use App\Services\Tools\UtilsService;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Show;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use function Symfony\Component\Translation\t;

class GeneratePageController extends AdminController
{


    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new GeneratePage(['adminInfo']), function (Grid $grid) {
            /** 对话框新增 */
            $grid->disableCreateButton();
            $grid->tools(function (Grid\Tools $tools) {
                $tools->append(UtilsService::dialogForm('添加', \Illuminate\Support\Facades\Request::url().'/create','create-CorpLicenseOrder'));
            });
            $grid->model()->orderBy('id', 'desc');
//            $grid->disableBatchActions();
            $grid->disableViewButton();
            $grid->column('id')->sortable();
            $grid->column('adminInfo.username', '用户')->copyable();
            $grid->column('name', '页面名称')->copyable();
            $grid->column('html_type', '页面类型')->using(\App\Models\GeneratePage::HTML_TYPE)->label([
                0 => 'success',
//                1 => 'success',
                2 => 'warning',
                3 => 'info',
            ]);
            $grid->column("oss_url", '下载地址')->downloadable();

            $grid->column("ask_avatar", '提问者头像')->image('',50,80);
            $grid->column("answer_avatar", '回答者头像')->image('',50,80);
            $grid->column('head_image','头图')->display(function ($head_image) {
                // 使用响应式图片类
                $head_image = json_decode($head_image,true);
                return collect($head_image)->map(function ($image) {
                    return "<img src={$image} class='img-thumbnail' style='max-height:80px;margin:5px;'>";
                })->implode(' ');
            });
//            $grid->column('head_image','头图')->display('查看')->modal(function ($modal) {
//                // 设置弹窗标题
//                $modal->title('头图 ');
//                $modal->xl();
//                $head_image = json_decode($this->head_image,true);
//                return view('admin.generate_page.head_image', ['head_image' => $head_image]);
//            });
            $grid->column("预览")->display(function () {
                $url = env('APP_URL') .'/generate/page/preview?id=' . $this->id . '&add_method=1';
                return "<a style='color: #ee00ee' href={$url} target='_blank'>预览</a>";
            })->help('默认预览为获客助手模式，可在地址修栏改：add_method=2，即可切换为二维码模式。');
            $grid->column('复制')
                ->display('复制')
                ->label()
                ->modal(function (Grid\Displayers\Modal $modal) {
                    // 标题
                    $modal->title('复制页面到其他用户');
                    // 自定义图标
                    $modal->icon('');//feather icon-check-circle
                    $modal->xl();
                    // 传递当前行字段值
                    $data = [
                        'html_id' => $this->id
                    ];
                    return CopyPageByAdminUser::make()->payload($data);
                });
            $grid->column('updated_at');
            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                $adminUser = AdminUser::query()->where('parent_id', 0)->pluck('username', 'id');
                $filter->equal('admin_uid', "用户")->select($adminUser)->width(2);
                $filter->like("name", '页面名称')->width(2);
                $filter->equal("html_type", '生成页面类型')->select(\App\Models\GeneratePage::HTML_TYPE)->width(2);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new GeneratePage(), function (Show $show) {
            if (!AdminUser::isAdmin($show->model())) {
                $show->field('N')->value("无数据");
            } else {
                $show->field('id');
            }
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        try {
            return Form::make(new GeneratePage(), function (Form $form) {

                $form->html(
                    <<<HTML
<div class="alert alert-info alert-dismissable">
            <h4><i class="fa fa-info"></i>&nbsp; 提示</h4>
          再次编辑后，会生成新的文件，并覆盖旧文件。
</div>
HTML
                );
                $form->hidden('id');

                $adminUsers = AdminUser::query()->pluck('username', 'id');
                $form->select('admin_uid', '用户')->options($adminUsers)->required();
//
                $name = date('Ymd_');
                $name = preg_replace('/^.{2}/', '', $name);
                $form->text('name', '页面名称')->width(4)->default($name)->required()->help('页面名称不可重复');
                $form->radio('file_type', '文件类型')->required()->options(['blade.php' => 'blade.php','html' => 'html'])->default('blade.php');
                $form->multipleImage('head_image', '头图')
//                ->compress()
                    ->saveAsJson()->uniqueName()->override()
                    ->disk('oss')
                    ->dir('generate_html/images')
                    ->saveFullUrl();
                $form->radio('html_type', '页面类型')
                    ->when([0], function ($form) {
                        $form->text('ask_name','提问者名称')->width(2);
                        $form->image('ask_avatar', '提问者头像')->uniqueName()->override()->disk('oss')->dir('generate_html/images')->saveFullUrl();
                        $form->image('answer_avatar', '回答者头像')->uniqueName()->override()->disk('oss')->dir('generate_html/images')->saveFullUrl();
//                    $form->color('button_color', '按钮颜色')->help('设置全局按钮默认颜色')
//                        ->type('color') // 设置为HTML5颜色类型
//                        ->default('#ef0b0b');
//                    $form->color('font_color', '文字颜色')->help('设置全局文字默认颜色')
//                        ->type('color') // 设置为HTML5颜色类型
//                        ->default('#fff');
                        $form->text('button_color', '按钮颜色')->width(2)->help('全局按钮默认颜色，默认红色');
                        $form->text('font_color', '文字颜色')->width(2)->help('全局文字默认颜色，默认白色');
                        $form->table('ask_content','问答/表单内容', function ($table) use($form) {
                            $table->textarea('content','问题')->required();
                            $table->list('options','选项')->required();
                            $table->list('is_stop','退出选项');
                            $table->select('type','类型')->required()->options(['question' => '问题','word' => '文字'])->default('question');
//                        $table->color('color','背景色');
                        })->saving(function ($v) use($form) {
                            return json_encode($v);
                        })->required();
                        $form->text('top_content','顶部内容');
                        $form->text('submit_button_title','提交按钮文案');
                        $form->textarea('end_content','退出结束语')->help('如果问答有退出项，请填写退出结束语，如果未填写，则使用默认结束语：【对不起，您暂时不符合参与条件！】');
                    })
                    ->options(\App\Models\GeneratePage::HTML_TYPE)->default(0)->required();
                $directory = GeneratePageService::getDirectorys();
                $form->select('directory','页面存放目录')->required()->options($directory);
//                $form->text('directory','页面存放目录');
//            $form->display('file_path','本地页面路径');
                $form->display('oss_url','下载地址');

                $form->disableViewButton();
                $form->disableViewCheck();
                $form->disableEditingCheck();
                $form->disableDeleteButton();
            })->saving(function (Form $form) {
                $check = \App\Models\GeneratePage::where('name',$form->input('name'))->first();
                if ($form->isCreating()) {
                    if ($check) {
                        return $form->response()->alert()->error('提示')->detail('该页面名称已存在-1');
                    }
                }
                if($form->isEditing()) {
                    if ($check && $check['id'] != $form->input('id')) {
                        return $form->response()->alert()->error('提示')->detail('该页面名称已存在-2');
                    }
                }
                $fileDel = $form->input('_file_del_');
                //如果是问答表单
                if($form->input('html_type') == 0 && !$fileDel) {
//                if(!$form->input('ask_name')){
//                    return $form->response()->alert()->error('提示')->detail('请填写提问者名称');
//                }
                    if(!$form->input('ask_avatar')){
                        return $form->response()->alert()->error('提示')->detail('请上传提问者头像');
                    }
                }

            })->saved(function (Form $form, $result) {
                if ($form->isCreating()) {
                    $id = $result;
                } else {
                    $id = $form->getKey();
                }
                if (!$id) {
                    return $form->response()->alert()->error('提示')->detail('创建失败，请稍后重试-1');
                }
                $html = \App\Models\GeneratePage::query()->where('id', $id)->first();
                if (!$html) {
                    return $form->response()->alert()->error('提示')->detail('创建失败，请稍后重试-2');
                }
                $res = GeneratePageService::makePage(Admin::user(),$html);
                if (!$res) {
                    return $form->response()->alert()->error('提示')->detail('生成页面失败，请稍后重试-1');
                }
            });
        }catch (\Exception $exception){
            Log::error('生成页面异常：' . $exception->getMessage());
            Log::error('生成页面异常文件：' . $exception->getFile());
            Log::error('生成页面异常行数：' . $exception->getLine());
        }

    }

    public function destroy($id)
    {
        if(!AdminUser::isSystemOp()){
            return $this->form()->response()->error("无权限操作");
        }
    }
}
