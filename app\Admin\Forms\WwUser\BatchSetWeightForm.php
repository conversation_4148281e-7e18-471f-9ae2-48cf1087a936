<?php

namespace App\Admin\Forms\WwUser;

use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Models\AdminUser;
use App\Models\WwUser;
use App\Services\Tools\LogService;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class BatchSetWeightForm extends Form implements LazyRenderable
{
    use LazyWidget;

    // 处理请求
    public function handle(array $input)
    {
        LogService::inputLog('Tools','销售客服管理-权重', $input, Admin::user()->id, Admin::user()->username);
        if(!$input['id']){
            return $this->response()->alert()->error('提示')->detail('请选择销售。');
        }
        // id转化为数组
        $id = explode(',', $input['id']);
        if (! $id) {
            return $this->response()->alert()->error('提示')->detail('参数错误。');
        }


        $wwUsers = WwUser::query()->find($id);

        if ($wwUsers->isEmpty()) {
            return $this->response()->alert()->error('提示')->detail('销售不存在。');
        }
        /** @var WwUser $wwUser */
        foreach($wwUsers as $wwUser){
            if(!AdminUser::isAdmin($wwUser)){
                return $this->response()->alert()->error('提示')->detail('无操作权限。');
            }
            $wwUser->weight = $input['weight'];
            //添加操作日志队列
            AdminActionLogJob::dispatch(
                'batch_set_weight',
                $wwUser->id,
                AdminActionLog::ACTION_TYPE['销售'],
                '【' . $wwUser->name . '】，设置权重：' . $input['weight'],
                getIp(),
                Admin::user()->id
            )->onQueue('admin_action_log_job');

            $wwUser->save();
        }
        return $this->response()->alert()->success('提示')->detail('配置成功')->refresh();
    }

    public function form()
    {
        $this->number('weight','接粉权重')->help("可以配置1-10的数字，权重跟销售展示几率成正比。")->default(1)->min(0);
        $this->hidden('id')->value($this->payload['ids'] ?? ''); // 批量操作时使用BatchActionPlus 就可以$this->payload['ids'] 获取批量选中的ids
        $this->confirm('确认提交？');
    }

    // 返回表单数据，如不需要可以删除此方法
    public function default()
    {
        return [
            'password'         => '',
            'password_confirm' => '',
        ];
    }
}
