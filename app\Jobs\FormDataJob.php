<?php

namespace App\Jobs;

use App\Models\LinkViewRecord;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FormDataJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $formData;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($formData)
    {

        $this->formData = $formData;
    }

    /**
     * 防止任务重叠。 expireAfter 设置锁的最大持有时间（防止任务崩溃导致死锁）
     *
//     * @return array
//     */
//    public function middleware()
//    {
//        $vid = $this->formData['vid'] ?? '';
//        if (empty($vid)) {
//            return [];
//        }
//        return [(new WithoutOverlapping($vid))->releaseAfter(10)->expireAfter(15)];
//    }
    /**
     * Execute the job.
     *
     * @return mixed
     */
    public function handle()
    {
        $formData = $this->formData;
        $adminUid = 0;
        $wwLinkId = 0;
        $area = '';
        if($formData['vid'] && $formData['vid'] > 0) {
            $linkViewRecord = LinkViewRecord::query()
                ->select('id','ww_link_id','admin_uid','area')
                ->where('id',$formData['vid'])
                ->first();
            $adminUid = $linkViewRecord->admin_uid ?? 0;
            $wwLinkId = $linkViewRecord->ww_link_id ?? 0;
            $area = $linkViewRecord->area ?? '';
        }
        $where = [
            'admin_uid' => $adminUid,
            'name' => $formData['name'] ?? '',
            'phone' => $formData['phone'] ?? '',
            'ip' => $formData['ip'] ?? '',
            'vid' => $formData['vid'] ?? 0,
        ];
        $check = DB::table('form_data')->where($where)->first();
        if(!$check) {
            DB::table('form_data')->insert([
                'admin_uid' => $adminUid,
                'name' => $formData['name'] ?? '',
                'phone' => $formData['phone'] ?? '',
                'vid' => $formData['vid'] ?? 0,
                'ww_link_id' => $wwLinkId,
                'ip' => $formData['ip'] ?? '',
                'area' => $area,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ]);
        }
        return true;
    }
}
