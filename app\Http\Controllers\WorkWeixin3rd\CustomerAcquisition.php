<?php

	namespace App\Http\Controllers\WorkWeixin3rd;

	use App\Models\AdminUser;
    use App\Models\WwCorpInfo;
	use App\Models\WwUser;
	use App\Models\WwUserAddRecord;
    use App\Models\WwUsersOnlineLogs;
    use App\Services\Corp\WwCorpApiService;
    use App\Services\NotifySendService;
    use App\Services\Ocpx\OcpxSubmitService;
    use Illuminate\Support\Facades\Log;

	/**
	 * 好友变动通知
	 */
	class CustomerAcquisition
	{
		public function handler($message, $param): bool
        {
            $message = json_decode($message, true);
            //这里是智投新增的参数，用户把sid和suite_id传到系统内
            $message['wind_param'] = $param;

			//Log::info('CustomerAcquisition - Event - MESSAGE：' . json_encode($message));

			//Event - 自建应用的通知方式
			//InfoType - 第三方应用的通知方式
			$infoType = $message['Event'] ?? "";
			if (empty($infoType)) {
				$infoType = $message['InfoType'] ?? "";
			}
			if ($infoType == 'customer_acquisition') {
				switch ($message['ChangeType']) {
					case 'link_unavailable'://获客助手链接异常
						$this->link_unavailable($message);
						break;
					case 'delete_link'://获客助手链接被删除
						$this->delete_link($message);
						break;
					case 'customer_start_chat'://首次接收到消息
						$this->customer_start_chat($message);
						break;
					case 'message_from_customer'://多次收到消息
						$this->message_from_customer($message);
						break;
				}
			}
			if (isset($message['AuthType']) && $message['AuthType'] == 'customer_acquisition') {
				switch ($infoType) {
					case "approve_special_auth"://获客助手授权
						$this->approve_special_auth($message);
						break;
					case "cancel_special_auth"://获客助手取消授权
						$this->cancel_special_auth($message);
						break;
				}
			}
			return true;
		}

		private function link_unavailable($message): void
		{
			$linkId = $message['LinkId'] ?? "-10000000000000";
            $reqTimeStamp = $message['CreateTime'] ?? "0";
            $reqTimeStamp = $reqTimeStamp."_".date("Y-m-d H:i:s",$reqTimeStamp);
            $reqTimeStamp = $reqTimeStamp."_".json_encode(($message['wind_param'] ?? []));
			/** @var WwUser $wwUser */
			$wwUsers = WwUser::query()->where("cus_acq_link_id", $linkId)->get();
			foreach ($wwUsers as $wwUser) {
				$wwUser->cus_acq_link_status         = 0;
				$wwUser->cus_acq_link_status_message = '企微异常提醒';
				$wwUser->online_status               = 0;
				$wwUser->save();
                $adminUser = AdminUser::query()->where("id", $wwUser->admin_uid)->first();
				NotifySendService::sendWorkWeixinForError("[企微回调][获客助手][销售异常] " . "「" . $adminUser->username . "」「" . $wwUser->corpInfo->corp_name . "」「"  . $wwUser->name . "」「销售ID："  . $wwUser->id  . "」「销售账号："  . $wwUser->user_id  . "」销售异常，已自动下线，请及时查看 ".$reqTimeStamp);
                $robotsMessage = "[销售异常] " . "「" . $adminUser->username . "」用户的「" . $wwUser->corpInfo->corp_name . "」企微下「"  . $wwUser->name . "」「ID："  . $wwUser->id  . "」「账号："  . $wwUser->user_id  . "」销售异常，已自动下线，请及时查看";

                NotifySendService::sendCustomerForMessageByRobots($adminUser,$robotsMessage);
                //添加销售下线记录
                $onlineStatus = 0;//下线
                $source = '系统-获客助手异常自动下线';
                WwUsersOnlineLogs::addOnLineStatusLogs($wwUser, $onlineStatus, $source);
			}
		}

		private function delete_link($message): void
		{
			$linkId = $message['LinkId'] ?? "-10000000000000";
			/** @var WwUser $wwUser */
			$wwUsers = WwUser::query()->where("cus_acq_link_id", $linkId)->get();
			foreach ($wwUsers as $wwUser) {
				$wwUser->cus_acq_link_status         = 0;
				$wwUser->cus_acq_link_status_message = '获客链接被删除';
				$wwUser->online_status               = 0;
				$wwUser->save();
                NotifySendService::sendWorkWeixinForError("[企微回调][获客助手][链接删除] " . $wwUser->id . "「" . $wwUser->user_id . "」销售获客链接被删除，已自动下线，请及时查看");
			}
		}

		private function customer_start_chat($message): void
		{
			$addRecords = WwUserAddRecord::query()->where([
				'follow_user_id'  => $message['UserID'],
				'external_userid' => $message['ExternalUserID'],
			])->get();
			if ($addRecords->isEmpty()) {
				return;
			}
			/** @var WwUserAddRecord $addRecord */
			foreach ($addRecords as $addRecord) {
				$addRecord->customer_start_chat = 1;
				$addRecord->chat_seq            = 1;
				$addRecord->save();

				//如果用户开口了，那么进行OCPX的上报
				//进行OCPX的上报
				$ocpxObj = new OcpxSubmitService();
				$ocpxObj->up($addRecord, 'customer_start_chat', 0);
			}
		}

		private function message_from_customer($message): void
		{
			$chatId = $message['ChatKey'] ?? "";
			if (empty($chatId)) {
                NotifySendService::sendWorkWeixinForError("[企微回调][获客助手][多次开口] 回调数据没有ChatKey标签，相关数据如下:" . json_encode($message));
				return;
			}
            /** @var WwCorpInfo $corpInfo */
            $corpInfo = WwCorpInfo::getCorpInfoFromWwCallback($message);
			if (!$corpInfo) {
                NotifySendService::sendWorkWeixinForError("[企微回调][获客助手][多次开口] 回调无法获取到相关的企业信息，请立即查看，相关数据如下：" . json_encode($message));
				return;
			}
            $chatInfo = WwCorpApiService::getChatInfo($corpInfo, $chatId);
			if (!isset($chatInfo['errcode']) || $chatInfo['errcode'] != 0) {
                NotifySendService::sendWorkWeixinForError("[企微回调][获客助手][多次开口] 无法获取Chat的详细信息，相关数据如下：" . json_encode([$chatInfo, $message]));
				return;
			}
			$addRecords = WwUserAddRecord::query()->where([
				'state' => $chatInfo['chat_info']['state']
			])->get();
			/** @var WwUserAddRecord $addRecord */
			foreach ($addRecords as $addRecord) {
				$addRecord->chat_seq = $chatInfo['chat_info']['recv_msg_cnt'];
				$addRecord->save();
			}
		}

		private function approve_special_auth($message): void
		{
			/** @var WwCorpInfo $corpInfo */
			$corpInfo = WwCorpInfo::withTrashed()->where("corp_id", $message['AuthCorpId'])->where("suite_id", $message['SuiteId'])->first();
			if ($corpInfo) {
				WwCorpApiService::getCustomerAcquisitionQuota($corpInfo);
			}else{
                NotifySendService::sendWorkWeixinForError("[企微回调][获客助手][特殊授权] 无法获取企业微信记录，相关数据如下：" . json_encode([$message]));
            }
		}

		private function cancel_special_auth($message): void
		{
			/** @var WwCorpInfo $corpInfo */
			$corpInfo                                  = WwCorpInfo::withTrashed()->where("corp_id", $message['AuthCorpId'])->where("suite_id", $message['SuiteId'])->first();
            if ($corpInfo) {
                $corpInfo->customer_link_quota_update_time = null;
                $corpInfo->save();
            }else{
                NotifySendService::sendWorkWeixinForError("[企微回调][获客助手][特殊授权] 去销授权，无法获取企业微信记录，相关数据如下：" . json_encode([$message]));
            }

		}
	}
