{"__meta": {"id": "01K1J3ETWKNB6DGHCYB6PGCJ2F", "datetime": "2025-08-01 14:00:10", "utime": **********.387744, "method": "GET", "uri": "/ztfz/sale", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.234584, "end": **********.38776, "duration": 4.1531758308410645, "duration_str": "4.15s", "measures": [{"label": "Booting", "start": **********.234584, "relative_start": 0, "end": **********.792524, "relative_end": **********.792524, "duration": 0.****************, "duration_str": "558ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.792581, "relative_start": 0.****************, "end": **********.387762, "relative_end": 2.1457672119140625e-06, "duration": 3.****************, "duration_str": "3.6s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.867803, "relative_start": 0.****************, "end": **********.877738, "relative_end": **********.877738, "duration": 0.*****************, "duration_str": "9.93ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: admin::grid.table", "start": **********.105251, "relative_start": 1.****************, "end": **********.105251, "relative_end": **********.105251, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.table-toolbar", "start": **********.106871, "relative_start": 1.****************, "end": **********.106871, "relative_end": **********.106871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.batch-actions", "start": **********.108257, "relative_start": 1.****************, "end": **********.108257, "relative_end": **********.108257, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.table-pagination", "start": **********.111756, "relative_start": 1.8771719932556152, "end": **********.111756, "relative_end": **********.111756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.pagination", "start": **********.114602, "relative_start": 1.8800179958343506, "end": **********.114602, "relative_end": **********.114602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.dropdown", "start": **********.116686, "relative_start": 1.8821020126342773, "end": **********.116686, "relative_end": **********.116686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.switch", "start": **********.960041, "relative_start": 2.725456953048706, "end": **********.960041, "relative_end": **********.960041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.switch", "start": **********.960851, "relative_start": 2.726266860961914, "end": **********.960851, "relative_end": **********.960851, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.switch", "start": **********.961317, "relative_start": 2.7267329692840576, "end": **********.961317, "relative_end": **********.961317, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.radio", "start": **********.962464, "relative_start": 2.7278800010681152, "end": **********.962464, "relative_end": **********.962464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.radio", "start": **********.962921, "relative_start": 2.728336811065674, "end": **********.962921, "relative_end": **********.962921, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.964375, "relative_start": 2.7297909259796143, "end": **********.964375, "relative_end": **********.964375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.radio", "start": **********.965408, "relative_start": 2.7308239936828613, "end": **********.965408, "relative_end": **********.965408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.radio", "start": **********.965578, "relative_start": 2.7309939861297607, "end": **********.965578, "relative_end": **********.965578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.966316, "relative_start": 2.73173189163208, "end": **********.966316, "relative_end": **********.966316, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.radio", "start": **********.967098, "relative_start": 2.732513904571533, "end": **********.967098, "relative_end": **********.967098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.radio", "start": **********.967261, "relative_start": 2.7326769828796387, "end": **********.967261, "relative_end": **********.967261, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.968073, "relative_start": 2.7334887981414795, "end": **********.968073, "relative_end": **********.968073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.fixed-table", "start": **********.975991, "relative_start": 2.7414069175720215, "end": **********.975991, "relative_end": **********.975991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.table-toolbar", "start": **********.976807, "relative_start": 2.742223024368286, "end": **********.976807, "relative_end": **********.976807, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.batch-actions", "start": **********.977319, "relative_start": 2.742734909057617, "end": **********.977319, "relative_end": **********.977319, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.button", "start": **********.978392, "relative_start": 2.743807792663574, "end": **********.978392, "relative_end": **********.978392, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.form", "start": **********.419615, "relative_start": 3.185030937194824, "end": **********.419615, "relative_end": **********.419615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.fields", "start": **********.420588, "relative_start": 3.1860039234161377, "end": **********.420588, "relative_end": **********.420588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select", "start": **********.422051, "relative_start": 3.187466859817505, "end": **********.422051, "relative_end": **********.422051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.42332, "relative_start": 3.1887359619140625, "end": **********.42332, "relative_end": **********.42332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.424223, "relative_start": 3.18963885307312, "end": **********.424223, "relative_end": **********.424223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select-script", "start": **********.425036, "relative_start": 3.1904518604278564, "end": **********.425036, "relative_end": **********.425036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.425929, "relative_start": 3.191344976425171, "end": **********.425929, "relative_end": **********.425929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.hidden", "start": **********.42724, "relative_start": 3.1926558017730713, "end": **********.42724, "relative_end": **********.42724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.hidden", "start": **********.428021, "relative_start": 3.193436861038208, "end": **********.428021, "relative_end": **********.428021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.form", "start": **********.017399, "relative_start": 3.7828149795532227, "end": **********.017399, "relative_end": **********.017399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.fields", "start": **********.017831, "relative_start": 3.7832469940185547, "end": **********.017831, "relative_end": **********.017831, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select", "start": **********.018508, "relative_start": 3.783923864364624, "end": **********.018508, "relative_end": **********.018508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.019108, "relative_start": 3.7845239639282227, "end": **********.019108, "relative_end": **********.019108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.019927, "relative_start": 3.7853429317474365, "end": **********.019927, "relative_end": **********.019927, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select-script", "start": **********.020613, "relative_start": 3.7860288619995117, "end": **********.020613, "relative_end": **********.020613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.02114, "relative_start": 3.7865560054779053, "end": **********.02114, "relative_end": **********.02114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.multipleselect", "start": **********.022779, "relative_start": 3.7881948947906494, "end": **********.022779, "relative_end": **********.022779, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.023672, "relative_start": 3.789088010787964, "end": **********.023672, "relative_end": **********.023672, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.024041, "relative_start": 3.789456844329834, "end": **********.024041, "relative_end": **********.024041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select-script", "start": **********.024388, "relative_start": 3.789803981781006, "end": **********.024388, "relative_end": **********.024388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.024751, "relative_start": 3.7901668548583984, "end": **********.024751, "relative_end": **********.024751, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select", "start": **********.025544, "relative_start": 3.7909598350524902, "end": **********.025544, "relative_end": **********.025544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.026156, "relative_start": 3.791571855545044, "end": **********.026156, "relative_end": **********.026156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.026508, "relative_start": 3.791923999786377, "end": **********.026508, "relative_end": **********.026508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select-script", "start": **********.026843, "relative_start": 3.7922589778900146, "end": **********.026843, "relative_end": **********.026843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.027186, "relative_start": 3.7926018238067627, "end": **********.027186, "relative_end": **********.027186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.file", "start": **********.02865, "relative_start": 3.7940659523010254, "end": **********.02865, "relative_end": **********.02865, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.029362, "relative_start": 3.7947778701782227, "end": **********.029362, "relative_end": **********.029362, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.029722, "relative_start": 3.795137882232666, "end": **********.029722, "relative_end": **********.029722, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.keyvalue", "start": **********.030759, "relative_start": 3.796175003051758, "end": **********.030759, "relative_end": **********.030759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.textarea", "start": **********.032138, "relative_start": 3.7975540161132812, "end": **********.032138, "relative_end": **********.032138, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.032792, "relative_start": 3.798207998275757, "end": **********.032792, "relative_end": **********.032792, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.033159, "relative_start": 3.798574924468994, "end": **********.033159, "relative_end": **********.033159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.textarea", "start": **********.033896, "relative_start": 3.799311876296997, "end": **********.033896, "relative_end": **********.033896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.034778, "relative_start": 3.800194025039673, "end": **********.034778, "relative_end": **********.034778, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.03553, "relative_start": 3.800945997238159, "end": **********.03553, "relative_end": **********.03553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.listbox", "start": **********.037501, "relative_start": 3.802917003631592, "end": **********.037501, "relative_end": **********.037501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.038334, "relative_start": 3.8037497997283936, "end": **********.038334, "relative_end": **********.038334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.038744, "relative_start": 3.8041598796844482, "end": **********.038744, "relative_end": **********.038744, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.radio", "start": **********.039996, "relative_start": 3.8054118156433105, "end": **********.039996, "relative_end": **********.039996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.radio", "start": **********.040215, "relative_start": 3.805630922317505, "end": **********.040215, "relative_end": **********.040215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.041542, "relative_start": 3.806957960128784, "end": **********.041542, "relative_end": **********.041542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.042109, "relative_start": 3.8075249195098877, "end": **********.042109, "relative_end": **********.042109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.radio", "start": **********.042891, "relative_start": 3.808306932449341, "end": **********.042891, "relative_end": **********.042891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.radio", "start": **********.043087, "relative_start": 3.8085029125213623, "end": **********.043087, "relative_end": **********.043087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.04396, "relative_start": 3.8093760013580322, "end": **********.04396, "relative_end": **********.04396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.044303, "relative_start": 3.8097188472747803, "end": **********.044303, "relative_end": **********.044303, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.045307, "relative_start": 3.810722827911377, "end": **********.045307, "relative_end": **********.045307, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.045912, "relative_start": 3.8113279342651367, "end": **********.045912, "relative_end": **********.045912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.046248, "relative_start": 3.811663866043091, "end": **********.046248, "relative_end": **********.046248, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.046798, "relative_start": 3.812213897705078, "end": **********.046798, "relative_end": **********.046798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.04717, "relative_start": 3.8125858306884766, "end": **********.04717, "relative_end": **********.04717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.047508, "relative_start": 3.8129239082336426, "end": **********.047508, "relative_end": **********.047508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.number", "start": **********.048365, "relative_start": 3.8137810230255127, "end": **********.048365, "relative_end": **********.048365, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.048963, "relative_start": 3.8143789768218994, "end": **********.048963, "relative_end": **********.048963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.049301, "relative_start": 3.8147168159484863, "end": **********.049301, "relative_end": **********.049301, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.number", "start": **********.049929, "relative_start": 3.81534481048584, "end": **********.049929, "relative_end": **********.049929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.050343, "relative_start": 3.8157589435577393, "end": **********.050343, "relative_end": **********.050343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.050672, "relative_start": 3.8160879611968994, "end": **********.050672, "relative_end": **********.050672, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.051284, "relative_start": 3.816699981689453, "end": **********.051284, "relative_end": **********.051284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.052063, "relative_start": 3.817478895187378, "end": **********.052063, "relative_end": **********.052063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.052434, "relative_start": 3.81784987449646, "end": **********.052434, "relative_end": **********.052434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.053103, "relative_start": 3.818518877029419, "end": **********.053103, "relative_end": **********.053103, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.053733, "relative_start": 3.8191490173339844, "end": **********.053733, "relative_end": **********.053733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.054245, "relative_start": 3.8196609020233154, "end": **********.054245, "relative_end": **********.054245, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.checkbox", "start": **********.055875, "relative_start": 3.821290969848633, "end": **********.055875, "relative_end": **********.055875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.checkbox", "start": **********.056349, "relative_start": 3.8217649459838867, "end": **********.056349, "relative_end": **********.056349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.057612, "relative_start": 3.8230278491973877, "end": **********.057612, "relative_end": **********.057612, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.058, "relative_start": 3.823415994644165, "end": **********.058, "relative_end": **********.058, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.058603, "relative_start": 3.824018955230713, "end": **********.058603, "relative_end": **********.058603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.059001, "relative_start": 3.8244168758392334, "end": **********.059001, "relative_end": **********.059001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.059343, "relative_start": 3.824759006500244, "end": **********.059343, "relative_end": **********.059343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.059912, "relative_start": 3.8253278732299805, "end": **********.059912, "relative_end": **********.059912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.060307, "relative_start": 3.8257229328155518, "end": **********.060307, "relative_end": **********.060307, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.060639, "relative_start": 3.826054811477661, "end": **********.060639, "relative_end": **********.060639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.061196, "relative_start": 3.8266119956970215, "end": **********.061196, "relative_end": **********.061196, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.061579, "relative_start": 3.8269948959350586, "end": **********.061579, "relative_end": **********.061579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.061922, "relative_start": 3.8273379802703857, "end": **********.061922, "relative_end": **********.061922, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.hidden", "start": **********.062473, "relative_start": 3.8278889656066895, "end": **********.062473, "relative_end": **********.062473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.hidden", "start": **********.062935, "relative_start": 3.8283510208129883, "end": **********.062935, "relative_end": **********.062935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.column-selector", "start": **********.06385, "relative_start": 3.829265832901001, "end": **********.06385, "relative_end": **********.06385, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.checkbox", "start": **********.064068, "relative_start": 3.829483985900879, "end": **********.064068, "relative_end": **********.064068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.checkbox", "start": **********.065737, "relative_start": 3.83115291595459, "end": **********.065737, "relative_end": **********.065737, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.container", "start": **********.069613, "relative_start": 3.835028886795044, "end": **********.069613, "relative_end": **********.069613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.071786, "relative_start": 3.8372018337249756, "end": **********.071786, "relative_end": **********.071786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.073159, "relative_start": 3.8385748863220215, "end": **********.073159, "relative_end": **********.073159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.074405, "relative_start": 3.8398208618164062, "end": **********.074405, "relative_end": **********.074405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.075355, "relative_start": 3.840770959854126, "end": **********.075355, "relative_end": **********.075355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.07609, "relative_start": 3.841506004333496, "end": **********.07609, "relative_end": **********.07609, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.076883, "relative_start": 3.842298984527588, "end": **********.076883, "relative_end": **********.076883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.077277, "relative_start": 3.8426928520202637, "end": **********.077277, "relative_end": **********.077277, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.077923, "relative_start": 3.843338966369629, "end": **********.077923, "relative_end": **********.077923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.078287, "relative_start": 3.843702793121338, "end": **********.078287, "relative_end": **********.078287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.078898, "relative_start": 3.844313859939575, "end": **********.078898, "relative_end": **********.078898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.079253, "relative_start": 3.8446688652038574, "end": **********.079253, "relative_end": **********.079253, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.07988, "relative_start": 3.8452959060668945, "end": **********.07988, "relative_end": **********.07988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.080338, "relative_start": 3.8457539081573486, "end": **********.080338, "relative_end": **********.080338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.080985, "relative_start": 3.8464009761810303, "end": **********.080985, "relative_end": **********.080985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.082179, "relative_start": 3.847594976425171, "end": **********.082179, "relative_end": **********.082179, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.082668, "relative_start": 3.848083972930908, "end": **********.082668, "relative_end": **********.082668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.083183, "relative_start": 3.8485989570617676, "end": **********.083183, "relative_end": **********.083183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.083946, "relative_start": 3.8493618965148926, "end": **********.083946, "relative_end": **********.083946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.08433, "relative_start": 3.849745988845825, "end": **********.08433, "relative_end": **********.08433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.085573, "relative_start": 3.8509888648986816, "end": **********.085573, "relative_end": **********.085573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.086383, "relative_start": 3.8517990112304688, "end": **********.086383, "relative_end": **********.086383, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.087344, "relative_start": 3.852759838104248, "end": **********.087344, "relative_end": **********.087344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.088903, "relative_start": 3.854318857192993, "end": **********.088903, "relative_end": **********.088903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.089564, "relative_start": 3.854979991912842, "end": **********.089564, "relative_end": **********.089564, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.090338, "relative_start": 3.8557538986206055, "end": **********.090338, "relative_end": **********.090338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.091508, "relative_start": 3.856923818588257, "end": **********.091508, "relative_end": **********.091508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.092187, "relative_start": 3.857602834701538, "end": **********.092187, "relative_end": **********.092187, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.092924, "relative_start": 3.85834002494812, "end": **********.092924, "relative_end": **********.092924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.094267, "relative_start": 3.85968279838562, "end": **********.094267, "relative_end": **********.094267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.094918, "relative_start": 3.8603339195251465, "end": **********.094918, "relative_end": **********.094918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.095594, "relative_start": 3.8610098361968994, "end": **********.095594, "relative_end": **********.095594, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.09663, "relative_start": 3.862046003341675, "end": **********.09663, "relative_end": **********.09663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.097209, "relative_start": 3.8626248836517334, "end": **********.097209, "relative_end": **********.097209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.098517, "relative_start": 3.8639328479766846, "end": **********.098517, "relative_end": **********.098517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.099199, "relative_start": 3.864614963531494, "end": **********.099199, "relative_end": **********.099199, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.099933, "relative_start": 3.8653488159179688, "end": **********.099933, "relative_end": **********.099933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.table-pagination", "start": **********.101907, "relative_start": 3.8673229217529297, "end": **********.101907, "relative_end": **********.101907, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.pagination", "start": **********.102951, "relative_start": 3.8683669567108154, "end": **********.102951, "relative_end": **********.102951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.dropdown", "start": **********.104244, "relative_start": 3.869659900665283, "end": **********.104244, "relative_end": **********.104244, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.tab", "start": **********.109436, "relative_start": 3.874851942062378, "end": **********.109436, "relative_end": **********.109436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::layouts.content", "start": **********.110871, "relative_start": 3.8762869834899902, "end": **********.110871, "relative_end": **********.110871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.breadcrumb", "start": **********.111958, "relative_start": 3.8773739337921143, "end": **********.111958, "relative_end": **********.111958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.alerts", "start": **********.113363, "relative_start": 3.8787789344787598, "end": **********.113363, "relative_end": **********.113363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.exception", "start": **********.114349, "relative_start": 3.8797647953033447, "end": **********.114349, "relative_end": **********.114349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.toastr", "start": **********.115287, "relative_start": 3.8807029724121094, "end": **********.115287, "relative_end": **********.115287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::layouts.page", "start": **********.116786, "relative_start": 3.882201910018921, "end": **********.116786, "relative_end": **********.116786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::layouts.container", "start": **********.118749, "relative_start": 3.884164810180664, "end": **********.118749, "relative_end": **********.118749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.sidebar", "start": **********.120417, "relative_start": 3.8858330249786377, "end": **********.120417, "relative_end": **********.120417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.341949, "relative_start": 4.107364892959595, "end": **********.341949, "relative_end": **********.341949, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.343631, "relative_start": 4.109046936035156, "end": **********.343631, "relative_end": **********.343631, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.344307, "relative_start": 4.109722852706909, "end": **********.344307, "relative_end": **********.344307, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.344813, "relative_start": 4.110229015350342, "end": **********.344813, "relative_end": **********.344813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.345292, "relative_start": 4.110707998275757, "end": **********.345292, "relative_end": **********.345292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.34575, "relative_start": 4.111166000366211, "end": **********.34575, "relative_end": **********.34575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.346217, "relative_start": 4.111632823944092, "end": **********.346217, "relative_end": **********.346217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.346631, "relative_start": 4.112046957015991, "end": **********.346631, "relative_end": **********.346631, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.34723, "relative_start": 4.112645864486694, "end": **********.34723, "relative_end": **********.34723, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.347875, "relative_start": 4.113291025161743, "end": **********.347875, "relative_end": **********.347875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.34839, "relative_start": 4.1138060092926025, "end": **********.34839, "relative_end": **********.34839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.348897, "relative_start": 4.1143128871917725, "end": **********.348897, "relative_end": **********.348897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.349414, "relative_start": 4.114830017089844, "end": **********.349414, "relative_end": **********.349414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.350006, "relative_start": 4.115422010421753, "end": **********.350006, "relative_end": **********.350006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.350667, "relative_start": 4.1160829067230225, "end": **********.350667, "relative_end": **********.350667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.35123, "relative_start": 4.116645812988281, "end": **********.35123, "relative_end": **********.35123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.351873, "relative_start": 4.117288827896118, "end": **********.351873, "relative_end": **********.351873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.352452, "relative_start": 4.117867946624756, "end": **********.352452, "relative_end": **********.352452, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.353254, "relative_start": 4.1186699867248535, "end": **********.353254, "relative_end": **********.353254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.353981, "relative_start": 4.119396924972534, "end": **********.353981, "relative_end": **********.353981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.354842, "relative_start": 4.12025785446167, "end": **********.354842, "relative_end": **********.354842, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.355567, "relative_start": 4.120982885360718, "end": **********.355567, "relative_end": **********.355567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.356305, "relative_start": 4.121720790863037, "end": **********.356305, "relative_end": **********.356305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.356909, "relative_start": 4.1223249435424805, "end": **********.356909, "relative_end": **********.356909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.357435, "relative_start": 4.1228508949279785, "end": **********.357435, "relative_end": **********.357435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.357968, "relative_start": 4.12338399887085, "end": **********.357968, "relative_end": **********.357968, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.358449, "relative_start": 4.1238648891448975, "end": **********.358449, "relative_end": **********.358449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.358953, "relative_start": 4.124368906021118, "end": **********.358953, "relative_end": **********.358953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.359462, "relative_start": 4.1248779296875, "end": **********.359462, "relative_end": **********.359462, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.35999, "relative_start": 4.125405788421631, "end": **********.35999, "relative_end": **********.35999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.360485, "relative_start": 4.125900983810425, "end": **********.360485, "relative_end": **********.360485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.361006, "relative_start": 4.126421928405762, "end": **********.361006, "relative_end": **********.361006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.361467, "relative_start": 4.126882791519165, "end": **********.361467, "relative_end": **********.361467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.361969, "relative_start": 4.127384901046753, "end": **********.361969, "relative_end": **********.361969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.362499, "relative_start": 4.127914905548096, "end": **********.362499, "relative_end": **********.362499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.362987, "relative_start": 4.128402948379517, "end": **********.362987, "relative_end": **********.362987, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.363471, "relative_start": 4.128886938095093, "end": **********.363471, "relative_end": **********.363471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.363995, "relative_start": 4.129410982131958, "end": **********.363995, "relative_end": **********.363995, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.3645, "relative_start": 4.129915952682495, "end": **********.3645, "relative_end": **********.3645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.364997, "relative_start": 4.130412817001343, "end": **********.364997, "relative_end": **********.364997, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.365492, "relative_start": 4.130908012390137, "end": **********.365492, "relative_end": **********.365492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.36611, "relative_start": 4.131525993347168, "end": **********.36611, "relative_end": **********.36611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.366785, "relative_start": 4.1322009563446045, "end": **********.366785, "relative_end": **********.366785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.367394, "relative_start": 4.13280987739563, "end": **********.367394, "relative_end": **********.367394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.368241, "relative_start": 4.133656978607178, "end": **********.368241, "relative_end": **********.368241, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.369224, "relative_start": 4.1346399784088135, "end": **********.369224, "relative_end": **********.369224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.370349, "relative_start": 4.135764837265015, "end": **********.370349, "relative_end": **********.370349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.371119, "relative_start": 4.136534929275513, "end": **********.371119, "relative_end": **********.371119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.371896, "relative_start": 4.137311935424805, "end": **********.371896, "relative_end": **********.371896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.372646, "relative_start": 4.138062000274658, "end": **********.372646, "relative_end": **********.372646, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.373383, "relative_start": 4.138798952102661, "end": **********.373383, "relative_end": **********.373383, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.374095, "relative_start": 4.139510869979858, "end": **********.374095, "relative_end": **********.374095, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.374794, "relative_start": 4.140209913253784, "end": **********.374794, "relative_end": **********.374794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.375413, "relative_start": 4.140828847885132, "end": **********.375413, "relative_end": **********.375413, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.375948, "relative_start": 4.141363859176636, "end": **********.375948, "relative_end": **********.375948, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.376456, "relative_start": 4.141871929168701, "end": **********.376456, "relative_end": **********.376456, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.376956, "relative_start": 4.142371892929077, "end": **********.376956, "relative_end": **********.376956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.377459, "relative_start": 4.1428749561309814, "end": **********.377459, "relative_end": **********.377459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.377971, "relative_start": 4.1433868408203125, "end": **********.377971, "relative_end": **********.377971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.378469, "relative_start": 4.143884897232056, "end": **********.378469, "relative_end": **********.378469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.379007, "relative_start": 4.144423007965088, "end": **********.379007, "relative_end": **********.379007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.37962, "relative_start": 4.145035982131958, "end": **********.37962, "relative_end": **********.37962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.380145, "relative_start": 4.14556097984314, "end": **********.380145, "relative_end": **********.380145, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.380616, "relative_start": 4.146031856536865, "end": **********.380616, "relative_end": **********.380616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.381084, "relative_start": 4.146499872207642, "end": **********.381084, "relative_end": **********.381084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.navbar", "start": **********.381904, "relative_start": 4.147319793701172, "end": **********.381904, "relative_end": **********.381904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: announcements.index", "start": **********.382802, "relative_start": 4.1482179164886475, "end": **********.382802, "relative_end": **********.382802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.navbar-user-panel", "start": **********.383865, "relative_start": 4.149281024932861, "end": **********.383865, "relative_end": **********.383865, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 37853224, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.20", "PHP Version": "8.0.2", "Environment": "local", "Debug Mode": "Enabled", "URL": "sen.test", "Timezone": "PRC", "Locale": "zh_CN"}}, "views": {"count": 222, "nb_templates": 222, "templates": [{"name": "1x admin::grid.table", "param_count": null, "params": [], "start": **********.10514, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/table.blade.phpadmin::grid.table", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Ftable.blade.php&line=1", "ajax": false, "filename": "table.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.table"}, {"name": "2x admin::grid.table-toolbar", "param_count": null, "params": [], "start": **********.106785, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/table-toolbar.blade.phpadmin::grid.table-toolbar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Ftable-toolbar.blade.php&line=1", "ajax": false, "filename": "table-toolbar.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::grid.table-toolbar"}, {"name": "2x admin::grid.batch-actions", "param_count": null, "params": [], "start": **********.10815, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/batch-actions.blade.phpadmin::grid.batch-actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fbatch-actions.blade.php&line=1", "ajax": false, "filename": "batch-actions.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::grid.batch-actions"}, {"name": "2x admin::grid.table-pagination", "param_count": null, "params": [], "start": **********.111656, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/table-pagination.blade.phpadmin::grid.table-pagination", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Ftable-pagination.blade.php&line=1", "ajax": false, "filename": "table-pagination.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::grid.table-pagination"}, {"name": "2x admin::grid.pagination", "param_count": null, "params": [], "start": **********.114517, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/pagination.blade.phpadmin::grid.pagination", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fpagination.blade.php&line=1", "ajax": false, "filename": "pagination.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::grid.pagination"}, {"name": "2x admin::widgets.dropdown", "param_count": null, "params": [], "start": **********.116609, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/dropdown.blade.phpadmin::widgets.dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fdropdown.blade.php&line=1", "ajax": false, "filename": "dropdown.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::widgets.dropdown"}, {"name": "3x admin::grid.displayer.switch", "param_count": null, "params": [], "start": **********.959945, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/switch.blade.phpadmin::grid.displayer.switch", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Fswitch.blade.php&line=1", "ajax": false, "filename": "switch.blade.php", "line": "?"}, "render_count": 3, "name_original": "admin::grid.displayer.switch"}, {"name": "3x admin::grid.displayer.editinline.radio", "param_count": null, "params": [], "start": **********.962401, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/editinline/radio.blade.phpadmin::grid.displayer.editinline.radio", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Feditinline%2Fradio.blade.php&line=1", "ajax": false, "filename": "radio.blade.php", "line": "?"}, "render_count": 3, "name_original": "admin::grid.displayer.editinline.radio"}, {"name": "5x admin::widgets.radio", "param_count": null, "params": [], "start": **********.962857, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/radio.blade.phpadmin::widgets.radio", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fradio.blade.php&line=1", "ajax": false, "filename": "radio.blade.php", "line": "?"}, "render_count": 5, "name_original": "admin::widgets.radio"}, {"name": "3x admin::grid.displayer.editinline.template", "param_count": null, "params": [], "start": **********.96431, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/editinline/template.blade.phpadmin::grid.displayer.editinline.template", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Feditinline%2Ftemplate.blade.php&line=1", "ajax": false, "filename": "template.blade.php", "line": "?"}, "render_count": 3, "name_original": "admin::grid.displayer.editinline.template"}, {"name": "1x admin::grid.fixed-table", "param_count": null, "params": [], "start": **********.975859, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/fixed-table.blade.phpadmin::grid.fixed-table", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Ffixed-table.blade.php&line=1", "ajax": false, "filename": "fixed-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.fixed-table"}, {"name": "1x admin::filter.button", "param_count": null, "params": [], "start": **********.978327, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/button.blade.phpadmin::filter.button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::filter.button"}, {"name": "2x admin::widgets.form", "param_count": null, "params": [], "start": **********.419547, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/form.blade.phpadmin::widgets.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::widgets.form"}, {"name": "2x admin::form.fields", "param_count": null, "params": [], "start": **********.420517, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/fields.blade.phpadmin::form.fields", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ffields.blade.php&line=1", "ajax": false, "filename": "fields.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::form.fields"}, {"name": "3x admin::form.select", "param_count": null, "params": [], "start": **********.421881, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/select.blade.phpadmin::form.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 3, "name_original": "admin::form.select"}, {"name": "20x admin::form.error", "param_count": null, "params": [], "start": **********.423248, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/error.blade.phpadmin::form.error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 20, "name_original": "admin::form.error"}, {"name": "20x admin::form.help-block", "param_count": null, "params": [], "start": **********.424155, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/help-block.blade.phpadmin::form.help-block", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fhelp-block.blade.php&line=1", "ajax": false, "filename": "help-block.blade.php", "line": "?"}, "render_count": 20, "name_original": "admin::form.help-block"}, {"name": "4x admin::form.select-script", "param_count": null, "params": [], "start": **********.424969, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/select-script.blade.phpadmin::form.select-script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fselect-script.blade.php&line=1", "ajax": false, "filename": "select-script.blade.php", "line": "?"}, "render_count": 4, "name_original": "admin::form.select-script"}, {"name": "12x admin::scripts.select", "param_count": null, "params": [], "start": **********.425843, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/scripts/select.blade.phpadmin::scripts.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fscripts%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 12, "name_original": "admin::scripts.select"}, {"name": "4x admin::form.hidden", "param_count": null, "params": [], "start": **********.427175, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/hidden.blade.phpadmin::form.hidden", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fhidden.blade.php&line=1", "ajax": false, "filename": "hidden.blade.php", "line": "?"}, "render_count": 4, "name_original": "admin::form.hidden"}, {"name": "1x admin::form.multipleselect", "param_count": null, "params": [], "start": **********.022654, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/multipleselect.blade.phpadmin::form.multipleselect", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fmultipleselect.blade.php&line=1", "ajax": false, "filename": "multipleselect.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.multipleselect"}, {"name": "1x admin::form.file", "param_count": null, "params": [], "start": **********.028583, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/file.blade.phpadmin::form.file", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ffile.blade.php&line=1", "ajax": false, "filename": "file.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.file"}, {"name": "1x admin::form.keyvalue", "param_count": null, "params": [], "start": **********.030694, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/keyvalue.blade.phpadmin::form.keyvalue", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fkeyvalue.blade.php&line=1", "ajax": false, "filename": "keyvalue.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.keyvalue"}, {"name": "2x admin::form.textarea", "param_count": null, "params": [], "start": **********.032074, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/textarea.blade.phpadmin::form.textarea", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ftextarea.blade.php&line=1", "ajax": false, "filename": "textarea.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::form.textarea"}, {"name": "1x admin::form.listbox", "param_count": null, "params": [], "start": **********.037347, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/listbox.blade.phpadmin::form.listbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Flistbox.blade.php&line=1", "ajax": false, "filename": "listbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.listbox"}, {"name": "2x admin::form.radio", "param_count": null, "params": [], "start": **********.039919, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/radio.blade.phpadmin::form.radio", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fradio.blade.php&line=1", "ajax": false, "filename": "radio.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::form.radio"}, {"name": "7x admin::form.input", "param_count": null, "params": [], "start": **********.045217, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/input.blade.phpadmin::form.input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Finput.blade.php&line=1", "ajax": false, "filename": "input.blade.php", "line": "?"}, "render_count": 7, "name_original": "admin::form.input"}, {"name": "2x admin::form.number", "param_count": null, "params": [], "start": **********.048297, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/number.blade.phpadmin::form.number", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fnumber.blade.php&line=1", "ajax": false, "filename": "number.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::form.number"}, {"name": "1x admin::form.checkbox", "param_count": null, "params": [], "start": **********.055799, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/checkbox.blade.phpadmin::form.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.checkbox"}, {"name": "3x admin::widgets.checkbox", "param_count": null, "params": [], "start": **********.056282, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/checkbox.blade.phpadmin::widgets.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 3, "name_original": "admin::widgets.checkbox"}, {"name": "1x admin::grid.column-selector", "param_count": null, "params": [], "start": **********.063759, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/column-selector.blade.phpadmin::grid.column-selector", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fcolumn-selector.blade.php&line=1", "ajax": false, "filename": "column-selector.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.column-selector"}, {"name": "1x admin::filter.container", "param_count": null, "params": [], "start": **********.069492, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/container.blade.phpadmin::filter.container", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fcontainer.blade.php&line=1", "ajax": false, "filename": "container.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::filter.container"}, {"name": "14x admin::filter.where", "param_count": null, "params": [], "start": **********.071674, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/where.blade.phpadmin::filter.where", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fwhere.blade.php&line=1", "ajax": false, "filename": "where.blade.php", "line": "?"}, "render_count": 14, "name_original": "admin::filter.where"}, {"name": "6x admin::filter.text", "param_count": null, "params": [], "start": **********.073058, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/text.blade.phpadmin::filter.text", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Ftext.blade.php&line=1", "ajax": false, "filename": "text.blade.php", "line": "?"}, "render_count": 6, "name_original": "admin::filter.text"}, {"name": "8x admin::filter.select", "param_count": null, "params": [], "start": **********.075282, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/select.blade.phpadmin::filter.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 8, "name_original": "admin::filter.select"}, {"name": "1x admin::widgets.tab", "param_count": null, "params": [], "start": **********.109357, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/tab.blade.phpadmin::widgets.tab", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Ftab.blade.php&line=1", "ajax": false, "filename": "tab.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::widgets.tab"}, {"name": "1x admin::layouts.content", "param_count": null, "params": [], "start": **********.110774, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/layouts/content.blade.phpadmin::layouts.content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Flayouts%2Fcontent.blade.php&line=1", "ajax": false, "filename": "content.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::layouts.content"}, {"name": "1x admin::partials.breadcrumb", "param_count": null, "params": [], "start": **********.111879, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/breadcrumb.blade.phpadmin::partials.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.breadcrumb"}, {"name": "1x admin::partials.alerts", "param_count": null, "params": [], "start": **********.113286, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/alerts.blade.phpadmin::partials.alerts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Falerts.blade.php&line=1", "ajax": false, "filename": "alerts.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.alerts"}, {"name": "1x admin::partials.exception", "param_count": null, "params": [], "start": **********.114274, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/exception.blade.phpadmin::partials.exception", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fexception.blade.php&line=1", "ajax": false, "filename": "exception.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.exception"}, {"name": "1x admin::partials.toastr", "param_count": null, "params": [], "start": **********.115218, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/toastr.blade.phpadmin::partials.toastr", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Ftoastr.blade.php&line=1", "ajax": false, "filename": "toastr.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.toastr"}, {"name": "1x admin::layouts.page", "param_count": null, "params": [], "start": **********.116717, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/layouts/page.blade.phpadmin::layouts.page", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Flayouts%2Fpage.blade.php&line=1", "ajax": false, "filename": "page.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::layouts.page"}, {"name": "1x admin::layouts.container", "param_count": null, "params": [], "start": **********.118628, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\resources\\views/vendor/admin/layouts/container.blade.phpadmin::layouts.container", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fresources%2Fviews%2Fvendor%2Fadmin%2Flayouts%2Fcontainer.blade.php&line=1", "ajax": false, "filename": "container.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::layouts.container"}, {"name": "1x admin::partials.sidebar", "param_count": null, "params": [], "start": **********.120298, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/sidebar.blade.phpadmin::partials.sidebar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.sidebar"}, {"name": "65x admin::partials.menu", "param_count": null, "params": [], "start": **********.34185, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/menu.blade.phpadmin::partials.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 65, "name_original": "admin::partials.menu"}, {"name": "1x admin::partials.navbar", "param_count": null, "params": [], "start": **********.381817, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/navbar.blade.phpadmin::partials.navbar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fnavbar.blade.php&line=1", "ajax": false, "filename": "navbar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.navbar"}, {"name": "1x announcements.index", "param_count": null, "params": [], "start": **********.382723, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\resources\\views/announcements/index.blade.phpannouncements.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fresources%2Fviews%2Fannouncements%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "announcements.index"}, {"name": "1x admin::partials.navbar-user-panel", "param_count": null, "params": [], "start": **********.383779, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/navbar-user-panel.blade.phpadmin::partials.navbar-user-panel", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fnavbar-user-panel.blade.php&line=1", "ajax": false, "filename": "navbar-user-panel.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.navbar-user-panel"}]}, "queries": {"count": 50, "nb_statements": 49, "nb_visible_statements": 50, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 2.7477000000000005, "accumulated_duration_str": "2.75s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Admin.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", "line": 145}, {"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 36}], "start": **********.947243, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "wind_rich", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `wr_admin_users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Admin.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", "line": 145}, {"index": 18, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.9681432, "duration": 0.25115, "duration_str": "251ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wind_rich", "explain": null, "start_percent": 0, "width_percent": 9.14}, {"sql": "insert into `wr_operation_logs` (`admin_uid`, `user_name`, `path`, `method`, `ip`, `input`, `updated_at`, `created_at`) values (1, 'ZtFzSuperAdminPro', 'ztfz/sale', 'GET', '127.0.0.1', '[]', '2025-08-01 14:00:07', '2025-08-01 14:00:07')", "type": "query", "params": [], "bindings": [1, "ZtFzSuperAdminPro", "ztfz/sale", "GET", "127.0.0.1", "[]", "2025-08-01 14:00:07", "2025-08-01 14:00:07"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 51}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.226887, "duration": 0.10015, "duration_str": "100ms", "memory": 0, "memory_str": null, "filename": "OpRecord.php:51", "source": {"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FHttp%2FMiddleware%2FOpRecord.php&line=51", "ajax": false, "filename": "OpRecord.php", "line": "51"}, "connection": "wind_rich", "explain": null, "start_percent": 9.14, "width_percent": 3.645}, {"sql": "select * from `wr_announcements` where `wr_announcements`.`deleted_at` is null order by `id` desc limit 7", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Announcement.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\Announcement.php", "line": 24}, {"index": 15, "namespace": null, "name": "app/Admin/bootstrap.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\bootstrap.php", "line": 62}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Admin.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", "line": 164}, {"index": 17, "namespace": null, "name": "app/Admin/bootstrap.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\bootstrap.php", "line": 80}, {"index": 19, "namespace": "middleware", "name": "admin.bootstrap", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\Bootstrap.php", "line": 14}], "start": **********.336306, "duration": 0.05335, "duration_str": "53.35ms", "memory": 0, "memory_str": null, "filename": "Announcement.php:24", "source": {"index": 14, "namespace": null, "name": "app/Models/Announcement.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\Announcement.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAnnouncement.php&line=24", "ajax": false, "filename": "Announcement.php", "line": "24"}, "connection": "wind_rich", "explain": null, "start_percent": 12.785, "width_percent": 1.942}, {"sql": "select `wr_admin_roles`.*, `wr_admin_role_users`.`user_id` as `pivot_user_id`, `wr_admin_role_users`.`role_id` as `pivot_role_id`, `wr_admin_role_users`.`created_at` as `pivot_created_at`, `wr_admin_role_users`.`updated_at` as `pivot_updated_at` from `wr_admin_roles` inner join `wr_admin_role_users` on `wr_admin_roles`.`id` = `wr_admin_role_users`.`role_id` where `wr_admin_role_users`.`user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/HasPermissions.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\HasPermissions.php", "line": 88}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/HasPermissions.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\HasPermissions.php", "line": 76}, {"index": 21, "namespace": "middleware", "name": "admin.permission", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\Permission.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 23, "namespace": "middleware", "name": "admin.bootstrap", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\Bootstrap.php", "line": 19}], "start": **********.539904, "duration": 0.07227, "duration_str": "72.27ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:88", "source": {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/HasPermissions.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\HasPermissions.php", "line": 88}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FHasPermissions.php&line=88", "ajax": false, "filename": "HasPermissions.php", "line": "88"}, "connection": "wind_rich", "explain": null, "start_percent": 14.727, "width_percent": 2.63}, {"sql": "select * from `wr_ww_users` where `wr_ww_users`.`deleted_at` is not null limit 10", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Admin/Traits/RecycleBinTrait.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Traits\\RecycleBinTrait.php", "line": 38}, {"index": 15, "namespace": null, "name": "app/Admin/Traits/RecycleBinTrait.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Traits\\RecycleBinTrait.php", "line": 25}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 232}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasTools.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasTools.php", "line": 53}, {"index": 18, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 235}], "start": **********.674071, "duration": 0.047700000000000006, "duration_str": "47.7ms", "memory": 0, "memory_str": null, "filename": "RecycleBinTrait.php:38", "source": {"index": 14, "namespace": null, "name": "app/Admin/Traits/RecycleBinTrait.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Traits\\RecycleBinTrait.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FTraits%2FRecycleBinTrait.php&line=38", "ajax": false, "filename": "RecycleBinTrait.php", "line": "38"}, "connection": "wind_rich", "explain": null, "start_percent": 17.357, "width_percent": 1.736}, {"sql": "select count(*) as aggregate from `wr_ww_users` where `wr_ww_users`.`deleted_at` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.728626, "duration": 0.04752, "duration_str": "47.52ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 19.093, "width_percent": 1.729}, {"sql": "select * from `wr_ww_users` where `wr_ww_users`.`deleted_at` is not null limit 20 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.781055, "duration": 0.04952, "duration_str": "49.52ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 20.823, "width_percent": 1.802}, {"sql": "select * from `wr_ww_corp_info` where `wr_ww_corp_info`.`id` in (3) and `wr_ww_corp_info`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.8362138, "duration": 0.05046, "duration_str": "50.46ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 22.625, "width_percent": 1.836}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` in (1, 4, 5) and `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.890404, "duration": 0.04811, "duration_str": "48.11ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 24.461, "width_percent": 1.751}, {"sql": "select `wr_ww_users_groups`.*, `wr_ww_users_groups_rel`.`ww_user_id` as `pivot_ww_user_id`, `wr_ww_users_groups_rel`.`ww_group_id` as `pivot_ww_group_id`, `wr_ww_users_groups_rel`.`created_at` as `pivot_created_at`, `wr_ww_users_groups_rel`.`updated_at` as `pivot_updated_at` from `wr_ww_users_groups` inner join `wr_ww_users_groups_rel` on `wr_ww_users_groups`.`id` = `wr_ww_users_groups_rel`.`ww_group_id` where `wr_ww_users_groups_rel`.`ww_user_id` in (1, 2, 3, 4, 7, 8, 9, 10, 11, 12) and `wr_ww_users_groups`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.943959, "duration": 0.05051, "duration_str": "50.51ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 26.212, "width_percent": 1.838}, {"sql": "select * from `wr_ww_user_qrcodes` where `is_used` = 0 and `wr_ww_user_qrcodes`.`ww_user_id` in (1, 2, 3, 4, 7, 8, 9, 10, 11, 12)", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.999689, "duration": 0.07684, "duration_str": "76.84ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 28.05, "width_percent": 2.797}, {"sql": "select * from `wr_admin_sub_user_auth_corps` where `wr_admin_sub_user_auth_corps`.`deleted_at` is null order by `id` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 461}, {"index": 18, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.123727, "duration": 0.04952, "duration_str": "49.52ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:117", "source": {"index": 14, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=117", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "117"}, "connection": "wind_rich", "explain": null, "start_percent": 30.847, "width_percent": 1.802}, {"sql": "select `id`, `corp_name` from `wr_ww_corp_info` where `wr_ww_corp_info`.`id` in (1, 3, 5, 6) and `wr_ww_corp_info`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, {"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 461}, {"index": 23, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.176543, "duration": 0.05483, "duration_str": "54.83ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:117", "source": {"index": 19, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=117", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "117"}, "connection": "wind_rich", "explain": null, "start_percent": 32.649, "width_percent": 1.995}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 461}, {"index": 27, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}], "start": **********.234134, "duration": 0.04918, "duration_str": "49.18ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 34.645, "width_percent": 1.79}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 461}, {"index": 27, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}], "start": **********.286212, "duration": 0.048350000000000004, "duration_str": "48.35ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 36.434, "width_percent": 1.76}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 9 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 461}, {"index": 27, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}], "start": **********.3379061, "duration": 0.04875, "duration_str": "48.75ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 38.194, "width_percent": 1.774}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 461}, {"index": 27, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}], "start": **********.389499, "duration": 0.048659999999999995, "duration_str": "48.66ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 39.968, "width_percent": 1.771}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 461}, {"index": 27, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}], "start": **********.441297, "duration": 0.048909999999999995, "duration_str": "48.91ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 41.739, "width_percent": 1.78}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 461}, {"index": 27, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}], "start": **********.492367, "duration": 0.048960000000000004, "duration_str": "48.96ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 43.519, "width_percent": 1.782}, {"sql": "select `title`, `id` from `wr_ww_users_groups` where `wr_ww_users_groups`.`deleted_at` is null order by `id` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 510}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 1008}], "start": **********.54582, "duration": 0.04819, "duration_str": "48.19ms", "memory": 0, "memory_str": null, "filename": "WwUserController.php:510", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 510}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwUserController.php&line=510", "ajax": false, "filename": "WwUserController.php", "line": "510"}, "connection": "wind_rich", "explain": null, "start_percent": 45.301, "width_percent": 1.754}, {"sql": "select `username`, `id` from `wr_admin_users` where `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 527}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 1008}], "start": **********.5963361, "duration": 0.04912, "duration_str": "49.12ms", "memory": 0, "memory_str": null, "filename": "WwUserController.php:527", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 527}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwUserController.php&line=527", "ajax": false, "filename": "WwUserController.php", "line": "527"}, "connection": "wind_rich", "explain": null, "start_percent": 47.055, "width_percent": 1.788}, {"sql": "select count(*) as aggregate from `wr_ww_users` where `type` = 1 and `wr_ww_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.647889, "duration": 0.04739, "duration_str": "47.39ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 48.843, "width_percent": 1.725}, {"sql": "select * from `wr_ww_users` where `type` = 1 and `wr_ww_users`.`deleted_at` is null order by `id` desc limit 20 offset 0", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.697431, "duration": 0.04995, "duration_str": "49.95ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 50.567, "width_percent": 1.818}, {"sql": "select * from `wr_ww_corp_info` where `wr_ww_corp_info`.`id` in (1, 3) and `wr_ww_corp_info`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.7497709, "duration": 0.04793, "duration_str": "47.93ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 52.385, "width_percent": 1.744}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` in (1, 4) and `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.7996929, "duration": 0.05115, "duration_str": "51.15ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 54.13, "width_percent": 1.862}, {"sql": "select `wr_ww_users_groups`.*, `wr_ww_users_groups_rel`.`ww_user_id` as `pivot_ww_user_id`, `wr_ww_users_groups_rel`.`ww_group_id` as `pivot_ww_group_id`, `wr_ww_users_groups_rel`.`created_at` as `pivot_created_at`, `wr_ww_users_groups_rel`.`updated_at` as `pivot_updated_at` from `wr_ww_users_groups` inner join `wr_ww_users_groups_rel` on `wr_ww_users_groups`.`id` = `wr_ww_users_groups_rel`.`ww_group_id` where `wr_ww_users_groups_rel`.`ww_user_id` in (5, 6, 13) and `wr_ww_users_groups`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.853359, "duration": 0.0506, "duration_str": "50.6ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 55.991, "width_percent": 1.842}, {"sql": "select * from `wr_ww_user_qrcodes` where `is_used` = 0 and `wr_ww_user_qrcodes`.`ww_user_id` in (5, 6, 13)", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.9064279, "duration": 0.0497, "duration_str": "49.7ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 57.833, "width_percent": 1.809}, {"sql": "select * from `wr_admin_sub_user_auth_corps` where `wr_admin_sub_user_auth_corps`.`deleted_at` is null order by `id` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.984114, "duration": 0.0507, "duration_str": "50.7ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:117", "source": {"index": 14, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=117", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "117"}, "connection": "wind_rich", "explain": null, "start_percent": 59.642, "width_percent": 1.845}, {"sql": "select `id`, `corp_name` from `wr_ww_corp_info` where `wr_ww_corp_info`.`id` in (1, 3, 5, 6) and `wr_ww_corp_info`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, {"index": 20, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.039192, "duration": 0.04864, "duration_str": "48.64ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:117", "source": {"index": 19, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=117", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "117"}, "connection": "wind_rich", "explain": null, "start_percent": 61.487, "width_percent": 1.77}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.090889, "duration": 0.05053, "duration_str": "50.53ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 63.257, "width_percent": 1.839}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.143523, "duration": 0.05017, "duration_str": "50.17ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 65.096, "width_percent": 1.826}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 9 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.195791, "duration": 0.05124, "duration_str": "51.24ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 66.922, "width_percent": 1.865}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.249196, "duration": 0.05411, "duration_str": "54.11ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 68.787, "width_percent": 1.969}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.305495, "duration": 0.05149, "duration_str": "51.49ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 70.756, "width_percent": 1.874}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.359062, "duration": 0.05214, "duration_str": "52.14ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 72.63, "width_percent": 1.898}, {"sql": "select * from `wr_admin_sub_user_auth_corps` where `wr_admin_sub_user_auth_corps`.`deleted_at` is null order by `id` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.428594, "duration": 0.051609999999999996, "duration_str": "51.61ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:117", "source": {"index": 14, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=117", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "117"}, "connection": "wind_rich", "explain": null, "start_percent": 74.527, "width_percent": 1.878}, {"sql": "select `id`, `corp_name` from `wr_ww_corp_info` where `wr_ww_corp_info`.`id` in (1, 3, 5, 6) and `wr_ww_corp_info`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, {"index": 20, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.4828272, "duration": 0.04963, "duration_str": "49.63ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:117", "source": {"index": 19, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=117", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "117"}, "connection": "wind_rich", "explain": null, "start_percent": 76.406, "width_percent": 1.806}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.5348241, "duration": 0.055979999999999995, "duration_str": "55.98ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 78.212, "width_percent": 2.037}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.592779, "duration": 0.048670000000000005, "duration_str": "48.67ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 80.249, "width_percent": 1.771}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 9 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.643488, "duration": 0.04952, "duration_str": "49.52ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 82.021, "width_percent": 1.802}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.695024, "duration": 0.04876, "duration_str": "48.76ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 83.823, "width_percent": 1.775}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.746065, "duration": 0.04987, "duration_str": "49.87ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 85.597, "width_percent": 1.815}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.7981021, "duration": 0.04947, "duration_str": "49.47ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 87.412, "width_percent": 1.8}, {"sql": "select `id` from `wr_ww_users_groups` where `admin_uid` = 1 and `wr_ww_users_groups`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/WwUsersGroup.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\WwUsersGroup.php", "line": 155}, {"index": 14, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 234}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.8630672, "duration": 0.04714, "duration_str": "47.14ms", "memory": 0, "memory_str": null, "filename": "WwUsersGroup.php:155", "source": {"index": 13, "namespace": null, "name": "app/Models/WwUsersGroup.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\WwUsersGroup.php", "line": 155}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwUsersGroup.php&line=155", "ajax": false, "filename": "WwUsersGroup.php", "line": "155"}, "connection": "wind_rich", "explain": null, "start_percent": 89.213, "width_percent": 1.716}, {"sql": "select `ww_user_group_ids` from `wr_admin_sub_users` where `admin_uid` = 1 and `wr_admin_sub_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/WwUsersGroup.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\WwUsersGroup.php", "line": 156}, {"index": 17, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 234}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.913357, "duration": 0.04725, "duration_str": "47.25ms", "memory": 0, "memory_str": null, "filename": "WwUsersGroup.php:156", "source": {"index": 16, "namespace": null, "name": "app/Models/WwUsersGroup.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\WwUsersGroup.php", "line": 156}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwUsersGroup.php&line=156", "ajax": false, "filename": "WwUsersGroup.php", "line": "156"}, "connection": "wind_rich", "explain": null, "start_percent": 90.928, "width_percent": 1.72}, {"sql": "select `title`, `id` from `wr_ww_users_groups` where `id` in (1, 2) and `wr_ww_users_groups`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/WwUsersGroup.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\WwUsersGroup.php", "line": 161}, {"index": 14, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 234}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.962814, "duration": 0.04858, "duration_str": "48.58ms", "memory": 0, "memory_str": null, "filename": "WwUsersGroup.php:161", "source": {"index": 13, "namespace": null, "name": "app/Models/WwUsersGroup.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\WwUsersGroup.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwUsersGroup.php&line=161", "ajax": false, "filename": "WwUsersGroup.php", "line": "161"}, "connection": "wind_rich", "explain": null, "start_percent": 92.648, "width_percent": 1.768}, {"sql": "select * from `wr_admin_menu` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 115}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/MenuCache.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\MenuCache.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 99}], "start": **********.122998, "duration": 0.055119999999999995, "duration_str": "55.12ms", "memory": 0, "memory_str": null, "filename": "ModelTree.php:123", "source": {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FModelTree.php&line=123", "ajax": false, "filename": "ModelTree.php", "line": "123"}, "connection": "wind_rich", "explain": null, "start_percent": 94.416, "width_percent": 2.006}, {"sql": "select `wr_admin_permissions`.*, `wr_admin_permission_menu`.`menu_id` as `pivot_menu_id`, `wr_admin_permission_menu`.`permission_id` as `pivot_permission_id`, `wr_admin_permission_menu`.`created_at` as `pivot_created_at`, `wr_admin_permission_menu`.`updated_at` as `pivot_updated_at` from `wr_admin_permissions` inner join `wr_admin_permission_menu` on `wr_admin_permissions`.`id` = `wr_admin_permission_menu`.`permission_id` where `wr_admin_permission_menu`.`menu_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 115}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 98}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/MenuCache.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\MenuCache.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 99}], "start": **********.184028, "duration": 0.048159999999999994, "duration_str": "48.16ms", "memory": 0, "memory_str": null, "filename": "ModelTree.php:123", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FModelTree.php&line=123", "ajax": false, "filename": "ModelTree.php", "line": "123"}, "connection": "wind_rich", "explain": null, "start_percent": 96.422, "width_percent": 1.753}, {"sql": "select `wr_admin_roles`.*, `wr_admin_role_menu`.`menu_id` as `pivot_menu_id`, `wr_admin_role_menu`.`role_id` as `pivot_role_id`, `wr_admin_role_menu`.`created_at` as `pivot_created_at`, `wr_admin_role_menu`.`updated_at` as `pivot_updated_at` from `wr_admin_roles` inner join `wr_admin_role_menu` on `wr_admin_roles`.`id` = `wr_admin_role_menu`.`role_id` where `wr_admin_role_menu`.`menu_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 115}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 98}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/MenuCache.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\MenuCache.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 99}], "start": **********.2425282, "duration": 0.05015, "duration_str": "50.15ms", "memory": 0, "memory_str": null, "filename": "ModelTree.php:123", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FModelTree.php&line=123", "ajax": false, "filename": "ModelTree.php", "line": "123"}, "connection": "wind_rich", "explain": null, "start_percent": 98.175, "width_percent": 1.825}]}, "models": {"data": {"App\\Models\\WwUserQrcode": {"value": 160, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwUserQrcode.php&line=1", "ajax": false, "filename": "WwUserQrcode.php", "line": "?"}}, "Dcat\\Admin\\Models\\Role": {"value": 156, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Dcat\\Admin\\Models\\Menu": {"value": 61, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FMenu.php&line=1", "ajax": false, "filename": "Menu.php", "line": "?"}}, "Dcat\\Admin\\Models\\Permission": {"value": 56, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\WwUser": {"value": 23, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwUser.php&line=1", "ajax": false, "filename": "WwUser.php", "line": "?"}}, "App\\Models\\AdminUser": {"value": 23, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminUser.php&line=1", "ajax": false, "filename": "AdminUser.php", "line": "?"}}, "App\\Models\\AdminSubUserAuthCorp": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=1", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "?"}}, "App\\Models\\WwCorpInfo": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwCorpInfo.php&line=1", "ajax": false, "filename": "WwCorpInfo.php", "line": "?"}}, "App\\Models\\WwUsersGroup": {"value": 14, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwUsersGroup.php&line=1", "ajax": false, "filename": "WwUsersGroup.php", "line": "?"}}, "Dcat\\Admin\\Models\\Administrator": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FAdministrator.php&line=1", "ajax": false, "filename": "Administrator.php", "line": "?"}}, "App\\Models\\Announcement": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAnnouncement.php&line=1", "ajax": false, "filename": "Announcement.php", "line": "?"}}}, "count": 528, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://sen.test/ztfz/sale", "action_name": "dcat.admin.sale.index", "controller_action": "App\\Admin\\Controllers\\WwUserController@index", "uri": "GET ztfz/sale", "controller": "App\\Admin\\Controllers\\WwUserController@index<a href=\"phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwUserController.php&line=72\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Admin\\Controllers", "prefix": "/ztfz", "file": "<a href=\"phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwUserController.php&line=72\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Admin/Controllers/WwUserController.php:72-108</a>", "middleware": "admin.app:admin, web, admin", "duration": "4.17s", "peak_memory": "4MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1744015230 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1744015230\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Img0SE1IcWFxOTNBaFlLb0lSTkNHS2c9PSIsInZhbHVlIjoiUmE2bkRQaXBXOHh3d09aY2RYcGRMZDdVaW9QYWh2ay9GajNJaFJzc0hOSE9mTDBsRmxmb3Z5OVlUVkZvWjhRa0dmdHBMd2ZnUmU0bCt4MEMyVEVLcFQyM2s2dklySFZBVFRSMWdEelZVdldsdGp4QUludzJnREE0QXMrS0plNHIiLCJtYWMiOiIwNWE1Y2E3NDk4ZGE0NDQ4ZGYwOTMyZTY1ZjNmZjY0ZmI2ZjZkMTg2YjU5NDRjNWMzNDliYTUzNzY0MGNmMjVmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkZRY05jMUVqRnpDNTBiTXpDaEdSUnc9PSIsInZhbHVlIjoiQVhMV3hQbFNJYmprLzJrRE5LL1I1a2dSNHZHWk9nYURUNUxLcUpxM0tqVzNISXVnbDBzWmprcnJTSU5HVnJxdHAzOWdZTTNxaldlOEVaWFQvZ0J4RDZIUUZ5MG02T0JMUEh0aUlCdHk4bi9FR0lNUGNPL3lWUSttUlg3ZHVGNXYiLCJtYWMiOiJkOGMwZTkwOWMzYjM4ZjQ2ZDQyMDcwMTE2ZTI3NmRhYzI3Nzk4ZDNjODg5ZWUxMDkwY2UxZDY0NjQ2ZGE2MzRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">zh-CN,zh;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">sen.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1442250756 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0esJyfQEGYXbNzItiQn8t75dzSvXZmiKqhcwIHBs</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Sc1mCMq4OSwvqqSk8mbaIttmOBzjSrhvaXfSjoYm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1442250756\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-585014335 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Aug 2025 06:00:10 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-585014335\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-96393909 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>prev</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://sen.test/ztfz/sale</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">admin.prev.url</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0esJyfQEGYXbNzItiQn8t75dzSvXZmiKqhcwIHBs</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://sen.test/ztfz/sale</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-96393909\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://sen.test/ztfz/sale", "action_name": "dcat.admin.sale.index", "controller_action": "App\\Admin\\Controllers\\WwUserController@index"}, "badge": null}}