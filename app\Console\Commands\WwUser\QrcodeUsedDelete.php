<?php

namespace App\Console\Commands\WwUser;

use App\Jobs\WwUser\DeleteWwUserUsedQrCodesJob;
use App\Models\WwUser;
use App\Models\WwUserQrcode;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class QrcodeUsedDelete extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'WwUser:UsedQrCodeDelete';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '删除已经使用的二维码，并记录到ww_user_qrcodes_delete表中';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $where = [
            'is_used' => 1,//已使用的
            'ww_user_type' => 1,//类型是销售的
        ];

        $deleteAt = date('Y-m-d H:i:s', strtotime('-8 hour'));
        WwUserQrcode::query()->where($where)
            ->where('updated_at','<=',$deleteAt)
            ->chunkById(1000, function ($list) {
            if ($list) {
                $list = $list->toArray();
                foreach ($list as $k => $v) {
                    $wwUser = WwUser::withTrashed()->where('id', $v['ww_user_id'])->first();
                    if ($wwUser) {
                        DeleteWwUserUsedQrCodesJob::dispatch($v, $wwUser)->onQueue('delete_used_qrcode');
                    }
                }
            }
        });


        return Command::SUCCESS;
    }
}
