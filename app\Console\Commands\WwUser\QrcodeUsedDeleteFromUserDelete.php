<?php

namespace App\Console\Commands\WwUser;

use App\Jobs\WwUser\DeleteWwUserUsedQrCodesJob;
use App\Models\WwUser;
use App\Models\WwUserQrcode;
use Illuminate\Console\Command;

class QrcodeUsedDeleteFromUserDelete extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'QrcodeUsedDeleteFromUserDelete';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '如果销售被删除，对应的二维码也要全部清理掉';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        /**
         * 如果销售被删除，对应的二维码也要全部清理掉
         */
//        $adminUids = [
//            163,164,165,166,167,169,172,173,174,250
//        ];
        WwUser::withTrashed()
            ->select("id")
//            ->whereIn('admin_uid',$adminUids)
            ->whereNotNull("deleted_at")
            ->where('have_qrcode',1)//判断有二维码的才去删除
            ->orderByDesc('id')
            ->chunkById(1000, function ($wwUsers) {
            if($wwUsers->isNotEmpty()){
                $wwUserIds = array_column($wwUsers->toArray(), "id");
                WwUserQrcode::query()->whereIn("ww_user_id",$wwUserIds)->chunkById(1000, function ($qrCodeList) {
                    if ($qrCodeList) {
                        $qrCodeList = $qrCodeList->toArray();
                        foreach ($qrCodeList as $k => $qrCode) {
                            $wwUser = WwUser::query()->withTrashed()->where('id', $qrCode['ww_user_id'])->first();
                            if ($wwUser) {
                                $this->info('清理已删除销售的二维码,二维码ID：'. $qrCode['id'] . '，销售ID：' . $wwUser->id);
                                DeleteWwUserUsedQrCodesJob::dispatch($qrCode, $wwUser)->onQueue('delete_used_qrcode');
                            }
                        }
                    }
                });
            }
        });
        return Command::SUCCESS;
    }
}
