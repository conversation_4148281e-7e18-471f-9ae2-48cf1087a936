<?php

namespace App\Jobs\WwUser;

use App\Models\WwUserQrcode;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;


class QrCodeSaveUpJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $wwUser;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($wwUser)
    {
        //
        $this->wwUser = $wwUser;
    }

    /**
     * Execute the job.
     *
     * @return mixed
     */
    public function handle()
    {
        $wwUser = $this->wwUser;
        if($wwUser->online_status == 1){
            $qrcodeNumMax = 1500;
            $preNum = 40;
        }else{
            $qrcodeNumMax = 100;
            $preNum = 10;
        }
        $where = [
            'is_used' => 0,
            'ww_user_id' => $wwUser->id,
            'skip_verify' => $wwUser->skip_verify
        ];
        $count = WwUserQrcode::query()->where($where)->count();
        if ($count < $qrcodeNumMax) {
            for ($i = 0; $i < $preNum; $i++) { //一般一次生成30个
                QrCodeSaveUpItemJob::dispatch($wwUser)->onQueue('qr_code_save_up_item');
            }
        }
    }
}
