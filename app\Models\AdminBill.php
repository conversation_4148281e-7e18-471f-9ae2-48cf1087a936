<?php

    namespace App\Models;

    use Dcat\Admin\Traits\HasDateTimeFormatter;
    use Illuminate\Database\Eloquent\Relations\BelongsTo;
    use Illuminate\Database\Eloquent\SoftDeletes;
    use Illuminate\Database\Eloquent\Model;

    /**
     * @property mixed|string $date
     * @property mixed $admin_uid
     * @property int|mixed $add_price
     * @property float $balance
     * @property float|int|mixed $wr_price
     * @property int|mixed $wr_price_unit
     * @property mixed $sum_wr_view_count
     * @property float|mixed $ad_price
     * @property float|mixed $ad_price_rate
     * @property int|mixed $sum_ad_conversions_count
     * @property int|mixed $sum_ad_valid_click_count
     * @property int|mixed $sum_ad_view_count
     * @property int|mixed $sum_ad_cost
     * @property int|mixed $sum_all_ad_cost
     * @property AdminUser $adminInfo
     * @property float|int|mixed $sum_price
     * @property int|mixed $nos_price
     * @property int|mixed $nos_price_unit
     * @property int|mixed $sum_nos_view_count
     */
    class AdminBill extends Model
    {
        use HasDateTimeFormatter;
        use SoftDeletes;

        protected $table = 'admin_bill';

        public function adminInfo(): BelongsTo
        {
            return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
        }
    }
