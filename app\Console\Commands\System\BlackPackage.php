<?php

namespace App\Console\Commands\System;

use App\Models\WechatUser;
use Dcat\EasyExcel\Excel;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class BlackPackage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'BlackPackage';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Excel::export()
            ->chunk(function (int $times) { // $times 表示循环次数，从1开始，可以当做查询页数实用
                // 每次获取1000条数据导入
                $chunkSize = 10000;
                $this->info($times*$chunkSize);

                $data =  WechatUser::query()->where("is_black","<>",0)->orderBy("id")->limit($chunkSize)->offset(($times-1)*$chunkSize)->select("openid")->get();
                if(empty($data)){
                    return null;
                }
                $temp = [];
                foreach($data as $datum){
                    if(empty($datum->openid)){
                        continue;
                    }
                    $temp[]['openid'] = $datum->openid;
                }
                return $temp;
            })
            ->store(Storage::path("WrSystem/".'W-屏蔽人群-'.date("Y-m-d-H-i").".csv"));
        return Command::SUCCESS;
    }
}
