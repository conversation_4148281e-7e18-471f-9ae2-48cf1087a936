<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;

/**
 * @property mixed|string $qr_code_link
 * @property int|mixed    $room_base_id
 * @property mixed|string $room_base_name
 * @property int|mixed    $auto_create_room
 * @property mixed        $qr_code
 * @property mixed        $config_id
 * @property mixed|string $state
 * @property int|mixed    $skip_verify
 * @property mixed|string $qrcode_remark
 * @property int|mixed    $qrcode_scene
 * @property int|mixed    $qrcode_type
 * @property int|mixed    $ww_user_type
 * @property int|mixed    $is_used
 * @property mixed        $corp_auth_id
 * @property mixed|string $corp_id
 * @property mixed        $ww_app_id
 * @property mixed        $admin_uid
 * @property mixed        $ww_user_id
 * @property mixed        $id
 */
class WwUserQrCodesDelete extends Model
{
    use HasDateTimeFormatter;

    protected $table = 'ww_user_qrcodes_delete';

}
