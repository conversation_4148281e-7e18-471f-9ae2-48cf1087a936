<?php

namespace App\Console\Commands\Tads;

use App\Jobs\TencentAd\CountAdAccountHourlyReportJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class GetAdAccountMonitorCount extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'Tads:GetAdAccountMonitorCount';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '广告账户获取监测数据-按天汇总';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $date = date('Y-m-d');

        $hourlyReports = DB::table('ad_account_monitor')
            ->select(DB::raw(
                'date,
                    admin_uid,
                    account_id,
                    ANY_VALUE(conversions_rate) as conversions_rate,
                    SUM(view_count) as view_count,
                    SUM(valid_click_count) as valid_click_count,
                    SUM(ad_cost) as ad_cost,
                    SUM(conversions_count) as conversions_count,
                    SUM(conversions_cost) as conversions_cost,
                    SUM(jump_pv_count) as jump_pv_count,
                    SUM(jump_uv_count) as jump_uv_count,
                    SUM(add_count) as add_count,
                    SUM(open_count) as open_count'
            ))
            ->where('date', $date)
            ->groupBy('date', 'admin_uid', 'account_id')
            ->get();
        //        dd($hourlyReports);
        if ($hourlyReports->isNotEmpty()) {
            $hourlyReports = json_decode(json_encode($hourlyReports), true);
            foreach ($hourlyReports as $reports) {
                CountAdAccountHourlyReportJob::dispatch($reports)->onQueue('count_ad_account_hourly_report');
            }
        }
        return Command::SUCCESS;
    }
}
