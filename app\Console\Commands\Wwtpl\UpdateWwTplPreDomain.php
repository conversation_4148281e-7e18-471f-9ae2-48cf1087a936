<?php

namespace App\Console\Commands\Wwtpl;

use App\Models\WechatUser;
use App\Models\WwTpl;
use App\Services\Tools\QrcodeService;
use Dcat\EasyExcel\Excel;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class UpdateWwTplPreDomain extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'Tpl:UpdateWwTplPreDomain';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '修改落地页的预览二维码';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
       $list = WwTpl::query()->get();
       if($list->isEmpty()){
           return false;
       }
       $domain = 'http://pre.smart-ark.cn';
       foreach($list as $item){
           $fileName ='tpl_pre_qrcode-' . $item->id;
           $preQrcodeUrl = $domain .  '/tpl/preview?tpl_id=' . $item->id . '&token=' . md5($item->id);
           $preQrcode = QrcodeService::createQrcodeFile($fileName, $preQrcodeUrl,1);
           if($preQrcode){
               WwTpl::query()->where('id', $item->id)->update(['pre_qrcode' => $preQrcode]);
               $this->info('落地页模板id：' . $item->id . '，重新生成预览二维码成功');
           }
       }
        return Command::SUCCESS;
    }
}
