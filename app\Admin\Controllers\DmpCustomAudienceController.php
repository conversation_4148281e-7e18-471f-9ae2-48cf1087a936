<?php

	namespace App\Admin\Controllers;

	use App\Admin\Grid\DmpCustomAudience\GetAudiencesButton;
    use App\Admin\Repositories\DmpCustomAudience;
    use App\Models\AdminUser;
    use Dcat\Admin\Form;
    use Dcat\Admin\Grid;
    use Dcat\Admin\Http\Controllers\AdminController;
    use Dcat\Admin\Show;

    class DmpCustomAudienceController extends AdminController
	{
		/**
		 * Make a grid builder.
		 *
		 * @return Grid
		 */
		protected function grid()
		{
			return Grid::make(new DmpCustomAudience(), function (Grid $grid) {
				$grid->disableActions();
				$grid->disableCreateButton();

				$grid->tools(new GetAudiencesButton());

				$grid->model()->orderByDesc("created_time");

				$grid->column('id')->sortable();
				$grid->column('audience_id');
				$grid->column('name')->ClickCopy(10, "");
				$grid->column('owner_admin_uid')->select(AdminUser::query()->pluck("username", "id"));
				$grid->column("auto_push")->switch();
				//				$grid->column('cooperated')->help("深度数据合作，可将您的数据数据从 DMP 平台导出，平台将为您进行定制化的挖掘，进行深度数据合作")->using([0 => '否', 1 => '是']);
				//				$grid->column('type')->using([
				//					'CUSTOMER_FILE' => '号码文件',
				//					'LOOKALIKE'     => '拓展',
				//					'USER_ACTION'   => '用户行为',
				//					'KEYWORD'       => '关键词',
				//					'AD'            => '广告',
				//					'COMBINE'       => '组合',
				//					'LABEL'         => '标签',
				//				]);
				//				$grid->column('source')->using([
				//					'ADVERTISER_OWN_DATA' => '一方',
				//					'TENCENT_DATA'        => '二方',
				//					'UNKNOWN'             => '未知',
				//				]);
				$grid->column('status')->using([
					'PENDING'    => '待处理',
					'PROCESSING' => '处理中',
					'SUCCESS'    => '成功可用',
					'ERROR'      => '错误',
					'FROZEN'     => '冻结',
					'THAWING'    => '解冻中',
					'LOCKING'    => '锁定',
				]);
				$grid->column('online_status')->using([
					'ONLINE'  => '在线',
					'LOADING' => '上线中',
					'OFFLINE' => '不在线',
				])->dot([
					'ONLINE'  => 'success',
					'LOADING' => 'warning',
					'OFFLINE' => 'danger',
				]);
				$grid->column('error_code')->using([
					1   => "表示系统错误",
					101 => "表示种子人群活跃用户低于 2K",
					102 => "表示种子人群无共同特征",
					201 => "表示人群上传的号码包文件格式错误；",
					202 => "表示解析人群上传的号码包文件失败",
					203 => "表示号码包文件人群匹配失败",
				], '无');
				$grid->column('user_count');
				$grid->column('created_time');
				$grid->column('last_modified_time');
				$grid->column('description')->ClickCopy(5, "");
				$grid->filter(function (Grid\Filter $filter) {
					$filter->expand();
					$filter->panel();
					$filter->like("name")->width(2);
					$filter->equal('status')->select([
						'PENDING'    => '待处理',
						'PROCESSING' => '处理中',
						'SUCCESS'    => '成功可用',
						'ERROR'      => '错误',
						'FROZEN'     => '冻结',
						'THAWING'    => '解冻中',
						'LOCKING'    => '锁定',
					])->width(2);
					$filter->equal('online_status')->select([
						'ONLINE'  => '在线',
						'LOADING' => '上线中',
						'OFFLINE' => '不在线',
					])->width(2);
				});
			});
		}

		/**
		 * Make a show builder.
		 *
		 * @param mixed $id
		 *
		 * @return Show
		 */
		protected function detail($id)
		{
			return Show::make($id, new DmpCustomAudience(), function (Show $show) {
				$show->field('id');
				$show->field('account_id');
				$show->field('audience_id');
				$show->field('name');
				$show->field('outer_audience_id');
				$show->field('description');
				$show->field('cooperated');
				$show->field('type');
				$show->field('source');
				$show->field('status');
				$show->field('online_status');
				$show->field('is_own');
				$show->field('error_code');
				$show->field('user_count');
				$show->field('created_time');
				$show->field('last_modified_time');
				$show->field('audience_spec');
				$show->field('created_at');
				$show->field('updated_at');
			});
		}

		/**
		 * Make a form builder.
		 *
		 * @return Form
		 */
		protected function form()
		{
			return Form::make(new DmpCustomAudience(), function (Form $form) {
				$form->display('id');
				$form->text('account_id');
				$form->text('audience_id');
				$form->text('name');
				$form->text('auto_push');
				$form->text('outer_audience_id');
				$form->text('description');
				$form->text('cooperated');
				$form->text('type');
				$form->text('source');
				$form->text('status');
				$form->text('online_status');
				$form->text('is_own');
				$form->text('error_code');
				$form->text('user_count');
				$form->text('created_time');
				$form->text('last_modified_time');
				$form->text('audience_spec');

				$form->display('created_at');
				$form->display('updated_at');
			});
		}
	}
