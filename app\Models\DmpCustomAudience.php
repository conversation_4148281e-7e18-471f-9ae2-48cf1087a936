<?php
	
	namespace App\Models;
	
	use Dcat\Admin\Traits\HasDateTimeFormatter;
	use Illuminate\Database\Eloquent\Model;
	
	/**
	 * @property false|mixed|string $audience_spec
	 * @property mixed              $last_modified_time
	 * @property mixed              $created_time
	 * @property mixed              $user_count
	 * @property mixed              $error_code
	 * @property mixed              $is_own
	 * @property mixed              $online_status
	 * @property mixed              $status
	 * @property mixed              $source
	 * @property mixed              $type
	 * @property mixed              $cooperated
	 * @property mixed              $description
	 * @property mixed              $outer_audience_id
	 * @property mixed              $name
	 * @property mixed              $audience_id
	 * @property mixed              $account_id
	 * @property int|mixed          $owner_admin_uid
	 * @property int|mixed          $auto_push
	 */
	class DmpCustomAudience extends Model
	{
		use HasDateTimeFormatter;
		
		protected $table = 'dmp_custom_audiences';
		
	}
