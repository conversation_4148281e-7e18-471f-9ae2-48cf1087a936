<?php

	namespace App\Admin\Extensions\Tools\WwCorp;

    use App\Models\AdminSubUserAuthCorp;
    use App\Services\Corp\WwCorpApiService;
    use Dcat\Admin\Grid\BatchAction;
    use Illuminate\Http\Request;

    class GetCustomerAcquisitionQuotaTools extends BatchAction
	{

		// 处理请求
		public function handle(Request $request)
		{
            $windId = [];
            $failId = [];
            $keys = $this->getKey();
            foreach ($keys as $key) {
                /** @var AdminSubUserAuthCorp $subUserAuthCorp */
                $subUserAuthCorp = AdminSubUserAuthCorp::query()->with("corpInfo")->find($key);

                $resp = WwCorpApiService::getCustomerAcquisitionQuota($subUserAuthCorp->corpInfo);

                if ($resp['status']) {
                    $windId[] = $subUserAuthCorp->corpInfo->corp_name;
                } else {
                    $failId[] = $subUserAuthCorp->corpInfo->corp_name;
                }
            }
            $successText = '';
            $failText = '';

            if (!empty($windId)) {
                $successText = '<b>= = 成功企业名称 = =</b><br>' . implode('<br>', $windId) . '<br><br>';
            }

            if (!empty($failId)) {
                $failText = '<b>= = 失败企业名称 = =</b><br>' . implode('<br>', $failId) . '<br><br>';
            }

            $response = $this->response()->alert();

            if (!empty($windId) && empty($failId)) {
                // 全部成功
                return $response->success('完成')->detail($successText);
            } elseif (empty($windId) && !empty($failId)) {
                // 全部失败
                return $response->error('全部失败')->detail($failText);
            } elseif (!empty($windId) && !empty($failId)) {
                // 部分成功部分失败
                return $response->warning('部分成功<br>失败的请单独获取')->detail($successText . $failText);
            } else {
                // 无任何数据
                return $response->info('无操作对象')->detail('无任何处理结果');
            }
		}

		// 设置请求参数
		public function parameters()
        {

        }

	}
