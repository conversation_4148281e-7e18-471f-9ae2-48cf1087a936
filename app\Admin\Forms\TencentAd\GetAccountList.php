<?php

    namespace App\Admin\Forms\TencentAd;

    use Admin;
    use App\Jobs\TencentAd\TencentAdTrackClickConfigJob;
    use App\Models\TencentAdAccount;
    use App\Models\AdminUser;
    use App\Services\TenCentAd\DmpService;
    use App\Services\TenCentAd\OauthAccountService;
    use App\Services\Tools\LogService;
    use Dcat\Admin\Contracts\LazyRenderable;
    use Dcat\Admin\Traits\LazyWidget;
    use Dcat\Admin\Widgets\Form;
    use Illuminate\Support\Facades\Log;

    class GetAccountList extends Form implements LazyRenderable
    {
        use LazyWidget;

        public function handle(array $input)
        {
            LogService::inputLog('Tools','腾讯账户管理-拉取子户', $input, Admin::user()->id, Admin::user()->username);
            if (!empty($input['error_msg'])) {
                return $this->response()->error($input['error_msg'])->refresh();
            }

            $subAccountId = $input['sub_account_id'];
            if (str_contains($subAccountId, ",")) {
                $subAccountIds = explode(",", $subAccountId);
            }
            if (str_contains($subAccountId, "，")) {
                $subAccountIds = explode("，", $subAccountId);
            }
            if (str_contains($subAccountId, " ")) {
                $subAccountIds = explode(" ", $subAccountId);
            }
            if (str_contains($subAccountId, PHP_EOL)) {
                $subAccountIds = explode(PHP_EOL, $subAccountId);
            }
            if (!isset($subAccountIds)) {
                $subAccountIds = explode("\r\n", $subAccountId);
            }
            if (empty($subAccountIds)) {
                return $this->response()->error('请填写需要拉取的子户ID，如需拉取全部账户，请联系智投运营进行批量操作')->refresh();
            }

            /** @var TencentAdAccount $accountInfo */
            $accountInfo = TencentAdAccount::query()->find($this->payload['account_data_id']);
            if (!$accountInfo) {
                return $this->response()->error('账户不存在，请退出或刷新后重试')->refresh();
            }
            if (!AdminUser::isAdmin($accountInfo) && !Admin::user()->isRole("wop")) {
                return $this->response()->error('无操作权限')->refresh();
            }
            $accountIdData = [];
            OauthAccountService::organization_account_relation($accountInfo, $accountIdData);
            if (empty($accountIdData)) {
                return $this->response()->error('请检查账户权限，未拉取到任何子户');
            }
            $accountIds = [];
            foreach ($accountIdData as $item) {
                $accountIds[] = $item['account_id'];
            }
            if (AdminUser::isSystemOp() && isset($input['admin_user_id']) && !empty($input['admin_user_id'])) {
                $adminUid = $input['admin_user_id'];
            } else {
                $adminUid = Admin::user()->id;
            }
            $adAccountIds = TencentAdAccount::query()->where("account_id", $accountIds)->pluck("account_id", "account_id")->toArray();
            foreach ($accountIdData as $accountIdDatum) {
                if (in_array($accountIdDatum['account_id'], $adAccountIds)) {
                    continue;
                }
                if (!in_array($accountIdDatum['account_id'], $subAccountIds)) {
                    continue;
                }
                $lastAccount = TencentAdAccount::query()->where("account_id", $accountIdDatum['account_id'])->first();
                if ($lastAccount) {
                    continue;
                }
                $adAccount = new TencentAdAccount();
                $adAccount->account_id = $accountIdDatum['account_id'];
                $adAccount->access_token = $accountInfo->access_token;
                $adAccount->refresh_token = $accountInfo->refresh_token;
                $adAccount->access_token_expires_in = $accountInfo->access_token_expires_in;
                $adAccount->refresh_token_expires_in = $accountInfo->refresh_token_expires_in;
                $adAccount->scope_list = $accountInfo->scope_list;
                $adAccount->account_role_type = $accountInfo->account_role_type;
                $adAccount->wechat_account_id = $accountInfo->wechat_account_id;
                $adAccount->account_type = 'ACCOUNT_TYPE_ADVERTISER';
                $adAccount->role_type = $accountInfo->role_type;
                $adAccount->account_name = $accountInfo->account_name;
                $adAccount->login_name = $accountInfo->login_name;
                $adAccount->admin_uid = $adminUid;
                $adAccount->corporation_name = $accountIdDatum['corporation_name'];
                $adAccount->save();

                DmpService::autoPush($adAccount);
                //创建监测链接
                if($adAccount->account_type == 'ACCOUNT_TYPE_ADVERTISER'){
                    $feedbackName = 'ZTFZ-DN-智投方舟-' . $adAccount->account_id;
                    $secondCategoryType = 'WEB';
                    TencentAdTrackClickConfigJob::dispatch($adAccount, $feedbackName,$secondCategoryType)->onQueue('ad_track_click_config');
                }
            }

            // 返回响应结果并刷新页面
            return $this->response()->success("拉取完成，请刷新列表查看")->refresh();
        }

        public function default()
        {
            return [
                // 展示上个页面传递过来的值
                'account_data_id' => $this->payload['account_data_id'] ?? '',
            ];
        }

        public function form()
        {
            /** @var TencentAdAccount $accountInfo */
            $accountInfo = TencentAdAccount::query()->find($this->payload['account_data_id']);
            if (!$accountInfo) {
                $this->display("提示")->value("账户不存在，请退出或刷新后重试");
                $this->hidden("error_msg")->value("账户不存在，请退出或刷新后重试");
                return;
            }
            if (!AdminUser::isAdmin($accountInfo) && !Admin::user()->isRole("wop")) {
                $this->display("提示")->value("无操作权限");
                $this->hidden("error_msg")->value("无操作权限");
                return;
            }
            if ($accountInfo->account_type == 'ACCOUNT_TYPE_ADVERTISER') {
                $this->display("提示")->value("当前账户就是子户，无子户可拉取");
                $this->hidden("error_msg")->value("当前账户就是子户，无子户可拉取");
                return;
            }
            if ($accountInfo->role_type != 'ROLE_TYPE_ROOT') {
                $this->display("提示")->value("当前权限非广告主主体管理员或者业务单元管理员，无法拉取");
                $this->hidden("error_msg")->value("当前权限非广告主主体管理员或者业务单元管理员，无法拉取");
                return;
            }
            $this->hidden("account_data_id");
            if (AdminUser::isSystemOp()) {
                $this->select("admin_user_id", '拉取到')->options(AdminUser::query()->pluck("username", "id"));
            }
            $this->textarea('sub_account_id', '子户ID')->required()->help("子账户ID，一行一个");
        }
    }
