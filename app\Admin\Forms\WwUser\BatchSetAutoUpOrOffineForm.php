<?php

namespace App\Admin\Forms\WwUser;

use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Models\AdminUser;
use App\Models\WwUser;
use App\Services\Tools\LogService;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class BatchSetAutoUpOrOffineForm extends Form implements LazyRenderable
{
    use LazyWidget;

    // 处理请求
    public function handle(array $input)
    {
        LogService::inputLog('Tools','销售客服管理-自动上下线', $input, Admin::user()->id, Admin::user()->username);
        if(!$input['id']){
            return $this->response()->alert()->error('提示')->detail('请选择销售。');
        }
        // id转化为数组
        $id = explode(',', $input['id'] ?? null);
        if (!$id) {
            return $this->response()->alert()->error('提示')->detail('参数错误。');
        }

        $wwUsers = WwUser::query()->find($id);
        if ($wwUsers->isEmpty()) {
            return $this->response()->alert()->error('提示')->detail('销售不存在。');
        }
        /** @var WwUser $wwUser */
        foreach($wwUsers as $wwUser){
            if(!AdminUser::isAdmin($wwUser)){
                return $this->response()->alert()->error('提示')->detail('无操作权限。');
            }
            $wwUser->auto_status_config = $input['wwUsersData']['auto_status_config'];
            $wwUser->up_time = $input['wwUsersData']['up_time'];
            $wwUser->down_time = $input['wwUsersData']['down_time'];
            $wwUser->down_add_count = $input['wwUsersData']['down_add_count'];
            $wwUser->save();
            switch ($input['wwUsersData']['auto_status_config']) {
                case 0:
                    $action = '关闭';
                    break;
                case 1:
                    $action = '按时间段 ：上线时间：'. $input['wwUsersData']['up_time'] .'，下线时间：' .$input['wwUsersData']['down_time'];
                    break;
                case 2:
                    $action = '按加粉量：上线时间：'. $input['wwUsersData']['up_time'] .'，加粉量：' .$input['wwUsersData']['down_add_count'];
                    break;
                default:
                    $action = '未知';
            }
            //添加操作日志队列
            AdminActionLogJob::dispatch(
                'batch_auto_online',
                $wwUser->id,
                AdminActionLog::ACTION_TYPE['销售'],
                '批量设置自动上下线「' . $wwUser->name  . '」ID：「' . $wwUser->id  . '」修改为->' . $action,
                getIp(),
                Admin::user()->id,
            )->onQueue('admin_action_log_job');
        }
        return $this->response()->alert()->success('提示')->detail('配置成功')->refresh();
    }

    public function form()
    {
        $this->radio('wwUsersData.auto_status_config','自动上下线')->options([
            0=>'关闭',
            1=>'按时间段',
            2=>'按加粉量',
        ])->when([1,2],function($form){
            $form->time('wwUsersData.up_time','上线时间')->format("HH:mm");
        })->when([1],function($form){
            $form->time('wwUsersData.down_time','下线时间')->format("HH:mm")->default(date("H:",time())."00")->help('如果选择按时间段自动上下线，且要设置下线时间，下线时间请大于上线时间。');
        })->when([2],function($form){
            $form->number('wwUsersData.down_add_count','下线粉丝量')->help("当日加够数量大于等于改数量，将会自动下线该销售");
        })->default(0);

        // 批量操作时使用BatchActionPlus 就可以$this->payload['ids'] 获取批量选中的ids
        $this->hidden('id')->value($this->payload['ids'] ?? '');
        $this->confirm('确认提交？');
    }

    // 返回表单数据，如不需要可以删除此方法
    public function default()
    {
        return [
            'password'         => '',
            'password_confirm' => '',
        ];
    }
}
