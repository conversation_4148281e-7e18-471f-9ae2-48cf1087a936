<?php

	namespace App\Console\Commands\System;


    use App\Services\NotifySendService;
    use Illuminate\Console\Command;
	use Illuminate\Support\Facades\DB;
	use Illuminate\Support\Facades\Http;
	use Symfony\Component\Console\Command\Command as CommandAlias;

	class CheckDomain extends Command
	{
		/**
		 * The name and signature of the console command.
		 *
		 * @var string
		 */
		protected $signature = 'CheckDomain';

		/**
		 * The console command description.
		 *
		 * @var string
		 */
		protected $description = 'Command description';

		/**
		 * Execute the console command.
		 *
		 * @return int
		 */
		public function handle()
		{
			$domain = DB::table("page_domain")->where("status", 1)->first();
			if (!$domain) {
				NotifySendService::sendWorkWeixinForError("[超级紧急][立刻通知] '无可用域名！！！！【请立即通知】'");
				return CommandAlias::FAILURE;
			}
			$status = $this->checkOne($domain->host);
			if (!$status) {
				DB::table("page_domain")->where("id", $domain->id)->update([
					'status' => 0
				]);
				//查询这个类型的域名还剩余多少个
				$count = DB::table("page_domain")->where("status", 1)->count();
                NotifySendService::sendWorkWeixinForError($domain->host . '已封禁,剩余：' . $count);
			} else {
				DB::table("page_domain")->where("id", $domain->id)->update([
					'updated_at' => date("Y-m-d H:i:s", time())
				]);
			}
			return CommandAlias::SUCCESS;
		}

		public function checkOne($domain): bool
		{
			$link  = explode("//", $domain)[1];
			$array = [
				'',
				'/asc',
			];
			foreach ($array as $item) {
				$this->info($link . $item);
				$url  = 'https://cgi.urlsec.qq.com/index.php?m=url&a=validUrl&url=' . $link . $item;
				$resp = Http::get($url);
				$data = $resp->json();
				if (isset($data['reCode']) && $data['reCode'] == -202) {
					continue;
				}
				if (isset($data['reCode']) && $data['reCode'] == -203) {
					return false;
				}
				//通知抓紧检查
				NotifySendService::sendWorkWeixinForError($domain . "域名检测结果异常，请查看结果" . json_encode($data, JSON_UNESCAPED_UNICODE));
				return false;
			}

			return true;
		}
	}
