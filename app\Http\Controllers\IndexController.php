<?php

namespace App\Http\Controllers;

use App\Models\AdminUser;
use App\Models\GeneratePage;
use App\Models\WwTpl;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Redirector;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;

class IndexController
{
    public function index(){
        return view('errors.system-error', [
            'message' => '系统错误'
        ]);
    }

    /**
     * 生成页面预览
     * @param Request $request
     * @return Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|void
     */
    public function generatePagePreview(Request $request)
    {

        $id = $request->input("id", "");
        if (!$id) {
            return view('errors.system-error', [
                'message' => '页面不存在-1'
            ]);
        }
        $html = GeneratePage::query()->where("id", $id)->first();
        if (!$html) {
            return view('errors.system-error', [
                'message' => '页面不存在-2'
            ]);
        }
        $addMethod = $request->input("add_method");

        try {

            $data = [
                'id' => '',
                'ww_user_name' => '',
                'qrcode_id' => '',
                'qrcode_url' => env('BASE_QRCODE_URL'),
                'cus_acq_link' => env('BASE_QRCODE_URL'),
                'state' => '',
                'vid' => 0,
                'add_method' => $addMethod,
            ];
            return view("customer.{$html->directory}." . $html->name, $data);
        } catch (\Exception $e) {
            dd($e->getMessage());
            dd('预览失败');
        }

    }


    /**
     * 落地页模板预览
     * @param $id
     * @return Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|void
     */
    public function tplPre(Request $request)
    {
        $data = $request->all();
        if(!$data['tpl_id']){
            return view('errors.system-error', [
                'message' => '参数错误-1'
            ]);
        }
        if(!$data['token']){
            return view('errors.system-error', [
                'message' => '非法请求'
            ]);
        }
        /** @var \App\Models\WwTpl $tpl */
        $tpl = WwTpl::query()->find($data['tpl_id']);
        if(!$tpl){
            return view('errors.system-error', [
                'message' => '落地页不存在'
            ]);
        }
        $data = [
            'id' => '',
            'ww_user_name' => '',
            'qrcode_id' => '',
            'qrcode_url' =>  env('BASE_QRCODE_URL'),
            'cus_acq_link' =>  env('BASE_QRCODE_URL'),
            'state' => '',
            'vid' => 0
        ];
        if ($tpl->type == 1) {
            $data['add_method'] = 1;
        } else {
            $data['add_method'] = 2;
        }
        if (empty($tpl->view_file)) {
            return view('errors.system-error', [
                'message' => '落地页模板文件未配置'
            ]);
        }
        return view($tpl->view_file, $data);
    }

}
