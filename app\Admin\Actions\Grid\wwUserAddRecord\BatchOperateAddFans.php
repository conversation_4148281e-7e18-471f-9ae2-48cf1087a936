<?php

namespace App\Admin\Actions\Grid\wwUserAddRecord;

use App\Admin\Extensions\Grid\BatchActionPlus;
use App\Admin\Forms\wwUserAddRecord\BatchOperateFansForm;

class BatchOperateAddFans extends BatchActionPlus
{
    /**
     * @return string
     */
    public $title = '<button class="btn btn-primary ww_user_batch_btn" >批量操作</button>';
    public $buttonText = '<button class="btn btn-primary ww_user_batch_btn" >批量操作</button>';
    public $warning = '请选择加粉明细!';

    public function form(): BatchOperateFansForm
    {
        // 实例化表单类
        return BatchOperateFansForm::make();
    }
}
