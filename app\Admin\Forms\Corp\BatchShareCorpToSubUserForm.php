<?php

namespace App\Admin\Forms\Corp;

use App\Models\AdminSubUser;
use App\Models\AdminSubUserAuthCorp;
use App\Models\AdminUser;
use App\Services\Tools\LogService;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class BatchShareCorpToSubUserForm extends Form implements LazyRenderable
{
    use LazyWidget;

    // 处理请求
    public function handle(array $input)
    {
        LogService::inputLog('Tools','企微管理-批量-共享给子账户', $input, Admin::user()->id, Admin::user()->username);
        if (!$input['id']) {
            return $this->response()->alert()->error('提示')->detail('请选择数据');
        }
        // id转化为数组
        $subUserCorpIds = explode(',', $input['id']);

        if (!$input['sub_user_ids']) {
            return $this->response()->alert()->error('提示')->detail('请选择要共享的子账户');
        }
        $subUserIds = $input['sub_user_ids'];//选择的子账号ID

        foreach ($subUserCorpIds as $subUserCorpId) {

            $subUserAuthCorp = AdminSubUserAuthCorp::query()->find($subUserCorpId);
            /** 验证共享企业访问权限  success为true时，data为corpInfo */
            $validateSubUserAuthCorp = AdminSubUserAuthCorp::validateSubUserAuthCorpAccess($subUserAuthCorp);
            if (!$validateSubUserAuthCorp['success']) {
                return $this->response()->alert()->error('提示')->detail($validateSubUserAuthCorp['message']);
            }
            $corpInfo = $validateSubUserAuthCorp['data'];

            foreach ($subUserIds as $adminUserId) {
                AdminSubUserAuthCorp::addCorpRecord($adminUserId, $corpInfo,$subUserAuthCorp->auto_label);
            }
        }
        return $this->response()->alert()->success('提示')->detail('操作成功')->refresh();
    }

    public function form()
    {
        if (AdminUser::isSystemOp()) {
            $this->multipleSelect('sub_user_ids', '选择子账户')->options(AdminUser::query()->pluck("username", "id"))->required();
        } else {
            $this->multipleSelect('sub_user_ids', '选择子账户')->options(AdminSubUser::getAdminUsers(Admin::user()->id))->required();
        }
        $this->hidden('id')->attribute('id', 'reset-share_corp_to_sub_user_id');
    }

}
