<?php

    namespace App\Jobs;

    use App\Http\Controllers\WorkWeixin3rd\ChangeExternalContact;
    use App\Models\AdminSubUserAuthCorp;
    use App\Models\AdminUser;
    use App\Models\WwUser;
    use App\Services\Corp\WwCorpApiService;
    use Illuminate\Bus\Queueable;
    use Illuminate\Contracts\Queue\ShouldBeUnique;
    use Illuminate\Contracts\Queue\ShouldQueue;
    use Illuminate\Foundation\Bus\Dispatchable;
    use Illuminate\Queue\InteractsWithQueue;
    use Illuminate\Queue\SerializesModels;
    use Illuminate\Support\Facades\Log;
    use function App\Http\Controllers\WorkWeixin3rd\getLabelId;

    class SetTagsJob implements ShouldQueue
    {
        use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

        protected $message;
        protected $addRecord;

        protected $corpInfo;
        protected $viewWwUserInfoId;

        /**
         * Create a new job instance.
         *
         * @return void
         */
        public function __construct($message, $addRecord, $corpInfo, $viewWwUserInfoId)
        {

            $this->message = $message;
            $this->addRecord = $addRecord;
            $this->corpInfo = $corpInfo;
            $this->viewWwUserInfoId = $viewWwUserInfoId;


        }

        /**
         * Execute the job.
         *
         * @return mixed
         */
        public function handle()
        {
            $obj = new ChangeExternalContact();

            $addRecord = $this->addRecord;
            $corpInfo = $this->corpInfo;
            $message = $this->message;
            $viewWwUserInfoId = $this->viewWwUserInfoId;

            $labelData = [];
            $labelDataFromWords = [];



            //判断投放链接是否有标签
            if (!empty($addRecord->linkInfo->ww_label)) {
                $labels = explode(PHP_EOL, $addRecord->linkInfo->ww_label);//处理ww_link表中企微标签字段
                $labelDataFromWords = array_merge($labelDataFromWords,$labels);
            }
            //判断企业微信主体 是否有标签
            $viewWwUserInfo = WwUser::query()->withTrashed()->where('id', $viewWwUserInfoId)->first();
            $corpAuthInfo = AdminSubUserAuthCorp::query()->find($viewWwUserInfo->corp_auth_id);
            if ($corpAuthInfo && !empty($corpAuthInfo->auto_label)) {
                $labels = explode(PHP_EOL, $corpAuthInfo->auto_label);//处理admin_sub_user_auth_corps表中企自动打标签字段
                $labelDataFromWords = array_merge($labelDataFromWords,$labels);
            }

            //这里判断，如果有客户需要屏蔽页进粉只打销售的标签，把投放链接标签和企微标签置空
            if($addRecord->page_type == 'audit') {
                $adminUids = AdminUser::auditPageOnlySetWwUserTag(); //获取配置了屏蔽页进粉只打销售的标签的客户ID
                if(in_array($addRecord->admin_uid, $adminUids)) {
                    $labelDataFromWords = [];
                }
            }

            //处理企业微信主体、投放链接，两处手动填写的标签
            if (!empty($labelDataFromWords)) {
                // 遍历标签数组，去除空标签和首尾空格
                foreach ($labelDataFromWords as $key => $value) {
                    if (empty(trim($value))) {
                        // 如果为空，则从数组中移除该标签
                        unset($labelDataFromWords[$key]);
                        continue;
                    }
                    // 去除当前标签的首尾空格
                    $labelDataFromWords[$key] = trim($value);
                }
            }
            //处理企业微信主体、投放链接，两处手动填写的标签,通过标签，反向对比，查找标签 ID
            if(!empty($labelDataFromWords)){
                $allLabels = WwCorpApiService::getCorpTagList($corpInfo);
                foreach ($labelDataFromWords as $label) {
                    //调用 getLabelIdInJob函数，从所有标签中查找当前标签对应的标签 ID
                    $labelId = getLabelIdInJob($allLabels, $label);
                    if(!$labelId){
                        $labelData[$labelId][] = $label;
                    }else{
                        $labelData[$labelId] = $label;
                    }

                }
            }

            //查找销售的标签
            if ($viewWwUserInfo && !empty($viewWwUserInfo->ww_corp_label_ids)) {
                //销售标签这里默认就是标签 ID
                $labelIds = json_decode($viewWwUserInfo->ww_corp_label_ids, true);
                $labelName = json_decode($viewWwUserInfo->ww_corp_label_names, true);
                foreach($labelIds as $key=>$wwLabelId){
                    $labelData[$wwLabelId] = $labelName[$key] ?? "";
                }
            }


            //处理最终的 labelData
            if(!empty($labelData)){
                for ($j = 0; $j < 3; $j++) {
                    $addRecordLabel = $addRecord->label;
                    if(empty($addRecordLabel)){
                        $addRecordLabel = [];
                    }else{
                        $addRecordLabel = json_decode($addRecordLabel,true);
                    }

                    $addRecordLabelReq = $addRecord->label_req;
                    if(empty($addRecordLabelReq)){
                        $addRecordLabelReq = [];
                    }else{
                        $addRecordLabelReq = json_decode($addRecordLabelReq,true);
                    }

                    $addRecordLabelResp = $addRecord->label_resp;
                    if(empty($addRecordLabelResp)){
                        $addRecordLabelResp = [];
                    }else{
                        $addRecordLabelResp = json_decode($addRecordLabelResp,true);
                    }

                    $tempLabelIds = array_keys($labelData);
                    $labelIds = [];
                    foreach($tempLabelIds as $k => $labelId){
                        if($labelId){
                            $labelIds[] = $labelId;
                        }
                    }
                    $labelResp = WwCorpApiService::markTag($corpInfo, $message['UserID'], $message['ExternalUserID'], $labelIds);

                    $addRecordLabel[] = $labelData;
                    $addRecordLabelReq[] = [$message['UserID'], $message['ExternalUserID'], $labelIds];
                    $addRecordLabelResp[] = $labelResp;

                    $addRecord->label = json_encode($addRecordLabel,JSON_UNESCAPED_UNICODE);
                    $addRecord->label_req = json_encode($addRecordLabelReq,JSON_UNESCAPED_UNICODE);
                    $addRecord->label_resp = json_encode($addRecordLabelResp,JSON_UNESCAPED_UNICODE);
                    $addRecord->save();
                }
            }

            $obj->edit_external_contact($message);
            return true;
        }
    }
