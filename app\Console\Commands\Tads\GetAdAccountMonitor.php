<?php

    namespace App\Console\Commands\Tads;

    use App\Jobs\TencentAd\AdAccountHourlyReportJob;
    use App\Models\TencentAdAccount;
    use App\Services\TenCentAd\DataService;
    use Illuminate\Console\Command;

    class GetAdAccountMonitor extends Command
    {
        /**
         * The name and signature of the console command.
         *
         * @var string
         */
        protected $signature = 'Tads:GetAdAccountMonitor';

        /**
         * The console command description.
         *
         * @var string
         */
        protected $description = '广告账户获取监测数据';

        /**
         * Execute the console command.
         *
         * @return int
         */
        public function handle()
        {
            $accountList = TencentAdAccount::query()
                ->where('monitor_status', 1)
//                ->where('account_id', '********')
                ->get();
            if ($accountList->isNotEmpty()) {
                $startDate = date("Y-m-d");
                $endDate = date("Y-m-d");

                /** @var TencentAdAccount $account */
                foreach ($accountList as $account) {
//                    $hourlyReportList = DataService::hourlyReports($account,$startDate,$endDate); //获取接口数据
//                    dd($hourlyReportList);
                    AdAccountHourlyReportJob::dispatch($account,$startDate,$endDate)->onQueue('ad_account_hourly_report');
                }
            }
            return Command::SUCCESS;
        }
    }
