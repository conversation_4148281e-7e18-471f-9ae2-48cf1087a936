<?php

namespace App\Admin\Controllers;

use App\Models\WwCorpInfo;
use App\Services\Corp\WwCorpApiService;
use Dcat\Admin\Form;
use Dcat\Admin\Traits\HasUploadedFile;
use EasyWeChat\Kernel\Form\File;
use EasyWeChat\Kernel\Form\Form as EasyWeChatForm;

class FileController
{
    use HasUploadedFile;

    public function handle()
    {
        $disk = $this->disk('local');

        // 判断是否是删除文件请求
        if ($this->isDeleteRequest()) {
            // 删除文件并响应
            return $this->deleteFileAndResponse($disk);
        }

        // 获取上传的文件
        $file = $this->file();
        $corpInfo = WwCorpInfo::query()->where("id", 3)->first();

        $res = WwCorpApiService::uploadImage($corpInfo,$file->getRealPath());
        dd($res);

        // 获取上传的字段名称
        $column = $this->uploader()->upload_column;

        $dir = 'my-images';
        $newName = $column.'-我的文件名称.'.$file->getClientOriginalExtension();

        $result = $disk->putFileAs($dir, $file, $newName);

        $path = "{$dir}/$newName";

        return $result
            ? $this->responseUploaded($path, $disk->url($path))
            : $this->responseErrorMessage('文件上传失败');
    }
}
