<?php /** @noinspection PhpInconsistentReturnPointsInspection */

    namespace App\Admin\Controllers;

    use Admin;
    use App\Admin\Forms\TencentAd\GetAccountList;
    use App\Admin\Forms\TencentAd\HandleOcpxUpFailForm;
    use App\Admin\Grid\TencentAd\AdAccountSwitchAccounts;
    use App\Admin\Repositories\TencentAdAccount;
    use App\Jobs\TencentAd\TencentAdTrackClickConfigJob;
    use App\Models\TencentAdAccount as TencentAdAccountModel;
    use App\Models\AdminSubUser;
    use App\Models\AdminUser;
    use App\Models\WwLink;
    use App\Services\NotifySendService;
    use App\Services\TenCentAd\DmpService;
    use App\Services\TenCentAd\OauthAccountService;
    use App\Services\Tools\UtilsService;
    use Dcat\Admin\Admin as DcatAdmin;
    use Dcat\Admin\Form;
    use Dcat\Admin\Grid;
    use Dcat\Admin\Http\Controllers\AdminController;
    use Dcat\Admin\Layout\Content;
    use Dcat\Admin\Show;
    use Dcat\Admin\Widgets\Alert;
    use Dcat\Admin\Widgets\Modal;
    use Illuminate\Contracts\Pagination\LengthAwarePaginator;
    use Illuminate\Http\Request;
    use Illuminate\Support\Facades\DB;
    use Illuminate\Support\Facades\Redirect;
    use Illuminate\Support\Facades\Session;

    /**
     * @property int $id
     * @property     $input
     */
    class TencentAdAccountController extends AdminController
    {

        private $clientId = '**********';
        private $secret = 'niQHld0Q5idyFreh';
//        private  $callbackUrl =  'https://rich-test.smart-ark.cn/admin/tencent_ad/auth/callback';


        public function createAuthCallBack(Request $request)
        {
//            return view('admin.tencent_ad.notice',['account_id' =>  111111]);
            if (empty(\Dcat\Admin\Admin::user()->id)) {
                return view('admin.ad_account.auth_error',['error_code' => 0,'massage' => '请先登录后台账户后再进行绑定，绑定期间不要更换浏览器。','back_path' => '/ztfz/tencent']);
            }
            $authCode = $request->get("authorization_code", "");
            if (empty($authCode)) {
                return view('admin.ad_account.auth_error',['error_code' => -1,'massage' => '请请退出后重试。','back_path' => '/ztfz/tencent']);
            }
            $resp = OauthAccountService::getAccessToken($authCode);
            if ($resp['code'] != 0) {
                NotifySendService::sendWorkWeixinForError("[系统报错][API调用][createAuthCallBack]用户「" . Admin::user()->id . "」 " . Admin::user()->username . " 的广告账户授权失败，失败原因是：" . $resp['message']);
                return view('admin.ad_account.auth_error',['error_code' => -2,'massage' => '请请退出后重试。','back_path' => '/ztfz/tencent']);
            }
            $authInfo = $resp['data'];
            // 查询当前账户是否存在，如存在，直接更新即可
            /** @var \App\Models\TencentAdAccount $adAccount */
            $adAccount = TencentAdAccountModel::query()->where("account_id", $authInfo['authorizer_info']['account_id'])->first();
            if ($adAccount) {
                $adAccount->scope_list = json_encode($authInfo['authorizer_info']['scope_list'], JSON_UNESCAPED_UNICODE);
                $adAccount->access_token = $authInfo['access_token'];
                $adAccount->refresh_token = $authInfo['refresh_token'];
                $adAccount->access_token_expires_in = date("Y-m-d H:i:s", (time() + $authInfo['access_token_expires_in']));
                $adAccount->refresh_token_expires_in = date("Y-m-d H:i:s", (time() + $authInfo['refresh_token_expires_in']));
                $adAccount->save();
                return view('admin.tencent_ad.notice',['account_id' =>  $adAccount->account_id]);
            }
            // 如果账户不存在，获取账户信息
            $adAccount = new TencentAdAccountModel();
            $adAccount->account_id = $authInfo['authorizer_info']['account_id'];
            $adAccount->access_token = $authInfo['access_token'];
            $adAccount->refresh_token = $authInfo['refresh_token'];
            $adAccount->access_token_expires_in = date("Y-m-d H:i:s", (time() + $authInfo['access_token_expires_in']));
            $adAccount->refresh_token_expires_in = date("Y-m-d H:i:s", (time() + $authInfo['refresh_token_expires_in']));
            $adAccount->scope_list = json_encode($authInfo['authorizer_info']['scope_list'], JSON_UNESCAPED_UNICODE);
            $adAccount->account_role_type = $authInfo['authorizer_info']['account_role_type'];
            $adAccount->wechat_account_id = $authInfo['authorizer_info']['wechat_account_id'];
            $adAccount->account_type = $authInfo['authorizer_info']['account_type'];
            $adAccount->role_type = $authInfo['authorizer_info']['role_type'];
            $adAccount->account_name = $authInfo['authorizer_info']['account_name'];
            $adAccount->login_name = $authInfo['authorizer_info']['login_name'];
            $adAccount->admin_uid = Admin::user()->id;

            $accountInfoResp = OauthAccountService::advertiserGet($adAccount);
            if ($accountInfoResp['code'] != 0) {
                //				NotifySendService::sendWorkWeixinForError("[系统报错][API调用][createAuthFromWind]用户「" . Admin::user()->id . "」 " . Admin::user()->username . " 的广告账户授权失败，失败原因是：" . $accountInfoResp['message']);
                //				return '授权失败-3，请退出后重试';
            } else {
                $accountInfo = $accountInfoResp['data']['list'][0];
                $adAccount->corporation_name = $accountInfo['corporation_name'];
                $adAccount->corporate_image_name = $accountInfo['corporate_image_name'] ?? "";
                $adAccount->corporate_image_logo = $accountInfo['corporate_image_logo'] ?? "";
                $adAccount->certification_image = $accountInfo['certification_image'];
                $adAccount->system_industry_id = $accountInfo['system_industry_id'];
                $adAccount->mdm_name = $accountInfo['mdm_name'];
                $adAccount->agency_account_id = $accountInfo['agency_account_id'];
                $adAccount->operators = json_encode($accountInfo['operators']);
                $adAccount->memo = $accountInfo['memo'] ?? "";
            }
            $adAccount->save();
            DmpService::autoPush($adAccount);
            //创建监测链接
            $feedbackName = 'ZTFZ-DN-智投方舟-' . $adAccount->account_id;
            $secondCategoryType = 'WEB';
            TencentAdTrackClickConfigJob::dispatch($adAccount, $feedbackName,$secondCategoryType)->onQueue('tencent_ad_track_click_config');
            Session::flash("ad_account_auth_status", 1);
            return Redirect::to('/'.UtilsService::getConfigRoutePrefix().'/tencent');
        }

        public function index(Content $content)
        {
            $auth_status = Session::get("ad_account_auth_status", '');

            $c = $content
                ->translation($this->translation())
                ->title($this->title())
                ->description($this->description()['index'] ?? trans('admin.list'));
            if (in_array($auth_status, [0, 1])) {
                if ($auth_status === 1) {
                    $alert = Alert::make('授权成功，请查看列表确认');
                    $alert->success();
                } else {
                    $alert = Alert::make('授权失败，请重试，如果多次失败，请联系系统运营');
                    $alert->warning();
                }
                $alert->removable();
                $c->row($alert);
            }
            return $c->body($this->grid());
        }

        /**
         * Make a grid builder.
         *
         * @return Grid
         */
        protected function grid()
        {

            if (Admin::user()->isAdministrator()) {
                $adAccountIds = AdminUser::query()->pluck("username", 'id')->toArray();
            } elseif (\Dcat\Admin\Admin::user()->isRole("wop")) {
                $adAccountIds = AdminUser::query()->pluck("username", 'id')->toArray();
            } else {
                $adAccountIds = AdminUser::query()->whereIn("id", AdminSubUser::getAdminUids(\Dcat\Admin\Admin::user()->id))->pluck("username", 'id')->toArray();
            }
            return Grid::make(new TencentAdAccount(['adminInfo','industryInfo']), function (Grid $grid) use ($adAccountIds) {
                if (!AdminUser::isSystemOp()) {
                    $grid->model()->whereIn("admin_uid", AdminSubUser::getAdminUids(Admin::user()->id));
                }
//                $grid->paginate(AdminUser::getPaginate());
                $grid->model()->select("id","corporation_name","account_id","mdm_name","admin_uid","account_type",'system_industry_id',"created_at")->orderByDesc('id');
                $grid->disableCreateButton();
                $grid->disableActions();
                $callbackUrl =  env('APP_URL')  . '/ztfz/tencent_ad/auth/callback';
                $authUrl = 'https://developers.e.qq.com/oauth/authorize?client_id='.$this->clientId.'&redirect_uri='.urlencode($callbackUrl) . "&state=wind_rich";
                $grid->tools([
                    new AdAccountSwitchAccounts(),
                    "<a href=$authUrl  target='_blank' class='btn btn-primary disable-outline float-right' style='color: white;margin-left: 5px'><i class='feather icon-plus-square'></i>绑定腾讯广告账户</a>",
                    
                ]);
                if (Admin::user()->isRole("wop")) {
                    $grid->disableBatchDelete();
                }
//                $grid->column('id')->sortable();
                $grid->column('corporation_name', '企业名称')->display(function ($value) {
                    return empty($value) ? "无" : $value;
                });
                $grid->column('account_id', "账户ID");

                $grid->column('mdm_name', '主体名称')->display(function ($value) {
                    return empty($value) ? "无" : $value;
                })->editable();
                $grid->column("admin_uid", '操作账号')->select($adAccountIds)->help("广告账户授权后可迁移至其他账号，如A账户已授权，可以分配至B账号，账号将自动转移，相应的广告账户下如果有企微投放链接，会同步转移到 B 账号");
                $grid->column("account_type", "账户类型")->using([
                    'ACCOUNT_TYPE_ADVERTISER' => '广告账户',
                    'ACCOUNT_TYPE_BM' => 'BM账户',
                    'ACCOUNT_TYPE_AGENCY' => '代理商账户',
                    'ACCOUNT_TYPE_DATA_NEXUS' => 'DN账户',
                    'ACCOUNT_TYPE_DSP' => 'DSP账户',
                    'ACCOUNT_TYPE_TDC' => 'TDC账户',
                    'ACCOUNT_TYPE_TONE' => 'T1账户',
                ]);
                $grid->column('拉取子账户')->display('拉取子账户')->label()->modal(function (Grid\Displayers\Modal $modal) {
                    // 标题
                    $modal->title('拉取子账户');
                    // 自定义图标
                    $modal->icon('');// feather icon-check-circle
                    $modal->xl();
                    // 传递当前行字段值
                    $data = [
                        'account_data_id' => $this->id
                    ];
                    return GetAccountList::make()->payload($data);
                });
                if(AdminUser::isSystemOp()){
//                    $grid->column('ID');
                    $grid->column('industryInfo.name', "行业类型");
                }
                $grid->column('created_at', '绑定时间');
                if(AdminUser::isSystemOp()){
                    $grid->tools(function (Grid\Tools $tools) {
                        $modal = Modal::make()
                            ->lg()
                            ->title('处理账户无权限回传失败')
                            ->body(HandleOcpxUpFailForm::make())
                            ->button('<button class="btn btn-primary" ><i class="feather icon-aperture"></i>&nbsp;处理账户无权限回传失败</button>');
                        $tools->append($modal);
                    });
                }
                $grid->filter(function (Grid\Filter $filter) {
                    $filter->expand();
                    $filter->panel();
                    $filter->where('account_id', function ($query) {
                        $keyWord = $this->input;
                        if (str_contains($keyWord, ",")) {
                            $keyWords = explode(",", $keyWord);
                        }
                        if (str_contains($keyWord, "，")) {
                            $keyWords = explode("，", $keyWord);
                        }
                        if (str_contains($keyWord, " ")) {
                            $keyWords = explode(" ", $keyWord);
                        }
                        if (!isset($keyWords)) {
                            $keyWords = [$keyWord];
                        }
                        $query->whereIn("account_id", $keyWords);
                    }, '账户ID')->width(2);

                    if (\Dcat\Admin\Admin::user()->parent_id == 0) { // 主账号
                        if (AdminUser::isSystemOp()) {
                            $filter->equal("admin_uid", '操作账号')->select(AdminUser::query()->pluck("username", 'id')->toArray())->width(2);
                        } else {
                            $adminIds = AdminSubUser::getALLAdminUids(\Dcat\Admin\Admin::user()->id);
                            $filter->equal("admin_uid", '操作账号')->select(AdminUser::query()->whereIn("id", $adminIds)->pluck("username", 'id')->toArray())->width(2);
                        }

                    }

//                    if(AdminUser::isSystemOp()){
//                        $filter->equal('id','ID')->width(2);
//                    }
                    $filter->like('corporation_name', '企业名称')->width(2);
                    $filter->like('mdm_name', '主体名称')->width(2);
//                    $filter->like('system_industry_id', '行业ID')->width(2);
                });
            });
        }

        /**
         * Make a show builder.
         *
         * @param mixed $id
         *
         * @return Show
         */
        protected function detail($id)
        {
            return Show::make($id, new TencentAdAccount(), function (Show $show) {
                if (!AdminUser::isAdmin($show->model())) {
                    $show->field('N')->value("无数据");
                } else {
                    $show->field('id');
                    $show->field('corporation_name');
                    $show->field('account_id');
                }
            });
        }

        /**
         * Make a form builder.
         *
         * @return Form
         */
        protected function form()
        {
            return Form::make(new TencentAdAccount(), function (Form $form) {
                if (!$form->isCreating() && !AdminUser::isAdmin($form->model()) && !AdminUser::isSystemOp()) {
                    $form->display('N')->value("无数据");
                } else {
                    $form->display('id');
                    $form->text('corporation_name');
                    $form->text('mdm_name');
                    $form->text('account_uin');
                    $form->text('account_id');
                    $form->text('admin_uid');
                    $form->saving(function (Form $form) {
                        if ($form->input("admin_uid")) {
                            if (!$form->isCreating() && !AdminUser::isAdmin($form->model())  && !AdminUser::isSystemOp()) {
                                return $form->response()->alert()->error('提示')->detail('无权限操作');
                            }
                            // 检验该账户是否存在链接，如存在链接，不允许迁移
                            $wwLinks = WwLink::query()->where("account_id", $form->model()->account_id)->get();
                            if ($wwLinks->isNotEmpty()) {
                                if (AdminUser::isSystemOp()) {
                                    return $form->response()->alert()->error('无法迁移，当前账号存在使用中的微信投放链接，请删除后再转移');
                                }
                                /** @var WwLink $wwLink */
                                foreach($wwLinks as $wwLink){
                                    $wwLink->admin_uid = $form->input("admin_uid");
                                    $wwLink->save();
                                }
                            }
                        }
                    });
                    $form->display('created_at');
                    $form->display('updated_at');
                }
            });
        }

        public function destroy($id)
        {
            $data = TencentAdAccountModel::query()->whereIn("id", explode(",", $id))->get();
            foreach ($data as $datum) {
                if (!AdminUser::isAdmin($datum)) {
                    return $this->form()->response()->alert()->error('提示')->detail('无权限操作');
                }
                $wwLink = WwLink::query()->where("account_id",$datum->account_id)->first();
                if($wwLink){
                    return $this->form()->response()->alert()->error('提示')->detail('账户存在投放链接，无法删除');
                }
            }
            return $this->form()->destroy($id);
        }

        public function getAdAccount(Request $request): LengthAwarePaginator
        {
            $keyword = $request->get("q", 0);
            return TencentAdAccountModel::query()
                ->where(function ($query) use ($keyword) {
                    $query->where('account_id', 'like', "{$keyword}%")->orWhere("account_name", 'like', "{$keyword}%");
                })
                ->where('admin_uid', Admin::user()->id)
                ->select('account_id as id', DB::raw("concat(account_id,'-',account_name) as text"))
                ->paginate(null, ['account_id as id', 'account_id as text']);
            // 限制返回数量
        }


        public function getAdAccountNoLink(Request $request): LengthAwarePaginator
        {
            $wwLinkAccountIds = WwLink::query()->where("admin_uid", Admin::user()->id)->pluck("account_id")->toArray();
            $wwLinkAccountIds = array_filter($wwLinkAccountIds);
            $keyword = $request->get("q", 0);
            return TencentAdAccountModel::query()
                ->where(function ($query) use ($keyword) {
                    $query->where('account_id', 'like', "{$keyword}%")->orWhere("account_name", 'like', "{$keyword}%");
                })
                ->whereNotIn("account_id",$wwLinkAccountIds)
                ->where('admin_uid', Admin::user()->id)
                ->select('account_id as id', DB::raw("concat(account_id,'-',account_name) as text"))
                ->paginate(null, ['account_id as id', 'account_id as text']);
            // 限制返回数量
        }


        /**
         * 搜索腾讯广告账户
         * @param Request $request
         * @return LengthAwarePaginator
         */
        public function searchTencentAdAccount(Request $request): LengthAwarePaginator
        {
            $keyword = $request->get("q", 0);
            $noLink = $request->get("no_link", 0);
        
            return TencentAdAccountModel::query()
                ->when(!AdminUser::isSystemOp(), function ($query){
                    /** 如果不是系统运营超管 查询自己 */
                    $query->where('admin_uid', Admin::user()->id);
                })
                ->when($noLink, function ($query) {
                    $wwLinkAccountIds = WwLink::query()->where("admin_uid", DcatAdmin::user()->id)->pluck("account_id")->toArray();
                    $wwLinkAccountIds = array_filter($wwLinkAccountIds);
                    $query->whereNotIn("account_id", $wwLinkAccountIds);
                })
                // 如果when 中有 orWhere 那么最好包一个闭包查询  不然会影响外部的where 条件
                ->when($keyword, function ($query) use ($keyword) {
                    return $query->where(function ($subQuery) use ($keyword) {
                        $subQuery->where('account_id', 'like', "%{$keyword}%")
                                ->orWhere('account_name', 'like', "%{$keyword}%");
                    });
                })
                ->select('account_id as id', DB::raw("concat(account_id,'-',account_name) as text"))
                ->paginate(null, ['account_id as id', 'account_id as text']);
            // 限制返回数量
        }
    }
