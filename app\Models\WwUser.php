<?php

namespace App\Models;

use App\Jobs\AdminActionLogJob;
use App\Jobs\WwUser\ReSetWwUserTodayShowCountJob;

use App\Server\WwCorpApi;
use App\Services\Corp\WwCorpApiService;
use App\Services\NotifySendService;
use App\Services\ProviderService;
use App\Services\System\SpeedLogService;
use Dcat\Admin\Admin;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Dcat\Admin\Widgets\Tooltip;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Laravel\Scout\Searchable;

/**
 * @property mixed $id
 * @property int|mixed $subscribe
 * @property mixed $down_add_count
 * @property mixed $down_time
 * @property mixed $up_time
 * @property mixed $auto_status_config
 * @property mixed|string $license_b_ex_time
 * @property mixed|string $license_c_ex_time
 * @property mixed|string $license_b_status
 * @property mixed|string $license_c_status
 * @property mixed|string $license_c_json
 * @property mixed|string $license_b_json
 * @property int|mixed $cus_acq_link_status
 * @property mixed|string $wind_label
 * @property mixed $weight
 * @property mixed|string $qrcode
 * @property mixed|string $qrcode_config_id
 * @property mixed|string $status
 * @property mixed|string $alias
 * @property mixed $name
 * @property mixed $user_id
 * @property mixed $open_user_id
 * @property int|mixed $type
 * @property mixed $corp_auth_id
 * @property mixed|string $corp_id
 * @property mixed $ww_app_id
 * @property mixed $admin_uid
 * @property int|mixed $cus_acq_link_skip_verify
 * @property mixed $cus_acq_link_url
 * @property mixed $cus_acq_link_name
 * @property mixed $cus_acq_link_id
 * @property mixed $cus_acq_link_status_message
 * @property mixed $online_status
 * @property mixed $add_method
 * @property mixed $today_show_count
 * @property WwCorpInfo $corpInfo
 * @property mixed $today_add_count
 * @property mixed $welcome_message
 * @property mixed $ww_corp_label_ids
 * @property mixed $ww_corp_label_names
 * @property int $skip_verify
 */
class WwUser extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;
    use Searchable;

    protected $table = 'ww_users';


    const LICENSE_C_STATUS = [
        '0' => '无效',
        '1' => '有效',
        '2' => '预警',
    ];

    /**
     * 获取未使用二维码数量
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function notUseQrCode()
    {
        return $this->hasMany(WwUserQrcode::class, 'ww_user_id', 'id')
            ->where('is_used', 0);
    }

    /**
     * 获取屏蔽销售
     * @param WwLink $wwLink
     * @param $ww_group_id
     * @return bool|array
     */
    public static function getShieldUserFromGroup(WwLink $wwLink, $ww_group_id,$linkViewRecord): bool|array
    {
        $wwUserGroup = WwUsersGroup::query()->with(['wwUsers', 'wwUsers.corpInfo'])->find($ww_group_id);
        if ($wwUserGroup) {
            $wwUsers = $wwUserGroup->wwUsers;
        } else {
            $wwUsers = WwUser::query()->where("id", 0)->get();
        }
        if ($wwUsers->isNotEmpty()) {
            $weightRate = 99999;
            /** @var WwUser $user */
            foreach ($wwUsers as $user) {
                // 防止出现配置的销售，是其他人授权的企业微信
                $corpIds = AdminSubUserAuthCorp::query()->whereIn("admin_uid", AdminSubUser::getALLAdminUids($wwLink->admin_uid))->pluck("corp_id")->toArray();
                if (!in_array($user->corp_id, $corpIds)) {
                    continue;
                }
                if ($user->online_status != 1 || $user->weight == 0) {
                    continue;
                }
                if ($user->add_method == 1 && ($user->cus_acq_link_id == -1 || empty($user->cus_acq_link_id))) {
                    continue;
                }
                if ($user->add_method == 2) {
                    if (!WwUserQrcode::query()
                        ->where("is_used", 0)
                        ->where("ww_user_id", $user->id)
                        ->where("skip_verify", $user->skip_verify)
                        ->exists()) {
                        continue;
                    }
                }
                $tempWeightRate = $user->today_show_count / $user->weight;
                if ($weightRate > $tempWeightRate) {
                    $weightRate = $tempWeightRate;
                    $wwUserInfo = $user;
                }
            }
        }
        if (isset($wwUserInfo)) {
            $wwUserInfo->today_show_count++;// 今日展示次数+1
            $wwUserInfo->save();
            // 如果是二维码模式，寻找对应的二维码
            if ($wwUserInfo->add_method == 2) {
                /** @var WwUserQrcode $wwUserQrcode */
                $wwUserQrcode = WwUserQrcode::query()->where("is_used", 0)->where("ww_user_id", $wwUserInfo->id)->where("skip_verify", $wwUserInfo->skip_verify)->first();
                if (!$wwUserQrcode) {
                    NotifySendService::sendWorkWeixinForError("[广告页面][屏蔽销售][未获取到二维码-1] " . "「" . $wwLink->adminInfo->username . "」用户的「" . $wwLink->id . "」链接，账户ID「" . $wwLink->account_id . "」，销售ID：【" . $wwUserInfo->id . "】，销售名称：【" . $wwUserInfo->name . "】，未获取到二维码，屏蔽销售分组是「" . (($wwLink->wwUserGroup) ? $wwLink->wwUserGroup->title : $wwLink->ww_user_group_id) . "」");
                    return false;
                }
                $wwUserQrcode->is_used = 1;
                $wwUserQrcode->save();
                return [
                    'id' => $wwUserInfo->id,
                    'add_method' => $wwUserInfo->add_method,
                    'ww_user_name' => $wwUserInfo->name, //(!empty($wwUserInfo->alias)?"「".$wwUserInfo->alias."」":"")
                    'qrcode_id' => $wwUserQrcode->id,
                    'qrcode_url' => $wwUserQrcode->qr_code,
                    'cus_acq_link' => '',
                    'state' => $wwUserQrcode->state,
                    'vid'  => $linkViewRecord->id ?? -1,
                    'corp_id' => $wwUserInfo->corp_id,
                    'corp_auth_id' => $wwUserInfo->corp_auth_id,
                    'admin_uid' => $wwUserInfo->admin_uid ?? 0,
                ];
            } else {
                $state = getLinkState();
                return [
                    'id' => $wwUserInfo->id,
                    'add_method' => $wwUserInfo->add_method,
                    'ww_user_name' => $wwUserInfo->name, //(!empty($wwUserInfo->alias)?"「".$wwUserInfo->alias."」":"")
                    'qrcode_id' => -1,
                    'qrcode_url' => '',
                    'cus_acq_link' => $wwUserInfo->cus_acq_link_url . "?customer_channel=" . $state,
                    'state' => $state,
                    'vid'  => $linkViewRecord->id ?? -1,
                    'corp_id' => $wwUserInfo->corp_id,
                    'corp_auth_id' => $wwUserInfo->corp_auth_id,
                    'admin_uid' => $wwUserInfo->admin_uid ?? 0,
                ];
            }
        } else {
            return false;
        }
    }

    /**
     * 获取投放销售
     * @param WwLink $wwLink
     * @param $ww_group_id
     * @param LinkViewRecord $linkViewRecord
     * @return bool|array
     */
    public static function getUserFromGroup(WwLink $wwLink, $ww_group_id, LinkViewRecord $linkViewRecord): bool|array
    {
//        SpeedLogService::info("00026+1");//性能日志，单独做记录使用
        $viewCountInc = true;
        // 查看该用户是否有展示过销售，如展示过，仅展示当前
        if ($linkViewRecord->ww_user_id) {
            $wwUserInfo = WwUser::query()->find($linkViewRecord->ww_user_id);
            $viewCountInc = false;
        }
//        SpeedLogService::info("00026+2");//性能日志，单独做记录使用
        //获取展示销售的策略
        $showWwUserPolicy = WwLink::getShowWwUserPolicy($wwLink);
        $linkViewRecord->deep = $showWwUserPolicy;
        $linkViewRecord->save();
//        SpeedLogService::info("00026+3");//性能日志，单独做记录使用
        // 同一个主体同一个用户访问，7天内展示同一个销售
        if ($showWwUserPolicy == 2 && (!isset($wwUserInfo) || !$wwUserInfo)) {

            $days = 7;
            $adAccountInfo = $wwLink->adAccountInfo;
            if ($adAccountInfo) {
                $accountIds = TencentAdAccount::query()->where("corporation_name", $adAccountInfo->corporation_name)->pluck("account_id");
                $wwLinkIds = WwLink::query()->where("admin_uid", $linkViewRecord->admin_uid)->whereIn("account_id", $accountIds)->pluck("id");

                /** @var LinkViewRecord $lastViewRecord */
                $lastViewRecord = LinkViewRecord::query()
                    ->whereIn("ww_link_id", $wwLinkIds)
                    ->where("user_id", $linkViewRecord->user_id)
                    ->whereNotNull("ww_user_id")
                    ->where("created_at", ">", date("Y-m-d", time() - 86400 * $days) ." 00:00:00")
                    ->orderByDesc("id")
                    ->first();
//                dd($lastViewRecord);
                if ($lastViewRecord && $lastViewRecord->ww_user_id) {
                    /** @var WwUser $wwUserInfo */
                    $wwUserInfo = WwUser::query()->find($lastViewRecord->ww_user_id);
                    $viewCountInc = false;
                    if ($wwUserInfo) {
                        if ($wwUserInfo->online_status != 1 || $wwUserInfo->weight == 0) {
                            $wwUserInfo = false;
                            $viewCountInc = true;
                        }
                    }
                }
            }
        }
//        SpeedLogService::info("00026+4");//性能日志，单独做记录使用
        //策略3：同一个 IP 1 天内，除非原销售被删除，或不在线，或权重为0 则寻找新的销售,不然仅能看到同一个销售
        if ($showWwUserPolicy == 3 && (!isset($wwUserInfo) || !$wwUserInfo)) {
            $ip = $linkViewRecord->ip;
            /** @var LinkViewRecord $lastViewRecord */
            $lastViewRecordQuery = LinkViewRecord::query()
                ->where("admin_uid", $linkViewRecord->admin_uid)
                ->where("ip", $ip)
                ->whereNotNull("ww_user_id")
                ->orderByDesc("id");

            $lastViewRecord = $lastViewRecordQuery
                ->where("created_at", ">", date("Y-m-d", time()) . " 00:00:00")
                ->first();

            if ($lastViewRecord) {
                $viewCountInc = false;
                /** @var WwUser $wwUserInfo */
                $wwUserInfo = WwUser::query()->find($lastViewRecord->ww_user_id);

                if ($wwUserInfo) {
                    if ($wwUserInfo->online_status != 1 || $wwUserInfo->weight == 0) {
                        $wwUserInfo = false;
                        $viewCountInc = true;
                    }
                }
            }
        }
//        SpeedLogService::info("00026+5");//性能日志，单独做记录使用
        if (!isset($wwUserInfo) || !$wwUserInfo) {
            $wwUserGroup = WwUsersGroup::query()->with(['wwUsers', 'wwUsers.corpInfo'])->find($ww_group_id);
            if ($wwUserGroup) {
                $wwUsers = $wwUserGroup->wwUsers->shuffle();;
            } else {
                $wwUsers = WwUser::query()->where("id", 0)->get();
            }
            if ($wwUsers->isNotEmpty()) {
                $weightRate = 99999;
                // 防止出现配置的销售，是其他人授权的企业微信
                $corpIds = AdminSubUserAuthCorp::query()->whereIn("admin_uid", AdminSubUser::getALLAdminUids($wwLink->admin_uid))->pluck("corp_id")->toArray();

                /** @var WwUser $user */
                foreach ($wwUsers as $user) {
                    if (!in_array($user->corp_id, $corpIds)) {
                        continue;
                    }
                    if ($user->online_status != 1 || $user->weight == 0) {
                        continue;
                    }
                    if ($user->add_method == 1 && ($user->cus_acq_link_id == -1 || empty($user->cus_acq_link_id))) {
                        continue;
                    }
                    if ($user->add_method == 2 && $user->type != 3) {
                        if (!WwUserQrcode::query()->where("is_used", 0)->where("ww_user_id", $user->id)->where("skip_verify", $user->skip_verify)->exists()) {
                            continue;
                        }
                    }
                    $tempWeightRate = $user->today_show_count / $user->weight;
                    if ($weightRate > $tempWeightRate) {
                        $weightRate = $tempWeightRate;
                        $wwUserInfo = $user;
                    }
                }
            }
        }

        //策略4：同一个 IP 1 自然日内，展示过该企微分组下的某一个销售后，
        //自然日内锁死该销售，除非销售被删除，或下线，获取权重为0，不然不展示新的销售。
        //$wwUserInfo 为新找到的销售
        if ($showWwUserPolicy == 4 && (isset($wwUserInfo) && $wwUserInfo)) {
            //获取用户的IP
            $ip = $linkViewRecord->ip;
            //获取当前需要展示销售的企微分组
            $corpGroupName = AdminSubUserAuthCorp::withTrashed()
                ->where('id', $wwUserInfo->corp_auth_id)
                ->value("corp_group_name");

            //查询分组下所有的企微IDS
            if (!empty($corpGroupName)) {
                $corpGroupIds = AdminSubUserAuthCorp::query()
                    ->where("admin_uid", $wwUserInfo->admin_uid)
                    ->where("corp_group_name", $corpGroupName)
                    ->pluck("corp_id")
                    ->toArray();
            } else {
                $corpGroupIds = [$wwUserInfo->corp_id];
            }
            //查询今日访问记录内，是否存在上一步查询出的企微IDS下的销售展示记录
            /** @var LinkViewRecord $lastViewRecord */
            $lastViewRecordQuery = LinkViewRecord::query()
                ->where("admin_uid", $linkViewRecord->admin_uid)
                ->where("ip", $ip)
                ->whereNotNull("ww_user_id")
                ->orderByDesc("id")
                ->whereIn("corp_id", $corpGroupIds);

            $lastViewRecord = $lastViewRecordQuery
                ->where("created_at", ">", date("Y-m-d", time()) . " 00:00:00")
                ->first();
            //如果今天存在相应的访问记录，判断记录内的销售是否正常
            if ($lastViewRecord) {
                $viewCountInc = false;
                //判断销售是否还存在，未删除
                /** @var WwUser $oldWwUser */
                $oldWwUser = WwUser::query()->find($lastViewRecord->ww_user_id);

                if ($oldWwUser) {
                    //判断销售是否下线或者权重为0，如果是，那么不使用该销售
                    if ($oldWwUser->online_status == 1 && $oldWwUser->weight > 0) {
                        $wwUserInfo = $oldWwUser;
                    } else {
                        $viewCountInc = true;
                    }
                }
            }
        }

        //策略5：同一个用户当天内，展示过该企微分组下的某一个销售后，
        //当天内锁死该销售，除非销售被删除，或下线，或权重为0，不然不展示新的销售。
        if ($showWwUserPolicy == 5 && (isset($wwUserInfo) && $wwUserInfo)) {
            //获取当前需要展示销售的企微分组
            $corpGroupName = AdminSubUserAuthCorp::withTrashed()
                ->where('id',$wwUserInfo->corp_auth_id)
                ->value("corp_group_name");

            //查询分组下所有的企微IDS
            if(!empty($corpGroupName)){
                $corpGroupIds = AdminSubUserAuthCorp::query()
                    ->where("admin_uid",$wwUserInfo->admin_uid)
                    ->where("corp_group_name",$corpGroupName)
                    ->pluck("corp_id")
                    ->toArray();
            }else{
                $corpGroupIds = [$wwUserInfo->corp_id];
            }
            //查询今日访问记录内，是否存在上一步查询出的企微IDS下的销售展示记录
            /** @var LinkViewRecord $lastViewRecord */
            $lastViewRecordQuery = LinkViewRecord::query()
                ->where("admin_uid", $linkViewRecord->admin_uid)
                ->where("user_id", $linkViewRecord->user_id)
                ->whereIn("corp_id", $corpGroupIds)
                ->whereNotNull("ww_user_id")
                ->orderByDesc("id");

            //TODO 如果某个客户需要定制天数
            $days = 0;
            if ($linkViewRecord->admin_uid == 999999) {
                $days = 11;//
            }
            $lastViewRecord = $lastViewRecordQuery
                ->where("created_at", ">", date("Y-m-d", time() - 86400 * $days)." 00:00:00")
                ->first();
            //如果今天存在相应的访问记录，判断记录内的销售是否正常
            if ($lastViewRecord) {
                $viewCountInc = false;
                //判断销售是否还存在，未删除
                /** @var WwUser $oldWwUser */
                $oldWwUser = WwUser::query()->find($lastViewRecord->ww_user_id);

                if ($oldWwUser) {
                    //判断销售是否下线或者权重为0，如果是，那么不使用该销售
                    if ($oldWwUser->online_status == 1 && $oldWwUser->weight > 0) {
                        $wwUserInfo = $oldWwUser;
                    }else{
                        $viewCountInc = true;
                    }
                }
            }
        }


//        SpeedLogService::info("00026+6");//性能日志，单独做记录使用
        if (isset($wwUserInfo) && $wwUserInfo) {
            // 查看该用户今日是否有添加过销售
            /** @var WwUserAddRecord $addRecord */
            $addRecord = WwUserAddRecord::query()
                ->where("user_id", $linkViewRecord->user_id)
                ->where('corp_id', $wwUserInfo->corp_id)
                ->where('admin_uid', $linkViewRecord->admin_uid)
                ->where("created_at", ">", date("Y-m-d"))
                ->first();
            SpeedLogService::info("00026+7");//性能日志，单独做记录使用
            if ($addRecord) {
                $lastWwUser = WwUser::query()->find($addRecord->ww_user_id);
                if ($lastWwUser) {
                    $wwUserInfo = $lastWwUser;
                }
                $viewCountInc = false;
            }
            if ($viewCountInc) {
                $wwUserInfo->today_show_count++;
                $wwUserInfo->save();
            }
            // 如果是二维码模式，寻找对应的二维码
            if ($wwUserInfo->add_method == 2) {
                if ($wwUserInfo->type == 3) {
                    return [
                        'id' => $wwUserInfo->id,
                        'add_method' => $wwUserInfo->add_method,
                        'ww_user_name' => $wwUserInfo->name, //(!empty($wwUserInfo->alias)?"「".$wwUserInfo->alias."」":"")
                        'qrcode_id' => -1,
                        'qrcode_url' => "https://oss.smart-ark.cn/" . $wwUserInfo->qrcode,
                        'cus_acq_link' => '',
                        'state' => 'zt_t2_hand_upload',
                        'vid' => $linkViewRecord->id,
                        'corp_id' => $wwUserInfo->corp_id,
                        'corp_auth_id' => $wwUserInfo->corp_auth_id,
                        'admin_uid' => $wwUserInfo->admin_uid ?? 0,
                    ];
                } else {
                    /** @var WwUserQrcode $wwUserQrcode */
                    $wwUserQrcode = WwUserQrcode::query()->where("is_used", 0)->where("ww_user_id", $wwUserInfo->id)->where("skip_verify", $wwUserInfo->skip_verify)->first();
                    if (!$wwUserQrcode) {
                        NotifySendService::sendWorkWeixinForError("[广告页面][投放销售][未获取到二维码-2] " . "「" . $wwLink->adminInfo->username . "」用户的「" . $wwLink->id . "」链接，账户ID「" . $wwLink->account_id . "」，销售ID：【" . $wwUserInfo->id . "】，销售名称：【" . $wwUserInfo->name . "】，未获取到二维码，投放分组是「" . (($wwLink->wwUserGroup) ? $wwLink->wwUserGroup->title : $wwLink->ww_user_group_id) . "」");
                        return false;
                    }
                    $wwUserQrcode->is_used = 1;
                    $wwUserQrcode->save();

                    $returnData = [
                        'id' => $wwUserInfo->id,
                        'add_method' => $wwUserInfo->add_method,
                        'ww_user_name' => $wwUserInfo->name ?? '',
                        'qrcode_id' => $wwUserQrcode->id,
                        'qrcode_url' => $wwUserQrcode->qr_code,
                        'cus_acq_link' => '',
                        'state' => $wwUserQrcode->state,
                        'vid' => $linkViewRecord->id,
                        'corp_id' => $wwUserInfo->corp_id,
                        'corp_auth_id' => $wwUserInfo->corp_auth_id,
                        'admin_uid' => $wwUserInfo->admin_uid ?? 0,
                    ];;

                    if ($wwUserInfo->type == 2 && !empty($wwUserQrcode->qr_code_link)) {
                        $returnData['add_method'] = 1;
                        $returnData['cus_acq_link'] = $wwUserQrcode->qr_code_link;
                    }
                    return $returnData;
                }
            } else {
                $state = getLinkState();
                return [
                    'id' => $wwUserInfo->id,
                    'add_method' => $wwUserInfo->add_method,
                    'ww_user_name' => $wwUserInfo->name ?? '',
                    'qrcode_id' => -1,
                    'qrcode_url' => '',
                    'cus_acq_link' => $wwUserInfo->cus_acq_link_url . "?customer_channel=" . $state,
                    'state' => $state,
                    'vid' => $linkViewRecord->id,
                    'corp_id' => $wwUserInfo->corp_id,
                    'corp_auth_id' => $wwUserInfo->corp_auth_id,
                    'admin_uid' => $wwUserInfo->admin_uid ?? 0,
                ];
            }
        } else {
//            SpeedLogService::info("00026+8");//性能日志，单独做记录使用
            return false;
        }
    }


    /**
     * 销售上线检查
     * @param WwUser $wwUser
     * @param $source
     * @param $adminUid
     * @return array
     */
    public static function onlineCheck(WwUser $wwUser): array
    {
        // 检查获客助手是否正常
        if ($wwUser->type == 1 && !$wwUser->cus_acq_link_status) {
            // 如果手动上线的时候，销售的获客助手是异常的，那么就自动刷新获客助手
            if (!empty($wwUser->cus_acq_link_id)) {
                $delResp = WwCorpApiService::delete_link($wwUser->corpInfo, $wwUser); // 先删除旧的获客助手链接
                if (isset($delResp['errcode']) && $delResp['errcode'] != 0) {
                    NotifySendService::sendWorkWeixinForError("[企微销售][销售上线检测][未通过]，销售ID： " . $wwUser->id . "，删除旧的获客助手链接失败，接口返回信息：" . json_encode($delResp));
                    Log::info("[企微销售][销售上线检测][未通过] ，销售ID：" . $wwUser->id . "，删除旧的获客助手链接失败，接口返回信息：" . json_encode($delResp));
                    return [
                        'status' => false,
                        'message' => '刷新获客助手失败-3'
                    ];
                }
            }
            // 创建新的获客助手链接
            $linkData = WwCorpApiService::create_link($wwUser->corpInfo, $wwUser);
            if (isset($linkData['errcode']) && $linkData['errcode'] != 0) { // 创建失败
                NotifySendService::sendWorkWeixinForError("[企微销售][销售上线检测][未通过] ，销售ID：" . $wwUser->id . "，创建新的获客助手失败，接口返回信息：" . json_encode($linkData));
                Log::info("[企微销售][销售上线检测][未通过] ，销售ID：" . $wwUser->id . "，创建新的获客助手失败，接口返回信息：" . json_encode($linkData));
                $wwUser->cus_acq_link_status = 0;
                $wwUser->cus_acq_link_status_message = $linkData['errmsg'] ?? '';
                $wwUser->save();
                return [
                    'status' => false,
                    'message' => '刷新获客助手失败-4'
                ];
            } else { // 创建成功
                $wwUser->cus_acq_link_status = 1;
                $wwUser->cus_acq_link_status_message = $linkData['errmsg'] ?? '';
                $wwUser->cus_acq_link_id = $linkData['link']['link_id'];
                $wwUser->cus_acq_link_name = $linkData['link']['link_name'];
                $wwUser->cus_acq_link_url = $linkData['link']['url'];
                $wwUser->cus_acq_link_skip_verify = 1;
                $wwUser->save();
            }
        }

        // 判断销售可见范围
        if (!$wwUser->subscribe) {
            Log::info("[企微销售][销售上线检测][未通过] ，销售ID：" . $wwUser->id . "，销售不在可见范围");
            return [
                'status' => false,
                'message' => '销售不在可见范围，禁止上线'
            ];
        }
        $wwUser->online_status = 1;
        $wwUser->save();

        // 查询这个管理员的所有销售，进行展示量清0
        self::reSetTodayShowCount($wwUser->admin_uid);


        return [
            'status' => true,
            'message' => '允许上线'
        ];
    }

    public function corpInfo(): BelongsTo
    {
        return $this->BelongsTo(WwCorpInfo::class, 'corp_id', 'id');
    }

    public function adminInfo(): BelongsTo
    {
        return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
    }

    public function wwUserGroups(): \Illuminate\Database\Eloquent\Relations\BelongsToMany
    {
        return $this->belongsToMany(WwUsersGroup::class, WwUsersGroupsRel::class, 'ww_user_id', 'ww_group_id')->withTimestamps();
    }

    /**
     * @param $cus_acq_link_status_message
     * @return string
     */
    public static function getCusAcqLinkErrString($cus_acq_link_status_message): string
    {
        if (str_contains($cus_acq_link_status_message, "user real name has not been verified")) {
            $errorString = "账号未实名认证，请重新导入";
        } elseif (str_contains($cus_acq_link_status_message, "api forbidden")) {
            $errorString = "企微未授权获客助手或该销售是未授权时导入的，请重新导入";
        } elseif (str_contains($cus_acq_link_status_message, "user list or group creater no license")) {
            $errorString = "账号无许可证，购买后请通过上下线操作刷新，请重新导入";
        } elseif (str_contains($cus_acq_link_status_message, "userid not found")) {
            $errorString = "用户不存在或不在可见范围内，请打开可见范围";
        } elseif (str_contains($cus_acq_link_status_message, "user not activate")) {
            $errorString = "用户非活跃状态，需要登录一次企微，随后重新导入";
        } elseif (str_contains($cus_acq_link_status_message, "customer acquisition link exceed limit")) {
            $errorString = "客户企业微信的获客助手已满，请删除其他销售后重新导入";
        } elseif (str_contains($cus_acq_link_status_message, "企微异常提醒")) {
            $errorString = "该销售目前状态异常，继续进粉可能导致封号，如仍要继续使用，请重新导入";
        } elseif (str_contains($cus_acq_link_status_message, "exceed contact way count")) {
            $errorString = "该企业对外二维码创建额度已满，请联系客户或智投运营进行删除";
        } else {
            $temp = explode(",", $cus_acq_link_status_message);
            $errorString = "未知，请稍后重试：" . ($temp[0] ?? "");
        }
        return $errorString;
    }


    /**
     * 记录编辑销售操作记录
     *
     * @param $input
     *
     * @return mixed
     */
    public static function addEditWwUserLogs($input, $wwUser)
    {


        $action = '编辑【' . $wwUser->name . '】' . '销售ID：' . $wwUser->id . '；';
        if ($input) {
            // 判断是否修改自动上下线规则
            if (isset($input['auto_status_config'])) {
                switch ($input['auto_status_config']) {
                    case 0:
                        $autoDesc = '关闭' . '；';
                        break;
                    case 1:
                        if (isset($input['up_time']) && isset($input['down_time'])) {
                            $autoDesc = '按时间段，上线时间：' . $input['up_time'] . '，下线时间：' . $input['down_time'] . '；';
                        }
                        break;
                    case 2:
                        if (isset($input['up_time']) && isset($input['down_add_count'])) {
                            $autoDesc = '按加粉量，上线时间：' . $input['up_time'] . '，加粉量：' . $input['down_add_count'] . '；';
                        }
                        break;
                    default:
                        $autoDesc = '未知' . '；';
                }
                $action .= '【自动上下线】：' . $autoDesc;

            }
            //分组
            if (isset($input['ww_user_groups'])) {
                $groupIds = explode(',', $input['ww_user_groups']);
                $groupList = WwUsersGroup::query()
                    ->whereIn('id', $groupIds)
                    ->pluck('title', 'id')
                    ->toArray();
                foreach ($groupList as $groupId => $groupTitle) {
                    $groupList[$groupId] = '分组ID：' . $groupId . '，分组名称：[' . $groupTitle . ']；';
                }
                $groupList = implode(',', $groupList);
                $action .= '【分组】：' . $groupList;
            }

            // 权重
            if (isset($input['weight'])) {
                $weightDesc = '【权重】：' . $input['weight'] . '；';
                $action .= $weightDesc;

            }
            //上下线状态
            if (isset($input['online_status'])) {
                $onlineStatusDesc = '下线' . '；';
                if ($input['online_status'] == 1) {
                    $onlineStatusDesc = '上线' . '；';
                }
                $action .= '【状态】：' . $onlineStatusDesc;
            }

            //模式
            if (isset($input['add_method'])) {
                $addMethodDesc = '获客助手' . '；';
                if ($input['add_method'] == 2) {
                    $addMethodDesc = '二维码' . '；';
                }
                $action .= '【加粉方式】：' . $addMethodDesc;
            }
            //智投标签
            if (isset($input['wind_label'])) {
                $action .= '【智投标签】：' . $input['wind_label'] . '；';
            }

            // 判断是否修改了企微标签
            if (isset($input['ww_corp_label_ids']) && !empty($input['ww_corp_label_ids'])) {
                $ww_corp_label_ids = array_filter($input['ww_corp_label_ids'], function ($value) {
                    return $value !== '' && $value !== null;
                });

                if ($ww_corp_label_ids && $ww_corp_label_ids != $wwUser->ww_corp_label_ids) {
                    $ww_corp_label_names = json_decode($input['ww_corp_label_names'], true);
                    $ww_corp_label_names = implode(',', $ww_corp_label_names);
                    $corpLabelDesc = '【企微标签】：' . $ww_corp_label_names . '；';
                    $action .= $corpLabelDesc;

                }
            }
            AdminActionLogJob::dispatch(
                'edit_ww_user',
                $input['id'] ?? $wwUser->id,
                AdminActionLog::ACTION_TYPE['销售'],
                $action,
                getIp(),
                Admin::user()->id
            )->onQueue('admin_action_log_job');
        }

        return true;
    }


    /**
     * 销售今天展示清0
     *
     * @param $adminUid
     *
     * @return mixed
     */
    public static function reSetTodayShowCount($adminUid)
    {
        $corpIds = AdminSubUserAuthCorp::query()->where("admin_uid", $adminUid)->pluck("corp_id") ?? [];
        if ($corpIds) {
            $wwUsers = self::query()
                ->whereIn('corp_id', $corpIds)
                ->where('today_show_count', '>', 0)
                ->where("admin_uid", $adminUid)
                ->get();
            if ($wwUsers->isNotEmpty()) {
                foreach ($wwUsers as $wwUser) {
                    ReSetWwUserTodayShowCountJob::dispatch($wwUser)->onQueue('reset_today_show_count');// 今日展示清0队列
                }
            }
        }
        return true;
    }

    /**
     * 获取重复导入销售配置
     * @param $adminUid
     * @return bool
     */
    public static function getRepeatImportStatus($adminUid): bool
    {
        $repeatImportConfig = WwUserRepeatImport::query()
            ->select('id', 'admin_uid')
            ->where("status", 1)
            ->get()
            ->toArray();
        if (empty($repeatImportConfig)) {
            return false;
        }
        $configAdminUids = array_column($repeatImportConfig, 'admin_uid');
        $adminUids = AdminUser::getAdminUidsByParentId($configAdminUids);
        return in_array($adminUid, $adminUids);
    }


    /**
     * 导入销售操作日志处理
     * @param $wwUser //销售
     * @param $input //input 数据
     * @param $adminUid //用户ID
     * @param $ip //ip
     * @return mixed
     */
    public static function importWwUserActionLogHandle($wwUser, $input, $adminUid, $ip)
    {

        //处理操作导入销售日志
        $onlineStatusDesc = '上线';
        if ($input['online_status'] == 0) {
            $onlineStatusDesc = '下线';
        }
        $autoStatusConfigDesc = match ($input['auto_status_config']) {
            '1' => '按时间段：上线时间：' . $input['up_time'] . '，下线时间：' . $input['down_time'],
            '2' => '按粉丝量：下线粉丝量：' . $input['down_add_count'] . '，上线时间：' . $input['up_time'],
            default => '关闭',
        };
        //处理分组
        $selectGroupIds = '未选择';
        if (!empty($input['group_id'])) {
            $selectGroupIds = implode('，',$input['group_id']);
        }
        $selectCorpLabel = '未选择';
        if(!empty($input['ww_corp_label_ids'])){
            $ww_corp_label_ids = json_decode($input['ww_corp_label_ids'],true);
            $corpLabels = CorpLabels::query()
                ->select('ww_corp_label_name')
                ->whereIn('ww_corp_label_id', $ww_corp_label_ids)
                ->pluck('ww_corp_label_name')
                ->toArray();
            $selectCorpLabel = implode('，',$corpLabels);
        }

        //操作日志队列
        AdminActionLogJob::dispatch(
            'create_ww_user',
            $wwUser->id,
            AdminActionLog::ACTION_TYPE['销售'],
            '添加销售「' . $wwUser->id . '」-「' . $wwUser->name . '」销售。在线状态：' . $onlineStatusDesc . '；接粉权重：' . $input['weight'] . '；自动上下线：' . $autoStatusConfigDesc . '；销售分组：' . $selectGroupIds . '；企微标签：' . $selectCorpLabel . '；智投管理标签：' . $input['wind_label'] ?? '无',
            $ip,
            $adminUid
        )->onQueue('admin_action_log_job');
        return true;
    }

    /**
     * 创建获客助手链接
     * @param $corpInfo //企微
     * @param $wwUser // 销售
     * @return mixed
     */
    public static function createCusAcqLink($corpInfo, $wwUser)
    {
        $linkData = WwCorpApiService::create_link($corpInfo, $wwUser);
        if ($linkData['errcode'] != 0) { // 创建失败
            $wwUser->cus_acq_link_status = 0;
            $wwUser->cus_acq_link_status_message = $linkData['errmsg'];
        } else { // 创建成功
            $wwUser->cus_acq_link_status = 1;
            $wwUser->cus_acq_link_status_message = $linkData['errmsg'];
            $wwUser->cus_acq_link_id = $linkData['link']['link_id'];
            $wwUser->cus_acq_link_name = $linkData['link']['link_name'];
            $wwUser->cus_acq_link_url = $linkData['link']['url'];
            $wwUser->cus_acq_link_skip_verify = 1;
        }
        return $wwUser;
    }


    /**
     * 处理销售标签
     * @param $tagData 企微标签列表
     * @param $wwCorpLabelIds 企微标签ID
     * @return mixed
     */
    public static function handleWwUserCorlLabel($tagData, $wwCorpLabelIds)
    {
        $wwCorpLabelNames = [];
        if (!empty($wwCorpLabelIds)) {
            $wwCorpLabelIds = json_decode($wwCorpLabelIds, true);
            foreach ($wwCorpLabelIds as $corpLabelId) {
                $wwCorpLabelNames[] = $tagData[$corpLabelId] ?? '';
            }
        }
        return json_encode($wwCorpLabelNames, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 根据企微许可证试用期时间，判断销售企微许可证状态
     * @param $trailEndTime
     * @return int
     */
    public static function checkWwUsersLicenseStatus($trailEndTime)
    {
        if (!$trailEndTime) {
            return 0;
        }
        $exDays = (strtotime($trailEndTime) - time()) / 86400;
        if ($exDays > 3) {
            $exStatus = 1;
        } elseif ($exDays > 0) {
            $exStatus = 2;
        } else {
            $exStatus = 0;
        }
        return $exStatus;
    }

    /**
     * 获取发送欢迎语参数
     *
     * @param string $WelcomeCode    回调的参数
     * @param string $welcomeMessage 发送信息
     * @return array 请求参数
     */
    public static function getWelcomeMessageParams(string $WelcomeCode, string $welcomeMessage): array
    {
        $welcomeMessage = json_decode($welcomeMessage,true);
        return array_merge(['welcome_code' => $WelcomeCode], $welcomeMessage);
    }


    /**
     * 当前销售转移给其他销售
     *
     * @param WwUser $wwUser    目标销售
     * @return array 请求参数
     */
    public function TransferTo($wwUser)
    {

        if (!$this->corpInfo) {
            return [
                'status' => false,
                'message' => '企业信息不存在'
            ];
        }

        // 调用批量转移接口
        $result = ProviderService::batchTransferLicense($this->corpInfo, [
            [
                'handover_userid' => $this->user_id,  // 转出销售user_id
                'takeover_userid' => $wwUser->user_id,  // 转入销售user_id
            ]
        ]);

        if (count($result) && $result['errcode'] == 0 && isset($result['transfer_result'][0]['errcode']) && $result['transfer_result'][0]['errcode'] == 0) {
            // 转移成功
            $wwUser->license_c_ex_time = $this->license_c_ex_time; // 转移许可证过期时间
            $exStatus = self::checkWwUsersLicenseStatus($wwUser->license_c_ex_time);
            $wwUser->license_c_status = $exStatus;
            $wwUser->save();

            $action = '转移销售【' . $this->name . '】'
                    . '销售 ID：' . $this->id
                    . ' 许可证给【' . $wwUser->name . '】销售 ID：'
                    . $wwUser->id
                    . '；<br /> 转移许可证过期时间：'
                    . $this->license_c_ex_time . '；<br /> 转移许可证状态：'
                    . self::LICENSE_C_STATUS[$exStatus] . '；';
            AdminActionLogJob::dispatch(
                'transfer_ww_user',
                $this->id,
                AdminActionLog::ACTION_TYPE['销售'],
                $action,
                getIp(),
                Admin::user()? Admin::user()->id: -1
            )->onQueue('admin_action_log_job');

            return [
                'status' => true,
                'message' => '转移成功',
            ];
        } else {
            return [
                'status' => false,
                'message' => '转移失败：' . ($result['transfer_result'][0]['errcode'] ?? '请稍后重试'),
            ];
        }
    }
}
