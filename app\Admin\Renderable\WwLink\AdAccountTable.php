<?php

    namespace App\Admin\Renderable\WwLink;

    use App\Admin\Repositories\TencentAdAccount;
    use App\Models\WwLink;
    use Dcat\Admin\Admin;
    use Dcat\Admin\Grid;
    use Dcat\Admin\Grid\LazyRenderable;

    class AdAccountTable extends LazyRenderable
    {
        public function grid(): Grid
        {
            return Grid::make(new TencentAdAccount(), function (Grid $grid) {
                $grid->paginate(100);
                $grid->disableFilterButton();

                $wwLinkAccountIds = WwLink::query()->where("admin_uid", Admin::user()->id)->pluck(
                    "account_id"
                )->toArray();
                $wwLinkAccountIds = array_filter($wwLinkAccountIds);

                $grid->model()->where('admin_uid', Admin::user()->id)->whereNotIn(
                    "account_id",
                    $wwLinkAccountIds
                )->select(
                    "id",
                    "corporation_name",
                    "account_id",
                    "mdm_name",
                    "admin_uid",
                    "account_type",
                    "created_at"
                )->orderByDesc('id');

                $grid->column('id')->sortable();
                $grid->column('corporation_name', '企业名称')->display(function ($value) {
                    return empty($value) ? "无" : $value;
                });
                $grid->column('account_id', "账户ID");
                $grid->column('mdm_name', '主体名称')->display(function ($value) {
                    return empty($value) ? "无" : $value;
                })->editable();

                $grid->column('created_at', '绑定时间');

                $grid->filter(function (Grid\Filter $filter) {
                    $filter->expand();
                    $filter->panel();
                    $filter->where('account_id', function ($query) {
                        $keyWord = $this->input;
                        if (str_contains($keyWord, ",")) {
                            $keyWords = explode(",", $keyWord);
                        }
                        if (str_contains($keyWord, "，")) {
                            $keyWords = explode("，", $keyWord);
                        }
                        if (str_contains($keyWord, " ")) {
                            $keyWords = explode(" ", $keyWord);
                        }
                        if (!isset($keyWords)) {
                            $keyWords = [$keyWord];
                        }
                        $query->whereIn("account_id", $keyWords);
                    }, '账户ID')->width(4);
                    $filter->like('corporation_name', '企业名称')->width(2);
                    $filter->like('mdm_name', '主体名称')->width(2);
                });
            });
        }
    }
