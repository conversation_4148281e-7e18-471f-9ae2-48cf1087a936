<?php

namespace App\Admin\Extensions\Tools\WwAdViewClickRecord;

use App\Admin\Extensions\Tools\ViewRecord\OcpxUploadGridActionByViewRecord;
use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Models\AdminUser;
use App\Models\LinkViewRecord;
use App\Models\WwLink;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid\BatchAction;

class OcpxUploadGridBatchActionByViewRecord extends BatchAction
{
    protected $title = '<button class="btn btn-primary"><i class="feather icon-upload-cloud"></i><span class="d-none d-sm-inline">&nbsp; 上报</span></button>';

    // 注意action的构造方法参数一定要给默认值
    public function __construct($title = null)
    {
        $this->title = $title;
        parent::__construct($title);
    }

    // 确认弹窗信息
    public function confirm(): array
    {
        return ['确认手动上报?', '您确认手动上报吗？同行为重复上报会去重<br>目前只支持腾讯广告'];
    }

    // 处理请求
    public function handle(): Response
    {
        // 获取页面的访问记录ID
        $ids = $this->getKey();

        // 获取访客记录
        $linkRecords = LinkViewRecord::query()->whereIn('id', $ids)->get();
        if (!$linkRecords) {
            return $this->response()->error('无操作权限');
        }
        // 获取访客记录的链接ID
        $linkRecordLinkId = $linkRecords->pluck('ww_link_id')->toArray();
        // 获取链接类型
        $mediaType = WwLink::query()->whereIn('id', $linkRecordLinkId)->pluck('media_type')->unique()->toArray();
        // 判断数组是否只有一个元素且该元素是"腾讯广告"
        if (!(count($mediaType) === 1 && in_array("腾讯广告", $mediaType))) {
            return $this->response()->alert()->error('错误')->detail('选择的访客记录里包含非腾讯广告的记录');
        }
        foreach ($linkRecords as $linkRecord) {
            if (!AdminUser::isAdmin($linkRecord)) {
                return $this->response()->error('无操作权限');
            }
            // 调用访客记录行操作的上报方法
            $ocpxUpload = new OcpxUploadGridActionByViewRecord();
            $ocpxUpload->tencentAdOcpx($linkRecord);
        }
        // TODO 检查重复1
        //记录操作日志
        AdminActionLogJob::dispatch(
            'link_view_record_batch_ocpx_upload',
            Admin::user()->id,
            AdminActionLog::ACTION_TYPE['账号'],
            '实时访客批量手动上报「' . Admin::user()->username . '」，实时访客记录ID：' . implode(',', $ids),
            getIp(),
            Admin::user()->id
        )->onQueue('admin_action_log_job');
        return $this->response()->success('上报完成，请查看上报结果')->refresh();

    }


}
