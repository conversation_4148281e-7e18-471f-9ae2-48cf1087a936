<?php

    namespace App\Admin\Controllers;

    use Admin;
    use App\Admin\Actions\Grid\corp\BatchShareCorp;
    use App\Admin\Actions\Grid\corp\DeleteCusAcqLinkButton;
    use App\Admin\Actions\Grid\corp\GetCustomerAcquisitionQuota;
    use App\Admin\Actions\Grid\corp\SynCorpLabelAction;
    use App\Admin\Forms\Corp\UpdateCorpNameForm;
    use App\Admin\Repositories\AdminSubUserAuthCorp;
    use App\Http\Controllers\WorkWeixin3rdController;
    use App\Jobs\AdminActionLogJob;
    use App\Models\AdminActionLog;
    use App\Models\AdminUser;
    use App\Models\WwAppList;
    use App\Services\NotifySendService;
    use Dcat\Admin\Form;
    use Dcat\Admin\Grid;
    use Dcat\Admin\Http\Controllers\AdminController;
    use Dcat\Admin\Layout\Content;
    use Dcat\Admin\Show;
    use Dcat\Admin\Widgets\Alert;
    use Dcat\Admin\Widgets\Modal;
    use Dcat\Admin\Widgets\Tooltip;
    use Illuminate\Http\Request;
    use Illuminate\Http\Response;
    use Illuminate\Support\Facades\Log;
    use Illuminate\Support\Facades\Redirect;
    use Illuminate\Support\Facades\Session;

    /**
     * @property int $id
     */
    class AdminSubUserAuthCorpController extends AdminController
    {
        private $corpInfo;

        /**
         * Index interface.
         *
         * @param Content $content
         * @return Content
         */
        public function index(Content $content)
        {
            $auth_status = Session::get("auth_status", '');

            $c = $content
                ->translation($this->translation())
                ->title($this->title())
                ->description($this->description()['index'] ?? trans('admin.list'));
            if (in_array($auth_status, [0, 1])) {
                if ($auth_status === 1) {
                    $alert = Alert::make('授权成功，请查看列表确认');
                    $alert->success();
                } else {
                    $alert = Alert::make('授权失败，请重试，如果多次失败，请联系系统运营');
                    $alert->warning();
                }
                $alert->removable();
                $c->row($alert);
            }
            return $c->body($this->grid());
        }

        /**
         * Make a grid builder.
         *
         * @return Grid
         */
        protected function grid()
        {
            return Grid::make(new AdminSubUserAuthCorp(['corpInfo', 'corpInfo.suiteInfo:suite_name,suite_id', 'adminInfo']), function (Grid $grid) {
                $grid->paginate(AdminUser::getPaginate());
//                $grid->disableActions();
                $grid->disableCreateButton();
                $grid->disableViewButton();
                $grid->disableEditButton();
//                $grid->disableDeleteButton();
                if(!AdminUser::isSystemOp()){ //超管和运营显示删除按钮
                    $grid->disableActions();
                }
                $grid->disableBatchActions();
//                $grid->disableViewButton();
                $grid->fixColumns(0, 0);
                //跳转登陆的授权模式
                $wwApp = WwAppList::getMyApp();
                $grid->tools('<a target="_blank" href="' . admin_route('ww_corp_install', ['sid' => $wwApp->md5_id]) . '"><button class="btn btn-primary ww_user_batch_btn float-right" style="margin-left: 5px"><i class="fa fa-weixin"></i>&nbsp&nbsp<span class="selected"></span>登陆授权-' . $wwApp->suite_name . '</button></a>');

                //授权企业微信 - 二维码模式 - 第三方应用
                $appQrcode = WwAppList::getMyAppQrcode();
                $modal = Modal::make()
                    ->sm()
                    ->title('授权企业微信')
                    ->body('<img style="width:100%" src="' . $appQrcode . '">')
                    ->button('<button class="btn btn-primary float-right" style="margin-left: 5px"><i class="fa fa-qrcode"></i>&nbsp;&nbsp;二维码授权-' . $wwApp->suite_name . '</button>');
                $grid->tools($modal);

                //授权企业微信 - 自建应用
                $selfAppQrcode = WwAppList::getSelfAppQrCode(0);
                if($selfAppQrcode){
                    $modal = Modal::make()
                        ->sm()
                        ->title('授权自建应用')
                        ->body('<i>说明：通过该二维码授权的企业微信，进粉记录带头像列，相应授权略微复杂，<u style="color: red">扫码后需联系智投运营发送授权通知</u>才可使用</i><img style="width:100%;margin-top: 15px" src="' . $selfAppQrcode['qrcode'] . '">')
                        ->button('<button class="btn btn-primary float-right" style="margin-left: 5px"><i class="fa fa-qrcode"></i>&nbsp;&nbsp;二维码授权-' . $selfAppQrcode['suite_name'] . '</button>');
                    $grid->tools($modal);
                }
                //批量获取获客助手余额
//                if(AdminUser::isSystemOp()){
//                    $grid->tools([new GetCustomerAcquisitionQuotaTools('<button class="btn btn-primary ww_user_batch_btn" style="margin-left: 5px"><i class="fa fa-cny"></i>&nbsp;&nbsp;获客助手余额</button>')]);
//                }

                $grid->tools(function (Grid\Tools $tools) {
                    $encryptAdminAccount = Admin::user()->encrypt_id;
                    // 使用 Modal 组件创建弹出窗口
                    $modal = Modal::make()
                        ->lg()
                        ->title('智投方舟账号标识')
                        ->body(
                            view(
                                '3rdPage.encrypt_admin_account',
                                [
                                    'encryptAdminAccount' => $encryptAdminAccount ?? '',
                                ]
                            )) // 加载视图内容
                        ->button('<button class="btn btn-primary"> 智投方舟账号标识</button>');
                    return $tools->append($modal);
                });
                if(AdminUser::isSystemOp()){
                    $grid->tools(function (Grid\Tools $tools) {
                        $modal = Modal::make()
                            ->lg()
                            ->title('修改企微名称')
                            ->body(UpdateCorpNameForm::make())
                            ->button('<button class="btn btn-primary" ><i class="feather icon-refresh-cw"></i>&nbsp&nbsp修改企微名称</button>');
                        $tools->append($modal);
//                        $tools->append('</div>');
                    });
                }
                //批量共享按钮
                $grid->tools([new BatchShareCorp()]);
                if (AdminUser::isSystemOp()) {
                    $grid->model()->orderByDesc("id");
                    $grid->column("adminInfo.username", "客户")->ClickCopy(8, "");
                } else {
                    $grid->model()->where("admin_uid", Admin::user()->id)->orderByDesc("id");
                }
                $grid->column('id','授权ID')->sortable();
                //            $grid->column('admin_uid');
                $grid->column('corp_id');
                $grid->column('corpInfo.corp_name')->ClickCopy(8, "");
                $grid->column('corpInfo.corp_square_logo_url')->image("", "50");
                $grid->column('更新企微标签')->action(SynCorpLabelAction::class);
                $grid->column('GetCustomerAcquisitionQuota', '获客助手')->action(GetCustomerAcquisitionQuota::class);
                $grid->column("corpInfo.customer_link_quota_balance")->display(function ($value) {
                    if(!$this->corpInfo->customer_link_quota_update_time){
                        $customer_link_quota_update_time = '余额更新时间：暂无';
                    }else{
                        $customer_link_quota_update_time = '余额更新时间：' .  $this->corpInfo->customer_link_quota_update_time;
                    }

                    $width  = 120;
                    Tooltip::make('.ww_corp_label_names_help_message' . $this->id)->title($customer_link_quota_update_time);
                    return '<div style="max-width: ' . $width . 'px; white-space: nowrap; overflow: hidden;text-overflow: ellipsis;" class="ww_corp_label_names_help_message' . $this->id . '">' . $value . '</div>';
                });
                $grid->column('auto_label','企微标签')->textarea()->help('每行一个，回车换行，配置的标签需要在企业微信后台标签库配置。');
                $grid->column('corp_group_name','企微分组')->editable()->help('如果多个企微配置同一个分组名称，请确保分组名称一致。');
                $grid->column('corpInfo.agent_auth_mode')->using([
                    -1 => '自建',
                    0 => '管理员',
                    1 => '成员'
                ]);
                $grid->column("corpInfo.suiteInfo.suite_name", '授权应用');
                $grid->column('created_at');

                $grid->column('清理获客助手链接')->action(DeleteCusAcqLinkButton::class);

                $grid->filter(function (Grid\Filter $filter) {
                    $filter->panel();
                    $filter->expand();
                    $filter->equal('id')->width(2);
                    $filter->like('corpInfo.corp_name')->width(2);
                    $filter->like('corp_group_name','分组名称')->width(2);
                    if (Admin::user()->isAdministrator()) {
                        $filter->like('adminInfo.username', '客户')->width(2);
                    }
                });
            });
        }

        public function installApp(Request $request)
        {
            try {
                $app = WorkWeixin3rdController::getApp('', $request->get("sid"));
                $preAuthCodeResp = $app->getClient()->withAccessToken($app->getSuiteAccessToken())->get('cgi-bin/service/get_pre_auth_code');
                $preAuthCodeData = $preAuthCodeResp->toArray();
                $installQuery = [
                    'suite_id' => $app->getAccount()->getSuiteId(),
                    'pre_auth_code' => $preAuthCodeData['pre_auth_code'] ?? "",
                    'redirect_uri' => admin_route('ww_corp_install_callback', ['sid' => $request->get("sid")]),
                    'state' => Admin::user()->id,
                    'auth_type' => 1
                ];
                $installUrl = 'https://open.work.weixin.qq.com/3rdapp/install?' . http_build_query($installQuery);
                return Redirect::to($installUrl);
            }catch (\Exception $exception){
                Log::error('安装企微失败：' . $exception->getMessage());
                if(env('APP_ENV') == 'production') {
                    NotifySendService::sendWorkWeixinForError('安装企微失败：' . $exception->getMessage());
                }
            }
        }

        public function installAppCallBack(Request $request)
        {
            $param = $request->all();
            $param['AuthCode'] = $param['auth_code'];
            $status = \App\Http\Controllers\WorkWeixin3rd\AuthController::createAuth($param);
            if ($status) {
                Session::flash("auth_status", 1);
            } else {
                Session::flash("auth_status", 0);
            }

            return Redirect::to("/ztfz/corp");
        }

        /**
         * Make a show builder.
         *
         * @param mixed $id
         *
         * @return Show
         */
        protected function detail($id)
        {
            return Show::make($id, new AdminSubUserAuthCorp(), function (Show $show) {
                if (!AdminUser::isAdmin($show->model())) {
                    $show->field('N')->value("无数据");
                } else {
                    $show->field('id');
                    $show->field('corp_id');
                    $show->field('created_at');
                    $show->field('updated_at');
                }
            });
        }

        /**
         * Make a form builder.
         *
         * @return Form
         */
        protected function form()
        {
            return Form::make(new AdminSubUserAuthCorp(), function (Form $form) {
                $form->hidden('auto_label');
                $form->hidden('corp_group_name');
                $form->saving(function (Form $form) {
                    $id = $form->getKey();
                    $input = $form->input();
                    if (isset($input['_inline_edit_'])) {
                        unset($input['_inline_edit_'], $input['_method'], $input['_token']);
                        $firstKey = array_key_first($input);
                        $actionTypeMessage = match ($firstKey) {
                            'corp_group_name' => '企微分组',
                            'auto_label'      => '企微标签',
                        };
                        $actionType = match ($firstKey) {
                            'corp_group_name' => 'inline_edit_corp_group_name',
                            'auto_label'      => 'inline_edit_corp_auto_label',
                        };
                        $oldValue = \App\Models\AdminSubUserAuthCorp::query()
                            ->find($id)
                            ->toArray();
                        AdminActionLogJob::dispatch(
                            $actionType,
                            $id,
                            AdminActionLog::ACTION_TYPE['企微'],
                            'ID：' . $id . '，操作：' . $actionTypeMessage . '<br>修改前值：' . $oldValue[$firstKey].
                            '<br>修改后值：' . $input[$firstKey],
                            getIp(),
                            Admin::user()->id
                        )->onQueue('admin_action_log_job');
                    }
                });

            });
        }

        public function destroy($id)
        {
            $data = \App\Models\AdminSubUserAuthCorp::query()->whereIn("id", explode(",", $id))->get();
            foreach ($data as $datum) {
                if (!AdminUser::isSystemOp()) {
                    return $this->form()->response()->error("无权限操作");
                }
            }
            return $this->form()->destroy($id);
        }

    }
