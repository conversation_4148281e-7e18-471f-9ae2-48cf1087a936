<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property mixed $site_set
 * @property mixed $date
 * @property mixed $sec_og_conv_auto_acquisition_pv
 * @property mixed $fir_og_conv_auto_acquisition_pv
 * @property mixed $lan_jump_button_click_cost
 * @property mixed $lan_jump_button_ctr
 * @property mixed $lan_button_click_cost
 * @property mixed $lan_jump_button_clickers
 * @property mixed $lan_button_click_count
 * @property mixed $platform_page_view_rate
 * @property mixed $platform_page_view_count
 * @property mixed $zone_header_click_count
 * @property mixed $click_detail_count
 * @property mixed $click_head_count
 * @property mixed $click_image_count
 * @property mixed $no_interest_count
 * @property mixed $video_outer_play7s_count
 * @property mixed $video_outer_play5s_count
 * @property mixed $video_outer_play3s_rate
 * @property mixed $video_outer_play3s_count
 * @property mixed $video_outer_play_cost
 * @property mixed $video_outer_play_rate
 * @property mixed $video_outer_play_time_avg_rate
 * @property mixed $video_outer_play_time_count
 * @property mixed $avg_user_play_count
 * @property mixed $video_outer_play_user_count
 * @property mixed $video_outer_play_count
 * @property mixed $deep_conversions_cost
 * @property mixed $deep_conversions_rate
 * @property mixed $deep_conversions_count
 * @property mixed $conversions_cost
 * @property mixed $conversions_rate
 * @property mixed $conversions_count
 * @property mixed $real_cost_top
 * @property mixed $thousand_display_price
 * @property mixed $acquisition_cost
 * @property mixed $cost
 * @property mixed $valuable_click_cost
 * @property mixed $valuable_click_count
 * @property mixed $ctr
 * @property mixed $cpc
 * @property mixed $click_user_count
 * @property mixed $valid_click_count
 * @property mixed $view_user_count
 * @property mixed $view_count
 * @property mixed $dynamic_creative_id
 * @property mixed $adgroup_id
 * @property mixed $admin_uid
 * @property mixed $account_id
 * @property mixed $system_industry_id
 */
class AdAccountDynamicCreativesDatum extends Model
{
	use HasDateTimeFormatter;
    protected $table = 'ad_account_dynamic_creatives_data';

    public function dcData(): BelongsTo
    {
        return $this->BelongsTo(AdAccountDynamicCreative::class, 'dynamic_creative_id', 'dynamic_creative_id');
    }
    public function adGroup(): BelongsTo
    {
        return $this->BelongsTo(AdAccountAdGroup::class, 'adgroup_id', 'adgroup_id');
    }
    public function industry(): BelongsTo
    {
        return $this->BelongsTo(TencentAdAccountIndustry::class, 'system_industry_id', 'system_industry_id');
    }
}
