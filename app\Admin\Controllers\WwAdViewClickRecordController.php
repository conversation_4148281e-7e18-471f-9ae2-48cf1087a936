<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\Tools\ViewRecord\OcpxUploadGridActionByViewRecord;
use App\Admin\Extensions\Tools\WwAdViewClickRecord\OcpxUploadGridBatchActionByViewRecord;
use App\Admin\Forms\ClickRecord\ClickRecordFilter;
use App\Models\AdminUser;
use App\Models\LinkViewRecord;
use App\Models\LinkViewRecordOcpx;
use App\Models\TencentAdAccount;
use App\Models\WwLink;
use App\Models\WwUserAddRecord;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Widgets\Card;
use function PHPUnit\Framework\isNull;

/**
 * @property $ww_link_id
 * @property $input
 * @property $id
 */
class WwAdViewClickRecordController extends AdminController
{
    private array $wwLinkInfo = [];

    /**
     * Index interface.
     *
     * @param Content $content
     *
     * @return Content
     */
    public function index(Content $content)
    {
        $filter = $_GET;
        // Log::info("001-".microtime(true));
        $data = json_decode($this->getRealTimeData(), true);
        // Log::info("002-".microtime(true));
        $title      = $data['title'] ?? [];
        $addFinal   = $data['add'] ?? [];
        $clickFinal = $data['click'] ?? [];
        // Log::info("003-".microtime(true));
        $card = Card::make(view("admin.grid.clickRecord", [
            'clickData' => json_encode($clickFinal),
            'addData'   => json_encode($addFinal),
            'title'     => json_encode($title),
            'filter' => $filter,
        ]));
        // Log::info("004-".microtime(true));
        $card->title(new ClickRecordFilter($filter));

        return $content
            ->translation($this->translation())
            ->title($this->title())
            ->description($this->description()['index'] ?? trans('admin.list'))
            ->row($card)
            ->row($this->grid());
    }

    public function getRealTimeData(): bool|string
    {
        $filter = request()->all()['filter'] ?? [];
        //Log::info("001-A-" . microtime(true));
        $dateTimeWhere = [
            date("Y-m-d H:i", time() - 45 * 60) . ":00",
            date("Y-m-d H:i", time()) . ":59",
        ];

        if (AdminUser::isSystemOp()) {
            if (!empty($filter['ad_account_name'])) {
                $wwLinkObj = WwLink::query();
                if (!empty($filter['admin_uid'])) {
                    $wwLinkObj->where("admin_uid", $filter['admin_uid']);
                }
                $wwLinkIds = $wwLinkObj->whereIn("account_id", TencentAdAccount::query()->where("corporation_name", $filter['ad_account_name'])->pluck("account_id"))->pluck("id");
            }
            if (!empty($filter['b_time'])) {
                $dateTimeWhere[0] = $filter['b_time'];
            }
            if (!empty($filter['e_time'])) {
                $dateTimeWhere[1] = $filter['e_time'];
            }
        }

        $title     = [];
        $clickData = [];
        $addData   = [];
        for ($i = strtotime($dateTimeWhere[0]); $i <= strtotime($dateTimeWhere[1]); $i += 60) {
            $clickData[date("Y-m-d H:i", $i)] = 0;
            $addData[date("Y-m-d H:i", $i)]   = 0;
            $title[]                          = date("H:i", $i);
        }
        //Log::info("001-B-" . microtime(true));
        if (AdminUser::isSystemOp()) {
            if (!empty($filter['admin_uid'])) {
                $commonWhere['admin_uid'] = $filter['admin_uid'];
            } else {
                $commonWhere = [];
            }
        } else {
            $commonWhere = ['admin_uid' => Admin::user()->id];
        }

        // dd($filter);
        if (isset($filter['ww_link_id'])) {
            $commonWhere['ww_link_id'] = $filter['ww_link_id'];
        }
        //Log::info("001-C-" . microtime(true));
        $clickModel = LinkViewRecord::query();
        if (isset($wwLinkIds)) {
            $clickModel->whereIn('ww_link_id', $wwLinkIds);
        }
        $clickList = $clickModel
            ->where($commonWhere)
            ->whereBetween("created_at", $dateTimeWhere)
            ->selectRaw("count(id) as num,DATE_FORMAT(created_at,'%Y-%m-%d %H:%i') as min ")
            ->groupBy("min")
            ->get();
        foreach ($clickList as $value) {
            $clickData[$value->min] = $value->num;
        }
        //Log::info("001-D-" . microtime(true));
        $addModel = WwUserAddRecord::query();
        if (isset($wwLinkIds)) {
            $addModel->whereIn('ww_link_id', $wwLinkIds);
        }
        $addList = $addModel
            ->where($commonWhere)
            ->whereBetween("external_contact_created_at", $dateTimeWhere)
            ->selectRaw("count(id) as num,DATE_FORMAT(external_contact_created_at,'%Y-%m-%d %H:%i') as min ")
            ->groupBy("min")
            ->get();

        foreach ($addList as $value) {
            $addData[$value->min] = $value->num;
        }
        //Log::info("001-E-" . microtime(true));
        $clickFinal = [];
        foreach ($clickData as $value) {
            $clickFinal[] = $value;
        }
        $addFinal = [];
        foreach ($addData as $value) {
            $addFinal[] = $value;
        }
        //Log::info("001-F-" . microtime(true));
        return json_encode([
            'title' => $title,
            'click' => $clickFinal,
            'add'   => $addFinal
        ]);
    }

    protected function grid(): Grid
    {
        return Grid::make(new \App\Admin\Repositories\LinkViewRecord(), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->showRefreshButton();
            $grid->scrollbarX();
            $grid->async();
            $grid->model()->where("admin_uid", Admin::user()->id)->orderByDesc('id');
            $grid->column('id', 'ID')->sortable();
            $grid->column('ww_link_id', '链接 ID');
            $grid->column("wwLinkInfo.ad_account_info.account_id", "账户ID")->display(function () {
                $this->wwLinkInfo = WwLink::getWwLinkInfoFromRedis($this->ww_link_id);
                return $this->wwLinkInfo['ad_account_info']['account_id'] ?? "暂无";
            });
            $grid->column("wwLinkInfo.ad_account_info.corporation_name", "账户名称")->display(function () {
                return $this->wwLinkInfo['ad_account_info']['corporation_name'] ?? "暂无";
            });
            $grid->column("wwLinkInfo.remark", "链接备注")->display(function () {
                return $this->wwLinkInfo['remark'] ?? "暂无";
            });
            $grid->column("wwLinkInfo.tpl_info.name", "投放页面")->display(function () {
                return $this->wwLinkInfo['tpl_info']['name'] ?? "暂无";
            });
            $grid->column("wwLinkInfo.ww_user_group.title", "投放分组")->display(function () {
                return $this->wwLinkInfo['ww_user_group']['title'] ?? "暂无";
            });
            $grid->column('ip');
            $grid->column('area', '地域');
            $grid->column('isp', '服务商');
            $grid->column('created_at', '访问时间');
            $grid->column('view_count', '访问次数');
            $grid->column('ww_user_name', '客服微信')->ClickCopy(10, "...");
            $grid->column('add_ww_user_id', '加粉')->display(function ($value) {
                if ($value) {
                    return '加粉';
                }
                return '未加';
            });
            $grid->column('add_time', '加粉时间');
//            $grid->column('checkOcpx', '上报结果')->display(function () {
//                $id                 = $this->id;
//                $LinkViewRecordOcpx = LinkViewRecordOcpx::query()->where("link_view_record_id", $id)->first();
//
//
//                $resultList         = [
//                    0 => '未知',
//                    1 => '成功',
//                    2 => '失败',
//                ];
//
//                if ($LinkViewRecordOcpx) {
//                    $result = $LinkViewRecordOcpx['result'];
//                    return $resultList[$result] ?? '未知';
//                }
//            });
            $grid->disableCreateButton();
            $grid->disableViewButton();
            $grid->disableDeleteButton();
            $grid->disableEditButton();
            $grid->disableBatchDelete();
            $grid->showColumnSelector();
            $grid->fixColumns(0, 0);
            $grid->paginate(30);
            $grid->disableActions();
//            $grid->tools(function (Grid\Tools $tools) {
//                $tools->append(new OcpxUploadGridBatchActionByViewRecord()); //批量上报
//            });
//            $grid->actions(function (Grid\Displayers\Actions $actions) {
//                $actions->prepend(new OcpxUploadGridActionByViewRecord()); //访问记录上报
//            });
            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                $filter->expand();
                $filter->equal('id', "ID")->width(2);
                $filter->equal('ww_link_id', "链接 ID")->width(2);
                $filter->where('账户ID', function ($query) {
                    $accountId = $this->input;
                    $linkIds   = WwLink::query()
                        ->where('account_id', $accountId)
                        ->pluck('id')->toArray();
                    $query->whereIn('ww_link_id', $linkIds);
                })->width(2);
                $filter->like('ip')->width(2);
                $filter->between('created_at', '访问时间')->width(4)->datetime();
                $filter->between('add_time', '加粉时间')->width(4)->datetime();
                $filter->where('加粉', function ($query) {
                    if ($this->input) {
                        $query->whereNotNull('add_time');
                    }
                })->width(2)->select([
                    0 => '全部',
                    1 => '添加成功',
                ]);
            });
        });
    }
}
