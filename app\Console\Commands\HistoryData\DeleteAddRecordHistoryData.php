<?php

namespace App\Console\Commands\HistoryData;

use App\Models\WwUserAddRecord;
use App\Services\NotifySendService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DeleteAddRecordHistoryData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'Data:DeleteAddRecordHistoryData';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '删除ww_user_add_record15天前历史数据';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $deleteDate = date('y-m-d H:i:s', strtotime('-15 day'));
        try {
            WwUserAddRecord::query()->where('external_contact_created_at', '<=', $deleteDate)
                ->chunkById(1000, function ($addRecords){
                    if (!empty($addRecords)) {
                        DB::beginTransaction();
                        $deleteIds = [];
                        $deleteData = [];
                        $addRecords = $addRecords->toArray();
                        foreach ($addRecords as $addRecord) {
                            $this->info($addRecord['external_contact_created_at']);
                            $deleteIds[] = $addRecord['id'];
                            $deleteData[] = $addRecord;
                        }
                        //转移到冷库
                        if ($deleteData) {
                            DB::table('ww_user_add_record_delete')->insert($deleteData);
                        }
                        //删除原数据
                        if ($deleteIds) {
                            $deleteIds = array_chunk($deleteIds, 500); // 每批 500 条
                            foreach ($deleteIds as $deleteId) {
                                WwUserAddRecord::query()->whereIn('id', $deleteId)->delete();//删除原数据
                            }
                        }
                        DB::commit();
                    }
                });
        } catch (\Exception $e) {
            DB::rollBack();
            $errorMsg ='批量转移add_record数据到冷库失败：' . $e->getMessage();
            Log::error($errorMsg);
            NotifySendService::sendWorkWeixinForError($e->getMessage());
            throw $e;
        }
        return Command::SUCCESS;
    }


}
