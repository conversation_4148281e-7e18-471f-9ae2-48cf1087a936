<?php

namespace App\Admin\Forms\WwLink;

use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Models\AdminUser;
use App\Models\WwLink;
use App\Models\WwUsersGroup;
use App\Services\Tools\LogService;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class BatchSeWwUserGroupForm extends Form implements LazyRenderable
{
    use LazyWidget;

    // 处理请求
    public function handle(array $input)
    {
        LogService::inputLog('Tools','ww_link-链接列表-投放销售分组', $input, Admin::user()->id, Admin::user()->username);

        // id转化为数组
        $id = explode(',', $input['id'] ?? null);
        if (!$id) {
            return $this->response()->alert()->error('提示')->detail('参数错误，请刷新页面后重试-1。');
        }

        $wwLik = WwLink::query()->find($id);
        if ($wwLik->isEmpty()) {
            return $this->response()->alert()->error('提示')->detail('页面不存在。');
        }

        foreach ($wwLik as $key => $value) {
            if (!AdminUser::isAdmin($value)) {
                return $this->response()->alert()->error('提示')->detail('参数错误，请刷新页面后重试-2。');
            }

            $value->ww_user_group_id = $input['ww_user_group_id'];
            $value->save();
            AdminActionLogJob::dispatch(
                'batch_set_ww_link_user_group',
                $value->id,
                AdminActionLog::ACTION_TYPE['链接'],
                '批量配置企微链接销售分组，账户ID：「' . $value->account_id . '」，分组ID：' . $value->ww_user_group_id ,
                getIp(),
                Admin::user()->id
            )->onQueue('admin_action_log_job');
        }
        return $this->response()->alert()->success('提示')->detail('配置成功')->refresh();
    }

    public function form()
    {
//        $wwUsersGroup = WwUsersGroup::query()->where("admin_uid", Admin::user()->id)->where('type',1)->pluck("title", 'id');
        //$this->select('ww_user_group_id', '投放销售分组')->options(WwUsersGroup::getMyAllSubGroupList())->required();
        $this->select('ww_user_group_id', '投放销售分组')->options(WwUsersGroup::getMyAdGroupList())->required();//修改为展示授权分组以及自身分组
        $this->hidden('id')->attribute('id', 'reset-ww_group_id');
    }

    // 返回表单数据，如不需要可以删除此方法
    public function default()
    {
        return [
            'password' => '',
            'password_confirm' => '',
        ];
    }
}
