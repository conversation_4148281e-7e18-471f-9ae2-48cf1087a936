<?php

namespace App\Admin\Controllers;

use App\Models\AdminSubUser;
use App\Models\AdminSubUserAuthCorp;
use App\Models\AdminUser;
use App\Models\CountCorpsData;
use App\Models\WwCorpInfo;
use App\Services\Tools\UtilsService;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Show;

/**
 * @property $corp_id
 */
class CountCorpsDataController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */


    protected function grid()
    {
        return Grid::make(new CountCorpsData(), function (Grid $grid) {
            $grid->paginate(AdminUser::getPaginate());
            //不管是主账号还是子账号，都可以查看所有数据 获取主账号ID
            if(Admin::user()->id !== 1){
                $adminInfo = AdminUser::query()->find(Admin::user()->id);
                if ($adminInfo->parent_id) {
                    $adminUid = $adminInfo->parent_id;
                } else {
                    $adminUid = $adminInfo->id;
                }
                if($adminUid){
                    $grid->model()->where("admin_uid", $adminUid);
                }else{
                    $grid->model()->where("admin_uid", Admin::user()->id);
                }
            }
            $grid->export()->rows(function ($rows) {
                foreach($rows as $key=>$row){
                    $rows[$key]['corpInfo.corp_name'] = WwCorpInfo::query()->where("id",$row->corp_id)->value("corp_name");
                }
                return $rows;
            })->chunkSize(2000);
            $grid->model()->orderByDesc("date");
            $grid->disableCreateButton();
            $grid->disableBatchActions();
            $grid->disableActions();
            $grid->showColumnSelector();
            $grid->column('id')->sortable();
//            $grid->column('corp_id')->sortable();
//            $grid->column('admin_uid')->sortable();
            $grid->column('corpInfo.corp_name','企微')->display(function (){
                return WwCorpInfo::query()->where("id",$this->corp_id)->value("corp_name");
            });
            $grid->column('date','日期');
            $grid->column("demand","需求量")->editable()->help("可以在此编辑今日的需求量，做对比");
            $grid->column("remark","备注")->editable();
            $grid->column('add_count','进粉量')->sortable()->display(function ($value) {
                return UtilsService::beautifyNumber($value, 'minimal', 'success', ['tooltip' => '新增客户数量']);
            });
            $grid->column('cus_del_count','客户删除')->sortable()->display(function ($value) {
                return UtilsService::beautifyNumber($value, 'box', 'warning', ['tooltip' => '客户删除数量']);
            });
            $grid->column('ww_del_count', '企微删除')->sortable()->display(function ($value) {
                return UtilsService::beautifyNumber($value, 'badge', 'danger', ['tooltip' => '企微删除数量']);
            });
            $grid->column('pure_add_count', '净增数量')->sortable()->display(function ($value) {
                return UtilsService::beautifyNumber($value, 'pill', 'info', ['tooltip' => '净增客户数量']);
            });
            $grid->column('customer_start_chat_count','开口')->sortable()->display(function ($value) {
                return UtilsService::beautifyNumber($value, 'icon', 'info', [
                    'tooltip' => '客户主动发起聊天的数量',
                    'icon' => 'feather icon-message-circle'
                ]);
            });
            $grid->column('updated_at','更新时间')->sortable();

            $grid->filter(function (Grid\Filter $filter) use($grid){
                $filter->expand();
                $filter->panel();
                $corpAuthList = AdminSubUserAuthCorp::getCorpAuthListBuyAdminUid(Admin::user()->id);
                $corpIds = array_keys($corpAuthList);
                $corpList = AdminSubUserAuthCorp::getCorpAuthListBuyCorpIds($corpIds);
                $filter->in('corp_id', '企业微信')->multipleSelect($corpList)->width(2);
                $filter->equal("date",'日期')->date()->width(2);
                $filter->equal("remark",'备注')->width(2);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new CountCorpsData(), function (Show $show) {
            if (!AdminUser::isAdmin($show->model())) {
                $show->field('N')->value("无数据");
            }else {
                $show->field('id');
            }
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new CountCorpsData(), function (Form $form) {
            if (!$form->isCreating() && !AdminUser::isAdmin($form->model())) {
                $form->display('N')->value("无数据");
            } else {
                $form->display('id');
                $form->number("demand");
                $form->text("remark");
            }

        });
    }

    public function destroy($id)
    {
        return $this->form()->response()->error("无权限操作");
    }
}
