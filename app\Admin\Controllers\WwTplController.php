<?php

namespace App\Admin\Controllers;

use Admin;
use App\Admin\Repositories\WwTpl;
use App\Models\AdminSubUser;
use App\Models\AdminUser;
use App\Services\Tools\QrcodeService;
use App\Services\Tools\UtilsService;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Show;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Request as RequestFacade;

/**
 * @property int       $id
 * @property $admin_uid
 * @property AdminUser $adminInfo
 * @property           $pre_qrcode
 */
class WwTplController extends AdminController
{
    public function viewTpl($id)
    {
        /** @var \App\Models\WwTpl $tpl */
        $tpl = \App\Models\WwTpl::query()->find($id);
        if(!$tpl){
            return view('errors.system-error', [
                'message' => '落地页不存在'
            ]);
        }
        if (!AdminUser::isSystemOp() && !Admin::user()->isRole("wop")) {
            if (!$tpl) {
                return view('errors.system-error', [
                    'message' => '无权限操作-1'
                ]);
            }
            /** @var AdminUser $adminUser */
            $adminUser = Admin::user();
            if ($tpl->admin_uid != $adminUser->id && $tpl->admin_uid != $adminUser->parent_id) {
                return view('errors.system-error', [
                    'message' => '无权限操作-2'
                ]);
            }
        }
        $data = [
            'id' => '',
            'ww_user_name' => '',
            'qrcode_id' => '',
            'qrcode_url' =>  env('BASE_QRCODE_URL'),
            'cus_acq_link' =>  env('BASE_QRCODE_URL'),
            'state' => '',
            'vid' => 0
        ];
        if ($tpl->type == 1) {
            $data['add_method'] = 1;
        } else {
            $data['add_method'] = 2;
        }
        if (empty($tpl->view_file)) {
            return view('errors.system-error', [
                'message' => '落地页模板文件未配置，请联系运营'
            ]);
        }
        return view($tpl->view_file, $data);
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {

        return Grid::make(new WwTpl(['adminInfo']), function (Grid $grid) {
            $grid->paginate(AdminUser::getPaginate());
            /** 对话框新增 */
            $grid->disableViewButton();
//
            if (!AdminUser::isSystemOp()) {
                $grid->disableCreateButton();
                $grid->disableActions();
            } else {
                $grid->disableCreateButton();
                $grid->tools(function (Grid\Tools $tools) {
                    $className = collect(RequestFacade::segments())->last();
                    $tools->append(UtilsService::dialogForm('添加', RequestFacade::url() . '/create', "create-{$className}"));
                });
            }
            $grid->disableBatchActions();
            if (AdminUser::isSystemOp()) {//如果是管理员，那么显示全部模板
                $grid->column("adminInfo.username", "客户");
            } else {
                /** @var AdminUser $adminUser */
                $adminUser = Admin::user();
                if (Admin::user()->isRole('customer')) { //如果是主账户，显示自身全部模板，以及子账户的全部模板
                    $grid->model()->where("admin_uid", AdminSubUser::getAdminUids($adminUser->id))->orderByDesc("id");
                    $grid->column('归属账号')->display(function () use ($adminUser) {
                        if ($adminUser->id == $this->admin_uid) { //如果是自己创建的则直接显示主账号
                            return '主账号';
                        } else {
                            $username = $this->adminInfo->username;
                            return str_replace($adminUser->username, '', $username);
                        }
                    });
                } else { //如果是子账户，显示自身模板，以及父级授权展示的模板
                    $tplIdData = AdminSubUser::query()->where('admin_uid', $adminUser->id)->value('tpl_ids');
                    $tplIds = json_decode($tplIdData, true);
                    if (!$tplIds) $tplIds = [];
                    if (in_array(-1, $tplIds)) {
                        //判断授权是否是-1，代表全部，如果是-1，那么展示主账户全部+自己的全部
                        $grid->model()->whereIn("admin_uid", [$adminUser->id, $adminUser->parent_id]);
                    } else {
                        //如果授权不是全部，那么展示授权的ID，以及自己的全部
                        $grid->model()->where(function ($query) use ($tplIds, $adminUser) {
                            $query->whereIn('id', $tplIds)
                                ->orWhere("admin_uid", $adminUser->id);
                        });
                    }
                }
            }
            $grid->model()->orderByDesc("id");
            $grid->column('id','落地页ID')->sortable();
            //            $grid->column('admin_uid');
            $grid->column('name','落地页名称')->editable();
            $grid->column('type', '落地页类型')->using([
                0 => '屏蔽',
                1 => '投放',
            ])->dot(
                [
                    0 => 'danger',
                    1 => 'success',
                ],
                'primary' // 第二个参数为默认值
            );


            $grid->column("product")->help("用于新建投放链接的时候，自动匹配相同产品的屏蔽规则使用")->editable();
            if (AdminUser::isSystemOp()) {
                $grid->column('is_generate', '后台生成')->using([0 => '否', 1 => '是'])
                    ->dot(
                        [
                            0 => 'danger',
                            1 => 'success',
                        ],
                    );
            }

            if (AdminUser::isSystemOp()) {
                $grid->column("view_file",'落地页文件');
            }
//            $grid->column("预览")->display(function () {
//                return '<a target="_blank" href="/' . UtilsService::getConfigRoutePrefix() . '/PreviewPage/' . $this->id . '">预览</a>';
//            });
//            $grid->column('pre_qrcode',"扫码预览")->display(function () {
//                if($this->pre_qrcode){
//                    return "<img src='{$this->pre_qrcode}' alt='qrcode' width='100' height='100'>";
//                }
//                return '';
//
//            });
            $grid->column('预览功能')->display('<button class="btn btn-outline-primary  "> 预览页面 </button>')->modal(function ($modal) {
                $domain  = config('app.url');
                $modal->title("预览功能");
                $modal->xl();
                $modal->icon("");
                return view('admin.tpl.Preview',[
                    'link' => $domain.'/'.UtilsService::getConfigRoutePrefix().'/PreviewPage/'.$this->id,
                    'pre_qrcode' => $this->pre_qrcode,
                ]);
            });
            $grid->column('updated_at')->sortable();
//            if (AdminUser::isSystemOp()) {
                //分析问答数据
//                    $grid->actions(function (Grid\Displayers\Actions $actions) {
//                        $actions->prepend(new AnalyzeAskData());
//                    });
//            }
            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                if (AdminUser::isSystemOp()) {
                    $adminUser = AdminUser::query()->pluck('username', 'id')->toArray();
                    $filter->equal('admin_uid')->select($adminUser)->width(2);
                }
                $filter->equal('id')->width(2);
                $filter->like('name')->width(2);
                $filter->like('product')->width(2);
                $filter->equal('type')->select([0 => '屏蔽', 1 => '投放'])->width(2);

            });

        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new WwTpl(), function (Show $show) {
            if (!AdminUser::isAdmin($show->model())) {
                $show->disableDeleteButton();
                $show->disableEditButton();
                $show->disableListButton();
                $show->disableQuickEdit();
                $show->field('N')->value("无数据");
            } else {
                $show->field('id');
                $show->field('name');
            }
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new WwTpl(), function (Form $form) {
            $form->disableViewCheck();
            $form->disableEditingCheck();
            $form->disableViewButton();
            $form->disableDeleteButton();
            $form->hidden('pre_qrcode');
            if (!$form->isCreating() && !AdminUser::isAdmin($form->model())) {
                $form->display('N')->value("无数据");
            } else {
                $form->display('id');
                if (AdminUser::isSystemOp()) {
                    $form->select("admin_uid", '管理员')->options(AdminUser::query()->orderByDesc('id')->pluck("username", "id"));
                } else {
                    $form->hidden("admin_uid")->default(Admin::user()->id);
                }
                $form->text('name')->required();
                $form->radio('type', '类型')->options([
                    0 => '屏蔽',
                    1 => '投放',
                ])->required()->default(1);
                if (AdminUser::isSystemOp()) {
                    $form->radio('is_generate', '后台生成')->options([0 => '否', 1 => '是'])->default(0);
                    $viewsPath = resource_path('views/customer'); // 获取视图文件夹的路径
                    $views = [];
                    foreach (File::allFiles($viewsPath) as $view) {
                        // 过滤出.blade.php文件，也就是Blade视图文件
                        if (str_contains($view->getFilename(), 'blade.php')) {
                            $bladePath = str_replace(".blade.php", "", str_replace("/", ".", $view->getRelativePathname()));
                            $views['customer.' . $bladePath] = 'customer.' . $bladePath;
                        }
                    }
                    $form->select('view_file')->options($views);
                }
                $form->text("product");
            }

        })->saved(function (Form $form,$result) {
            if ($form->isCreating()) {
                $id = $result;
            } else {
                $id = $form->getKey();
            }

            if(!$form->input('pre_qrcode')){
                $fileName ='tpl_pre_qrcode-' . $id;
//                $url = 'http://tplpre.smart-ark.cn';
                $url = 'http://pre.smart-ark.cn';
                $preQrcodeUrl =$url .  '/tpl/preview?tpl_id=' . $id . '&token=' . md5($id);
                $imgPath = QrcodeService::createQrcodeFile($fileName, $preQrcodeUrl,1);
                \App\Models\WwTpl::query()->where('id', $id)->update(['pre_qrcode' => $imgPath]);
            }
        });

    }

    public function destroy($id)
    {
        $data = \App\Models\WwTpl::query()->whereIn("id", explode(",", $id))->get();
        foreach ($data as $datum) {
            if (!AdminUser::isAdmin($datum)) {
//                    return $this->form()->response()->error("无权限操作");
                return $this->form()->response()->alert()->error('提示')->detail('下无权限操作');
            }
        }
        return $this->form()->destroy($id);
    }
}
