<?php

namespace App\Console\Commands\Admin;

use App\Models\AdminBill;
use App\Models\AdminUser;
use App\Models\WwLink;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class Bill extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'Admin:Bill';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        //整理账户
        /** @var AdminUser[] $adminUsers */
        $adminUsers = AdminUser::query()
//            ->where("id", '', 5)
            ->where("parent_id", "<>", 1)
            ->get();

        $adminData = [];
        foreach ($adminUsers as $adminUser) {
            if (!$adminUser->parent_id) {
                $adminData[$adminUser->id]['admin_uid']            = $adminUser->id;
                $adminData[$adminUser->id]['ad_price_rate']        = $adminUser->ad_price_rate;
                $adminData[$adminUser->id]['created_at']           = $adminUser->created_at->toDatetimeString();
                $adminData[$adminUser->id]['username']             = $adminUser->username;
                $adminData[$adminUser->id]['sub_admin_uid'][]      = $adminUser->id;
                $adminData[$adminUser->id]['sub_admin_username'][] = $adminUser->username;
            } else {
                $adminData[$adminUser->parent_id]['sub_admin_uid'][]      = $adminUser->id;
                $adminData[$adminUser->parent_id]['sub_admin_username'][] = $adminUser->username;
            }
        }
        $startDate = strtotime(date("Y-m-d", time() - 86400 * 5));
        //$startDate = strtotime("2025-05-28");
        $today     = strtotime(date("Y-m-d", time()));
        while ($startDate <= $today) {
            $date = date("Y-m-d", $startDate);
            $this->info($date);
            foreach ($adminData as $adminDatum) {
                if (strtotime(date("Y-m-d", strtotime($adminDatum['created_at']))) > $startDate) {
                    continue;
                }
                $where     = [
                    'admin_uid' => $adminDatum['admin_uid'],
                    'date'      => $date
                ];
                $adminBill = AdminBill::query()->where($where)->first();
                if (!$adminBill) {
                    $adminBill            = new AdminBill();
                    $adminBill->admin_uid = $adminDatum['admin_uid'];
                    $adminBill->date      = $date;
                    $adminBill->add_price = 0;
                }
                //$this->info(json_encode($adminDatum['sub_admin_uid']));
                //获取哪些链接ID是非屏蔽的
                $noShieldWwLinks    = WwLink::withTrashed()->whereIn("admin_uid", $adminDatum['sub_admin_uid'])->where("need_shield", 0)->select("id", "account_id")->get();
                $noShieldAccountIds = [];
                if (!$noShieldWwLinks->isEmpty()) {
                    //校验一下，是否只有非屏蔽的，如果有屏蔽的链接，那么从非屏蔽的里面剔除掉
                    foreach ($noShieldWwLinks as $wwLink) {
                        if (!WwLink::query()->where("account_id", $wwLink->account_id)->where("need_shield", 1)->exists()) {
                            $noShieldAccountIds[] = $wwLink->account_id;
                        }
                    }
                }
                //sum_ad_cost 查询名下所有的账户消耗，真实的应该是，非屏蔽的点击量*单价 + 屏蔽的账户总消耗*比率
                // 2024-03-23 修改为只查询使用屏蔽的账户消耗 -
                $vccData                    = DB::table("ad_account_vcc_daily")
                    ->whereIn("admin_uid", $adminDatum['sub_admin_uid'])
                    ->where("date", $date)
                    ->whereNotIn("account_id", $noShieldAccountIds)
                    ->select(
                        DB::raw("sum(ad_cost) as sum_ad_cost"),
                        DB::raw("sum(view_count) as sum_ad_view_count"),
                        DB::raw("sum(valid_click_count) as sum_ad_valid_click_count"),
                        DB::raw("sum(conversions_count) as sum_ad_conversions_count")
                    )
                    ->first();
                $adminBill->sum_ad_cost     = $vccData->sum_ad_cost ?? 0;
                $sumAllVccData              = DB::table("ad_account_vcc_daily")
                    ->whereIn("admin_uid", $adminDatum['sub_admin_uid'])
                    ->where("date", $date)
                    ->select(DB::raw("sum(ad_cost) as sum_all_ad_cost"))
                    ->first();
                $adminBill->sum_all_ad_cost = $sumAllVccData->sum_all_ad_cost ?? 0;
                //查询是否有配置，如果没有配置，按照如下计算
                $ad_price_rate = 0;
                if ($adminDatum['ad_price_rate'] > 0) {
                    $ad_price_rate = $adminDatum['ad_price_rate'] / 100;
                }

                $adminBill->sum_ad_view_count        = $vccData->sum_ad_view_count ?? 0;
                $adminBill->sum_ad_valid_click_count = $vccData->sum_ad_valid_click_count ?? 0;
                $adminBill->sum_ad_conversions_count = $vccData->sum_ad_conversions_count ?? 0;
                $adminBill->ad_price_rate            = $ad_price_rate;
                $adminBill->ad_price                 = round($adminBill->sum_ad_cost * $ad_price_rate);
                $wr_price_unit                       = 2;

                $viewData                            = DB::table("link_view_data_by_minute")
                    ->whereIn("admin_uid", $adminDatum['sub_admin_uid'])
                    ->where("date", ">=", $date . " 00:00:00")
                    ->where("date", "<=", $date . " 23:59:59")
                    ->select(DB::raw("sum(view_count) as sum_wr_view_count"))
                    ->first();
                $adminBill->sum_wr_view_count        = $viewData->sum_wr_view_count ?? 0;
                $adminBill->wr_price_unit            = $wr_price_unit;
                $adminBill->wr_price                 = $adminBill->sum_wr_view_count * $wr_price_unit;

                //计算无需屏蔽的费用
                $noShieldLinkIds = [];
                if ($noShieldWwLinks->isNotEmpty()) {
                    $noShieldLinkData = $noShieldWwLinks->toArray();
                    $noShieldLinkIds  = array_column($noShieldLinkData, 'id');
                }
                //计算非腾讯的点击量
                $wwLinksNoEqq = WwLink::withTrashed()->whereIn("admin_uid", $adminDatum['sub_admin_uid'])->where("media_type", "<>", "腾讯广告")->select("id", "account_id")->get();
                if ($wwLinksNoEqq->isNotEmpty()) {
                    foreach ($wwLinksNoEqq as $item) {
                        $noShieldLinkIds[] = $item->id;
                    }
                }
                if (!empty($noShieldLinkIds)) {
                    $nosViewData                   = DB::table("link_view_data_by_minute")
                        ->whereIn("admin_uid", $adminDatum['sub_admin_uid'])
                        ->whereIn("ww_link_id", $noShieldLinkIds)
                        ->where("date", ">=", $date . " 00:00:00")
                        ->where("date", "<=", $date . " 23:59:59")
                        ->select(DB::raw("sum(view_count) as sum_nos_view_count"))
                        ->first();
                    $adminBill->sum_nos_view_count = $nosViewData->sum_nos_view_count ?? 0;
                    $adminBill->nos_price_unit     = $wr_price_unit;
                    $adminBill->nos_price          = $adminBill->sum_nos_view_count * $wr_price_unit;
                } else {
                    $adminBill->sum_nos_view_count = 0;
                    $adminBill->nos_price_unit     = $wr_price_unit;
                    $adminBill->nos_price          = 0;
                }
                $adminBill->sum_price = $adminBill->ad_price + $adminBill->nos_price;
                /** @var AdminBill $yesterdayBalance */
                $yesterdayBalance = AdminBill::query()->where("admin_uid", $adminDatum['admin_uid'])->where("date", date("Y-m-d", $startDate - 86400)
                )->first();
                if (!$yesterdayBalance) {
                    $yesterdayBalance = 0;
                } else {
                    $yesterdayBalance = $yesterdayBalance->balance;
                }
                $adminBill->balance = $yesterdayBalance - $adminBill->sum_price + $adminBill->add_price;
                $adminBill->save();
            }
            $startDate += 86400;
        }
        return Command::SUCCESS;
    }
}
