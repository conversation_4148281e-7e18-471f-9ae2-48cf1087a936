<?php /** @noinspection PhpDuplicatedCharacterInStrFunctionCallInspection */

	namespace App\Admin\Controllers;

	use App\Admin\Forms\OTools\GetShieldReason;
    use App\Models\AdminUser;
    use Dcat\Admin\Admin;
    use Dcat\Admin\Http\Controllers\AdminController;
    use Dcat\Admin\Layout\Content;
    use Dcat\Admin\Widgets\Modal;

    class OperateToolsController extends AdminController
	{
		/**
		 * Index interface.
		 *
		 * @param Content $content
		 * @return Content
		 */
		public function index(Content $content)
		{
			return $content
				->translation($this->translation())
				->title('运营工具箱')
				->description('这里是工具箱')
				->body(self::getContent());
		}

		public static function getContent()
		{
            $data['ShareCorp'] = Modal::make()
                ->lg()
                ->title('获取屏蔽原因')
                ->body(GetShieldReason::make())
                ->button('<button class="btn btn-primary disable-outline" style="color: white;margin-left: 5px"><i class="feather icon-aperture"></i>&nbsp&nbsp获取屏蔽原因</button>');

            $data['TextProcessor'] = Modal::make()
                ->xl()
                ->title('文本处理器(字符太多会卡)')
                ->body(view('Tools.text-processor'))
                ->button('<button class="btn btn-info disable-outline" style="color: white;margin-left: 5px"><i class="feather icon-file-text"></i>&nbsp&nbsp文本处理器</button>');

            // 性能分析器performance-analyzer
            if (Admin::user()->isAdministrator()) {
                $data['asfsaf'] = Modal::make()
                    ->xl()
                    ->title('性能分析器')
                    ->body(view('Tools.performance-analyzer'))
                    ->button('<button class="btn btn-info disable-outline" style="color: white;margin-left: 5px"><i class="feather icon-activity"></i>&nbsp&nbsp性能分析器</button>');

            }

			$rowNum  = 5;
			$html    = '<table>';
			$itemNum = 0;
			foreach ($data as $key => $datum) {
				$itemNum++;
				if ($itemNum == 1) {
					$html .= "<tr>";
				}
				$html .= "<td style='padding: 30px'>" . $datum . "</td>";
				if ($itemNum % $rowNum == 0) {
					$html .= "</tr><tr>";
				}
			}
			$html = rtrim($html, "</tr><tr>");
			$html .= "<tr/></table>";
			return $html;
		}
	}
