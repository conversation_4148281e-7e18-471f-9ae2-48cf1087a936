<?php

namespace App\Console\Commands\HistoryData;

use App\Models\OcpxRecord;
use App\Services\NotifySendService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DeleteOcpxRecordHistoryData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'Data:DeleteOcpxRecordHistoryData';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '删除ww_ocpx_record 15天前历史数据';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $deleteDate = date('y-m-d H:i:s', strtotime('-15 day'));
        try {
            OcpxRecord::query()->where('created_at', '<=', $deleteDate)
                ->chunkById(1000, function ($ocpxRecords){
                    if (!empty($ocpxRecords)) {
                        DB::beginTransaction();
                        $deleteIds = [];
                        $deleteData = [];
                        $ocpxRecords = $ocpxRecords->toArray();
                        foreach ($ocpxRecords as $record) {
                            $this->info($record['created_at']);
                            $deleteIds[] = $record['id'];
                            $deleteData[] = $record;
                        }
                        //转移到冷库
                        if ($deleteData) {
                            DB::table('ocpx_record_delete')->insert($deleteData);
                        }
                        //删除原数据
                        if ($deleteIds) {
                            $deleteIds = array_chunk($deleteIds, 500); // 每批 500 条
                            foreach ($deleteIds as $deleteId) {
                                OcpxRecord::query()->whereIn('id', $deleteId)->delete();//删除原数据
                            }
                        }
                        DB::commit();
                    }
                });
        } catch (\Exception $e) {
            DB::rollBack();
            $errorMsg = '批量转移ww_ocpx_record数据到冷库失败：' . $e->getMessage();
            Log::error($errorMsg);
            NotifySendService::sendWorkWeixinForError($e->getMessage());
            throw $e;
        }
        return Command::SUCCESS;
    }


}
