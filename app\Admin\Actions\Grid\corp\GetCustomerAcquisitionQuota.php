<?php

	namespace App\Admin\Actions\Grid\corp;

	use App\Models\AdminSubUserAuthCorp;
	use App\Services\Corp\WwCorpApiService;
	use Dcat\Admin\Actions\Response;
	use Dcat\Admin\Grid\RowAction;
	use Dcat\Admin\Traits\HasPermissions;
	use Illuminate\Contracts\Auth\Authenticatable;
	use Illuminate\Database\Eloquent\Model;
	use Illuminate\Http\Request;

	class GetCustomerAcquisitionQuota extends RowAction
	{
		/**
		 * @return string
		 */
		protected $title = 'Title';

		/**
		 * Handle the action request.
		 *
		 * @param Request $request
		 *
		 * @return Response
		 */
		public function handle(Request $request)
		{
			/** @var AdminSubUserAuthCorp $subUserAuthCorp */
			$subUserAuthCorp = AdminSubUserAuthCorp::query()->with("corpInfo")->find($this->getKey());

			$resp = WwCorpApiService::getCustomerAcquisitionQuota($subUserAuthCorp->corpInfo);
			if ($resp['status']) {
				return $this->response()->success($resp['message'])->refresh();
			} else {
				return $this->response()->error($resp['message']);
			}
		}

		/**
		 * @return string|array|void
		 */
		public function confirm()
		{
			// return ['Confirm?', 'contents'];
		}

		protected function html()
		{
			if (empty($this->row->corpInfo->customer_link_quota_update_time)) {
				$name = '<button class="' . $this->getElementClass() . ' btn btn-sm btn-outline-danger"> 刷新状态 </button>';
			} else {
				$name = '<button class="' . $this->getElementClass() . ' btn btn-sm btn-outline-primary  "> 获取余额 </button>';
			}

			return $name;
		}

		/**
		 * @param Model|Authenticatable|HasPermissions|null $user
		 *
		 * @return bool
		 */
		protected function authorize($user): bool
		{
			return true;
		}

		/**
		 * @return array
		 */
		protected function parameters()
		{
			return [];
		}
	}
