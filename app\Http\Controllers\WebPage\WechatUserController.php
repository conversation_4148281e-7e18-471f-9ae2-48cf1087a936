<?php

    namespace App\Http\Controllers\WebPage;

    use App\Models\AdminDomain;
    use App\Models\AdminUser;
    use App\Models\WechatUser;
    use App\Models\WhiteUserList;
    use App\Services\CacheService;
    use App\Services\NotifySendService;
    use Illuminate\Contracts\Foundation\Application;
    use Illuminate\Http\RedirectResponse;
    use Illuminate\Http\Request;
    use Illuminate\Routing\Redirector;
    use Illuminate\Support\Facades\Cache;
    use Illuminate\Support\Facades\DB;
    use Illuminate\Support\Facades\Http;
    use Illuminate\Support\Facades\Log;
    use Illuminate\Support\Facades\Redirect;

    class WechatUserController
    {
        public function customerToWechatUserLogin(Request $request)
        {
            $param = $request->all();

            $md5 = md5(microtime().mt_rand(100000,9999999));
            $param['wxlt'] = $md5;
//            NotifySendService::sendWorkWeixinForError($md5);
            CacheService::setWeChatAuthCheck($md5);
//            Log::info('微信环境登陆：' . json_encode($param));
            // Log::info(request()->headers->get('referer'));
            // Log::info($_SERVER['HTTP_REFERER'] ?? "--");
//            $url = 'https://rich-test.smart-ark.cn/al?' . http_build_query($param);
            $url = env('APP_URL') . '/al?' . http_build_query($param);
            return Redirect::to($url);
        }

        public function wechatUserLogin(Request $request)
        {
            // Log::info("WL - ".request()->headers->get('referer'));
            // Log::info("WL - ".($_SERVER['HTTP_REFERER'] ?? "--"));
            $query = $request->all();
            if (isset($query['code'])) {
                $query['r_code'] = $query['code'];
                unset($query['code']);
            }

//            if (!isset($query['wxlt'])) {
//                return view('errors.system-error', [
//                    'message' => '非法访问 请退出后重试 -888'
//                ]);
//            }
//            $getWeChatAuthCheck =  CacheService::getWeChatAuthCheck($query['wxlt']);
//            if (!$getWeChatAuthCheck) {
//                return view('errors.system-error', [
//                    'message' => '非法访问 请退出后重试 -999'
//                ]);
//            }
            $redirectUrl = AdminDomain::getWeChatLoginDomain();;//智投管理工具 公众号配置的网页授权域名
            $ri = $redirectUrl . route('wechatUserLoginCallback', [], false) . "?" . http_build_query($query);

            $param = [
                'appid' => self::getAppInfo()['appid'],
                'redirect_uri' => $ri,
                'response_type' => 'code',
                'scope' => 'snsapi_base',
            ];
            $url = 'https://open.weixin.qq.com/connect/oauth2/authorize?' . http_build_query($param) . "#wechat_redirect";
            return redirect($url);
        }

        /**
         * @return string[]
         */
        public static function getAppInfo(): array
        {

//            return [
//                'name' => '智投方舟',
//                'appid' => 'wx4eb2acc8684ea47c',
//                'secret' => 'df4eba94d45aef9e942dfd2e6cb359b4'
//            ];

            return [
                'name' => '智投方舟工具',
                'appid' => 'wx4ad1dc06e23b15d7',
                'secret' => 'edf7dcc88c4865028b40a9ae906a0c33'
            ];
        }

        /**
         * 客户的公众号信息
         * @return string[]
         */
        public static function getCustomerAppInfo(): array
        {
            $appInfo = [
                314 => [ //禾君（深圳）咨询服务中心
                    'name' => '管理工坊',
                    'appid' => 'wxe24321028728f28e',
                    'secret' => 'de2713f46a715a7d84dcfa23c7c9abcb',
                    'domian' => 'https://hj.zhidekeji.com',//授权登陆的域名
                ],
            ];
            return $appInfo;
        }

        public function wechatUserLoginCallback(Request $request)
        {
            $code = $request->get('code');
            $appInfo = self::getAppInfo();
            $appid = $appInfo['appid'];
            $secret = $appInfo['secret'];
            $url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=" . $appid . "&secret=" . $secret . "&code=$code&grant_type=authorization_code";
            $response = Http::get($url);
            $data = $response->json();
            if (!isset($data['openid'])) {
                if (isset($data['errcode']) && $data['errcode'] == 40029) {
                    // code失效，重新进入登陆流程
                    unset($_GET['code']);
                    unset($_GET['state']);
                    $loginUrl = "https://" . \Illuminate\Support\Facades\Request::getHost() . route('wechatUserLogin', [], false) . "?" . http_build_query($_GET);
                    return Redirect::to($loginUrl);
                }
                NotifySendService::sendWorkWeixinForError("[微信公众号][微信登陆][wechatUserLoginCallback]" . ($data['errmsg'] ?? json_encode($data)));
                return view('errors.system-error', [
                    'message' => '系统错误，请退出后重试-777'
                ]);
            }
            /** @var WechatUser $wechatUser */
            $wechatUser = WechatUser::query()->where("appid", self::getAppInfo()['appid'])->where("openid", $data['openid'])->first();
            if (!$wechatUser) {
                $wechatUser = new  WechatUser();
                $wechatUser->appid = self::getAppInfo()['appid'];
                $wechatUser->openid = $data['openid'];
                $wechatUser->unionid = $data['unionid'] ?? "";
                $wechatUser->ip = getIp();
                $wechatUser->access_token = $data['access_token'] ?? "";
                $wechatUser->refresh_token = $data['refresh_token'] ?? "";

                if (isset($data['expires_in'])) {
                    $time = time() + $data['expires_in'];
                } else {
                    $time = time();
                }
                $wechatUser->access_token_expires_in = date("Y-m-d H:i:s", $time);
                $wechatUser->parent_uid = 0;
                $wechatUser->save();
            }
            if (empty($wechatUser->md5_id)) {
                $wechatUser->md5_id = md5($wechatUser->id . "V587WonAi");
                $wechatUser->save();
            }

            $_GET['uuid'] = $wechatUser->md5_id;
            unset($_GET['code']);
            unset($_GET['state']);

            if (isset($_GET['r_code'])) {
                $_GET['code'] = $_GET['r_code'];
                unset($_GET['r_code']);
            }
            $csrfKey = md5( sha1(md5($_GET['uuid'] . time() . microtime() . rand(10000, 99999))) . rand(10000, 99999));
            $_GET['xt'] = $csrfKey;
            Cache::store('redis')->set($csrfKey, 0, 600);
            // 登录完成后，校验客户跳转审核还是投放，获取域名库的域名
            $domain = DB::table("page_domain")->where("status", 1)->first();
            if (!$domain) {
                $domain = DB::table("page_domain")->first();
            }
            $ri = $domain->host . route('shieldCheck', [], false);
            return redirect($ri . "?" . http_build_query($_GET));
        }

        /**
         * 添加白名单
         * @param Request $request
         * @return Redirector|string|RedirectResponse|Application
         */
        public function addWhite(Request $request): Redirector|string|RedirectResponse|Application
        {
            $query = $request->all();
            if (!isset($query['aeid'])) {
                return '无法访问-1';
            }
            $adminUser = AdminUser::query()->where("encrypt_id", $query['aeid'])->first();
            if (!$adminUser) {
                return '无法访问-2';
            }
            $ri = "https://" . \Illuminate\Support\Facades\Request::getHost() . route('whiteLoginCallback', [], false) . "?" . http_build_query($query);
            $param = [
                'appid' => self::getAppInfo()['appid'],
                'redirect_uri' => $ri,
                'response_type' => 'code',
                'scope' => 'snsapi_base',
            ];
            $url = 'https://open.weixin.qq.com/connect/oauth2/authorize?' . http_build_query($param) . "#wechat_redirect";
            return redirect($url);
        }

        /**
         * 添加白名单回调
         * @param Request $request
         * @return RedirectResponse|string
         */
        public function whiteLoginCallback(Request $request)
        {
            $query = $request->all();
            if (!isset($query['aeid'])) {
                return '无法访问-1';
            }
            /** @var AdminUser $adminUser */
            $adminUser = AdminUser::query()->where("encrypt_id", $query['aeid'])->first();
            if (!$adminUser) {
                return '无法访问-2';
            }

            $code = $request->get('code');
            $url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=" . self::getAppInfo()['appid'] . "&secret=" . self::getAppInfo()['secret'] . "&code=$code&grant_type=authorization_code";
            $response = Http::get($url);
            $data = $response->json();
            if (!isset($data['openid'])) {
                if (isset($data['errcode']) && $data['errcode'] == 40029) {
                    // code失效，重新进入登陆流程
                    unset($_GET['code']);
                    unset($_GET['state']);
                    $loginUrl = "https://" . \Illuminate\Support\Facades\Request::getHost() . route('addWhite', [], false) . "?" . http_build_query($_GET);
                    return Redirect::to($loginUrl);
                }
                NotifySendService::sendWorkWeixinForError("[微信公众号][微信登陆][whiteLoginCallback]" . ($data['errmsg'] ?? json_encode($data)));
                return '无法访问-3';
            }
            /** @var WechatUser $wechatUser */
            $wechatUser = WechatUser::query()->where("appid", self::getAppInfo()['appid'])->where("openid", $data['openid'])->first();
            if (!$wechatUser) {
                $wechatUser = new  WechatUser();
                $wechatUser->appid = self::getAppInfo()['appid'];
                $wechatUser->openid = $data['openid'];
                $wechatUser->unionid = $data['unionid'] ?? "";
                $wechatUser->ip = getIp();
                $wechatUser->access_token = $data['access_token'] ?? "";
                $wechatUser->refresh_token = $data['refresh_token'] ?? "";

                if (isset($data['expires_in'])) {
                    $time = time() + $data['expires_in'];
                } else {
                    $time = time();
                }
                $wechatUser->access_token_expires_in = date("Y-m-d H:i:s", $time);
                $wechatUser->parent_uid = 0;
                $wechatUser->save();
            }

            if (empty($wechatUser->md5_id)) {
                $wechatUser->md5_id = md5($wechatUser->id . "V587WonAi");
                $wechatUser->save();
            }

            $wechatUser->is_black = 5;
            $wechatUser->save();

            Log::info($adminUser->id . ',' . $wechatUser->openid);
            $whiteUser = WhiteUserList::getIsWhite($adminUser->id, $wechatUser->openid, getIp(),'腾讯广告');
            if ($whiteUser) {
               $whiteUserCheck = WhiteUserList::query()
                   ->where("admin_uid", $adminUser->id)
                   ->where("openid", $wechatUser->openid)
                   ->first();
               if($whiteUserCheck){
                   $whiteUserCheck->ip = getIp();
                   $whiteUserCheck->save();

                   // 返回警告视图 - 已存在白名单
                   return view('admin.white_list.result', [
                       'type' => 'warning',
                       'title' => '白名单状态',
                       'message' => '您已经是白名单用户，无需重复设置',
                       'md5_id' => $whiteUserCheck->md5_id ?? '',
                   ]);
               }
            }

            // 创建新的白名单记录
            $whiteUser = new WhiteUserList();
            $whiteUser->md5_id = $wechatUser->md5_id;
            $whiteUser->openid = $wechatUser->openid;
            $whiteUser->wechat_user_id = $wechatUser->id;
            $whiteUser->admin_uid = $adminUser->id;
            $whiteUser->ip = getIp();
            $whiteUser->save();

            // 返回成功视图
            return view('admin.white_list.result', [
                'type' => 'success',
                'title' => '配置成功',
                'message' => '恭喜！您已成功加入白名单，现在可以回到智投后台进行相关操作了。',
                'md5_id' => $whiteUser->md5_id
            ]);
        }

        /**
         * 客户自己的公众号授权登陆
         * @param Request $request
         * @return mixed
         */
        public function customerWeChatLogin(Request $request)
        {
            $adminUid = $request->input('admin_uid');
            if(!$adminUid){
                return '用户不存在-1';
            }
            $vid =  $request->input('vid');
            if(!$vid){
                return '访问记录不存在-1';
            }

            $appInfo = self::getCustomerAppInfo();
            $redirectUri = $appInfo[$adminUid]['domian'] . '/wechat/customerLoginCallback';
            $appid = $appInfo[$adminUid]['appid'];
            $param = [
                'appid' => $appid,
                'redirect_uri' => $redirectUri,
                'response_type' => 'code',
                'scope' => 'snsapi_base',
                'state' => $adminUid . ',' . $vid,
            ];
            $url = 'https://open.weixin.qq.com/connect/oauth2/authorize?' . http_build_query($param) . "#wechat_redirect";
            return redirect($url);
        }

        /**
         * 客户自己的公众号授权登陆回调
         * @param Request $request
         * @return mixed
         */
        public function customerWeChatLoginCallback(Request $request)
        {
            $state = $request->input('state');
            if(!$state){
                return view('errors.system-error', [
                    'message' => '非法请求-1'
                ]);
            }
            $state = explode(',', $state);
            $adminUid = $state[0];
            $vid =  $state[1];
            $appInfo = self::getCustomerAppInfo();
            $appid = $appInfo[$adminUid]['appid'];
            $secret = $appInfo[$adminUid]['secret'];
            $code = $request->get('code');
            $url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=" . $appid . "&secret=" . $secret . "&code=$code&grant_type=authorization_code";
            $response = Http::get($url);
            $data = $response->json();
            //将openid存储到redis
            if(!$data || !isset($data['openid'])){
                return view('errors.system-error', [
                    'message' => '系统错误，请退出后重试-1'
                ]);
            }
            $redisKey = 'pay_info:' . $adminUid . '_' . $vid;
            Cache::store('redis')->set($redisKey,$data['openid'],600);
//            $url = 'https://rich-test.smart-ark.cn/al?' . http_build_query([]);
            $url =   env('APP_URL') . '/al?' . http_build_query([]);
            return Redirect::to($url);
        }
    }
