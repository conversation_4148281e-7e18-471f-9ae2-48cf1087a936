<?php

	namespace App\Models;

	use Dcat\Admin\Traits\HasDateTimeFormatter;
    use Illuminate\Database\Eloquent\Model;
    use Illuminate\Database\Eloquent\Relations\BelongsTo;
    use Illuminate\Database\Eloquent\SoftDeletes;


    class AdYoukuAccount extends Model
	{
		use HasDateTimeFormatter;
		use SoftDeletes;

		protected $table = 'ad_youku_account';

        public function adminInfo(): BelongsTo
        {
            return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
        }

        public static function signature($content, $appKey, $token): string
        {
            if (!$content) {
                return false;
            }
            if (!is_array($content)) {
                return false;
            }
            //排序升序数组A-Z
            ksort($content);
            //apikey拼接 数组转字符串client_ip=XXX&conv_amount=XXX
            $contentStr = $appKey . http_build_query($content);
            //sha256加密拼接结果$contentStr,$token为密钥
            $secretStr = hash_hmac('sha256', $contentStr, $token);
            //对sha256加密结果取MD5,转成大写
            return strtoupper(md5($secretStr));
        }

    }
