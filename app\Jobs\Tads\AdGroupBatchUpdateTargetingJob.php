<?php

    namespace App\Jobs\Tads;

    use App\Models\TencentAdAccount;
    use App\Services\Tads\AdGroups\UpdateAdGroups;
    use Illuminate\Bus\Queueable;
    use Illuminate\Contracts\Queue\ShouldQueue;
    use Illuminate\Foundation\Bus\Dispatchable;
    use Illuminate\Queue\InteractsWithQueue;
    use Illuminate\Queue\SerializesModels;

    class AdGroupBatchUpdateTargetingJob implements ShouldQueue
    {
        use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

        protected mixed $AdBatchUpdateTargeting;


        /**
         * Create a new job instance.
         *
         * @return void
         */
        public function __construct($AdBatchUpdateTargeting)
        {
            $this->AdBatchUpdateTargeting = $AdBatchUpdateTargeting;
        }

        /**
         * Execute the job.
         *
         * @return mixed
         */
        public function handle()
        {
            $this->AdBatchUpdateTargeting->status = 2;
            $this->AdBatchUpdateTargeting->save();

            $adAccounts = TencentAdAccount::query()->whereIn("account_id",json_decode($this->AdBatchUpdateTargeting->account_ids,true))->get();
            foreach($adAccounts as $adAccount){
                UpdateAdGroups::updateForAccount($adAccount,$this->AdBatchUpdateTargeting);
            }

            $this->AdBatchUpdateTargeting->status = 3;
            $this->AdBatchUpdateTargeting->save();

        }
    }
