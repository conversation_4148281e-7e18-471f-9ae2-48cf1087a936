<?php

namespace App\Jobs\WwUser;

use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Models\WwUser;
use App\Models\WwUsersOnlineLogs;
use Dcat\Admin\Admin;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;


class WwUserAutoOnLineJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    protected $wwUser;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($wwUser)
    {

        $this->wwUser = $wwUser;


    }

    /**
     * Execute the job.
     *
     * @return mixed
     */
    public function handle()
    {
        //Log::info("销售自动上线队列开始执行");
        $todayTime = strtotime(date("Y-m-d ", time()) . $this->wwUser->up_time);
        if (time() >= $todayTime && (time() - $todayTime) < 240) {
           $source = '系统-定时自动上线';
            $onlineCheck = WwUser::onlineCheck($this->wwUser,$source);
           if(isset($onlineCheck) && $onlineCheck['status']){
               //记录操作日志
               //添加操作日志
               AdminActionLogJob::dispatch(
                   'time_auto_online',
                   $this->wwUser->id,
                   AdminActionLog::ACTION_TYPE['销售'],
                   '「' . $this->wwUser->name . '」ID：[' . $this->wwUser->id . ']，定时自动上线成功',
                   '',
                   $this->wwUser->admin_uid,
               )->onQueue('admin_action_log_job');
           }
        }
        return true;
    }
}
