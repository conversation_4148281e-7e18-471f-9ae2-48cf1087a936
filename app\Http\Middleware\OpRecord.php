<?php

	namespace App\Http\Middleware;

	use App\Models\OperationLog;
	use Closure;
	use Dcat\Admin\Admin;
	use Illuminate\Http\RedirectResponse;
	use Illuminate\Http\Request;
	use Illuminate\Http\Response;
	use Illuminate\Support\Facades\Redirect;

	class OpRecord
	{
		/**
		 * Handle an incoming request.
		 *
		 * @param Request                                       $request
		 * @param Closure(Request): (Response|RedirectResponse) $next
		 * @return Response|RedirectResponse
		 */
		public function handle(Request $request, Closure $next)
		{
            $domains = [
                'wind-rich.com',//saury 本地域名
                'sen.test',//Sen 本地域名
				'work.peoplesea.cn',// 温测试域名
                'admin.smart-ark.cn',//正式后台域名
                'rich-test.smart-ark.cn' //测试后台域名
            ];
			if (str_contains($request->path(), "ztfz") && !in_array($request->host(), $domains)) {
				return response()->view('errors.system-error', [
					'message' => '系统错误'
				]);
			}
			if (!Admin::user() && $request->path() != 'ztfz/auth/login' && $request->path() != 'ztfz/auth/logout') {
				return Redirect::to("ztfz/auth/logout");
			}
			//账户被禁用后，自动退出登陆
			if (Admin::user() && !Admin::user()->status && $request->path() != 'ztfz/auth/login' && $request->path() != 'ztfz/auth/logout') {
				return Redirect::to("ztfz/auth/logout");
			}
			$input = $request->all();
			$log   = new OperationLog();
			$log->setAttribute('admin_uid', Admin::user() ? Admin::user()->id : 0);
			$log->setAttribute('user_name', Admin::user() ? Admin::user()->username : "");
			$log->setAttribute('path', $request->path());
			$log->setAttribute('method', $request->method());
			$log->setAttribute('ip', getIp());
			$log->setAttribute('input', json_encode($input, JSON_UNESCAPED_UNICODE));
			$log->save();
			return $next($request);
		}
	}
