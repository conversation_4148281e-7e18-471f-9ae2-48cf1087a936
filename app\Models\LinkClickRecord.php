<?php
	
	namespace App\Models;
	
	use Dcat\Admin\Traits\HasDateTimeFormatter;
	use Illuminate\Database\Eloquent\Model;
	
	/**
	 * @property mixed     $admin_uid
	 * @property int|mixed $account_id
	 * @property int|mixed $view_count
	 * @property int|mixed $ww_link_id
	 * @property mixed     $user_id
	 * @property mixed     $id
	 */
	class LinkClickRecord extends Model
	{
		use HasDateTimeFormatter;
		
		protected $table = 'link_click_record';
		
	}
