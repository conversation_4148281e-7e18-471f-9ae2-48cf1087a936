<?php

	namespace App\Admin\Forms\WwUser;

    use App\Models\WwUser as ModelsWwUser;
    use Dcat\Admin\Contracts\LazyRenderable;
    use Dcat\Admin\Traits\LazyWidget;
    use Dcat\Admin\Widgets\Form;

    class TransferLicenseFrom extends Form implements LazyRenderable
    {
        use LazyWidget;
		/**
		 * Handle the form request.
		 *
		 * @param array $input
		 *
		 * @return mixed
		 */
		public function handle(array $input)
		{
            $toWwUserId = $input['to_ww_user_id'] ?? '';
            $wwUserId = $this->payload['ww_user_id'] ?? '';

            if (!empty($input['error_msg'])) {
                return $this->response()->alert()->error('提示')->detail($input['error_msg'] ?? '')->refresh();
            }

            $wwUser = ModelsWwUser::query()->find($wwUserId);

            if  (!$wwUser) {
                return $this->response()->alert()->error('提示')->detail('销售账号不存在，请退出或刷新后重试')->refresh();
            }

            $toWwUser = ModelsWwUser::query()->find($toWwUserId);

            $res = $wwUser->TransferTo($toWwUser);
            if (!$res['status']) {
                return $this->response()->alert()->error('提示')->detail($res['message'] ?? '')->refresh();
            }

            return $this->response()->alert()->success('提示')->detail('转移成功')->refresh();
		}

		/**
		 * Build a form here.
		 */
		public function form()
		{

            $this->html(
                <<<HTML
<div class="alert alert-info alert-dismissable">
            <h4></i>&nbsp; 提示</h4>
        1：转移成员和接收成员属于同一个企业<br/>
        2：转移成员的账号已激活，且在有效期<br/>
        3：转移许可的成员为离职成员，或不在服务商应用的可见范围内时，不限制下次转移的时间间隔<br/>
        4：转移许可的成员为在职成员且在服务商应用的可见范围内时，转移后30天后才可进行下次转移<br/>
        5：当接收成员许可与转移成员的许可重叠时（同时拥有基础账号或者互通账号），如果接收成员许可剩余时长小于等于20天则可以成功继承，否则会报错<br/>

</div>
HTML
            );
            $wwUserId = $this->payload['ww_user_id'] ?? '';

            if (!$wwUserId) {
                $this->display("提示")->value("销售账号，请退出或刷新后重试");
                $this->hidden("error_msg")->value("销售账号，请退出或刷新后重试");
                return;
            }
			$this->select('to_ww_user_id','选择转移许可证目标销售')
                ->help('请输入销售账号ID或者姓名搜索。')
                ->required()
                ->ajax(sprintf('api/getAllowTransferWwUser?ww_user_id=%s', $wwUserId));
		}

		/**
		 * The data of the form.
		 *
		 * @return array
		 */
		public function default()
		{
			return [
                'ww_user_id' => $this->payload['ww_user_id'] ?? '',
			];
		}
	}
