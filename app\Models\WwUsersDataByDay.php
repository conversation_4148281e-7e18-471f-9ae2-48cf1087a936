<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class WwUsersDataByDay extends Model
{
	use HasDateTimeFormatter;
    use SoftDeletes;
    protected $table = 'ww_users_data_by_days';

//    protected $fillable = ['id'];


// 或者没有定义$fillable，而$guarded也没有设置？


    public function wwUser(): BelongsTo
    {
        return $this->belongsTo(WwUser::class, 'ww_user_id')->withTrashed();
    }

    public function corpInfo(): BelongsTo
    {
        return $this->belongsTo(WwCorpInfo::class,'corp_id');
    }

}
