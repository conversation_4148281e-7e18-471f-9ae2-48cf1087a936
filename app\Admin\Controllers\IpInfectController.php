<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Grid\adminUser\BatchSetIpInFectStatus;
use App\Admin\Repositories\IpInfect;
use App\Models\AdminUser;
use App\Models\IpInfect as IpInfectModel;
use App\Services\CacheService;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class IpInfectController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid(): Grid
    {
        return Grid::make(new IpInfect(['adminInfo']), function (Grid $grid) {
            $grid->model()->orderBy('id', 'desc');
            $grid->column('id')->sortable();
            $grid->column('admin_uid', '用户ID');
            $grid->column("adminInfo.username", "用户");
            $grid->column('status', '状态')->using(IpInfectModel::STATUS)->label([
                0 => 'default',
                1 => 'success',
            ]);

            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) use ($grid) {
                $filter->panel();
                $filter->expand();
                $adminUidListName = AdminUser::query()
//                    ->where("parent_id", 0)
                    ->pluck("username", "id");
                $filter->equal('admin_uid', '用户')->select($adminUidListName)->loads(['corp_id', 'ww_group_id'], ['api/getCorpList', 'api/getGroupList'])->width(2);
                $filter->equal("status", '状态')->select(IpInfectModel::STATUS)->width(2);
            });
            $grid->disableViewButton();
            $grid->disableBatchActions();
            $grid->tools([new BatchSetIpInFectStatus()]);//批量修改状态
        });
    }


    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form(): Form
    {
        return Form::make(new IpInfect(), function (Form $form) {
            $form->display('id');
            $form->hidden('id');
            $adminUsers = AdminUser::query()
//                ->where("parent_id", 0)
                ->pluck("username", "id");
            $form->select('admin_uid', '用户')
                ->options($adminUsers)
                ->help('如果选择的是主账号，则主账号下所有子账号都会配置。')
                ->required();
            $form->radio('status', '状态')->options(IpInfectModel::STATUS)->default(0);
            $form->disableViewButton();
            $form->disableViewCheck();
            $form->disableDeleteButton();
        })->saving(function (Form $form) {
            $adminUid = $form->input('admin_uid');
            $adminUser = AdminUser::query()->where("id", $adminUid)->first();
//            $res = CacheService::getIpInfectData();
//            dd($res);
            $check = IpInfectModel::query()->where('admin_uid', $adminUid)->first();

            if ($form->isCreating()) {
                if ($check) {
                    return $form->response()->alert()->error('提示')->detail('该用户已配置-1');
                }
            }
            if ($form->isEditing()) {
                if ($check && $check['id'] != $form->input('id')) {
                    return $form->response()->alert()->error('提示')->detail('该用户已配置-2');
                }
            }

            if ($adminUser->parent_id == 0) { //如果是主账号
                $adminUidsList = AdminUser::getAdminUidsByParentId([$adminUid]);
            } else { //如果是自账号
                $adminUidsList = [$adminUid];
            }
            if ($adminUidsList) {
                if ($form->status == 0) { //关闭 就是往集合里新增admin_uid
                    CacheService::setIpInfectData($adminUidsList);
                } else { //开启 就是把admin_uid 从集合中移除
                    CacheService::deleteInfectData($adminUidsList);
                }
            }
        });
    }

    public function destroy($id)
    {
        if (!AdminUser::isSystemOp()) {
            return $this->form()->response()->error("无权限操作");
        }
        $adminUids = IpInfectModel::query()
            ->where("id", $id)
            ->pluck("admin_uid")
            ->toArray();

        if (!empty($adminUids)) {
            $adminUidsList = AdminUser::getAdminUidsByParentId($adminUids);
            $allConfigAdminUies = IpInfectModel::query()->where('status',0)->pluck('admin_uid')->toArray();
            if($allConfigAdminUies){ //获取所有配置的admin_uid
                if(in_array($id, $allConfigAdminUies)){ //如果当前用户ID在所有配置的admin_uid 数组中，则从要删除的的数组中排除
                    unset($adminUidsList[$id]);
                }
            }
            CacheService::deleteInfectData($adminUidsList);
        }
        return $this->form()->destroy($id);
    }

}
