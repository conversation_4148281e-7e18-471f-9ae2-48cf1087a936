<?php

namespace App\Console\Commands\WwUser;

use App\Models\CorpLicenseActiveCode;
use App\Models\CorpLicenseOrders;
use App\Models\WwUser;
use App\Services\Corp\WwCorpApiService;
use App\Services\Corp\WwProvApiService;
use App\Services\NotifySendService;
use App\Services\ProviderService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Symfony\Component\Console\Command\Command as CommandAlias;

class CheckCorpLicenseOrderPayJob extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'Wwuser:checkCorpLicenseOrderPayJob';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '更新未激活企微许可证订单';

    const STATUS = [
        1 => '支付成功',
        2 => '支付任务执行中',
        3 => '支付失败'
    ];

    const PAY_RESULT = [
        701160 => '存在未通过支付检查的企业',
        90432 => '充值账户未开通',
        90433 => '账户余额不足',
        701161 => '订单已经指定使用微信网银支付',
        48001 => '支付人无权限',
        701005 => '无效的接口许可订单id',
        701084 => '订单不是待支付状态',
        701147 => '部分订单超过了单比支付金额上限'
    ];

    /**
     * Execute the console command.
     *
     * @return int
     * @throws \Exception
     */
    public function handle()
    {
        Log::info('更新未激活企微许可证订单定时任务');
        $orderList = CorpLicenseOrders::query()
//            ->where('id',62)
            ->where(
            [
                'is_wx_pay' => 1,
                'active_result' => 0
            ])->get();
        if($orderList->isEmpty()){
            return false;
        }
        /** @var CorpLicenseOrders $item */
        foreach ($orderList as $item) {
            $payJobIds = json_decode($item->prov_pay_jobid, true);
            if (empty($payJobIds)) {
                Log::info('企微许可证订单-订单已支付，但是没有发起余额支付任务，请立即查看，订单ID：' . $item->id);

                //如果支付后没有prov_pay_jobid， 则再次提尝试交余额支付订单任务
                $submitPayJob = ProviderService::submitPayJob($item->order_id);
                Log::info(json_encode($submitPayJob));
                if (isset($submitPayJob['jobid'])) {
                    $jobIds = json_decode($item->prov_pay_jobid, true);
                    if (!$jobIds) {
                        $jobIds = [];
                    }
                    $jobIds[$submitPayJob['jobid']] = [
                        'time' => date("Y-m-d H:i:s")
                    ];
                    $item->prov_pay_jobid = json_encode($jobIds);
                    $item->is_prov_pay_submit = 1;
                    $item->save();
                }
                throw new \Exception($item->id . "订单已支付，但是没有发起余额支付任务，请立即查看");
            }
            $checkCount = 0;
            foreach ($payJobIds as $payJobId => $payJobData) {
                if (isset($payJobData['status']) && in_array($payJobData['status'], [0, 1, 3])) {
                    Log::info('企微许可证订单-订单已支付-跳过status状态为0, 1, 3的，订单ID：' . $item->id . '，status: ' . $payJobData['status']);
                    continue;
                }
                $checkCount++;
                $resp = ProviderService::payJobResult($payJobId);
                Log::info('企微许可证订单-获取订单支付结果，订单ID：' . $item->id  . '，结果：' . json_encode($resp));
                if (isset($resp['errcode']) && $resp['errcode'] == 0) {
                    $payJobIds[$payJobId]['status'] = $resp['status'];
                    $payJobIds[$payJobId]['status_string'] = self::STATUS[$resp['status']] ?? "未知";
                    $payJobIds[$payJobId]['pay_job_result'] = $resp['pay_job_result'] ?? [];

                    $payResultCode = 0;
                    if (isset($resp['pay_job_result']['errcode'])) {
                        $payResultCode = $resp['pay_job_result']['errcode'];
                    }
                    $payJobIds[$payJobId]['pay_job_result']['errcode_string'] = self::PAY_RESULT[$payResultCode] ?? "未知";
                    if ($resp['status'] == 1) {
                        Log::info('企微许可证订单-获取订单支付结果->成功，订单ID：' . $item->id);
                        $this->info("支付成功");
                        $item->is_prov_pay_success = 1;
                    }
                }
            }
            $item->prov_pay_jobid = json_encode($payJobIds, JSON_UNESCAPED_UNICODE);
            $item->save();
            if ($item->is_prov_pay_success != 1 && $checkCount <= 0) {
                $this->info("无执行中的任务，新建一条支付任务");
                Log::info('企微许可证订单-无执行中的任务，新建一条支付任务，订单ID：' . $item->id);
                $resp = ProviderService::submitPayJob($item->order_id); //用余额支付订单 - 提交余额支付订单任务
                if (isset($resp['jobid'])) {
                    $jobIds = json_decode($item->prov_pay_jobid, true);
                    if (!$jobIds) {
                        $jobIds = [];
                    }
                    $jobIds[$resp['jobid']] = [
                        'time' => date("Y-m-d H:i:s")
                    ];
                    $item->prov_pay_jobid = json_encode($jobIds);
                    $item->save();
                } else {
                    Log::info('企微许可证订单-余额支付订单失败，订单ID：' . $item->id . '，支付失败原因：'. $resp['errmsg'] ?? '');
                    NotifySendService::sendWorkWeixinForError($item->id . "订单，支付失败，请立即查看：" . $resp['errmsg']);
                }
            }
            if ($item->is_prov_pay_success) {
                $this->info("企微许可证-余额支付成功，开始获取激活码，订单ID：" . $item->id);
                Log::info('企微许可证-余额支付成功，开始获取激活码，订单ID：' . $item->id);
                $resp = ProviderService::listOrderAccount($item->order_id);
                Log::info('企微许可证-余额支付成功，获取订单中的账号列表，订单ID：' . $item->id  . '，账号列表' . json_encode($resp));

                if ($resp['errcode'] == 0) {
                    //获取本次需要激活的销售
                    $wwUserIds = json_decode($item->ww_users_id, true);
                    $wwUsers = [];
                    foreach ($wwUserIds as $wwUserId) {
                        $wwTemp = WwUser::query()->where("corp_id", $item->corp_id)->where('user_id', $wwUserId)->orderByDesc("id")->first();
                        if ($wwTemp) {
                            $wwUsers[] = $wwTemp->toArray();
                        }
                    }

                    if (count($wwUsers) != count($resp['account_list'])) {
                        Log::info('企微许可证订单-【购买个数】与【提交个数】不一致，订单ID：' . $item->id);
                        NotifySendService::sendWorkWeixinForError($item->id . "许可证订单【购买个数】与【提交个数】不一致，请立即查看");
                    }
                    $wwUserList = $wwUsers;
                    $i = 0;
                    //整理需要激活的列表
                    $active_list = [];

                    foreach ($resp['account_list'] as $activeCodeData) {
                        $where = [
                            'corp_license_order_id' => $item->order_id, //企微订单ID 下单购买账号接口返回的订单
                            'order_id' => $item->id,//corp_license_orders表主键ID
                            'active_code' => $activeCodeData['active_code']
                        ];
                        if (!CorpLicenseActiveCode::query()->where($where)->whereNotNull("use_time")->exists()) {
                            $activeCodeObj = new CorpLicenseActiveCode;

                            $activeCodeObj->corp_license_order_id = $item->order_id; //企微订单ID 下单购买账号接口返回的订单
                            $activeCodeObj->order_id = $item->id;//corp_license_orders表主键ID
                            $activeCodeObj->active_code = $activeCodeData['active_code'];
                            $activeCodeObj->corp_id = $item->corp_id;
                            if (!empty($activeCodeData['userid'])) {
                                $user_id = $activeCodeData['userid'];
                            } else {
                                if (isset($wwUserList[$i])) {

                                    $user_id = $wwUserList[$i]['user_id'];
                                    $activeCodeObj->ww_user_id = $wwUserList[$i]['id'];
                                    $i++;
                                    Log::info('企微许可证订单-需要激活的ID：' . $user_id);
                                    //需要激活的ID
                                    $active_list[] = [
                                        'active_code' => $activeCodeObj->active_code,
                                        'userid' => $user_id
                                    ];
                                } else {
                                    $user_id = 0;
                                }
                            }
                            $activeCodeObj->userid = $user_id;
                            $activeCodeObj->type = $activeCodeData['type'];
                            $activeCodeObj->save();
                        }else{
                            Log::info('企微许可证订单-购买成功-查询激活码已存在，订单ID：' . $item->id . '，where 条件：' . json_encode($where));
                        }
                    }

                    if (!empty($active_list)) {
                        $item->active_userId_list = json_encode($active_list);
                        $item->save();
                        Log::info('企微许可证订单-批量激活账号，订单ID：' . $item->id);
                        $resp = ProviderService::batchActiveAccount($item->corpInfo->corp_id, $active_list);
                        $item->active_result_json = json_encode($resp);
                        $item->save();

                        $successNum = 0;
                        $failNum = 0;
                        foreach ($resp['active_result'] as $activeResp) {
                            $where = [
                                'corp_license_order_id' => $item->order_id,//企微订单ID 下单购买账号接口返回的订单id
                                'order_id' => $item->id,//corp_license_orders表主键ID
                                'active_code' => $activeResp['active_code']
                            ];
                            $activeCode = CorpLicenseActiveCode::query()->where($where)->first();
                            if ($activeCode) {
                                $activeCode->resp_user_id = $activeResp['userid'];
                                $activeCode->errcode = $activeResp['errcode'];
                                $activeCode->use_time = date("Y-m-d H:i:s", time());
                                $activeCode->save();
                                if ($activeResp['errcode'] == 0) {
                                    $successNum++;
                                } else {
                                    $failNum++;
                                }
                            }
                            /** @var WwUser $wwUser */
                            $wwUser = WwUser::query()->find($activeCode->ww_user_id);
                            if ($wwUser && str_contains($wwUser->cus_acq_link_name, "user list or group creater no license")) {
                                $linkData = WwCorpApiService::create_link($wwUser->corpInfo, $wwUser);
                                if ($linkData['errcode'] == 0 && isset($linkData['link'])) {
                                    $wwUser->cus_acq_link_status = 1;
                                    $wwUser->cus_acq_link_status_message = $linkData['errmsg'];
                                    $wwUser->cus_acq_link_id = $linkData['link']['link_id'];
                                    $wwUser->cus_acq_link_name = $linkData['link']['link_name'];
                                    $wwUser->cus_acq_link_url = $linkData['link']['url'];
                                    $wwUser->cus_acq_link_skip_verify = 1;
                                } else {
                                    $wwUser->cus_acq_link_status = 0;
                                    $wwUser->cus_acq_link_status_message = $linkData['errmsg'] ?? '';
                                }
                            }
                        }
                        if ($successNum > 0) {
                            if ($failNum > 0) {
                                $item->active_result = 2;
                            } else {
                                $item->active_result = 1;
                            }
                            $item->save();
                            Log::info('企微许可证订单-激活状态：' . $item->active_result . '，订单ID：' . $item->id);
                            WwProvApiService::checkLicenseTime($item->corpInfo);
                        }
                    }else{
                        Log::info('企微许可证订单，没有需要激活的销售列表，订单ID：' . $item->id);
                    }
                }else{
                    Log::info('支付失败，订单ID：' . $item->id);
                }
            }
        }
        return CommandAlias::SUCCESS;
    }
}
