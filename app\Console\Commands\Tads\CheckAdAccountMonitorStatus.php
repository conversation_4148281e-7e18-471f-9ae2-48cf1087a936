<?php

namespace App\Console\Commands\Tads;

use App\Models\TencentAdAccount;
use App\Models\TencentAdAccountMonitorCount;
use App\Models\WwLink;
use App\Models\WwUserAddRecord;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CheckAdAccountMonitorStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'Tads:CheckAdAccountMonitorStatus';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '判断广告账户获取监测数据状态';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Log::info('判断广告账户获取监测数据状态并更新');
        $accountIds = TencentAdAccountMonitorCount::query()->pluck('account_id')->toArray();
        if(empty($accountIds)) {
            return false;
        }
        $accountList = TencentAdAccount::query()
            ->whereIn('account_id', $accountIds)
//            ->where('monitor_status',1)
            ->get();
        if($accountList->isNotEmpty()){
            foreach ($accountList as $account){
               $createWwLink = WwLink::query()->where('account_id', $account->account_id)->orderByDesc('id')->first();
               if(!$createWwLink){
                   $account->monitor_status = 0;
                   Log::info('判断广告账户获取监测数据状态并更新-账户ID：' . $account->account_id . '，【未创建投放链接】，monitor_status字段修改为0');
               }
               if($createWwLink){
                   $threeDaysAddRecord = WwUserAddRecord::query()
                       ->where('admin_uid', $createWwLink->admin_uid)
                       ->where('ww_link_id', $createWwLink->id)
                       ->where('external_contact_created_at', '>=', date('Y-m-d', strtotime('-3 days')))
                       ->first();
                   if(!$threeDaysAddRecord){
                       if($account->monitor_status == 0){ //如果就是0 的话，就跳过
                           continue;
                       }
                       $account->monitor_status = 0;
                       Log::info('判断广告账户获取监测数据状态并更新-账户ID：' . $account->id . '，【三天内“无”进粉记录】，monitor_status字段修改为0');
                   }else{
                       if($account->monitor_status == 1){ //如果就是1 的话，就跳过
                           continue;
                       }
                       Log::info('判断广告账户获取监测数据状态并更新-账户ID：' . $account->id . '，【三天内“有”进粉记录】，monitor_status字段修改为1');
                       $account->monitor_status = 1;
                   }
               }
               $account->save();
            }
        }
        return Command::SUCCESS;
    }
}
