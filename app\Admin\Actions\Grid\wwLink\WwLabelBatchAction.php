<?php

namespace App\Admin\Actions\Grid\wwLink;

use App\Admin\Forms\WwLink\WwLabelBatchActionForm;
use Dcat\Admin\Grid\BatchAction;
use Dcat\Admin\Widgets\Modal;
use Illuminate\Http\Request;

class WwLabelBatchAction extends BatchAction
{
    protected $title = '<button class="btn btn-primary ww_user_batch_btn"><i class="feather icon-tag"></i>&nbsp&nbsp<span class="selected"></span>企微标签</button>';

    // 注意action的构造方法参数一定要给默认值
    public function handle(Request $request)
    {
        return $this->response()
            ->success('Processed successfully: '.json_encode($this->getKey()))
            ->redirect('/');
    }
    public function render()
    {
        // 实例化表单类
        $form = WwLabelBatchActionForm::make();

        return Modal::make()
            ->lg()
            ->title('批量标签')
            // 因为此处使用了表单异步加载功能，所以一定要用 onLoad 方法
            // 如果是非异步方式加载表单，则需要改成 onShow 方法
            ->onLoad($this->getModalScript())
            ->button($this->title)
            ->body($form);
    }
    protected function getModalScript()
    {
        $warning = __('请选择记录');
        // 弹窗显示后往隐藏的id表单中写入批量选中的行ID
        return <<<JS
		// 获取选中的ID数组
		var key = {$this->getSelectedKeysScript()}
		if (key.length === 0) {
		$('.modal').modal('hide');
		        Dcat.swal.warning('{$warning}');
		        return false;
	    }
		$('#reset-ww_link_label_id').val(key);
JS;
    }
}
