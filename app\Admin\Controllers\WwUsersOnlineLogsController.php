<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\WwUsersOnlineLogs;
use App\Models\AdminUser;
use App\Services\Tools\UtilsService;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\Request;

class WwUsersOnlineLogsController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid(): Grid
    {
        return Grid::make(new WwUsersOnlineLogs(['adminInfo','corpInfo','wwUserInfo']), function (Grid $grid) {
            /** 对话框新增 */
//            $grid->disableCreateButton();
//            $grid->tools(function (Grid\Tools $tools) {
//                $className = collect(Request::segments())->last();
//                $tools->append(UtilsService::dialogForm('新增',Request::url().'/create',"create-{$className}"));
//            });
            $grid->model()->orderBy('id', 'desc');
            $grid->column('id')->sortable();
            $grid->column('ww_user_id','销售ID');
            $grid->column('wwUserInfo.name','销售名称');
            $grid->column('wwUserInfo.user_id', '销售账号ID');
            $grid->column('admin_uid','用户ID');
            $grid->column("adminInfo.username", "用户");
            $grid->column("corpInfo.corp_name", "企微");
            $grid->column('online_status', '状态')->using([0 => '下线', 1 => '上线'])->label([
                0 => 'danger',
                1 => 'success',
            ]);
            $grid->column('source','来源');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) use ($grid) {
                $filter->panel();
                $filter->expand();
                $filter->equal('ww_user_id', '销售ID')->width(2);
                $filter->equal('wwUserInfo.name', '销售名称')->width(2);
                $filter->equal('wwUserInfo.user_id', '销售账号ID')->width(2);
                $adminUidListName = AdminUser::query()->pluck("username", "id");
                $filter->equal('admin_uid', '用户')->select($adminUidListName)->load('corp_id','api/getCorpList')->width(2);
                $filter->equal('corp_id', '企微')->select()->width(2);
                $filter->equal("online_status", '状态')->select([0 => '下线', 1 => '上线'])->width(2);
                $filter->like('source', '来源')->width(2);
            });
            $grid->disableViewButton();
            $grid->disableBatchActions();
            $grid->disableCreateButton();
            $grid->disableDeleteButton();
            $grid->disableActions();
        });
    }

    protected function form()
    {
        return null;
    }

    protected function detail($id){
        return null;
    }

}
