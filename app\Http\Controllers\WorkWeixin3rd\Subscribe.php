<?php

	namespace App\Http\Controllers\WorkWeixin3rd;

	use App\Models\AdminUser;
    use App\Models\WwCorpInfo;
	use App\Models\WwUser;
    use App\Services\NotifySendService;
    use Illuminate\Support\Facades\Log;

	/**
	 * 好友变动通知
	 */
	class Subscribe
	{
		public function handler($message,$param): bool
        {
			//Event - 自建应用的通知方式
			//InfoType - 第三方应用的通知方式
            $message = json_decode($message, true);

			$infoType = $message['Event'] ?? "";
			if (empty($infoType)) {
				$infoType = $message['InfoType'] ?? "";
			}
			if ($infoType == 'subscribe' || $infoType == 'unsubscribe') {
				//Log::info('Event - subscribe - MESSAGE：' . json_encode($message));
				/** @var WwCorpInfo $corpInfo */
				$corpInfo = WwCorpInfo::withTrashed()->where("corp_id", $message['ToUserName'])->first();
				if ($corpInfo) {
					/** @var WwUser $wwUser */
					$wwUsers  = WwUser::query()->where("corp_id", $corpInfo->id)->where("open_user_id", $message['FromUserName'])->get();
					$wwUsers_ = WwUser::query()->where("corp_id", $corpInfo->id)->where("user_id", $message['FromUserName'])->get();
					$wwUsers  = $wwUsers->merge($wwUsers_);
					foreach ($wwUsers as $wwUser) {
						if ($infoType == 'subscribe') {
							$wwUser->subscribe = 1;
							$wwUser->save();
						} else {
							$wwUser->subscribe     = 0;
							$wwUser->online_status = 0;
							$wwUser->save();
                            $adminUser = AdminUser::query()->where("id", $wwUser->admin_uid)->first();
							NotifySendService::sendWorkWeixinForError("[企微回调][可见范围] " . $wwUser->id . "「" . $wwUser->user_id . "」取消了可见范围，已自动下线，请及时查看");
                            $robotsMessage = "[销售可见范围] " . "「" . $adminUser->username . "」用户的「" . $wwUser->corpInfo->corp_name . "」企微下「"  . $wwUser->name . "」「ID："  . $wwUser->id  . "」「账号："  . $wwUser->user_id  . "」销售被取消了可见范围，请及时查看";
                            NotifySendService::sendCustomerForMessageByRobots($adminUser,$robotsMessage);
						}
					}
				} else {
                    Log::info("[企微回调][可见范围] 回调无法获取到相关的企业信息，请立即查看，相关数据如下：" . json_encode($message));
					//NotifySendService::sendWorkWeixinForError("[企微回调][可见范围] 回调无法获取到相关的企业信息，请立即查看，相关数据如下：" . json_encode($message));
				}
			}
			return true;
		}
	}
