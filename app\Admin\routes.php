<?php

use App\Admin\Controllers\AdAccountMonitorCountController;
use App\Admin\Controllers\AdBatchUpdateTargetingController;
use App\Admin\Controllers\AdminActionLogController;
use App\Admin\Controllers\AdminBillController;
use App\Admin\Controllers\AdminBlockingAreaController;
use App\Admin\Controllers\AdminDomainController;
use App\Admin\Controllers\AdminUserController;
use App\Admin\Controllers\AdminSubUserAuthCorpController;
use App\Admin\Controllers\AdminSubUserController;
use App\Admin\Controllers\AdminUserMessageRobotsController;
use App\Admin\Controllers\AdOppoAccountController;
use App\Admin\Controllers\AdVivoAccountController;
use App\Admin\Controllers\AdYoukuAccountController;
use App\Admin\Controllers\AgainSetTagTaskController;
use App\Admin\Controllers\AnnouncementController;
use App\Admin\Controllers\ApiController;
use App\Admin\Controllers\AskDataController;
use App\Admin\Controllers\CorpLicenseOrderController;
use App\Admin\Controllers\CountCorpsDataController;
use App\Admin\Controllers\CustomerDomainController;
use App\Admin\Controllers\CustomerUserController;
use App\Admin\Controllers\DmpCustomAudienceController;
use App\Admin\Controllers\ExportOpenIdController;
use App\Admin\Controllers\FileController;
use App\Admin\Controllers\FormDataController;
use App\Admin\Controllers\GeneratePageController;
use App\Admin\Controllers\ExportTaskController;
use App\Admin\Controllers\IpInfectController;
use App\Admin\Controllers\MakeDomainTaskController;
use App\Admin\Controllers\OperateToolsController;
use App\Admin\Controllers\ShieldIpController;
use App\Admin\Controllers\ShieldPolicyController;
use App\Admin\Controllers\SwitchNativePageController;
use App\Admin\Controllers\SwitchUserController;
use App\Admin\Controllers\TencentAdAccountController;
use App\Admin\Controllers\TenCentAdTrackClickConfigController;
use App\Admin\Controllers\WhiteUserListController;
use App\Admin\Controllers\WwAdViewClickRecordController;
use App\Admin\Controllers\WwLinkController;
use App\Admin\Controllers\WwTplController;
use App\Admin\Controllers\WwUserAddRecordController;
use App\Admin\Controllers\WwUserController;
use App\Admin\Controllers\WwUserCorpsDataByDayController;
use App\Admin\Controllers\WwUserGroupsDataByDayController;
use App\Admin\Controllers\WwUsersDataByDayController;
use App\Admin\Controllers\WwUsersGroupController;
use App\Admin\Controllers\WwUsersOnlineLogsController;
use App\Models\Announcement;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use Dcat\Admin\Admin;

Admin::routes();

Route::group([
    'prefix'     => config('admin.route.prefix'),
    'namespace'  => config('admin.route.namespace'),
    'middleware' => config('admin.route.middleware'),
], function (Router $router) {

    $router->get('/', 'HomeController@index');
    $router->get('switch-user/{id}', [SwitchUserController::class, 'switchUser']);

    $router->resource("customer_user", CustomerUserController::class);//客户管理
    //客户账号管理
    $router->resource("sub_account", AdminSubUserController::class);//子账户管理
    $router->resource("robots_config",AdminUserMessageRobotsController::class);//其他配置
    $router->resource("action_logs", AdminActionLogController::class);//操作日志
    $router->resource("/auth/domain", CustomerDomainController::class);//账户域名列表

    //公告管理
    $router->resource('Announcement', AnnouncementController::class);
    //已读公告
    $router->any('readAnnouncement', [Announcement::class,'readAnnouncement']);

    //媒体账户管理
    //腾讯
    $router->resource("tencent", TencentAdAccountController::class);//腾讯投放账户管理
    $router->get("tencent_ad/auth/callback", [TencentAdAccountController::class, 'createAuthCallBack'])->name('tencent-ad.auth.callback');//腾讯广告账户授权回调
    $router->resource("track_click_config_for_tencent", TenCentAdTrackClickConfigController::class);//腾讯广告监测链接组配置
    $router->any("api/search/tencent_ad_account",[TencentAdAccountController::class,'searchTencentAdAccount'])->name('api.tencent-ad-account');//搜索腾讯广告账户API


    //oppo
    $router->resource("oppo", AdOppoAccountController::class);//opp广告账户管理
    $router->any("api/oppo_ad_account",[AdOppoAccountController::class,'getAdAccount'])->name('api.oppo-ad-account');//获取名下可用的账户ID

    //vivo
    $router->resource("vivo", AdVivoAccountController::class);//vivo投放账户管理
    $router->any("api/vivo_ad_account",[AdVivoAccountController::class,'getAdAccount'])->name('api.vivo-ad-account');//获取名下可用的账户Id
    $router->any("vivo_ad/auth/callback",[AdVivoAccountController::class,'authCallBack'])->name('vivo-ad-account.auth.callback');//vivo授权回调

    //优酷
    $router->resource("youku", AdYoukuAccountController::class);//优酷广告账户管理
    $router->any("api/youku_ad_account",[AdYoukuAccountController::class,'getAdAccount'])->name('api.youku-ad-account');//获取名下可用的账户ID


    //企微管理
    $router->resource("corp", AdminSubUserAuthCorpController::class);//企微管理
    $router->any("ww_corp/install", [AdminSubUserAuthCorpController::class, 'installApp'])->name("ww_corp_install");                          //企微安装
    $router->any("ww_corp/install_callback", [AdminSubUserAuthCorpController::class, 'installAppCallBack'])->name("ww_corp_install_callback");//企微安装回调
    $router->any("api/asyncGetWwCorpLabel",[ApiController::class,'asyncGetWwCorpLabel'])->name('api.asyncGetWwCorpLabel');//异步获取企微标签
    $router->resource("corp_license_order",CorpLicenseOrderController::class);//购买许可证


    //销售管理
    $router->resource("sale", WwUserController::class); //销售客服管理
    $router->resource("sale_groups", WwUsersGroupController::class);//销售客服分组
    $router->resource("online_logs",WwUsersOnlineLogsController::class);//销售上下线日志
    $router->any("api/getAllowTransferWwUser",[ApiController::class,'getAllowTransferWwUser'])->name('api.getAllowTransferWwUser');//获取可转移许可证的销售

    //投放管理
    $router->resource("posting_link", WwLinkController::class);                    //企微投放链接
    $router->resource("panding_page", WwTplController::class);                //落地页模板
    $router->get("PreviewPage/{id}", [WwTplController::class, 'viewTpl']);              //预览落地页

    //防水墙，也叫拦阻器
    $router->resource("shield_rules", ShieldPolicyController::class);    //屏蔽规则
    $router->any("api/getShieldPolicieWwGroupList",[ApiController::class,'getShieldPolicieWwGroupList'])->name('api.getShieldPolicieWwGroupList');//获取某个用户的屏蔽分组
    $router->any("api/getShieldPolicieAuditTpl",[ApiController::class,'getShieldPolicieAuditTpl'])->name('api.getShieldPolicieAuditTpl');//获取某个用户的屏蔽页面
    $router->resource("ip_blacklist", ShieldIpController::class);       //IP黑名单
    $router->resource("white_list", WhiteUserListController::class);  //白名单



    //运营相关
    $router->resource("dmpAudiences", DmpCustomAudienceController::class);//人群包管理
    $router->resource("oTools", OperateToolsController::class);//运营工具箱


    //系统工具
    $router->resource("domains", AdminDomainController::class);  //域名管理
    $router->resource("again_set_tag_task",AgainSetTagTaskController::class);//补打标签任务
    $router->resource("export_task",ExportTaskController::class);//导出任务
    $router->resource("generate_page", GeneratePageController::class);//生成页面
    $router->resource("switch_native_page", SwitchNativePageController::class);//腾讯广告账户切换原生页
    $router->resource("make_domain", MakeDomainTaskController::class);//生成域名
    $router->any("api/switch/ad_account/getList",[ApiController::class,'getAdAccountList']);//获取名下可用的账户ID
    $router->resource("admin_blocking_area", AdminBlockingAreaController::class);//设置404地域
    $router->resource("export_openid", ExportOpenIdController::class); // 导出openid

    $router->any("api/getGroupList",[ApiController::class,'getGroupList'])->name('api.getGroupList');//获取分组列表
    $router->any("api/getCorpList",[ApiController::class,'getCorpList'])->name('api.getCorpList');//获取企微列表
    $router->resource("ip_infect", IpInfectController::class);//ip传染配置





    //进粉记录
    $router->get("ww_user_add_record/xls-export", [WwUserAddRecordController::class, 'xlsExport']);//进粉记录xlswriter导出
    $router->resource("add_friends_record", WwUserAddRecordController::class);//进粉记录

    //数据统计
    $router->resource("sale_count",WwUsersDataByDayController::class);//销售统计-按天
    $router->resource("sale_group_count",WwUserGroupsDataByDayController::class);//销售组统计-按天
    $router->resource("sale_crop_count",WwUserCorpsDataByDayController::class);//按企微主体统计-天
    $router->resource("crop_count",CountCorpsDataController::class);//按企微主体汇总统计-天

    //实时访客
    $router->resource("realtime_visitors",WwAdViewClickRecordController::class);  //实时访客
    $router->any("realtime_visitors/getRealTimeData",[WwAdViewClickRecordController::class,'getRealTimeData']);//实时访客获取实时数据

    $router->resource("ad_monitor_tencent",AdAccountMonitorCountController::class);//广告账户数据监测

    //落地页数据
    $router->resource("page_form_data", FormDataController::class);//落地页表单数据
    $router->resource("page_ask_data", AskDataController::class);//问答提交数据

    //广告工具
    $router->any("ad/crowd_list/get", [ApiController::class,'getAudienceList'])->name('ad.crowd-list.get');//广告定向获取人群包
    $router->resource("ad/directional", AdBatchUpdateTargetingController::class);//修改广告定向
    $router->any("users/files", [FileController::class,'handle']);

    $router->resource("nE2QhUbom6XyzKEFU6E9", AdminBillController::class);//账单管理

});
