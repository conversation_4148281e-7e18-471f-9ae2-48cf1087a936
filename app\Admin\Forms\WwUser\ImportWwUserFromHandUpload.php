<?php

    namespace App\Admin\Forms\WwUser;

    use App\Jobs\AdminActionLogJob;
    use App\Models\AdminActionLog;
    use App\Models\AdminSubUserAuthCorp;
    use App\Models\WwAppList;
    use App\Models\WwCorpInfo;
    use App\Models\WwUser;
    use App\Models\WwUsersGroup;
    use App\Models\WwUsersGroupsRel;
    use App\Services\Tools\LogService;
    use Dcat\Admin\Admin;
    use Dcat\Admin\Widgets\Form;
    use Illuminate\Support\Facades\DB;

    class ImportWwUserFromHandUpload extends Form
    {
        /**
         * Handle the form request.
         *
         * @param array $input
         *
         * @return mixed
         */
        public function handle(array $input)
        {
            LogService::inputLog('Tools','销售客服管理-手动上传', $input, Admin::user()->id, Admin::user()->username);
            $corp_auth_id = $input['corp_auth_id'];
            /** @var AdminSubUserAuthCorp $corpAuthRecord */
            $corpAuthRecord = AdminSubUserAuthCorp::query()->with("corpInfo")->find($corp_auth_id);
            if (!$corpAuthRecord || !$corpAuthRecord->corpInfo) {
                return $this->response()->withValidation([
                    'corp_auth_id' => '请检查企业微信是否已授权'
                ])->error('请检查企业微信是否已授权');
            }

            /** @var WwAppList $wwApp */
            $wwApp = WwAppList::query()->where("suite_id", $corpAuthRecord->corpInfo->suite_id)->first();
            if (!$wwApp) {
                return $this->response()->error('导入失败，企微应用不存在，请联系管理员');
            }

            $qrCodeImages = explode(",", $input['qr_code_img']);
            if (count($qrCodeImages) != count($input['wechat'])) {
                return $this->response()->error('配置的群码和群名称 数量不一致.');
            }

            DB::beginTransaction();
            foreach ($qrCodeImages as $key => $datum) {
                $wwUser = new WwUser();

                //基础信息
                $wwUser->admin_uid = Admin::user()->id;
                $wwUser->ww_app_id = $wwApp->id;
                $wwUser->corp_id = $corpAuthRecord->corpInfo->id;
                $wwUser->corp_auth_id = $corpAuthRecord->id;
                $wwUser->type = 3;
                $wwUser->open_user_id = '手动上传';
                $wwUser->user_id = '手动上传';
                $wwUser->qrcode = $datum;
                $wwUser->qrcode_config_id = '手动上传';
                $wwUser->add_method = 2;
                $wwUser->name = $input['wechat'][$key];
                $wwUser->alias = "";
                $wwUser->cus_acq_link_name = '';


                $wwUser->weight = $input['weight'];
                $wwUser->wind_label = $input['label'] ?? "";

                $wwUser->cus_acq_link_status = 0;
                $wwUser->cus_acq_link_status_message = '手动上传不支持';

                $wwUser->auto_status_config = $input['auto_status_config'];
                $wwUser->up_time = $input['up_time'];
                $wwUser->down_time = $input['down_time'];
                $wwUser->down_add_count = $input['down_add_count'];
                $wwUser->subscribe = 1;
                $wwUser->save();

                //处理分组数据
                if (!empty($input['group_id'])) {
                    WwUsersGroupsRel::createWwGroupRel($wwUser, $input['group_id']);
                }
                //添加操作日志队列
                AdminActionLogJob::dispatch(
                    'create_ww_user_group',
                    $wwUser->id,
                    AdminActionLog::ACTION_TYPE['销售'],
                    '操作导入「' . $wwUser->id . '」-「' . $wwUser->name . '」手动群码。',
                    getIp(),
                    Admin::user()->id,
                )->onQueue('admin_action_log_job');
            }
            DB::commit();
            return $this->response()->alert()->success('提示')->detail('导入成功。')->refresh();
        }

        /**
         * Build a form here.
         */
        public function form()
        {
            $corpAuthRecord = AdminSubUserAuthCorp::getCorpAuthListBuyAdminUid(Admin::user()->id);
            $this->select("corp_auth_id", "企业微信")->options($corpAuthRecord)->required();
            $this->multipleImage('qr_code_img', "微信二维码")->autoUpload()->uniqueName()->help("如无特殊设计，图片宽度应尽量与页面宽度一致")->required()->saving(function ($paths) {
                return implode(",", $paths);
            });
            $this->list('wechat', '群名称')->required()->default([''])->help("上传N个群码，需要配置N个群名称，一一对应");
            $this->multipleSelect('group_id', '分组')->options(WwUsersGroup::query()->where("admin_uid", Admin::user()->id)->pluck("title", 'id'))->help("导入后，销售将自动进入该分组，如无请先创建");
            $this->text("label", "智投管理标签")->help("该标签为智投后台批量管理销售客服使用的标签，可通过标签批量配置客服，不是企业微信后台的进粉标签");
            $this->radio('auto_status_config', '自动上下线')->options([
                0 => '关闭',
                1 => '按时间段',
                2 => '按加粉量',
            ])->when([1, 2], function ($form) {
                $this->time('up_time', '上线时间');
            })->when([1], function ($form) {
                $form->time('down_time', '下线时间');
            })->when([2], function ($form) {
                $form->number('down_add_count', '下线粉丝量')->help("当日加够数量大于等于改数量，将会自动下线该销售");
            })->default(0);
            $this->number('weight', '权重')->help("可以配置1-10的数字，数字越大，该销售展示概率越高")->default(1);
        }

        /**
         * The data of the form.
         *
         * @return array
         */
        public function default()
        {
            return [

            ];
        }
    }

