<?php

namespace App\Admin\Extensions\Grid\Exporters;

use Dcat\Admin\Grid\Exporters\AbstractExporter;
use Vtiful\Kernel\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Dcat\Admin\Admin;

class XlsxWriterExporter extends AbstractExporter
{
    public function export()
    {
        try {
            // 1. 准备文件路径
            $filename = $this->getFilename();
            $tempDir = sys_get_temp_dir();
            $fullPath = $tempDir.'/'.$filename;

            // 2. 初始化Excel
            $excel = new Excel(['path' => $tempDir]);
            $file = $excel->fileName($filename);

            // 3. 设置表头和数据
            $headers = $this->getValidHeaders();
            $data = $this->getValidData();

            $file->header($headers);
            foreach ($data as $row) {
                $file->data($row);
            }

            // 4. 生成文件（确保路径正确）
            $generatedPath = $file->output();

            // 调试：检查文件是否生成
            if (!file_exists($generatedPath)) {
                throw new \Exception("Excel文件生成失败，路径：{$generatedPath}");
            }

            // 5. 返回下载响应（关键修正）
            return $this->createDownloadResponse($generatedPath, $filename);

        } catch (\Exception $e) {
            Admin::script("Dcat.error('导出失败: {$e->getMessage()}')");
            return back();
        }
    }

    /**
     * 创建下载响应（关键修正）
     */
    protected function createDownloadResponse($path, $filename)
    {
        // 确保文件存在
        if (!file_exists($path)) {
            throw new \Exception("文件不存在：{$path}");
        }

        // 清除输出缓冲
        if (ob_get_level()) {
            ob_end_clean();
        }

        // 创建响应
        $response = new BinaryFileResponse($path);
        $response->setContentDisposition(
            BinaryFileResponse::DISPOSITION_ATTACHMENT,
            $filename,
            iconv('UTF-8', 'ASCII//TRANSLIT', $filename)
        );
        $response->deleteFileAfterSend(true);

        // 添加必要的头信息
        $response->headers->set('Content-Type',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $response->headers->set('Pragma', 'public');
        $response->headers->set('Cache-Control', 'max-age=0');

        return $response;
    }

    /**
     * 获取有效数据（确保不为空）
     */
    protected function getValidData()
    {
        $data = $this->grid->buildData(true)
            ->map(function ($item) {
                return array_map(function ($value) {
                    if (is_null($value)) return '';
                    if (is_object($value)) return method_exists($value, '__toString')
                        ? (string)$value
                        : json_encode($value);
                    return $value;
                }, (array)$item);
            })
            ->toArray();

        return empty($data) ? [['暂无数据']] : $data;
    }

    /**
     * 获取有效表头
     */
    protected function getValidHeaders()
    {
        $headers = [];
        $this->grid->visibleColumns()->each(function ($column) use (&$headers) {
            $headers[] = "\xEF\xBB\xBF" . ($column->getLabel() ?: '未命名列');
        });
        return $headers ?: ["\xEF\xBB\xBF数据"];
    }

    public function getFilename()
    {
        return '1.xlsx';
    }
}
