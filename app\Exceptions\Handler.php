<?php

namespace App\Exceptions;

use App\Models\ExceptionRecord;
use App\Services\NotifySendService;
use Exception;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Session\TokenMismatchException;
use Throwable;;

class Handler extends ExceptionHandler
{
    /**
     * A list of exception types with their corresponding custom log levels.
     *
     * @var array<class-string<\Throwable>, \Psr\Log\LogLevel::*>
     */
    protected $levels = [
        //
    ];

    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<\Throwable>>
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    public function report(Exception|Throwable $e)
    {
        $errInfo = [
            'code'    => $e->getCode(),
            'file'    => $e->getFile(),
            'line'    => $e->getLine(),
            'message' => $e->getMessage(),
            'url'     => request()->fullUrl(),
            'ip'      => getIp()
        ];
        $excRecordObj = new ExceptionRecord();
        $excRecordObj->code = $e->getCode();
        $excRecordObj->file = $e->getFile();
        $excRecordObj->line = $e->getLine();
        $excRecordObj->message = $e->getMessage();
        $excRecordObj->url = request()->fullUrl();
        $excRecordObj->ip = getIp();
        $excRecordObj->server = json_encode($_SERVER, JSON_UNESCAPED_UNICODE);

        $excRecordObj->save();

        $notify  = true;
        if (str_contains($e->getMessage(), "CSRF token mismatch.")) {
            $notify = false;
        }
        if (str_contains($e->getMessage(), "App\Jobs\AddExternalContactJob has been attempted too many times or run too long. The job may have previously timed out.")) {
            $notify = false;
        }
        if ($e instanceof PermissionException) {  // 权限不足不提示
            $notify = false;
        }
        if ($notify) {
           if(env('APP_ENV') == 'production'){
                NotifySendService::sendWorkWeixinForError("[系统错误][超级紧急][立刻通知] ：\n" .
                    json_encode($errInfo, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE));
           }
        }
        parent::report($e);
    }

    public function render($request, Throwable $e)
    {
        // 捕获 CSRF Token 不匹配异常
        if ($e instanceof TokenMismatchException) {
            // 对于 AJAX 请求返回 JSON 响应
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'message' => '页面已过期，请刷新后重试。'
                ], 419);
            }
            // 对于普通请求，重定向回上一页并显示错误消息
            return redirect()->back()->withErrors(['csrf' => '页面已过期，请刷新后重试。']);
        }
        return parent::render($request, $e);
    }
}
