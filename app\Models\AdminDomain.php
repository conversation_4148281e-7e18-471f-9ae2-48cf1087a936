<?php

namespace App\Models;

use Dcat\Admin\Admin;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property mixed $domain
 * @property AdminUser $adminInfo
 * @property mixed $ali_cert_id
 */
class AdminDomain extends Model
{
    use HasDateTimeFormatter;
    use softDeletes;

    protected $table = 'admin_domains';

    const LISTENTER_STATUS = [
        'noAssocia' => '待关联',
        'Associating' => '关联中',
        'Associated' => '已关联',
        'Diassociating' => '解除关联中',
    ];

    const CERT_UPLOAD_STATUS = [
        0 => '未上传',
        1 => '已上传',
        2 => '上传失败',
        3 => '重新上传',
    ];

    const USER_CERT_UPLOAD_STATUS = [
        0 => '系统处理中',
        1 => '已上传',
        2 => '上传失败',
    ];

    const  HOST_MAP = [
        'default' => [
            '**************',
            '*************',
        ]
    ];

    public static function getDomainHost()
    {
        $adminUid = Admin::user()->id;

        /** @var AdminDomain $adminDomain */
        $adminDomain = self::query()->where("status", 1)->where("admin_uid", $adminUid)->first();
        if ($adminDomain) {
            return $adminDomain->domain;
        }

        if (Admin::user()->parent_id) {
            $adminUid = Admin::user()->parent_id;
        }
        /** @var AdminDomain $adminDomain */
        $adminDomain = self::query()->where("status", 1)->where("admin_uid", $adminUid)->first();
        if (!$adminDomain) {
            return '';
        }
        return $adminDomain->domain;
    }

    public function adminInfo(): BelongsTo
    {
        return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
    }

    public static function getDomainByAdminUid($adminUid)
    {
        if (!$adminUid) {
            return '';
        }
        $adminDomain = self::query()->where("status", 1)->where("admin_uid", $adminUid)->value('domain');
        if (!$adminDomain) {
            $adminUid = AdminUser::query()->where("id", $adminUid)->value('parent_id');
        }
        $adminDomain = self::query()->where("status", 1)->where("admin_uid", $adminUid)->value('domain');
        return $adminDomain ?? '';
    }

    /**
     * 检查IP属于哪个IP组
     *
     * @param string $ip
     * @return string|null
     */
    public static function getIpGroup(string $ip): ?string
    {
        foreach (AdminDomain::HOST_MAP as $groupName => $ips) {
            if (in_array($ip, $ips)) {
                return $groupName;
            }
        }

        return null;
    }

    public function cert(): HasOne
    {
        return $this->hasOne(AdminDomainCert::class, 'domain_id', 'id');
    }


    /**
     * 判断输入的域名是否匹配预定义的域名列表
     *
     * @param string $input 用户输入的域名字符串
     * @param array $domains 预定义的域名规则数组
     * @return bool 匹配返回 true，不匹配返回 false
     */
    public static function isDomainMatched(string $input, array $domains): bool
    {
        // 遍历所有域名规则
        foreach ($domains as $domainRule) {
            if (self::matchesSingleDomain($input, $domainRule)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查输入域名是否匹配单个域名规则
     *
     * @param string $input 输入的域名
     * @param string $domainRule 域名规则（可能包含通配符）
     * @return bool
     */
    public static  function matchesSingleDomain(string $input, string $domainRule): bool
    {
        // 精确匹配
        if ($input === $domainRule) {
            return true;
        }

        // 检查是否包含通配符
        if (!str_contains($domainRule, '*')) {
            return false;
        }

        // 验证通配符只能在开头
        if (!str_starts_with($domainRule, '*.')) {
            return false;
        }

        // 提取通配符后的域名部分
        $baseDomain = substr($domainRule, 2); // 去掉 "*."

        // 检查输入域名是否以基础域名结尾
        if (!str_ends_with($input, '.' . $baseDomain)) {
            return false;
        }

        // 确保不是精确匹配基础域名本身（除非有专门的规则）
        if ($input === $baseDomain) {
            return false;
        }

        // 检查子域名部分是否有效（不能为空，不能包含点）
        $subdomain = substr($input, 0, strlen($input) - strlen('.' . $baseDomain));

        // 子域名不能为空，且不能包含点（确保只匹配一级子域名）
        return !empty($subdomain) && !str_contains($subdomain, '.');
    }


    /**
     * 微信授权登陆域名
     * @return string
     */
    public static function getWeChatLoginDomain()
    {
//        return  'https://welogin.smart-ark.com';
        return 'https://wechat.smart-ark.com';
    }

}
