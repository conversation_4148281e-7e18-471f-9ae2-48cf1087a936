<?php

namespace App\Admin\Controllers;

use App\Models\AdminSubUser;
use App\Models\AdminSubUserAuthCorp;
use App\Models\AdminUser;
use App\Models\WwCorpInfo;
use App\Models\WwUser;
use App\Models\WwUsersDataByDay;
use App\Services\Tools\UtilsService;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Show;

/**
 * @property $admin_uid
 */
class WwUsersDataByDayController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {

        return Grid::make(new WwUsersDataByDay([]), function (Grid $grid) {
//                $grid->paginate(AdminUser::getPaginate());
            if (Admin::user()->id !== 1) { //如果不是超级管理员

                if (Admin::user()->isRole('customer')) { //如果是“客户”的角色 也就是主账号
                    $grid->model()->whereIn("admin_uid", AdminSubUser::getAdminUids(Admin::user()->id));//如果是主账号，则获取所有子账号的id，包括自己
                    $grid->column('归属账号')->display(function () {
                        if (Admin::user()->id == $this->admin_uid) { //如果是自己创建的则直接显示主账号
                            return '主账号';
                        } else {
                            $username = Admin::user()->where('id', $this->admin_uid)->value('username');
                            $username = str_replace(Admin::user()->username, '', $username);
                            return $username;
                        }
                    });

                } else { //如果是客户运营的角色 也就是子账号
                    $grid->model()->where("admin_uid", Admin::user()->id)->orderByDesc("id");
                }
            }
            $grid->export()->rows(function ($rows) {
                /** @var AdminUser $adminUser */
                $adminUser = Admin::user();
                foreach ($rows as $key => $row) {
                    if ($adminUser->id == $row->admin_uid) {
                        $rows[$key]['所属账号'] = '主账号';
                    } else {
                        $username  = $row->adminInfo ? $row->adminInfo->username : '';
                        $rows[$key]['所属账号'] = str_replace($adminUser->username, '', $username);
                    }
                    $rows[$key]['corp_name'] = $row->corp_name;
                    $rows[$key]['ww_user_name'] = $row->ww_user_name;
                }
                return $rows;
            })->chunkSize(2000);
            $grid->model()->orderByDesc("date");
            $grid->disableCreateButton();
            $grid->disableBatchActions();
            $grid->disableActions();
            $grid->showColumnSelector();
            $grid->column('id')->sortable();

            $grid->column('corp_name')->display(function ($corp_name) {
                if (!$corp_name) {
                    return "<span style='color: red'> 数据同步中...</span>";
                }
                return $corp_name;
            });
            $grid->column('ww_user_name', '销售')->display(function ($ww_user_name) {
                if (!$ww_user_name) {
                    return "<span style='color: red'> 数据同步中...</span>";
                }
                return $ww_user_name;
            });
            $grid->column('date');
            $grid->column('add_count', '新增数量')->sortable()->display(function ($value) {
                return UtilsService::beautifyNumber($value, 'minimal', 'success', ['tooltip' => '新增客户数量']);
            });
            $grid->column('cus_del_count', '客户删除')->sortable()->display(function ($value) {
                return UtilsService::beautifyNumber($value, 'box', 'warning', ['tooltip' => '客户删除数量']);
            });
            $grid->column('ww_del_count', '企微删除')->sortable()->display(function ($value) {
                return UtilsService::beautifyNumber($value, 'badge', 'danger', ['tooltip' => '企微删除数量']);
            });
            $grid->column('pure_add_count', '净增数量')->sortable()->display(function ($value) {
                return UtilsService::beautifyNumber($value, 'pill', 'info', ['tooltip' => '净增客户数量']);
            });
            $grid->column('customer_start_chat_count')->sortable()->display(function ($value) {
                return UtilsService::beautifyNumber($value, 'icon', 'info', [
                    'tooltip' => '客户主动发起聊天的数量',
                    'icon' => 'feather icon-message-circle'
                ]);
            });
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) use ($grid) {
                $filter->expand();
                $filter->panel();


                //所有带「所属账户」的列表，主账户都增加对应筛选
                if (AdminUser::isSystemOp()) { //主账号或运营
                    $adminIds = AdminSubUser::getALLAdminUids(Admin::user()->id);
                    $filter->equal("admin_uid", '操作账号')->select(AdminUser::query()->whereIn("id", $adminIds)->pluck("username", 'id')->toArray())->width(2);
                }
//
                $corpAuthList = AdminSubUserAuthCorp::getCorpAuthListBuyAdminUid(Admin::user()->id);
                $corpIds = array_keys($corpAuthList);
                $corpList = AdminSubUserAuthCorp::getCorpAuthListBuyCorpIds($corpIds);
                $filter->in('corp_id', '企业微信')->multipleSelect($corpList)->width(2);
                $wwUsers = WwUser::query()->orderByDesc('id')->whereIn('corp_id', $corpIds)->pluck("name", 'id');
                $filter->in('ww_user_id', '销售')->multipleSelect($wwUsers)->width(2);

                $filter->equal("date")->date()->width(2);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new WwUsersDataByDay(), function (Show $show) {
//
            if (!AdminUser::isAdmin($show->model())) {
                $show->field('N')->value("无数据");
            } else {
                $show->field('id');
            }
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new WwUsersDataByDay(), function (Form $form) {
            if (!$form->isCreating() && !AdminUser::isAdmin($form->model())) {
                $form->display('N')->value("无数据");
            } else {
                $form->display('id');
            }
        });
    }

    public function destroy($id)
    {
        return $this->form()->response()->error("无权限操作");
    }
}
