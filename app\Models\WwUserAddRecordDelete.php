<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property mixed|string $follow_user_remark_tags_string
 * @property false|mixed|string $follow_user_remark_tags
 * @property false|mixed|string $follow_user_remark_mobiles
 * @property false|mixed|string $follow_user_remark_mobiles_sys
 * @property mixed|string $follow_user_add_way
 * @property mixed|string $follow_user_remark
 * @property mixed|string $gender
 * @property mixed $type
 * @property mixed|string $avatar
 * @property mixed $name
 * @property mixed $external_userid
 * @property mixed|string $state
 * @property mixed|string $date
 * @property int|mixed $view_count
 * @property mixed $ua
 * @property mixed|string $area
 * @property mixed|string $district
 * @property mixed|string $city
 * @property mixed|string $prov
 * @property bool|mixed|string $ip
 * @property mixed|string $page_type
 * @property mixed|string $click_id
 * @property int|mixed $ww_link_id
 * @property mixed $user_id
 * @property mixed $link_view_record_id
 * @property mixed $ww_user_id
 * @property mixed|string $corp_id
 * @property mixed $admin_uid
 * @property mixed|string $label
 * @property mixed|string $label_req
 * @property mixed|string $label_resp
 * @property mixed|string $is_delete
 * @property mixed|string $ocpx_result
 * @property mixed $follow_user_id
 * @property mixed $follow_user_created_at
 * @property mixed|string $external_contact_created_at
 * @property mixed $link_view_created_at
 * @property WwLink             $linkInfo
 * @property mixed              $ocpx_result_string
 * @property mixed              $id
 * @property mixed              $customer_start_chat
 * @property mixed              $chat_seq
 * @property mixed              $ww_user_group_id
 * @property mixed              $ww_user_group_name
 * @property mixed|string       $ww_user_wind_label
 * @property WwCorpInfo         $corpInfo
 * @property WwUser             $wwUserInfo
 * @property false|mixed|string $ocpx_rate_data
 * @property mixed|string       $trace_id
 */
class WwUserAddRecordDelete extends Model
{
    use HasDateTimeFormatter;

    protected $table = 'ww_user_add_record_delete';

    public function corpInfo(): BelongsTo
    {
        return $this->BelongsTo(WwCorpInfo::class, 'corp_id', 'id');
    }
    public function corpInfoWithTrashed(): BelongsTo
    {
        return $this->belongsTo(WwCorpInfo::class, 'corp_id', 'id')->withTrashed();
    }
    public function wwUserInfo(): BelongsTo
    {
        return $this->BelongsTo(WwUser::class, 'ww_user_id', 'id');
    }

    public function wwUserInfoWithTrashed(): BelongsTo
    {
        return $this->BelongsTo(WwUser::class, 'ww_user_id', 'id')->withTrashed();
    }

    public function linkInfo(): BelongsTo
    {
        return $this->BelongsTo(WwLink::class, 'ww_link_id', 'id')->withTrashed();
    }

    public function adminInfo(): BelongsTo
    {
        return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
    }
}
