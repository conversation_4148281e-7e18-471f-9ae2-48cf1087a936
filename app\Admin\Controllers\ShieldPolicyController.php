<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Grid\shieldPolicy\BatchSetArea;
use App\Admin\Forms\ShieldPolicy\CopyShieldPolicy;
use App\Admin\Forms\ShieldPolicy\UpdateShieldPolicyGroupAndAuditTplForm;
use App\Admin\Repositories\ShieldPolicy;
use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Models\AdminSubUser;
use App\Models\AdminUser;
use App\Models\Area;
use App\Models\ShieldPolicyRule;
use App\Models\TencentAdAccount;
use App\Models\WwTpl;
use App\Models\WwUsersGroup;
use App\Services\Tools\UtilsService;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Show;
use Dcat\Admin\Widgets\Tooltip;
use Illuminate\Support\Facades\Request;

/**
 * @property int $id
 */
class ShieldPolicyController extends AdminController
{
    const SWITCH = [
        1 => '启用',
        0 => '关闭'
    ];
//    'shield_rules/create'

    public function destroy($id)
    {
        $data = \App\Models\ShieldPolicy::query()->whereIn("id", explode(",", $id))->get();
        foreach ($data as $datum) {
            if (!AdminUser::isAdmin($datum)) {
                return $this->form()->response()->error("无权限操作");
            }
        }
        /** 记录删除操作日志 */
        $delIds = explode(",", $id);
        foreach ($delIds as $delId) {
            AdminActionLogJob::dispatch(
                'shield_policy_delete',
                $delId,
                AdminActionLog::ACTION_TYPE['防水墙'],
                '删除屏蔽规则ID:'.$delId,
                getIp(),
                Admin::user()->id, // 当前操作用户ID
            )->onQueue('admin_action_log_job');
        }
        return $this->form()->destroy($id);
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form(): Form
    {
        return Form::make(new ShieldPolicy(), function (Form $form) {
            // 设置表单为模态框模式
            $form->dialog();
            $config = [
                'ad_env'             => '广告环境检测',
                'default_block_ip'   => '默认IP屏蔽包',
                'share_block_ip'     => '共享IP屏蔽包',
                'default_block_user' => '默认屏蔽包',
                'share_block_user'   => '共享屏蔽包',
                'is_mobile'          => '仅限手机访问',
            ];

            $configHelp = [
                'ad_env'             => '非广告环境访问的用户，会进入配置的屏蔽页',
                'default_block_ip'   => '系统内置的风险网络IP，其中包含异常用户、红包党、羊毛党等',
                'share_block_ip'     => '系统用户自主配置的IP屏蔽，系统遴选后，生成的屏蔽IP列表',
                'default_block_user' => '系统内置的风险用户，其中包含异常用户、红包党、羊毛党等',
                'share_block_user'   => '系统用户主动进行的拉黑操作，系统遴选后，生成的屏蔽包列表',
                'is_mobile'          => '开启后只允许移动设备访问，PC端访问会进入屏蔽页',
            ];
            if (!$form->isCreating() && !AdminUser::isAdmin($form->model())) {
                $form->display('N')->value("无数据");
            } else {
                $form->display('id');
                $form->text('name')->required();
                $form->multipleSelect('mdm')->options(TencentAdAccount::query()->whereIn('admin_uid', AdminSubUser::getAdminUids(Admin::user()->id))->groupBy("corporation_name")->pluck("corporation_name", "corporation_name"))->help("用于新建投放链接的时候，自动匹配投放主体的屏蔽规则使用，留空代表适用所有主体")->saveAsJson();
                $form->multipleSelect('product')->options(WwTpl::query()->whereIn("admin_uid", AdminSubUser::getALLAdminUids(Admin::user()->id))->groupBy("product")->pluck("product", "product"))->help("用于新建投放链接的时候，自动匹配同落地页产品的屏蔽规则使用，留空代表适用所有产品")->saveAsJson();

                // 构建帮助信息HTML
                $helpHtml = '<div class="config-help-container" style="margin-top: 10px; padding: 10px; border-left: 3px solid #3085d6; background-color: #f8f9fa;">';
                foreach ($configHelp as $key => $help) {
                    $helpHtml .= "<div class='config-help-item' style='margin-bottom: 5px;'><strong>{$config[$key]}:</strong> {$help}</div>";
                }
                $helpHtml .= '</div>';

                $form->checkbox('config', '广告配置项')->options($config)->canCheckAll()->default(array_keys($config))->saveAsJson();
                $form->html($helpHtml, ' ');

                $tplList = WwTpl::query()->where("type", 0)->where('admin_uid', Admin::user()->id)->pluck("name", "id");
                $form->select("tpl_audit_id")->options($tplList)->required()->help("符合屏蔽规则的异常流量，会展示该页面");

                $wwUsersGroup = WwUsersGroup::query()->where("admin_uid", Admin::user()->id)->where('type', 0)->pluck("title", 'id');
                $form->select("ww_user_group_id")->options($wwUsersGroup)->required()->help("符合屏蔽规则的异常流量，会展示该规则的接粉销售");

                $form->hidden('admin_uid');
                $form->hidden('ad_env')->default(1)->help("非广告环境访问的用户，会进入配置的屏蔽页");
                $form->number('view_count')->min(0)->default(6)->help("用户单条链接的访问次数限制，如配置为6次，用户访问第6次的时候会进入配置的屏蔽页，0为不限制");
                $form->hidden('add_count')->default(0)->help("历史添加成功次数，比如一个客户添加过10次销售，如本条配置为10，那么该用户将会被屏蔽，0为不限制");//->min(0)
                $form->hidden('vc_count')->default(0)->help("历史访问次数，0为不限制");//->min(0)
                $form->hidden('default_block_ip')->default(1)->help("系统内置的风险网络IP，其中包含异常用户、红包党、羊毛党等");
                $form->hidden('share_block_ip')->default(1)->help("系统用户自主配置的IP屏蔽，系统遴选后，生成的屏蔽IP列表");
                $form->hidden('default_block_user')->default(1)->help("系统内置的风险用户，其中包含异常用户、红包党、羊毛党等");
                $form->hidden('share_block_user')->default(1)->help("系统用户主动进行的拉黑操作，系统遴选后，生成的屏蔽包列表");
                $form->hidden('is_mobile')->default(1);
                $form->html('<div id="search_block_area"></div>', '搜索选择地域');
                /** 搜搜节点组件 */
                UtilsService::searchJsTree('search_block_area','block_area');
                $areaData = Area::query()->select("id", "name", "pid as parent_id", "id as order")->whereIn("level", [1, 2])->get()->toArray();
                $form->tree('block_area')
                    ->nodes($areaData) // 设置所有节点
                    ->expand(false)
                    ->treeState(false) # 允许单独选择父节点
                    ->customFormat(function ($v) { // 格式化外部注入的值
                        if (!$v) {
                            return [];
                        }
                        return $v;
                    })->saveAsJson();
                $form->saving(function (Form $form) {
                    if (!$form->isCreating() && !AdminUser::isAdmin($form->model())) {
                        return $form->response()->error("无权限操作")->refresh();
                    }
                    $form->input("admin_uid", Admin::user()->id);

                    // 获取选中的配置项
                    $adConfig = array_filter($form->input('config'));

                    // 定义所有配置项
                    $configItems = ['ad_env', 'default_block_ip', 'share_block_ip', 'default_block_user', 'share_block_user', 'is_mobile'];

                    // 循环处理每个配置项
                    foreach ($configItems as $item) {
                        $form->$item = in_array($item, $adConfig) ? 1 : 0;
                    }

                    return true;
                });
                $form->saved(function (Form $form, $policyId) {
                    if ($form->model()->id) {
                        $policyId = $form->model()->id;
                    }
                    //清空之前的规则
                    ShieldPolicyRule::query()->where("shield_policy_id", $policyId)->delete();
                    /** @var \App\Models\ShieldPolicy $sp */
                    $sp = \App\Models\ShieldPolicy::query()->find($policyId);
                    $mdm = json_decode($sp->mdm, true);
                    $product = json_decode($sp->product, true);
                    if (!empty($product) || !empty($mdm)) {
                        if (empty($product)) {
                            $product = [''];
                        }
                        if (empty($mdm)) {
                            $mdm = [''];
                        }
                        foreach ($mdm as $mdmItem) {
                            foreach ($product as $proItem) {
                                $rule = new ShieldPolicyRule();
                                $rule->shield_policy_id = $policyId;
                                $rule->mdm = $mdmItem;
                                $rule->product = $proItem;
                                $rule->admin_uid = $sp->admin_uid;
                                $rule->save();
                            }
                        }
                    }

                    $input = $form->input();

                    // 获取当前操作的记录ID
                    $recordId = $form->getKey(); // 主键ID
                    // 构建日志消息
                    $action = $form->isCreating() ? 'create' : 'update';
                    $message = self::buildUserGroupActionMessage($input, $recordId,$action);
//                    dd($message);
                    AdminActionLogJob::dispatch(
                        'shield_policy_'.$action,
                        $recordId,
                        AdminActionLog::ACTION_TYPE['销售组'],
                        $message,
                        getIp(),
                        Admin::user()->id, // 当前操作用户ID
                    )->onQueue('admin_action_log_job');
                });
                $form->display('created_at');
                $form->display('updated_at');
                $form->disableViewButton();
                $form->disableDeleteButton();
                $form->disableViewCheck();
                $form->disableEditingCheck();
            }
        })->confirm('确认提交么？');
    }
    /**
     * 构建日志消息
     * @param $input
     * @param $recordId
     * @param $action
     * @return string
     */
    public static function buildUserGroupActionMessage($input,$recordId,$action): string
    {

        $name = $input['name'];
        $mdm = implode(',',array_filter($input['mdm']));
        $product = implode(',',array_filter($input['product']));
        $tplAuditId = $input['tpl_audit_id'];
        $wwUserGroupId = $input['ww_user_group_id'];
        $adminUid = $input['admin_uid'];
        $adEnv = $input['ad_env'] == 1 ? '打开':'关闭';
        $viewCount = $input['view_count'];
        $addCount = $input['add_count'];
        $vcCount = $input['vc_count'];
        $default_block_ip = $input['default_block_ip']  == 1 ? '打开':'关闭';
        $shareBlockIp = $input['share_block_ip']  == 1 ? '打开':'关闭';
        $defaultBlockUser = $input['default_block_user']  == 1 ? '打开':'关闭';
        $shareBlockUser = $input['share_block_user']  == 1 ? '打开':'关闭';
        $isMobile = $input['is_mobile']  == 1 ? '打开':'关闭';

        $areaMap = Area::query()->get()->pluck('name', 'id')->toArray();
        $areaIds = explode(',', $input['block_area']);
        $AreaNames = Area::mapAreaIdsToNames($areaIds, $areaMap);
        $blockAreaNames = implode(',', $AreaNames);
        return <<<HTML
规则名称：$name<br>
适用主体：$mdm<br>
适用产品：$product<br>
屏蔽页面：$tplAuditId<br>
异常流量接粉销售：$wwUserGroupId<br>
管理员ID：$adminUid<br>
广告环境检测：$adEnv<br>
访问次数限制：$viewCount<br>
添加成功次数：$addCount<br>
历史访问次数：$vcCount<br>
默认IP屏蔽包：$default_block_ip<br>
共享IP屏蔽包：$shareBlockIp<br>
默认屏蔽包：$defaultBlockUser<br>
共享屏蔽包：$shareBlockUser<br>
仅限手机访问：$isMobile<br>
屏蔽地区：$blockAreaNames<br>
HTML;
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid(): Grid
    {
        return Grid::make(new ShieldPolicy(['adminInfo', 'wwUserGroup', 'wwTpl']), function (Grid $grid) {
            $grid->paginate(AdminUser::getPaginate());
            if (!AdminUser::isSystemOp()) {
                $grid->model()->whereIn("admin_uid", AdminSubUser::getAdminUids(Admin::user()->id));
            }
            if (Admin::user()->isRole("wop")) {
                $grid->disableBatchDelete();
                $grid->disableActions();
                $grid->fixColumns(1, 0);
            } else {
                $grid->fixColumns(0, -1);

            }
            $grid->disableCreateButton();
            $grid->showColumnSelector();
            $grid->tools(function (Grid\Tools $tools) {
                $tools->append(new BatchSetArea()); //批量设置屏蔽地域
                $tools->append(UtilsService::dialogForm('添加',Request::url().'/create','create-shield-policy'));
            });

            $grid->model()->orderByDesc("updated_at");
            $grid->disableViewButton();
            $grid->column('id')->sortable();
            $grid->column('name');

            $grid->column('mdm')->display(function ($value) {
                $value = json_decode($value, true);
                return empty($value) ? "全部" : implode("<br/>", $value);
            });
            $grid->column('product')->display(function ($value) {
                $value = json_decode($value, true);
                return empty($value) ? "全部" : implode("<br/>", $value);
            });
            $grid->column('adminInfo.username', '操作人');
            $grid->column('wwUserGroup.title', '接粉销售');
            $grid->column('wwTpl.name', '屏蔽页面');
            $grid->column('ad_env')->using(self::SWITCH);

            $grid->column('default_block_ip')->using(self::SWITCH);
            $grid->column('share_block_ip')->using(self::SWITCH);
            $grid->column('default_block_user')->using(self::SWITCH);
            $grid->column('share_block_user')->using(self::SWITCH);
            $grid->column('is_mobile')->using(self::SWITCH);
            $grid->column('view_count');
            $grid->column('add_count');
            $grid->column('vc_count');
            $grid->column('block_area')->display(function ($value) {
                $areaIds = json_decode($value, true);
                $areaData = Area::query()->select("id", "name")->find($areaIds)->toArray();
                if (!empty($areaData)) {
                    $areaString = implode(",", array_column($areaData, 'name'));
                } else {
                    $areaString = '无配置';
                }
                Tooltip::make('.block_area_help_message' . $this->id)->title($areaString);
                return '<div class="block_area_help_message' . $this->id . '">' . count($areaIds) . "个地域" . '</div>';
            });
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();
            if (!Admin::user()->isRole("wop")) {
                $grid->column('复制')->display('复制')->label()->modal(function (Grid\Displayers\Modal $modal) {
                    // 标题
                    $modal->title('复制屏蔽策略');
                    // 自定义图标
                    $modal->icon('');//feather icon-check-circle
                    $modal->xl();
                    // 传递当前行字段值
                    $data = [
                        'policy_id' => $this->id
                    ];
                    return CopyShieldPolicy::make()->payload($data);
                });
            }
            if (AdminUser::isSystemOp()) {
                $grid->column('修改屏蔽分组/页面')
                    ->display('修改屏蔽分组/页面')
                    ->label()
                    ->modal(function (Grid\Displayers\Modal $modal) {
                        // 标题
                        $modal->title('修改屏蔽分组/页面');
                        // 自定义图标
                        $modal->icon('');//feather icon-check-circle
                        $modal->xl();
                        // 传递当前行字段值
                        $data = [
                            'shield_id' => $this->id
                        ];
                        return UpdateShieldPolicyGroupAndAuditTplForm::make()->payload($data);
                    });
            }

            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                $filter->equal('ID', '规则ID')->width(2);
                $filter->like('name')->width(2);
                if (AdminUser::isSystemOp()) {
                    $admins = AdminUser::query()->pluck('username', 'id')->toArray();
                    $filter->equal('admin_uid', '用户ID')->select($admins)->width(2);
                }
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail(mixed $id): Show
    {
        return Show::make($id, new ShieldPolicy(), function (Show $show) {
            if (!AdminUser::isAdmin($show->model())) {
                $show->field('N')->value("无数据");
            } else {
                $show->field('id');
                $show->field('name');
            }
        });
    }


}
