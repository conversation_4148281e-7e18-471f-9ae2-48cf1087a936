<?php
// app/Http/Middleware/RateLimitedJob.php

namespace App\Jobs\Middleware;

use Closure;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;

class RateLimitedJob
{
    public $second = 10;
    public function handle($job, Closure $next)
    {
        $lockKey = 'ad_account_hourly_report_key'; // 缓存锁键名
        Redis::throttle($lockKey)
            ->block(0)->allow(800)->every($this->second)
            ->then(function () use ($job, $next) {
                // 获得了锁
                $next($job);
            }, function () use ($job) {
                // 没有获取到锁
                $job->release($this->second);
            });
    }
}
