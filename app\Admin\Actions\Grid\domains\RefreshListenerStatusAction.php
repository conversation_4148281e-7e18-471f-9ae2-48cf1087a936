<?php

namespace App\Admin\Actions\Grid\domains;

use App\Models\AdminDomain;
use App\Services\AlibabaCloudService;
use App\Services\NotifySendService;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;

class RefreshListenerStatusAction extends RowAction
{

    protected string $listenerId = 'lsn-muwx0iancv5jwv6bb5';

    /**
     * @return string
     */
    public function title(): string
    {
        return '<i class="fa fa-refresh"></i> 刷新';
    }

    /**
     * Handle the action request.
     *
     *
     * @return Response
     */
    public function handle(): Response
    {
        $key = $this->getKey();

        $adminDomain = AdminDomain::query()->find($key);
        $aliCertId = $adminDomain->ali_cert_id;
        if (empty($aliCertId)) {
            return $this->response()
                ->alert()
                ->error('错误')
                ->detail('未查询到阿里证书ID')
                ->refresh();
        }
        /** 查询关联结果 */
        $queryListener = AlibabaCloudService::listListenerCertificates($this->listenerId, [$aliCertId]);
        if (!$queryListener['success']) {
            $errorData = $queryListener['errors'];
            $recommend = $errorData['Recommend'] ?? "#";
            return $this->response()
                ->alert()
                ->error('错误<br>'.$errorData['errCode'])
                ->detail('查询失败<br>' . $errorData['requestId'] . "<br><a href='$recommend' target='_black'>阿里云诊断地址</a>")
                ->refresh();
        }
        $data = $queryListener['data'];
        /** 未找到 */
        if ($data['TotalCount'] == 0) {
            $adminDomain->listener_status = "noAssocia";
            $adminDomain->save();
            return $this->response()
                ->alert()
                ->error('<br>'.$queryListener['message'])
                ->detail('未找到关联记录，状态设置为待关联<br>'.$data['RequestId']."<br>")
                ->refresh();
        }
        /** 如果有并且接口结果等于查询的id 更新状态 */
        $certificates = $data['Certificates'][0];
        if ($certificates['CertificateId'] == $aliCertId.'-cn-hangzhou') {
            /** 更新状态 */
            $adminDomain->listener_status = $certificates['Status'];
            $adminDomain->save();
            if($certificates['Status'] == 'Associated'){ //如果是已关联状态 给客户发送提醒。
                $message = '【' .$adminDomain->adminInfo->username  . '】您的域名：' . $adminDomain->domain . '，证书已上传成功，请查看。';
                NotifySendService::sendCustomerForMessageByRobots($adminDomain->adminInfo, $message);
            }
        }

        return $this->response()
            ->alert()
            ->success('完成')
            ->detail('更新状态成功<br>'.AdminDomain::LISTENTER_STATUS[$certificates['Status']])
            ->refresh();
    }


    /**
     * @return mixed
     */
    public function confirm()
    {
        // 不需要确认对话框
      return ['确认刷新？提交后将会给客户发送消息提醒。'];
    }
}
