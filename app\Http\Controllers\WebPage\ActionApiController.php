<?php

	namespace App\Http\Controllers\WebPage;

	use App\Jobs\OcpxSubmitJob;
    use App\Models\LinkViewRecord;
    use App\Models\WwUserAddRecord;
    use Dcat\Admin\Http\JsonResponse;
    use Illuminate\Http\Request;

    class ActionApiController
	{
        public function longPressAction(Request $request){
            $vid    = $request->get("vid", 0);

            /** @var LinkViewRecord $linkViewRecord */
            $linkViewRecord = LinkViewRecord::query()->find($vid);
            if(!$linkViewRecord){
                return JsonResponse::make([
                    'code' => -2
                ]);
            }
            $viewWwUserInfo = $linkViewRecord->wwUserInfo;
            if(!$viewWwUserInfo){
                return JsonResponse::make([
                    'code' => -3
                ]);
            }
            $addRecord = new WwUserAddRecord();
            $addRecord->admin_uid = $linkViewRecord->admin_uid;
            $addRecord->corp_id = $viewWwUserInfo->corp_id;
            $addRecord->ww_user_id = $linkViewRecord->ww_user_id;
            $addRecord->link_view_record_id = $linkViewRecord->id;
            $addRecord->user_id = $linkViewRecord->user_id;
            $addRecord->ww_link_id = $linkViewRecord->ww_link_id;
            $addRecord->click_id = $linkViewRecord->click_id;
            $addRecord->page_type = $linkViewRecord->page_type;
            $addRecord->ip = $linkViewRecord->ip;
            $addRecord->prov = $linkViewRecord->prov;
            $addRecord->city = $linkViewRecord->city;
            $addRecord->district = $linkViewRecord->district;
            $addRecord->area = $linkViewRecord->area;
            $addRecord->ua = $linkViewRecord->ua;
            $addRecord->view_count = $linkViewRecord->view_count;
            $addRecord->link_view_created_at = $linkViewRecord->created_at;
            $addRecord->date = date("Y-m-d");
            $addRecord->state = $linkViewRecord->state;
            $addRecord->external_userid = '';
            $addRecord->name = '长按行为-非真实进粉';
            $addRecord->avatar =  env('BASE_QRCODE_URL');
            $addRecord->type = 0;
            $addRecord->gender = 0;
            $addRecord->follow_user_id = $viewWwUserInfo->open_user_id;
            $addRecord->external_contact_created_at = date("Y-m-d H:i:s", time());
            $addRecord->ocpx_result = '[]';
            $addRecord->label = '[]';

            if ($addRecord->linkInfo->wwUserGroup) {
                $addRecord->ww_user_group_name = $addRecord->linkInfo->wwUserGroup->title;
                $addRecord->ww_user_group_id = $addRecord->linkInfo->wwUserGroup->id;
            }

            $addRecord->is_delete = 0;
            $addRecord->save();

            //只有第一次进粉才进行OCPX的上报
            $action = 'add_external_contact';
            OcpxSubmitJob::dispatch($addRecord,$action)->onQueue('ocpx_submit');
            return JsonResponse::make([
                'code' => 0
            ]);
        }
	}
