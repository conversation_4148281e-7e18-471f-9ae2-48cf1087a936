<?php

namespace App\Jobs\ExportOpenId;

use App\Models\AdminUser;
use App\Models\ExportOpenId;
use App\Models\WechatUser;
use App\Models\WwUserAddRecord;
use App\Models\WwUserAddRecordDelete;
use Dcat\EasyExcel\Excel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessExportOpenIdJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $task;

    public function __construct($task)
    {
        $this->task = $task;
        $this->queue = 'export_openid'; // 设置队列名称
    }

    public function handle()
    {
        $jobId = $this->job->uuid() ?? null;
        Log::info('导出openid任务队列');
        try {
            $task = $this->task;
            if (!$task) {
                return false;
            }
            ExportOpenId::query()->withTrashed()->where('id', $task['id'])->update(['status' => 1, 'job_id' => $jobId]);//状态改为导出中
            $fileName = $task['file_name'] . '.csv';
            Excel::export()->chunk(function ($times) use ($fileName, $task) {
                // 只查询前10页数据
                if ($times > 500) {
                    return false;
                }
                $date = [
                    $task['start_date'],
                    $task['end_date'],
                ];
                switch ($task['data_type']) {
                    case 0:
                        $table = 'ww_user_add_record';
                        break;
                    case 1:
                        $table = 'ww_user_add_record_delete';
                        break;
                    default:
                        $table = 'ww_user_add_record_delete';
                }
                $adminUid = $task['admin_uid'];
                $adminUser = AdminUser::query()->where('id', $adminUid)->first();
                if ($adminUser['parent_id'] == 0) { //如果是主账号
                    $adminUids = AdminUser::query()
                        ->where('id', $adminUid)
                        ->orWhere('parent_id', $adminUid)
                        ->pluck('id')
                        ->toArray();
                } else {
                    $adminUids = array($adminUser['id']);
                }


                $chunkSize = 1000;
                $openIds = DB::table($table . ' as records')
                    ->join('wechat_users as users', 'records.user_id', '=', 'users.id')
                    ->select('users.openid')
                    ->whereIn('records.admin_uid', $adminUids)
                    ->when($date[0] && $date[1], function ($query) use ($date) {
                        return $query->whereBetween('records.external_contact_created_at', $date);
                    })
                    ->when($task['corp_id'], function ($query) use ($task) {
                        return $query->where('records.corp_id', $task['corp_id']);
                    })
                    ->when($task['ww_user_group_id'], function ($query) use ($task) {
                        return $query->where('records.ww_user_group_id', $task['ww_user_group_id']);
                    })
                    ->when($task['ww_user_wind_label'] !== null, function ($query) use ($task) {
                        return $query->where('records.ww_user_wind_label', (string)$task['ww_user_wind_label']);
                    })
                    ->forPage($times, $chunkSize)
                    ->orderByDesc('users.id')
                    ->get();
                if ($openIds->isEmpty()) {
                    return false;
                }
                $list = [];
                foreach ($openIds as $key => $row) {
                    $list[$key]['openid'] = $row->openid;
                }
                return $list;
            })->csv()->disk('oss')->store('/export_openid/' . $fileName);
            if ($task) {
                $ossFilePath = env('CDN_URL') . '/export_openid/' . $fileName;
                ExportOpenId::query()->withTrashed()->where('id', $task['id'])->update([
                    'status' => 2,//导出成功
                    'file_path' => $ossFilePath
                ]);
            }

        } catch (\Exception $e) {
            ExportOpenId::query()->withTrashed()->where('id', $task['id'])->update(['status' => 3]); //导出失败
            $this->fail($e);
        }
    }
}
