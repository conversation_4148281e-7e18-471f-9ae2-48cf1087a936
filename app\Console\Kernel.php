<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {

        $schedule->command('Data:count')->everyMinute();//数据统计
        $schedule->command('Job:FailedJobsRetry')->everyMinute();//定时失败的队列重试

        //企微相关定时任务
        $schedule->command('WwUser:CheckWwUserLicenseTime')->dailyAt('00:00');//整体刷新所有企微下的销售的许可证状态
        $schedule->command('WwUser:CheckWwUserLicenseNotifySend')->hourly();//每个小时查询企业许可证状态 预警 无效 发送微信消息提醒
        $schedule->command('Wwuser:checkCorpLicenseOrderPayJob')->everyMinute();//处理购买许可证订单
        $schedule->command('WwUser:CorpLicenseOrderCanCel')->everyMinute();//企微许可证订单，20分钟未支付取消

        //15天前数据转移到冷库
        $schedule->command('Data:DeleteAddRecordHistoryData')->dailyAt('01:06');//删除ww_user_add_record 15天前历史数据
        $schedule->command('Data:DeleteClickRecordHistoryData')->dailyAt('01:36');//删除link_click_record 15天前历史数据
        $schedule->command('Data:DeleteViewRecordHistoryData')->dailyAt('02:06');//删除link_view_record 15天前历史数据
        $schedule->command('Data:DeleteOcpxRecordHistoryData')->dailyAt('02:36');//删除ocpx_record 15天前历史数据

        //腾讯广告相关定时任务
        $schedule->command('Tads:PageWechatPages')->everyMinute();//腾讯广告切换原生页
        $schedule->command('Tads:GetAdAccountMonitor')->everyTenMinutes();//定时获取广告账户监测数据-每小时的数据
        $schedule->command('Tads:GetAdAccountMonitorCount')->everyFiveMinutes();//定时获取广告账户监测数据-按天汇总
        $schedule->command('Tads:CheckAdAccountMonitorStatus')->everyTenMinutes();//判断广告账户获取监测数据状态并更新
        $schedule->command('Tads:GetDataSourceId')->everyMinute();//

        //销售相关定时任务
        $schedule->command('WwUser:ResetTodayShowCount')->dailyAt('00:06');
        $schedule->command('WwUser:QrCodeSaveUp')->everyMinute();//生成销售二维码
        $schedule->command('WwUserGroup:WgQrCodeSaveUp')->everyMinute();//生成销售群二维码
        $schedule->command('WwUser:UsedQrCodeDelete')->everyTenMinutes();//定时删除已经使用的销售二维码
        $schedule->command('QrcodeUsedDeleteFromUserDelete')->everyThirtyMinutes();//已删除销售清理二维码
        $schedule->command('WwUser:DeleteGruopQrCode')->everyMinute();//删除销售群二维码
        $schedule->command('WwUser:WwUserGroupCount')->everyMinute();//统计销售分组内销售数据
        $schedule->command('WwUser:OnlineStatus')->everyMinute();//销售定时上下线

        //其他
//        $schedule->command('CheckDomain')->everyTwoMinutes();//检查域名
        $schedule->command('Admin:BillNew')->hourly();//每小时执行一次
        $schedule->command('Data:ViewRecordByLinkMinute')->everyMinute();//按链接、按分钟、统计浏览量数据-PV
        $schedule->command('Tads:GetAccountVccByDay')->everyTwoHours();//每两小时执行一次

        /** 检查证书到期任务 */
        $schedule->command('ssl:notify-expiry')->dailyAt('9:00'); //每天9点执行

        $schedule->command('MakeDomain')->everyMinute(); //生成域名

        if(env('APP_ENV') == 'production'){
            $schedule->command('UpDomainToAliDns')->everyFifteenMinutes(); //生成域名并且解析到阿里云
        }











    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }

}
