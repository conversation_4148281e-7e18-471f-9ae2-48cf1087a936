<?php

namespace App\Admin\Forms\Admin;

use App\Models\AdminUser;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class BatchSetAuditOcpxForm extends Form implements LazyRenderable
{
    use LazyWidget;
    // 处理请求
    public function handle(array $input)
    {
        // id转化为数组
        $id = explode(',', $input['id'] ?? null);
        if (!$id) {
            return $this->response()->alert()->error('提示')->detail('参数错误。');
        }

        $adminUsers = AdminUser::query()->find($id);

        if ($adminUsers->isEmpty()) {
            return $this->response()->alert()->error('提示')->detail('用户不存在。');
        }

        foreach ($adminUsers as $key => $adminUser) {

            if(isset($input['audit_ocpx'])){
                $adminUser->audit_ocpx = $input['audit_ocpx'];
            }
            $adminUser->save();
        }
        return $this->response()->alert()->success('提示')->detail('配置成功')->refresh();
    }

    public function form()
    {
        $this->switch('audit_ocpx', '屏蔽进粉手动上报')->help('开启后，屏蔽页进粉可以手动上报。');
        $this->hidden('id')->value($this->payload['ids'] ?? '');
    }
}
