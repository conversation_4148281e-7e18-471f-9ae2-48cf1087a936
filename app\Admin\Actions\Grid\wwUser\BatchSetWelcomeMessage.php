<?php

namespace App\Admin\Actions\Grid\wwUser;

use App\Admin\Extensions\Grid\BatchActionPlus;
use App\Admin\Forms\WwUser\BatchSetWelcomeMessageForm;

class BatchSetWelcomeMessage extends BatchActionPlus
{
    /**
     * @return string
     */
    public $title = '欢迎语';
    public $buttonText = '<button class="btn btn-primary ww_user_batch_btn" ><i class="feather icon-message-circle"></i><span class="selected"></span>欢迎语</button>';


    public function form(): BatchSetWelcomeMessageForm
    {
        // 实例化表单类
        return BatchSetWelcomeMessageForm::make();
    }
}
