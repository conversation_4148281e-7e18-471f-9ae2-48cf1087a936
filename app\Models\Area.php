<?php

	namespace App\Models;

	use Dcat\Admin\Traits\HasDateTimeFormatter;
    use Illuminate\Database\Eloquent\Model;
    use Illuminate\Database\Eloquent\SoftDeletes;

    class Area extends Model
	{
		use HasDateTimeFormatter;
		use SoftDeletes;

		protected $table = 'area';


        /**
         * 将地域ID数组映射为名称数组
         *
         * @param array $areaIds 地域ID数组
         * @param array $areaMap 地域映射数组
         * @return array 地域名称数组
         */
        public static function mapAreaIdsToNames(array $areaIds, array $areaMap): array
        {
            return array_map(
                fn($id) => $areaMap[$id] ?? "错误地域($id)",
                $areaIds
            );
        }
	}
