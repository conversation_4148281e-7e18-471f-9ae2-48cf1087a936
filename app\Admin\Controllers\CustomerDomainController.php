<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Grid\domains\BatchDomainStatus;
use App\Admin\Forms\Domains\CertificateUploadForm;
use App\Admin\Forms\Domains\SetDefaultDomainForm;
use App\Admin\Repositories\AdminDomain;
use App\Models\AdminDomain as AdminDomainModels;
use App\Models\AdminDomainCert;
use App\Models\AdminSubUser;
use App\Models\AdminUser;
use Carbon\Carbon;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Show;
use Dcat\Admin\Widgets\Alert;
use Dcat\Admin\Widgets\Modal;

/**
 * @property $admin_uid
 * @property $input
 */
class CustomerDomainController extends AdminController
{
//    private $listener_status;

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid(): Grid
    {
        return Grid::make(new AdminDomain(), function (Grid $grid) {
            $grid->disableViewButton();
            $grid->disableDeleteButton();
            $grid->disableCreateButton();
            $grid->disableRowSelector();
//            $grid->disableActions();

            /** @var AdminUser $adminUser */
            $adminUser = Admin::user();

            $grid->model()->orderBy('id', 'desc')->where('admin_uid', AdminSubUser::getAdminUids($adminUser->id));

            $grid->column('id')->sortable();
            $grid->column('domain')->copyable();
            $grid->column('upload_status','证书上传状态')->using(AdminDomainModels::USER_CERT_UPLOAD_STATUS)->badge([
                0 => 'grey', // 设置默认颜色，不设置则默认为 default
                1 => 'success',
                2 => 'danger',
                3 => 'warning',
            ])->help('已部署可以投放使用');

            $grid->column('upload_status','证书上传状态')->display(function ($uploadStatus) use ($grid) {
                $grid->disableActions();
                if($uploadStatus == 1 && $this->listener_status == 'Associated'){
                    return  "<span style='color: #0B8B06'>已上传</span>";
                }
                if($uploadStatus == 3){
                    return  "<span style='color: red'>重新上传</span>";
                }
                return "<span style='color: peru'>待系统处理</span>";
            })->help('注意：证书为已上传状态，域名才可使用。');
            $grid->column('status','默认域名')->using([1 => '是', 0 => '否'])->dot([
                'default' => 'primary', // 设置默认颜色，不设置则默认为 default
                1 => 'success',
                0 => 'danger',
            ]);
            $grid->column('ssl_ex_time')->display(function ($value) {
                if (empty($value)) {
                    return '未获取过期日期';
                }

                $date = Carbon::parse($value);
                /** 使用 Carbon 的 isPast() 方法检查是否过期 */
                $isExpired = $date->isPast();

                return sprintf(
                    '<span style="%s">%s%s</span>',
                    $isExpired ? 'color: #ff4d4f; font-weight: bold;' : 'color: #52c41a;',
                    $date->format('Y-m-d'),
                    $isExpired ? ' (已过期)' : ''
                );
            });

            $grid->tools(function (Grid\Tools $tools) {
                $tools->append(Modal::make()
                    ->xl()
                    ->title('添加域名')
                    ->button('<button class="btn btn-primary"><i class="fa fa-cloud-upload"></i>&nbsp;&nbsp;添加域名</button>')
                    ->body(CertificateUploadForm::make())
                );
                $tools->append(Modal::make()
                    ->xl()
                    ->title('添加域名说明')
                    ->button('<button class="btn btn-primary"><i class="feather icon-info"></i>&nbsp;&nbsp;添加域名说明</button>')
                    ->body(view("admin.illustrate.customerDomainIllustrate"))
                );//说明
                $tools->append(Modal::make()
                    ->xl()
                    ->title('修改默认使用域名')
                    ->button('<button class="btn btn-primary"><i class="fa fa-fw fa-cogs"></i>&nbsp;&nbsp;修改默认使用域名</button>')
                    ->body(SetDefaultDomainForm::make())
                );//设置默认使用域名    customerDomainIllustrate.blade.php

            });

            $grid->filter(function (Grid\Filter $filter) use ($grid) {
                $filter->panel();
                $filter->expand();
                $filter->equal('id')->width(2);
//                    $filter->equal('admin_uid');
                $filter->like('domain')->width(2);
                $filter->gt('ssl_ex_time', '过期时间大于')->width(2);
                $filter->equal('status','域名状态')->select([1 => '正常', 0 => '异常'])->width(2);
            });


        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show|null
     */
    protected function detail(mixed $id): ?Show
    {
        return null;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form(): Form
    {
        return Form::make(new AdminDomain(), function (Form $form) {

            $form->display('id');
            $form->hidden('id');
            $form->hidden('upload_status');

            $form->url('domain')->default('https://')->required();
            $pem = '';
            $key = '';;
            $certInfo = AdminDomainCert::query()
                ->where('admin_uid', Admin::user()->id)
                ->where('domain_id', $form->model()->id)
                ->first();
            if($certInfo){
                $pem = $certInfo->pem ?? '';
                $key = $certInfo->key ?? '';
            }
            $form->textarea('pem', '证书(PEM格式)')->help('后缀为.pem的文件。')->value($pem)->required();
            $form->textarea('key', '密钥(KEY)')->help('后缀为.key的文件。')->value($key)->required();
            $form->disableViewButton();
            $form->disableViewCheck();
            $form->disableDeleteButton();

        })->saving(function (Form $form)  {
            $check = AdminDomainModels::query()->where('domain', $form->input('domain'))->first();
            if ($form->isCreating()) {
                if ($check) {
                    return $form->response()->alert()->error('提示')->detail('该域名已存在-1：' . $form->input('domain'));
                }
            }
            if($form->isEditing()){
                if ($check && $check['id'] != $form->input('id')) {
                    return $form->response()->alert()->error('提示')->detail('该域名已存在-2：' . $form->input('domain'));
                }
//                if($check['upload_status'] == 1 && $check['listener_status'] == 'noAssocia'){
//                    return $form->response()->alert()->error('提示')->detail('域名证书已经上传，禁止修改。' );
//                }
            }


            $certInfo = AdminDomainCert::query()
                ->where('admin_uid', Admin::user()->id)
                ->where('domain_id', $form->input('id'))
                ->first();
            if ($certInfo) {
                $certInfo->key = $form->input('key');
                $certInfo->pem = $form->input('pem');
                $certInfo->save();
            }
            $form->deleteInput(['pem','key']);
            $form->upload_status = 3; //重新上传
        });

    }


}
