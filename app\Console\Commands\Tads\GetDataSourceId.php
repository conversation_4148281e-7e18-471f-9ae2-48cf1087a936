<?php

	namespace App\Console\Commands\Tads;

	use App\Models\TencentAdAccount;
	use Illuminate\Console\Command;
	use Illuminate\Support\Facades\Http;

	class GetDataSourceId extends Command
	{
		/**
		 * The name and signature of the console command.
		 *
		 * @var string
		 */
		protected $signature = 'Tads:GetDataSourceId';

		/**
		 * The console command description.
		 *
		 * @var string
		 */
		protected $description = 'Command description';

		/**
		 * Execute the console command.
		 *
		 * @return int
		 */
		public function handle()
		{
			//	    修复数据源ID
			$accounts = TencentAdAccount::query()->where("data_source_id", 'unknown parameter error.')->orWhere("data_source_id", "")->get();
            if($accounts->isEmpty()) {
                return false;
            }
			foreach ($accounts as $account) {

				$account_id = $account->account_id;
				$interface  = 'user_action_sets/add';
				$url        = 'https://api.e.qq.com/v1.1/' . $interface;

				$common_parameters = [
					'access_token' => $account->access_token,
					'timestamp'    => time(),
					'nonce'        => md5(uniqid('', true))
				];

				$parameters = [
					'account_id' => $account->account_id,
					'type'       => 'WEB',
					'name'       => '智投方舟',
				];
				$res        = Http::asJson()->post($url . "?" . http_build_query($common_parameters), $parameters)->json();
				$this->info(json_encode($res));
				if ($res['code'] == 51000) {
					$errorMsg                = $res['message'];
					$dataSourceId            = str_replace("could create only one web action set for " . $account_id . " with existed one : ", "", $errorMsg);
					$account->data_source_id = $dataSourceId;
					$account->save();
				} else {
					if (isset($res['data']['user_action_set_id']) && !empty(isset($res['data']['user_action_set_id']))) {
						$dataSourceId = $res['data']['user_action_set_id'];

						$account->data_source_id = $dataSourceId;
						$account->save();
					}
				}
			}
			return Command::SUCCESS;
		}
	}
