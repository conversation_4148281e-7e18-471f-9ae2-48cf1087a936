<?php

namespace App\Console\Commands\Elasticsearch;


use App\Models\WwUser;

use App\Services\ElasticsearchService;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Symfony\Component\Console\Command\Command as CommandAlias;

class SyncWwUsersToEs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'Es:SyncWwUsersToEs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '同步销售数据到es';


    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $indexName = 'ww_users';
        $elasticsearch = new ElasticsearchService($indexName);
        $mapping = [
            'properties' => [
                'id' => [
                    'type' => 'integer',
                ],
                'name' => [
                    'type' => 'keyword',
//                    'analyzer' => 'ik_max_word',  // 中文分词
//                    'search_analyzer' => 'ik_smart'
                ],
                'corp_id' => [
                    'type' => 'integer',
                ],
                'type' => [
                    'type' => 'integer',
                ],
                'open_user_id' => [
                    'type' => 'keyword',
                ],
                'user_id' => [
                    'type' => 'keyword',
                ],
                'weight' => [
                    'type' => 'integer',
                ],
                'add_method' => [
                    'type' => 'integer',
                ],
                'status' => [
                    'type' => 'integer',
                ],
                'license_c_status' => [
                    'type' => 'integer',
                ],
                'today_show_count' => ['type' => 'integer'],
                'online_status' => ['type' => 'integer'],
                'created_at' => [
                    'type' => 'date',
                    'format' => 'yyyy-MM-dd HH:mm:ss'
                ],
                'updated_at' => [
                    'type' => 'date',
                    'format' => 'yyyy-MM-dd HH:mm:ss'
                ]
            ]
        ];
        $elasticsearch->createIndex($mapping);

        WwUser::query()->chunkById(1000, function ($wwUser) use ($elasticsearch) {
                $wwUser = $wwUser->toArray();
                $list = [];
                foreach ($wwUser as $user) {
                    $list[$user['id']] = $user;
                }
                $result = $elasticsearch->bulkIndexDocument($list);
                $this->info(json_encode($result));
            });
    }


}
