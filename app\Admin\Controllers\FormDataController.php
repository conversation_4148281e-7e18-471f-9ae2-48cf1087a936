<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\FormData;
use App\Models\AdminSubUser;
use App\Models\AdminUser;
use App\Models\WwLink;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Show;
use Illuminate\Http\Response;

/**
 * @property WwLink $wwLinkInfo
 * @property        $input
 */
class FormDataController extends AdminController
{

    /**
     * 判断是否默认展示当天的进粉记录
     *
     * @param Content $content
     *
     * @return Content
     */

    public function index(Content $content): Content
    {
        return $content
            ->header(trans('form-data.labels.page_form_data'))
            ->body($this->grid());
    }


    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid(): Grid
    {
        return Grid::make(new FormData(['adminInfo','wwLinkInfo']), function (Grid $grid) {
            $grid->paginate(AdminUser::getPaginate());
            if (!AdminUser::isSystemOp()) { //如果不是超级管理员
                if (Admin::user()->isRole('customer')) { //如果是“客户”的角色 也就是主账号
                    $grid->model()->whereIn("admin_uid", AdminSubUser::getAdminUids(Admin::user()->id));//如果是主账号，则获取所有子账号的id，包括自己
                    $grid->column('所属账号')->display(function () {
                        if (Admin::user()->id == $this->admin_uid) { //如果是自己创建的则直接显示主账号
                            return '主账号';
                        } else {
                            $username = Admin::user()->where('id', $this->admin_uid)->value('username');
                            $username = str_replace(Admin::user()->username, '', $username);
                            return $username;
                        }
                    });

                } else { //如果是客户运营的角色 也就是子账号
                    $grid->model()->where("admin_uid", Admin::user()->id)->orderByDesc("id");
                }
            }
            $grid->disableCreateButton();
            $grid->disableViewButton();
            $grid->disableDeleteButton();
            $grid->disableEditButton();
            $grid->disableBatchDelete();
            $grid->showColumnSelector();
            $grid->disableActions();
            $grid->model()->orderByDesc("id");
            $grid->column('id', 'ID')->sortable();
            if(AdminUser::isSystemOp()){
                $grid->column('vid', '访问记录ID');
                $grid->column('admin_uid', '用户ID')->display(function ($admin_uid){
                    if(!$admin_uid){
                        return '';
                    }
                    return $admin_uid;
                });
                $grid->column('adminInfo.name', '用户');
            }

            $grid->column('wwLinkInfo.remark', "链接")->display(function ($value) {
                if( $this->wwLinkInfo){
                    return $value . "「ID:" . $this->wwLinkInfo->id . "」";
                }
              return '';
            });
            $grid->column('wwLinkInfo.account_id', "账户ID")->ClickCopy(9);
//            $grid->column('vid', '访问记录ID');
            $grid->column('name', '姓名');
            $grid->column('phone', '手机');
            $grid->column('ip', 'IP');
            $grid->column('area', '地域');
            $grid->column('created_at');
            $grid->export();
            $grid->export()->csv()->rows(function ($rows) {
                return $this->exportData($rows);
            })->chunkSize(1000)->filename('表单数据' . date('Y-m-d H:i:s') . mt_rand(1000000, 9999999));

            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                $filter->equal('id')->width(2);
                $filter->equal('wwLinkInfo.ID',"投放链接ID")->width(2);
                $filter->equal('wwLinkInfo.account_id',"广告账户ID")->width(2);
            });
        });
    }

    private function exportData($rows)
    {
        $adminUser = Admin::user();
        foreach ($rows as $key => $row) {
            if($row->vid == 0 || $row->vid == -1){
                continue;
            }
            // 处理所属账号逻辑
            if ($adminUser->id == $row->admin_uid) {
                $rows[$key]['所属账号'] = '主账号';
            } else {
                $username               = $row->adminInfo ? $row->adminInfo->username : '';
                $rows[$key]['所属账号'] = str_replace($adminUser->username, '', $username);
            }
            $rows[$key]['ID'] = $row->id;
            $rows[$key]['wwLinkInfo.remark'] = '【' .$row->wwLinkInfo->remark . '】ID:' . $row->wwLinkInfo->id;
            $rows[$key]['wwLinkInfo.account_id'] = $row->wwLinkInfo->account_id;
        }
        return $rows;
    }

    public function destroy($id): Response|JsonResponse
    {
        return $this->form()->response()->error("无权限操作");
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form(): Form
    {
        return Form::make(new FormData(), function (Form $form) {
            if (!$form->isCreating() && !AdminUser::isAdmin($form->model())) {
                $form->display('N')->value("无数据");
            } else {
                $form->display('id');
            }
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail(mixed $id): Show
    {
        return Show::make($id, new FormData(), function (Show $show) {
            if (!AdminUser::isAdmin($show->model())) {
                $show->field('N')->value("无数据");
            } else {
                $show->field('id');
            }
        });
    }

}
