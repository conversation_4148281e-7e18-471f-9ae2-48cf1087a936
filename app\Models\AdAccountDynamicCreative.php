<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property mixed|string $asset_inconsistent_status
 * @property mixed|string $source
 * @property mixed|string $creative_set_approval_status
 * @property mixed|string $marketing_asset_verification
 * @property mixed|string $last_modified_time
 * @property mixed|string $created_time
 * @property mixed|string $is_deleted
 * @property mixed|string $configured_status
 * @property mixed|string $page_track_url
 * @property mixed|string $program_creative_info
 * @property mixed|string $click_tracking_url
 * @property mixed|string $impression_tracking_url
 * @property mixed|string $creative_components
 * @property mixed|string $dynamic_creative_type
 * @property mixed|string $delivery_mode
 * @property mixed|string $creative_template_id
 * @property mixed|string $dynamic_creative_name
 * @property mixed|string $dynamic_creative_id
 * @property mixed|string $adgroup_id
 * @property mixed        $system_industry_id
 * @property mixed        $account_id
 * @property mixed        $admin_uid
 */
class AdAccountDynamicCreative extends Model
{
	use HasDateTimeFormatter;
    protected $table = 'ad_account_dynamic_creatives';

    public function adAccountInfo(): BelongsTo
    {
        return $this->BelongsTo(TencentAdAccount::class, 'account_id', 'account_id');
    }
}
