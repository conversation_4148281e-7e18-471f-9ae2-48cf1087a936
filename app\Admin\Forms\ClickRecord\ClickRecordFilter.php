<?php

namespace App\Admin\Forms\ClickRecord;

use App\Models\AdminUser;
use App\Models\WwLink;
use Dcat\Admin\Admin;
use Dcat\Admin\Widgets\Form;

class ClickRecordFilter extends Form
{
    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
	    $url = admin_url("realtime_visitors");
		$param = [];
		if(!empty($input['ww_link_id'])){
			$param['ww_link_id'] = $input['ww_link_id'];
		}
        if(!empty($param)){
            $wwLink = WwLink::query()->find($input['ww_link_id']);

            if(!$wwLink){
                return $this->response()->error('页面不存在');
            }
            if (!AdminUser::isAdmin($wwLink)) {
                return $this->response()->error('异常操作，请刷新页面后重试');
            }
        }
        if(!empty($input['admin_uid'])) $param['admin_uid'] = $input['admin_uid'];
        if(!empty($input['ad_account_name'])) $param['ad_account_name'] = $input['ad_account_name'];
        if(!empty($input['b_time'])) $param['b_time'] = $input['b_time'];
        if(!empty($input['e_time'])) $param['e_time'] = $input['e_time'];

        return $this
				->response()
				->success('加载成功.')
				->redirect($url."?".http_build_query($param));
    }

    /**
     * Build a form here.
     */
    public function form()
    {
	    $this->disableResetButton();
		$this->disableSubmitButton();
//	    $this->column(3, function (Form $form) {
//		    $form->text("adp","ADP")->help("特殊使用");
//	    });
	    $this->column(6, function (Form $form) {

		    if(AdminUser::isSystemOp()){
                $data = WwLink::query()->orderByDesc('id')->get();
            }else{
                $data = WwLink::query()->orderByDesc('id')->where('admin_uid',Admin::user()->id)->get();
            }
		    $options = [];
		    foreach($data as $datum){
			    $options[$datum->id] = empty($datum->remark) ? $datum->id ."-ID页面无备注"."({$datum->account_id})" : $datum->remark . "({$datum->account_id})";
		    }
		    $form->select("ww_link_id",'投放链接')->options($options);
            if(AdminUser::isSystemOp()){
                $form->select("admin_uid",'管理员')->options(AdminUser::query()->pluck("username","id"));
                $form->text("ad_account_name","账户主体");
                $form->datetime("b_time",'开始时间')->default(date("Y-m-d",time())." 00:00:00");
                $form->datetime("e_time","结束时间")->default(date("Y-m-d",time())." 23:59:59");
            }
	    });
	    $this->column(3, function (Form $form) {
		    $form->html("<button type=\"submit\" class=\"btn btn-primary pull-left\" style='margin-top: 4px'><i class=\"feather icon-search\"></i> 搜索</button>");
	    });
    }

    /**
     * The data of the form.
     *
     * @return array
     */
    public function default()
    {
        return [
            'name'  => 'John Doe',
            'email' => '<EMAIL>',
        ];
    }
}
