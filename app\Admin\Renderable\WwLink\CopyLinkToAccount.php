<?php

    namespace App\Admin\Renderable\WwLink;

    use Admin;
    use App\Models\TencentAdAccount;
    use App\Models\WwLink;
    use Dcat\Admin\Contracts\LazyRenderable;
    use Dcat\Admin\Traits\LazyWidget;
    use Dcat\Admin\Widgets\Form;
    use Illuminate\Support\Facades\Log;

    class CopyLinkToAccount extends Form implements LazyRenderable
    {
        use LazyWidget;

        public function handle(array $input)
        {
            Log::info(Admin::user()->username . '：复制投放链接，' . json_encode($input));
            if (!empty($input['error_msg'])) {
                return $this->response()->error($input['error_msg'])->refresh();
            }
            /** @var WwLink $wwLink */
            $wwLink = WwLink::query()->find($input['ww_link_id']);
            if ($wwLink && $wwLink->admin_uid == Admin::user()->id) {
                if ($wwLink->media_type == '腾讯广告') {
                    $accountIds = $input['account_ids'];
                } else {
                    $accountIds = [];
                    for ($i = 0; $i < $input['link_num']; $i++) {
                        $accountIds[] = null;
                    }
                }
                foreach ($accountIds as $accountId) {
                    /** @var TencentAdAccount $adAccount */
                    $adAccount = TencentAdAccount::query()->where("account_id", $accountId)->first();
                    if ($adAccount && $adAccount->admin_uid != Admin::user()->id) {
                        return $this->response()->error("无权限操作")->refresh();
                    }
                    if (Admin::user()->repeat_ww_link == 0) {
                        $checkWhere = [
                            'admin_uid'  =>  Admin::user()->id,
                            'account_id' => $accountId,
                        ];
                        $checkRep = WwLink::query()->where($checkWhere)->first();
                        if($checkRep){
                            return $this->response()->error($accountId . '：该投放账户已创建链接，请重试');
                        }
                    }
                    $newWwLink = $wwLink->replicate();
                    $newWwLink->account_id = $accountId;
                    $newWwLink->save();
                    if ($newWwLink->media_type !== '腾讯广告') {
                        $newWwLink->account_id = $newWwLink->id + ********;
                    }
                    if (!WwLink::query()->where("link_ad_id", $newWwLink->account_id)->exists()) {
                        $newWwLink->link_ad_id = $newWwLink->account_id;
                    } else {
                        $newWwLink->link_ad_id = $newWwLink->account_id."_".$newWwLink::withTrashed()->where("link_ad_id",
                                $newWwLink->account_id)->count();
                    }
                    $newWwLink->remark = $newWwLink->account_id;
                    $newWwLink->save();
                }
                return $this->response()->success("复制完成")->refresh();
            } else {
                return $this->response()->error("未找到链接，请刷新后重试")->refresh();
            }
        }

        public function form()
        {
            $adminUserId = Admin::user()->id;
            $this->hidden("ww_link_id");
            /** @var WwLink $wwLink */
            $wwLink = WwLink::query()->find($this->payload['ww_link_id']);
            if ($wwLink && $wwLink->admin_uid == $adminUserId) {
                if ($wwLink->media_type == '腾讯广告') {
                    $this->multipleSelect('account_ids', "账户ID")
                        ->options(function ($keyword) use($adminUserId) { // 异步搜索回调
                            return TencentAdAccount::query()
                                ->where('admin_uid', $adminUserId)
                                ->where('account_id', 'like', "{$keyword}%")
                                ->pluck('account_id', 'account_id')
                                ->take(5); // 限制返回数量
                        })
                        ->ajax(admin_route('api.tencent-ad-account').'?no_link=1')->required(); // 启用异步搜索

                    // $this->multipleSelectTable('account_ids',"账户ID")
                    //     ->title('选择账户')
                    //     ->dialogWidth('60%') // 弹窗宽度，默认 800px
                    //     ->from(AdAccountTable::make()) // 设置渲染类实例，并传递自定义参数
                    //     ->model(TencentAdAccount::class, 'account_id', 'account_id'); // 设置编辑数据显示

                } else {
                    $this->number("link_num", "需要复制的数量")->min(1)->default(1);
                }

            } else {
                $errMsg = '未找到链接，请刷新后重试';
                $this->display("提示")->value($errMsg);
                $this->hidden('error_msg')->value($errMsg);
            }
        }

        public function default()
        {
            return [
                // 展示上个页面传递过来的值
                'ww_link_id' => $this->payload['ww_link_id'] ?? '',
            ];
        }
    }
