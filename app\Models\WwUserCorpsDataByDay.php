<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property mixed            $date
 * @property int|mixed|string $ww_user_corp_id
 */
class WwUserCorpsDataByDay extends Model
{
	use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'ww_user_corps_data_by_days';

	public function corpInfo(): BelongsTo
	{
		return $this->belongsTo(WwCorpInfo::class, 'ww_user_corp_id');
	}
}
