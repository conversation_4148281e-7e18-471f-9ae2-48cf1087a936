<?php

namespace App\Admin\Metrics\TencentAd;

use App\Models\TencentAdAccountMonitor;
use App\Models\TencentAdAccountMonitorCount;
use Dcat\Admin\Admin;
use Dcat\Admin\Widgets\Metrics\Line;
use Illuminate\Http\Request;

class AccountMonitorHourly extends Line
{

    // 这里的参数一定要设置默认值
    public function __construct(int $countId)
    {
        parent::__construct();

        $this->countId = $countId;
        $this->title('时报');
        $this->height = 335;
        $this->chartHeight = 500;

        // 图表数据
        $countData = TencentAdAccountMonitorCount::query()->where('id', $this->countId)->first();

        $where = [
            'admin_uid' => $countData->admin_uid,
            'account_id' => $countData->account_id,
            'date' => $countData->date
        ];

        $viewCount = TencentAdAccountMonitor::query()->where($where)->pluck('view_count', 'hour')->toArray();

        $validClickCount = TencentAdAccountMonitor::query()->where($where)->pluck('valid_click_count', 'hour')->toArray();
        $adCost = TencentAdAccountMonitor::query()->where($where)->pluck('ad_cost', 'hour')->toArray();
        foreach ($adCost as $key => $value) {
            $adCost[$key] = round($value / 100, 2);
        }
        $conversionsCount = TencentAdAccountMonitor::query()->where($where)->pluck('conversions_count', 'hour')->toArray();
        $conversionsRate = TencentAdAccountMonitor::query()->where($where)->pluck('conversions_rate', 'hour')->toArray();
        $conversionsCost = TencentAdAccountMonitor::query()->where($where)->pluck('conversions_cost', 'hour')->toArray();
        foreach ($conversionsCost as $key => $value) {
            $conversionsCost[$key] = round($value / 100, 2);
        }
        $pvData = TencentAdAccountMonitor::query()->where($where)->pluck('jump_pv_count', 'hour')->toArray();
        $addCount = TencentAdAccountMonitor::query()->where($where)->pluck('add_count', 'hour')->toArray();
        $openCount = TencentAdAccountMonitor::query()->where($where)->pluck('open_count', 'hour')->toArray();

        for ($i = 0; $i < 24; $i++) {
            //查询数据库是否存在
            if (!isset($viewCount[$i])) {
                $viewCount[$i] = 0;
            }
            if (!isset($validClickCount[$i])) {
                $validClickCount[$i] = 0;
            }
            if (!isset($adCost[$i])) {
                $adCost[$i] = 0;
            }
            if (!isset($conversionsCount[$i])) {
                $conversionsCount[$i] = 0;
            }
            if (!isset($conversionsRate[$i])) {
                $conversionsRate[$i] = 0;
            }
            if (!isset($conversionsCost[$i])) {
                $conversionsCost[$i] = 0;
            }
            if (!isset($pvData[$i])) {
                $pvData[$i] = 0;
            }
            if (!isset($addCount[$i])) {
                $addCount[$i] = 0;
            }
            if (!isset($openCount[$i])) {
                $openCount[$i] = 0;
            }
        }
        ksort($viewCount);
        ksort($validClickCount);
        ksort($adCost);
        ksort($conversionsCount);
        ksort($conversionsRate);
        ksort($conversionsCost);
        ksort($pvData);
        ksort($addCount);
        ksort($openCount);
//        dd($viewCount, $validClickCount, $adCost, $conversionsCount, $conversionsRate, $conversionsCost, $pvData, $addCount, $openCount);
        $this->withChart($viewCount, $validClickCount, $adCost, $conversionsCount, $conversionsRate, $conversionsCost, $pvData, $addCount, $openCount);

    }

    /**
     * 初始化卡片内容
     *
     * @return void
     */
    protected function init()
    {


//        parent::init();
//        $this->title('时报');
//        $this->height = 335;
//        $this->chartHeight = 500;
//        // 图表数据
//        $this->withChart($viewCount,$validClickCount,$adCost,$conversionsCount,$conversionsRate,$conversionsCost,$pvData,$addCount,$openCount);
    }

    /**
     * 设置图表数据.
     *
     * @param array $data
     *
     * @return $this
     */
    public function withChart($viewCount, $validClickCount, $adCost, $conversionsCount, $conversionsRate, $conversionsCost, $pvData, $addCount, $openCount)
    {
        return $this->chart([
            'type' => 'line',
            'chart' => [
                'type' => 'area',
                'toolbar' => [
                    'show' => true,
                ],
                'sparkline' => [
                    'enabled' => false,
                ],
                'grid' => [
                    'show' => false,
                    'padding' => [
                        'left' => 0,
                        'right' => 0,
                    ],
                ],

            ],
            'clear' => true,
            'colors' => ['#008FFB', '#00E396', '#FEB019', '#FF4560', '#775DD0', '#546E7A', '#D10CE8',' #5c6bc6','pink'],
            'series' => [
                [
                    'name' => '曝光量',
                    'data' => $viewCount,
                ],
                [
                    'name' => '点击量',
                    'data' => $validClickCount,
                ],
                [
                    'name' => '消耗',
                    'data' => $adCost,
                ],
                [
                    'name' => '转化数',
                    'data' => $conversionsCount,
                ],
                [
                    'name' => '转化率',
                    'data' => $conversionsRate,
                ],
                [
                    'name' => '转化成本',
                    'data' => $conversionsCost,
                ],
                [
                    'name' => 'PV',
                    'data' => $pvData,
                ],
                [
                    'name' => '进粉数',
                    'data' => $addCount,
                ],
                [
                    'name' => '开口数',
                    'data' => $openCount,
                ],

            ],
            'dataLabels' => [
                'enabled' => true
            ],
            'xaxis' => [
                'categories' => [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
                'title' => [
                    'text' => '小时'
                ],
                'labels' => [
                    'show' => true,
                ],
                'axisBorder' => [
                    'show' => true,
                ],
            ],
            'yaxis' => [
                'title' => [
                    'text' => '量'
                ]
            ]
        ]);
    }
}
