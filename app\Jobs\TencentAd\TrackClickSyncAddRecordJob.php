<?php

namespace App\Jobs\TencentAd;

use App\Models\WwUserAddRecord;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;


class TrackClickSyncAddRecordJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $addRecord;


    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($addRecord)
    {
        $this->addRecord = $addRecord;
    }

    /**
     * Execute the job.
     *
     * @return mixed
     */
    public function handle()
    {
        $select = [
            'id', 'adgroup_id', 'adgroup_name', 'dynamic_creative_id', 'dynamic_creative_name', 'site_set_name'
        ];
        $trackClickLog = DB::table('ad_track_click_logs')->select($select)->where([
            'impression_id' => $this->addRecord->click_id
        ])->first();
        if(!$trackClickLog){
            $trackClickLog = DB::table('ad_track_click_logs')->select($select)->where([
                'request_id' => $this->addRecord->click_id
            ])->first();
        }
        if(!$trackClickLog){
            $trackClickLog = DB::table('ad_track_click_logs')->select($select)->where([
                'click_id' => $this->addRecord->click_id
            ])->first();
        }
        if ($trackClickLog) {
            WwUserAddRecord::query()->where('id',$this->addRecord->id)->update([
                'track_click_log_id' => $trackClickLog->id,
                'adgroup_id' => $trackClickLog->adgroup_id, //广告组ID
                'adgroup_name' => $trackClickLog->adgroup_name ?? '',//广告组名称
                'dynamic_creative_id' => $trackClickLog->dynamic_creative_id,//创意ID
                'dynamic_creative_name' => $trackClickLog->dynamic_creative_name ?? '',//创意名称
                'site_set_name' => $trackClickLog->site_set_name ?? '',//广告版位
            ]);
        }

        return true;
    }
}
