<?php

namespace App\Admin\Actions\Grid\corp;

use App\Jobs\Corp\SyncCorpLabelJob;
use App\Models\AdminSubUserAuthCorp;
use App\Models\AdminUser;
use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Traits\HasPermissions;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;

class SynCorpLabelAction extends RowAction
{
    /**
     * @return string
     */
    protected $title = 'Title';

    public function handle()
    {
        $corpAuthId = $this->getKey();
        $corpAuthInfo = AdminSubUserAuthCorp::query()->with("corpInfo")->find($corpAuthId);
        if (!$corpAuthInfo || !$corpAuthInfo->corpInfo) {
            return $this->response()->alert()->error('提示')->detail('未查询到数据')->refresh();
        }
        if (!AdminUser::isAdmin($corpAuthInfo)) {
            return $this->response()->alert()->error('提示')->detail('无权限操作')->refresh();
        }
        SyncCorpLabelJob::dispatch($corpAuthId)->onQueue('syn_corp_label');
        return $this->response()->alert()->success('提示')->detail('同步中，请稍后查看。')->refresh();
    }

    /**
     * @return string|array|void
     */
    public function confirm()
    {
        return ['确认更新企微标签？'];
    }

    protected function html()
    {
        $name = '<button class="' . $this->getElementClass() . ' btn btn-sm btn-outline-primary  "> 更新企微标签 </button>';
        return $name;
    }

    /**
     * @param Model|Authenticatable|HasPermissions|null $user
     *
     * @return bool
     */
    protected function authorize($user): bool
    {
        return true;
    }

    /**
     * @return array
     */
    protected function parameters()
    {
        return [];
    }
}
