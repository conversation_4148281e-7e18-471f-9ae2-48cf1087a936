<?php


namespace App\Models;


class AdAction
{
    //腾讯广告上报行为类型：https://developers.e.qq.com/v3.0/docs/enums#api_action_type
    const TENCENT_TYPE = [
//        'RESERVATION'    => '预约[RESERVATION]',
//        'COMPLETE_ORDER' => '下单[COMPLETE_ORDER]',
//        'SCANCODE_WX'    => '扫码[SCANCODE_WX]',
//        'ADD_WECHAT'     => '添加微信成功[ADD_WECHAT]',
//        'SCANCODE'       => '加企业微信客服[SCANCODE]',
//        'PARTICIPATED'   => '低价课首次参课[PARTICIPATED]',
//        'ADD_GROUP'      => '加群[ADD_GROUP]',
        'RESERVATION'    => '预约',
        'COMPLETE_ORDER' => '下单',
        'SCANCODE_WX'    => '扫码',
        'ADD_WECHAT'     => '添加微信成功',
        'SCANCODE'       => '加企业微信客服',
        'PARTICIPATED'   => '低价课首次参课',
        'ADD_GROUP'      => '加群',
//        'OPTIMIZATIONGOAL_ECOMMERCE_SCANCODE_WX' => '扫码加粉'
    ];

    const OE_TYPE       = [
        'form'                => '表单提交',
        'wechat'              => '微信复制',
        'customer_effective'  => '有效获客',
        'wechat_qrcode_show'  => '微信_二维码展示',
        'wechat_qrcode_try'   => '微信_长按二维码',
        'work_wechat_added'   => '微信_添加企业微信',
        'work_wechat_dialog'  => '微信_用户首次消息',
        'work_wechat_confirm' => '微信_用户确认意向',
    ];
    const HUAWEI_TYPE   = [
        'reservation' => '预约',
        'keyAction'   => '关键行为',
        'followScan'  => '扫码关注',
    ];

    //快手广告上报行为类型：https://docs.qingque.cn/d/home/<USER>
    const KUAISHOU_TYPE = [
        9   => '表单',
        43  => '首次试听课到课',
        64  => '微信调起',
        118 => '添加企业微信',
        274 => '企微首次回复',
        //			1031 => '加粉后-开口3次',
        //			1032 => '加粉后-开口5次',
        //			1033 => '加粉后-开口10次',
        //			278  => '企微加粉-特殊-不开口',
        //			279  => '企微加粉-特殊-极端用户',
        //			284  => '删除企微好友',
    ];
    const KUAISHOU_DEEP_TYPE = [
        251   => '有效获客',
    ];

    const IQIYI_TYPE = [
        7   => '准进群',
        200 => '表单提交',
        203 => '关注微信号',
    ];

    const OPPO_TYPE = [
        101 => '表单提交',
        102 => '表单关键行为',
        103 => '表单-有效咨询',
        104 => '表单-微信关注',
        105 => '表单-网页购买',
        106 => '表单-电话拨打',
    ];

    const VIVO_TYPE = [
        'SUBMIT'        => '表单提交',
        'BUTTON_CLICK'  => '按钮点击',
        'IDENTIFY_CODE' => '识别微信二维码',
        'ADD_WECHAT'    => '添加微信',
        'DIALOGUE_MPA'  => '微信用户首次消息',
        'GET_WECHAT'    => '微信加粉',
    ];

    const YOUKU_TYPE = [
        'form_submit' => '表单提交',
        'wechat'      => '微信加粉',
    ];

    const XIMALAYA_TYPE = [
        2 => '表单提交',
        3 => '有效咨询',
        4 => '微信加好友',
        //            5 => '页面访问/支付页露出',
        //            6 => '其他',
        //            7 => '支付-存在意向',
        //            8 => '回访-信息确认',
        //            13 => '付费',
        //            22 => '有效加微',
    ];

    const BAIDU_TYPE = [
        1 => '咨询按钮点击',
        2 => '电话按钮点击',
        3 => '表单提交成功',
        4 => 'APP激活',
        5 => '表单按钮点击',
        6 => '下载按钮点击',
        7 => '购买按钮点击',
        8 => '短信咨询按钮点击',
        10 => '购买成功',
        14 => '订单提交成功',
        17 => '三句话咨询',
        18 => '留线索',
        19 => '一句话咨询',
        20 => '深度页面访问',
        25 => 'APP注册',
        26 => 'APP付费',
        27 => '客户自定义',
        28 => '次日留存',
        30 => '电话拨通',
        31 => '地图按钮点击',
        32 => 'QQ咨询按钮点击',
        33 => '抽奖按钮点击',
        34 => '投票按钮点击',
        35 => '微信复制按钮点击',
        41 => '申请',
        42 => '授信',
        45 => '商品下单成功',
        46 => '加入购物车',
        49 => '注册激活后登录',
        50 => '预约',
        51 => '有意向客户',
        52 => '深度使用',
        61 => '二次跳转',
        71 => '应用调起',
        '3,27' => '表单提交成功且客户自定义类型'
    ];

    const MEDIA_TYPE_CONFIGS = [
        '腾讯广告' => 'TENCENT_TYPE',
        '巨量广告' => 'OE_TYPE',
        '华为广告' => 'HUAWEI_TYPE',
        '快手广告' => 'KUAISHOU_TYPE',
        '爱奇艺广告' => 'IQIYI_TYPE',
        'OPPO广告' => 'OPPO_TYPE',
        'VIVO广告' => 'VIVO_TYPE',
        '优酷广告' => 'YOUKU_TYPE',
        '喜马拉雅广告' => 'XIMALAYA_TYPE'
    ];

    /**
     * 通过媒体类型返回回传行为数组
     * @param $mediaType
     * @return array
     */
    public static function getMediaType($mediaType): array
    {
        if (!array_key_exists($mediaType, self::MEDIA_TYPE_CONFIGS)) {
            return []; // 或者 return null;
        }

        return constant('self::' . self::MEDIA_TYPE_CONFIGS[$mediaType]);
    }

    /**
     * 获取媒体类型配置
     * @param string $mediaType
     * @return array
     */
    public static function getMediaTypeConfig(string $mediaType): array
    {
        $configs = [
            '腾讯广告' => ['accountId' => true, 'deepAction' => true],
            'OPPO广告' => ['accountId' => true, 'deepAction' => false],
            'VIVO广告' => ['accountId' => true, 'deepAction' => false,],
            '优酷广告' => ['accountId' => true, 'deepAction' => false,],
            '喜马拉雅广告' => ['accountId' => true, 'deepAction' => false,],
        ];

        return $configs[$mediaType] ?? ['accountId' => false, 'deepAction' => false];
    }
}
