<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class TencentAdAccountMonitor extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'ad_account_monitor';

    public function adminInfo(): BelongsTo
    {
        return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
    }

    public function adAccountInfo(): BelongsTo
    {
        return $this->BelongsTo(TencentAdAccount::class, 'account_id', 'account_id');
    }
}
