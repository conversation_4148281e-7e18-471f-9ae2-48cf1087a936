<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Redis\Connections\PhpRedisConnection;
use Illuminate\Support\Facades\Redis;

class Announcement extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;

    /**
     * 获取最新7条公告
     * @return array
     */
    public static function getAnnouncement(): array
    {
        return Announcement::query()->orderBy('id', 'desc')->limit(7)->get()->toArray();
    }

    /**
     * 读取公告接口
     * @param Request $request
     * @return false|JsonResponse
     */
    public function readAnnouncement(Request $request): bool|JsonResponse
    {
        $parameter = $request->input();
        if (isset($parameter['id'])) {
            /** @var PhpRedisConnection $redis */
            $redis = Redis::connection('announcement');
            $res = $redis->srem('NotReadAnnouncement',$parameter['id']);
            if ($res) {
                return response()->json(['code' => 0, 'data' => [true], 'message' => ''],JSON_UNESCAPED_UNICODE);
            }
            return response()->json(['code' => 1, 'data' => [false], 'message' => ''],JSON_UNESCAPED_UNICODE);
        }
        return false;
    }
}
