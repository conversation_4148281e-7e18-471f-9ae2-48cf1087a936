<?php

namespace App\Console\Commands\WwUser;

use App\Jobs\WwUser\QrCodeSaveUpJob;
use App\Models\WwUser;
use Illuminate\Console\Command;

class QrCodeSaveUp extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'WwUser:QrCodeSaveUp';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

//        $where = [
//            'corp_id' => 1610,
//        ];
        //获取需要二维码的销售
        WwUser::query()
            ->with("corpInfo")
            ->where("type", 1)
            ->where("add_method", 2)
            ->where("online_status",1)
            ->chunkById(100, function ($rows) {
            $this->saveUp($rows);
        });
        WwUser::query()
            ->with("corpInfo")
            ->where("type", 1)
            ->where("add_method", 2)
            ->where("online_status",0)
            ->chunkById(100, function ($rows) {
            $this->saveUp($rows);
        });
        return Command::SUCCESS;
    }

    function saveUp($rows){
        /** @var WwUser $wwUser */
        foreach ($rows as $wwUser) {
            //判断是否可见，只有可见的才会去生成
            if (!$wwUser->subscribe) {
                continue;
            }
            $this->alert("销售 ID：".$wwUser->id." SkipVerify：". $wwUser->skip_verify);
            QrCodeSaveUpJob::dispatch($wwUser)->onQueue('qr_code_save_up');
        }
    }
}
