<?php

namespace App\Admin\Forms\WwLink;

use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Models\AdminDomain;
use App\Models\AdminSubUser;
use App\Models\AdminUser;
use App\Models\WwLink;
use App\Services\Tools\LogService;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class BatchSetLinkUseDomainForm extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input): JsonResponse
    {
        LogService::inputLog('Tools','ww_link-链接列表-默认域名', $input, Admin::user()->id, Admin::user()->username);

        $ids = explode(',', $input['id'] ?? null);
        if (!$ids) {
            return $this->response()->alert()->error('提示')->detail('请选择需要操作的数据。');
        }
        if (!$input['domain']) {
            return $this->response()->alert()->error('配置选择域名不能为空');
        }

        $wwLinkQuery = WwLink::query()->whereIn('id', $ids);
        //获取企微投放链接的AdminUid
        $wwLinkAdminUids = $wwLinkQuery->pluck('admin_uid')->toArray();

        /** @var AdminUser $adminUser */
        $adminUser = Admin::user();
        // 判断wwLink的adminId和主账号和子账户的差集
        if (!empty(array_diff($wwLinkAdminUids, AdminSubUser::getAdminUids($adminUser->id)))) {
            return $this->response()->alert()->error('操作失败')->detail('错误代码：-88888');    // 证明选中的投放链接不属于主账号以及子账户
        }

        // 更新默使用域名
        WwLink::query()->whereIn('id',$ids)->update(['use_domain' => $input['domain']]);
        // 记录操作日志
        foreach ($ids as $id) {
            AdminActionLogJob::dispatch(
                'batch_set_ww_link_use_domain',
                $id,
                AdminActionLog::ACTION_TYPE['链接'],
                "批量设置投放链接使用域名，链接ID：「" . $id . '」，设置为：' . $input['domain'],
                getIp(),
                $adminUser->id
            );
        }
        return $this->response()->success('配置成功')->refresh();
    }

    public function form(): void
    {
        /** @var AdminUser $adminUser */
        $adminUser = Admin::user();
        $this->hidden('id')->attribute('id', 'reset-ww_link_use_domain');
        $adminDomain = AdminDomain::query()->whereIn('admin_uid', AdminSubUser::getAdminUids($adminUser->id))->pluck('domain', 'domain')->toArray();
        $this->select('domain', '域名')->options($adminDomain)->required();
    }
}
