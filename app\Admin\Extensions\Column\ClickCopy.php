<?php

	namespace App\Admin\Extensions\Column;

	use Dcat\Admin\Admin;
    use Dcat\Admin\Grid\Displayers\AbstractDisplayer;
    use Dcat\Admin\Support\Helper;

    /**
	 * Class Copyable.
	 *
	 * @see https://codepen.io/shaikmaqsood/pen/XmydxJ
	 */
	class ClickCopy extends AbstractDisplayer
	{
		public function display($limit = 100, $end = '...', $color = '#414750')
		{
			$this->addScript();

			//        dd($this->row->id);
			$this->value = Helper::htmlEntityEncode($this->value);

			$value = Helper::strLimit($this->value, $limit, $end);

			if ($value == $this->value) {
				$html = <<<HTML
<div class="limit-text" style="display: inline">
    <span class="text">
    <a href="javascript:void(0);" class="grid-column-copyable" style="color: {$color}"  data-content="{$this->value}" title="{$this->trans('copied')}" data-placement="bottom">
{$value}
</a>
</span></div>
HTML;
			} else {
				$html = <<<HTML
<div class="limit-text"  style="display: inline">
    <span class="text">
    <a href="javascript:void(0);" class="grid-column-copyable" style="color: {$color}"  data-content="{$this->value}" title="{$this->trans('copied')}" data-placement="bottom">
{$value}
</a>
</span>
    &nbsp;<a href="javascript:void(0);" class="limit-more">&nbsp;<i class="fa fa-angle-double-down"></i></a>
</div>
<div class="limit-text d-none">
    <span class="text">
    <a href="javascript:void(0);" class="grid-column-copyable" style="color: {$color}" data-content="{$this->value}" title="{$this->trans('copied')}" data-placement="bottom">
{$this->value}</a></span>
    &nbsp;<a href="javascript:void(0);" class="limit-more">&nbsp;<i class="fa fa-angle-double-up"></i></a>
</div>
HTML;
			}

			return $this->value === '' || $this->value === null ? $this->value : $html;
		}

		protected function addScript()
		{
			$script = <<<'JS'
$('.limit-more').click(function () {
    $(this).parent('.limit-text').toggleClass('d-none').siblings().toggleClass('d-none');
});
$('.grid-column-copyable').off('click').on('click', function () {

    var content = $(this).data('content');

    var $temp = $('<input>');

    $("body").append($temp);
    $temp.val(content).select();
    document.execCommand("copy");
    $temp.remove();

    $(this).tooltip('show');
});
JS;
			Admin::script($script);
		}
	}
