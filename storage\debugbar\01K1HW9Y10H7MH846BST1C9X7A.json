{"__meta": {"id": "01K1HW9Y10H7MH846BST1C9X7A", "datetime": "2025-08-01 11:55:09", "utime": **********.729167, "method": "GET", "uri": "/ztfz/posting_link?_pjax=%23pjax-container", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754020506.869107, "end": **********.729244, "duration": 2.8601369857788086, "duration_str": "2.86s", "measures": [{"label": "Booting", "start": 1754020506.869107, "relative_start": 0, "end": **********.30008, "relative_end": **********.30008, "duration": 0.****************, "duration_str": "431ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.3001, "relative_start": 0.*****************, "end": **********.729247, "relative_end": 3.0994415283203125e-06, "duration": 2.****************, "duration_str": "2.43s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.329615, "relative_start": 0.****************, "end": **********.34465, "relative_end": **********.34465, "duration": 0.015034914016723633, "duration_str": "15.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: admin::grid.displayer.switch", "start": **********.948119, "relative_start": 2.***************, "end": **********.948119, "relative_end": **********.948119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.switch", "start": **********.949802, "relative_start": 2.****************, "end": **********.949802, "relative_end": **********.949802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.switch", "start": **********.950594, "relative_start": 2.***************, "end": **********.950594, "relative_end": **********.950594, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.switch", "start": **********.951178, "relative_start": 2.08207106590271, "end": **********.951178, "relative_end": **********.951178, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.switch", "start": **********.951773, "relative_start": 2.0826659202575684, "end": **********.951773, "relative_end": **********.951773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.input", "start": **********.215872, "relative_start": 2.3467650413513184, "end": **********.215872, "relative_end": **********.215872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.21719, "relative_start": 2.348083019256592, "end": **********.21719, "relative_end": **********.21719, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.input", "start": **********.218809, "relative_start": 2.3497018814086914, "end": **********.218809, "relative_end": **********.218809, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.219499, "relative_start": 2.3503921031951904, "end": **********.219499, "relative_end": **********.219499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.input", "start": **********.220704, "relative_start": 2.3515970706939697, "end": **********.220704, "relative_end": **********.220704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.221186, "relative_start": 2.352078914642334, "end": **********.221186, "relative_end": **********.221186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.input", "start": **********.222243, "relative_start": 2.3531360626220703, "end": **********.222243, "relative_end": **********.222243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.222745, "relative_start": 2.353637933731079, "end": **********.222745, "relative_end": **********.222745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.input", "start": **********.224086, "relative_start": 2.3549790382385254, "end": **********.224086, "relative_end": **********.224086, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.224589, "relative_start": 2.3554821014404297, "end": **********.224589, "relative_end": **********.224589, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.textarea", "start": **********.609701, "relative_start": 2.740593910217285, "end": **********.609701, "relative_end": **********.609701, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.61095, "relative_start": 2.7418429851531982, "end": **********.61095, "relative_end": **********.61095, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.textarea", "start": **********.612093, "relative_start": 2.742985963821411, "end": **********.612093, "relative_end": **********.612093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.612665, "relative_start": 2.743557929992676, "end": **********.612665, "relative_end": **********.612665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.textarea", "start": **********.613617, "relative_start": 2.7445099353790283, "end": **********.613617, "relative_end": **********.613617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.614044, "relative_start": 2.744936943054199, "end": **********.614044, "relative_end": **********.614044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.textarea", "start": **********.614822, "relative_start": 2.7457149028778076, "end": **********.614822, "relative_end": **********.614822, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.615243, "relative_start": 2.746135950088501, "end": **********.615243, "relative_end": **********.615243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.textarea", "start": **********.6161, "relative_start": 2.746993064880371, "end": **********.6161, "relative_end": **********.6161, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.616473, "relative_start": 2.747365951538086, "end": **********.616473, "relative_end": **********.616473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.fixed-table", "start": **********.627878, "relative_start": 2.7587709426879883, "end": **********.627878, "relative_end": **********.627878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.table-toolbar", "start": **********.630155, "relative_start": 2.7610480785369873, "end": **********.630155, "relative_end": **********.630155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.batch-actions", "start": **********.63146, "relative_start": 2.76235294342041, "end": **********.63146, "relative_end": **********.63146, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.button", "start": **********.633429, "relative_start": 2.76432204246521, "end": **********.633429, "relative_end": **********.633429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.form", "start": **********.662043, "relative_start": 2.792936086654663, "end": **********.662043, "relative_end": **********.662043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.fields", "start": **********.664185, "relative_start": 2.7950780391693115, "end": **********.664185, "relative_end": **********.664185, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.display", "start": **********.665839, "relative_start": 2.796731948852539, "end": **********.665839, "relative_end": **********.665839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.666985, "relative_start": 2.7978780269622803, "end": **********.666985, "relative_end": **********.666985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.file", "start": **********.66847, "relative_start": 2.799362897872925, "end": **********.66847, "relative_end": **********.66847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.671642, "relative_start": 2.802535057067871, "end": **********.671642, "relative_end": **********.671642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.672444, "relative_start": 2.8033370971679688, "end": **********.672444, "relative_end": **********.672444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.hidden", "start": **********.673996, "relative_start": 2.804888963699341, "end": **********.673996, "relative_end": **********.673996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.hidden", "start": **********.674985, "relative_start": 2.805877923965454, "end": **********.674985, "relative_end": **********.674985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.column-selector", "start": **********.676669, "relative_start": 2.8075618743896484, "end": **********.676669, "relative_end": **********.676669, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.checkbox", "start": **********.677245, "relative_start": 2.808137893676758, "end": **********.677245, "relative_end": **********.677245, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.checkbox", "start": **********.679114, "relative_start": 2.810007095336914, "end": **********.679114, "relative_end": **********.679114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.container", "start": **********.682399, "relative_start": 2.8132920265197754, "end": **********.682399, "relative_end": **********.682399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.684148, "relative_start": 2.8150410652160645, "end": **********.684148, "relative_end": **********.684148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.68562, "relative_start": 2.8165130615234375, "end": **********.68562, "relative_end": **********.68562, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.687456, "relative_start": 2.8183488845825195, "end": **********.687456, "relative_end": **********.687456, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.688136, "relative_start": 2.8190290927886963, "end": **********.688136, "relative_end": **********.688136, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.689448, "relative_start": 2.820341110229492, "end": **********.689448, "relative_end": **********.689448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.690698, "relative_start": 2.8215909004211426, "end": **********.690698, "relative_end": **********.690698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.692859, "relative_start": 2.823751926422119, "end": **********.692859, "relative_end": **********.692859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.694224, "relative_start": 2.8251171112060547, "end": **********.694224, "relative_end": **********.694224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.694707, "relative_start": 2.8255999088287354, "end": **********.694707, "relative_end": **********.694707, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.695466, "relative_start": 2.8263590335845947, "end": **********.695466, "relative_end": **********.695466, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.695903, "relative_start": 2.826796054840088, "end": **********.695903, "relative_end": **********.695903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.697321, "relative_start": 2.828213930130005, "end": **********.697321, "relative_end": **********.697321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.698377, "relative_start": 2.8292698860168457, "end": **********.698377, "relative_end": **********.698377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.699262, "relative_start": 2.8301548957824707, "end": **********.699262, "relative_end": **********.699262, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.700437, "relative_start": 2.8313300609588623, "end": **********.700437, "relative_end": **********.700437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.70098, "relative_start": 2.8318729400634766, "end": **********.70098, "relative_end": **********.70098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.701509, "relative_start": 2.832401990890503, "end": **********.701509, "relative_end": **********.701509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.703993, "relative_start": 2.834886074066162, "end": **********.703993, "relative_end": **********.703993, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.705707, "relative_start": 2.8366000652313232, "end": **********.705707, "relative_end": **********.705707, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.70673, "relative_start": 2.837622880935669, "end": **********.70673, "relative_end": **********.70673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.707235, "relative_start": 2.838128089904785, "end": **********.707235, "relative_end": **********.707235, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.707807, "relative_start": 2.83870005607605, "end": **********.707807, "relative_end": **********.707807, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.table-pagination", "start": **********.711397, "relative_start": 2.842289924621582, "end": **********.711397, "relative_end": **********.711397, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.pagination", "start": **********.714482, "relative_start": 2.8453750610351562, "end": **********.714482, "relative_end": **********.714482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.dropdown", "start": **********.717766, "relative_start": 2.848659038543701, "end": **********.717766, "relative_end": **********.717766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::layouts.content", "start": **********.720091, "relative_start": 2.8509840965270996, "end": **********.720091, "relative_end": **********.720091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.breadcrumb", "start": **********.721537, "relative_start": 2.8524301052093506, "end": **********.721537, "relative_end": **********.721537, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.alerts", "start": **********.723122, "relative_start": 2.8540148735046387, "end": **********.723122, "relative_end": **********.723122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.exception", "start": **********.724191, "relative_start": 2.85508394241333, "end": **********.724191, "relative_end": **********.724191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.toastr", "start": **********.725171, "relative_start": 2.8560640811920166, "end": **********.725171, "relative_end": **********.725171, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 33396496, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.20", "PHP Version": "8.0.2", "Environment": "local", "Debug Mode": "Enabled", "URL": "sen.test", "Timezone": "PRC", "Locale": "zh_CN"}}, "views": {"count": 72, "nb_templates": 72, "templates": [{"name": "5x admin::grid.displayer.switch", "param_count": null, "params": [], "start": **********.948014, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/switch.blade.phpadmin::grid.displayer.switch", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Fswitch.blade.php&line=1", "ajax": false, "filename": "switch.blade.php", "line": "?"}, "render_count": 5, "name_original": "admin::grid.displayer.switch"}, {"name": "5x admin::grid.displayer.editinline.input", "param_count": null, "params": [], "start": **********.215771, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/editinline/input.blade.phpadmin::grid.displayer.editinline.input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Feditinline%2Finput.blade.php&line=1", "ajax": false, "filename": "input.blade.php", "line": "?"}, "render_count": 5, "name_original": "admin::grid.displayer.editinline.input"}, {"name": "10x admin::grid.displayer.editinline.template", "param_count": null, "params": [], "start": **********.217104, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/editinline/template.blade.phpadmin::grid.displayer.editinline.template", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Feditinline%2Ftemplate.blade.php&line=1", "ajax": false, "filename": "template.blade.php", "line": "?"}, "render_count": 10, "name_original": "admin::grid.displayer.editinline.template"}, {"name": "5x admin::grid.displayer.editinline.textarea", "param_count": null, "params": [], "start": **********.609616, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/editinline/textarea.blade.phpadmin::grid.displayer.editinline.textarea", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Feditinline%2Ftextarea.blade.php&line=1", "ajax": false, "filename": "textarea.blade.php", "line": "?"}, "render_count": 5, "name_original": "admin::grid.displayer.editinline.textarea"}, {"name": "1x admin::grid.fixed-table", "param_count": null, "params": [], "start": **********.627758, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/fixed-table.blade.phpadmin::grid.fixed-table", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Ffixed-table.blade.php&line=1", "ajax": false, "filename": "fixed-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.fixed-table"}, {"name": "1x admin::grid.table-toolbar", "param_count": null, "params": [], "start": **********.630048, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/table-toolbar.blade.phpadmin::grid.table-toolbar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Ftable-toolbar.blade.php&line=1", "ajax": false, "filename": "table-toolbar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.table-toolbar"}, {"name": "1x admin::grid.batch-actions", "param_count": null, "params": [], "start": **********.631362, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/batch-actions.blade.phpadmin::grid.batch-actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fbatch-actions.blade.php&line=1", "ajax": false, "filename": "batch-actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.batch-actions"}, {"name": "1x admin::filter.button", "param_count": null, "params": [], "start": **********.633338, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/button.blade.phpadmin::filter.button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::filter.button"}, {"name": "1x admin::widgets.form", "param_count": null, "params": [], "start": **********.661912, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/form.blade.phpadmin::widgets.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::widgets.form"}, {"name": "1x admin::form.fields", "param_count": null, "params": [], "start": **********.664097, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/fields.blade.phpadmin::form.fields", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ffields.blade.php&line=1", "ajax": false, "filename": "fields.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.fields"}, {"name": "1x admin::form.display", "param_count": null, "params": [], "start": **********.665757, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/display.blade.phpadmin::form.display", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fdisplay.blade.php&line=1", "ajax": false, "filename": "display.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.display"}, {"name": "2x admin::form.help-block", "param_count": null, "params": [], "start": **********.666881, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/help-block.blade.phpadmin::form.help-block", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fhelp-block.blade.php&line=1", "ajax": false, "filename": "help-block.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::form.help-block"}, {"name": "1x admin::form.file", "param_count": null, "params": [], "start": **********.668305, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/file.blade.phpadmin::form.file", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ffile.blade.php&line=1", "ajax": false, "filename": "file.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.file"}, {"name": "1x admin::form.error", "param_count": null, "params": [], "start": **********.671555, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/error.blade.phpadmin::form.error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.error"}, {"name": "2x admin::form.hidden", "param_count": null, "params": [], "start": **********.673774, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/hidden.blade.phpadmin::form.hidden", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fhidden.blade.php&line=1", "ajax": false, "filename": "hidden.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::form.hidden"}, {"name": "1x admin::grid.column-selector", "param_count": null, "params": [], "start": **********.676586, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/column-selector.blade.phpadmin::grid.column-selector", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fcolumn-selector.blade.php&line=1", "ajax": false, "filename": "column-selector.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.column-selector"}, {"name": "2x admin::widgets.checkbox", "param_count": null, "params": [], "start": **********.677169, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/checkbox.blade.phpadmin::widgets.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::widgets.checkbox"}, {"name": "1x admin::filter.container", "param_count": null, "params": [], "start": **********.68232, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/container.blade.phpadmin::filter.container", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fcontainer.blade.php&line=1", "ajax": false, "filename": "container.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::filter.container"}, {"name": "9x admin::filter.where", "param_count": null, "params": [], "start": **********.68406, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/where.blade.phpadmin::filter.where", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fwhere.blade.php&line=1", "ajax": false, "filename": "where.blade.php", "line": "?"}, "render_count": 9, "name_original": "admin::filter.where"}, {"name": "5x admin::filter.text", "param_count": null, "params": [], "start": **********.685487, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/text.blade.phpadmin::filter.text", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Ftext.blade.php&line=1", "ajax": false, "filename": "text.blade.php", "line": "?"}, "render_count": 5, "name_original": "admin::filter.text"}, {"name": "4x admin::filter.select", "param_count": null, "params": [], "start": **********.69058, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/select.blade.phpadmin::filter.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 4, "name_original": "admin::filter.select"}, {"name": "4x admin::scripts.select", "param_count": null, "params": [], "start": **********.69276, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/scripts/select.blade.phpadmin::scripts.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fscripts%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 4, "name_original": "admin::scripts.select"}, {"name": "1x admin::grid.table-pagination", "param_count": null, "params": [], "start": **********.711254, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/table-pagination.blade.phpadmin::grid.table-pagination", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Ftable-pagination.blade.php&line=1", "ajax": false, "filename": "table-pagination.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.table-pagination"}, {"name": "1x admin::grid.pagination", "param_count": null, "params": [], "start": **********.714346, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/pagination.blade.phpadmin::grid.pagination", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fpagination.blade.php&line=1", "ajax": false, "filename": "pagination.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.pagination"}, {"name": "1x admin::widgets.dropdown", "param_count": null, "params": [], "start": **********.717632, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/dropdown.blade.phpadmin::widgets.dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fdropdown.blade.php&line=1", "ajax": false, "filename": "dropdown.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::widgets.dropdown"}, {"name": "1x admin::layouts.content", "param_count": null, "params": [], "start": **********.719974, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/layouts/content.blade.phpadmin::layouts.content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Flayouts%2Fcontent.blade.php&line=1", "ajax": false, "filename": "content.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::layouts.content"}, {"name": "1x admin::partials.breadcrumb", "param_count": null, "params": [], "start": **********.721424, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/breadcrumb.blade.phpadmin::partials.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.breadcrumb"}, {"name": "1x admin::partials.alerts", "param_count": null, "params": [], "start": **********.722998, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/alerts.blade.phpadmin::partials.alerts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Falerts.blade.php&line=1", "ajax": false, "filename": "alerts.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.alerts"}, {"name": "1x admin::partials.exception", "param_count": null, "params": [], "start": **********.724099, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/exception.blade.phpadmin::partials.exception", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fexception.blade.php&line=1", "ajax": false, "filename": "exception.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.exception"}, {"name": "1x admin::partials.toastr", "param_count": null, "params": [], "start": **********.725092, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/toastr.blade.phpadmin::partials.toastr", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Ftoastr.blade.php&line=1", "ajax": false, "filename": "toastr.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.toastr"}]}, "queries": {"count": 34, "nb_statements": 33, "nb_visible_statements": 34, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 1.8735999999999995, "accumulated_duration_str": "1.87s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Admin.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", "line": 145}, {"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 36}], "start": **********.392969, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "wind_rich", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `wr_admin_users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Admin.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", "line": 145}, {"index": 18, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.4064772, "duration": 0.25185, "duration_str": "252ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wind_rich", "explain": null, "start_percent": 0, "width_percent": 13.442}, {"sql": "insert into `wr_operation_logs` (`admin_uid`, `user_name`, `path`, `method`, `ip`, `input`, `updated_at`, `created_at`) values (1, 'ZtFzSuperAdminPro', 'ztfz/posting_link', 'GET', '127.0.0.1', '{\\\"_pjax\\\":\\\"#pjax-container\\\"}', '2025-08-01 11:55:07', '2025-08-01 11:55:07')", "type": "query", "params": [], "bindings": [1, "ZtFzSuperAdminPro", "ztfz/posting_link", "GET", "127.0.0.1", "{\"_pjax\":\"#pjax-container\"}", "2025-08-01 11:55:07", "2025-08-01 11:55:07"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 51}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.6667008, "duration": 0.0601, "duration_str": "60.1ms", "memory": 0, "memory_str": null, "filename": "OpRecord.php:51", "source": {"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FHttp%2FMiddleware%2FOpRecord.php&line=51", "ajax": false, "filename": "OpRecord.php", "line": "51"}, "connection": "wind_rich", "explain": null, "start_percent": 13.442, "width_percent": 3.208}, {"sql": "select * from `wr_announcements` where `wr_announcements`.`deleted_at` is null order by `id` desc limit 7", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Announcement.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\Announcement.php", "line": 24}, {"index": 15, "namespace": null, "name": "app/Admin/bootstrap.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\bootstrap.php", "line": 62}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Admin.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", "line": 164}, {"index": 17, "namespace": null, "name": "app/Admin/bootstrap.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\bootstrap.php", "line": 80}, {"index": 19, "namespace": "middleware", "name": "admin.bootstrap", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\Bootstrap.php", "line": 14}], "start": **********.73679, "duration": 0.05003, "duration_str": "50.03ms", "memory": 0, "memory_str": null, "filename": "Announcement.php:24", "source": {"index": 14, "namespace": null, "name": "app/Models/Announcement.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\Announcement.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAnnouncement.php&line=24", "ajax": false, "filename": "Announcement.php", "line": "24"}, "connection": "wind_rich", "explain": null, "start_percent": 16.65, "width_percent": 2.67}, {"sql": "select `wr_admin_roles`.*, `wr_admin_role_users`.`user_id` as `pivot_user_id`, `wr_admin_role_users`.`role_id` as `pivot_role_id`, `wr_admin_role_users`.`created_at` as `pivot_created_at`, `wr_admin_role_users`.`updated_at` as `pivot_updated_at` from `wr_admin_roles` inner join `wr_admin_role_users` on `wr_admin_roles`.`id` = `wr_admin_role_users`.`role_id` where `wr_admin_role_users`.`user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/HasPermissions.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\HasPermissions.php", "line": 88}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/HasPermissions.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\HasPermissions.php", "line": 76}, {"index": 21, "namespace": "middleware", "name": "admin.permission", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\Permission.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 23, "namespace": "middleware", "name": "admin.bootstrap", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\Bootstrap.php", "line": 19}], "start": **********.905513, "duration": 0.04995, "duration_str": "49.95ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:88", "source": {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/HasPermissions.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\HasPermissions.php", "line": 88}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FHasPermissions.php&line=88", "ajax": false, "filename": "HasPermissions.php", "line": "88"}, "connection": "wind_rich", "explain": null, "start_percent": 19.32, "width_percent": 2.666}, {"sql": "select `id`, `parent_id` from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 52}, {"index": 17, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 261}, {"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 305}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.00926, "duration": 0.048670000000000005, "duration_str": "48.67ms", "memory": 0, "memory_str": null, "filename": "AdminSubUser.php:52", "source": {"index": 16, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUser.php&line=52", "ajax": false, "filename": "AdminSubUser.php", "line": "52"}, "connection": "wind_rich", "explain": null, "start_percent": 21.986, "width_percent": 2.598}, {"sql": "select `id` from `wr_admin_users` where `parent_id` = 1 and `status` = 1 and `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 57}, {"index": 14, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 261}, {"index": 17, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 305}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.06142, "duration": 0.04935, "duration_str": "49.35ms", "memory": 0, "memory_str": null, "filename": "AdminSubUser.php:57", "source": {"index": 13, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUser.php&line=57", "ajax": false, "filename": "AdminSubUser.php", "line": "57"}, "connection": "wind_rich", "explain": null, "start_percent": 24.584, "width_percent": 2.634}, {"sql": "select `username`, `id` from `wr_admin_users` where `id` in (1) and `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 262}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 305}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 1008}], "start": **********.1134808, "duration": 0.0487, "duration_str": "48.7ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:262", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=262", "ajax": false, "filename": "WwLinkController.php", "line": "262"}, "connection": "wind_rich", "explain": null, "start_percent": 27.218, "width_percent": 2.599}, {"sql": "select `id`, `parent_id` from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 276}, {"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 305}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.17207, "duration": 0.048909999999999995, "duration_str": "48.91ms", "memory": 0, "memory_str": null, "filename": "AdminSubUser.php:35", "source": {"index": 16, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUser.php&line=35", "ajax": false, "filename": "AdminSubUser.php", "line": "35"}, "connection": "wind_rich", "explain": null, "start_percent": 29.817, "width_percent": 2.61}, {"sql": "select `id` from `wr_admin_users` where `parent_id` = 1 and `status` = 1 and `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 39}, {"index": 14, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 276}, {"index": 17, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 305}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.22391, "duration": 0.05188, "duration_str": "51.88ms", "memory": 0, "memory_str": null, "filename": "AdminSubUser.php:39", "source": {"index": 13, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUser.php&line=39", "ajax": false, "filename": "AdminSubUser.php", "line": "39"}, "connection": "wind_rich", "explain": null, "start_percent": 32.427, "width_percent": 2.769}, {"sql": "select `ww_user_group_ids` from `wr_admin_sub_users` where `admin_uid` = 1 and `wr_admin_sub_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 278}, {"index": 19, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 305}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 1008}], "start": **********.2787142, "duration": 0.04962, "duration_str": "49.62ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:278", "source": {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 278}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=278", "ajax": false, "filename": "WwLinkController.php", "line": "278"}, "connection": "wind_rich", "explain": null, "start_percent": 35.196, "width_percent": 2.648}, {"sql": "select `id`, `parent_id` from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 297}, {"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 305}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.331501, "duration": 0.05052, "duration_str": "50.52ms", "memory": 0, "memory_str": null, "filename": "AdminSubUser.php:35", "source": {"index": 16, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUser.php&line=35", "ajax": false, "filename": "AdminSubUser.php", "line": "35"}, "connection": "wind_rich", "explain": null, "start_percent": 37.845, "width_percent": 2.696}, {"sql": "select `id` from `wr_admin_users` where `parent_id` = 1 and `status` = 1 and `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 39}, {"index": 14, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 297}, {"index": 17, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 305}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.384577, "duration": 0.048799999999999996, "duration_str": "48.8ms", "memory": 0, "memory_str": null, "filename": "AdminSubUser.php:39", "source": {"index": 13, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUser.php&line=39", "ajax": false, "filename": "AdminSubUser.php", "line": "39"}, "connection": "wind_rich", "explain": null, "start_percent": 40.541, "width_percent": 2.605}, {"sql": "select `title`, `id` from `wr_ww_users_groups` where `admin_uid` in (1) and `type` = 1 and `admin_uid` in (1) and `wr_ww_users_groups`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 299}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 305}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 1008}], "start": **********.437912, "duration": 0.05626, "duration_str": "56.26ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:299", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 299}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=299", "ajax": false, "filename": "WwLinkController.php", "line": "299"}, "connection": "wind_rich", "explain": null, "start_percent": 43.146, "width_percent": 3.003}, {"sql": "select count(*) as aggregate from `wr_ww_link` where `wr_ww_link`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.499933, "duration": 0.053829999999999996, "duration_str": "53.83ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 46.149, "width_percent": 2.873}, {"sql": "select * from `wr_ww_link` where `wr_ww_link`.`deleted_at` is null order by `id` desc limit 20 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.562369, "duration": 0.048600000000000004, "duration_str": "48.6ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 49.022, "width_percent": 2.594}, {"sql": "select * from `wr_ad_account` where `wr_ad_account`.`account_id` in ('********', '********', '********', '********', '********') and `wr_ad_account`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["********", "********", "********", "********", "********"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.618344, "duration": 0.048979999999999996, "duration_str": "48.98ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 51.616, "width_percent": 2.614}, {"sql": "select * from `wr_ww_tpl` where `wr_ww_tpl`.`id` in (1, 3) and `wr_ww_tpl`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.673487, "duration": 0.04954, "duration_str": "49.54ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 54.23, "width_percent": 2.644}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` in (1, 4) and `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.727351, "duration": 0.04843, "duration_str": "48.43ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 56.874, "width_percent": 2.585}, {"sql": "select * from `wr_ww_users_groups` where `wr_ww_users_groups`.`id` in (1, 6) and `wr_ww_users_groups`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.7793732, "duration": 0.04872, "duration_str": "48.72ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 59.459, "width_percent": 2.6}, {"sql": "select * from `wr_shield_policies` where `wr_shield_policies`.`id` in (1, 2) and `wr_shield_policies`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.8324568, "duration": 0.05205, "duration_str": "52.05ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 62.059, "width_percent": 2.778}, {"sql": "select * from `wr_ww_tpl` where `wr_ww_tpl`.`id` in (2, 4) and `wr_ww_tpl`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 29, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 31, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 32, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.8883069, "duration": 0.04869, "duration_str": "48.69ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 28, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 64.837, "width_percent": 2.599}, {"sql": "select * from `wr_admin_domains` where `status` = 1 and `admin_uid` = 1 and `wr_admin_domains`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 131}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}], "start": **********.953644, "duration": 0.04925, "duration_str": "49.25ms", "memory": 0, "memory_str": null, "filename": "AdminDomain.php:56", "source": {"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminDomain.php&line=56", "ajax": false, "filename": "AdminDomain.php", "line": "56"}, "connection": "wind_rich", "explain": null, "start_percent": 67.436, "width_percent": 2.629}, {"sql": "select * from `wr_admin_domains` where `status` = 1 and `admin_uid` = 1 and `wr_admin_domains`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 131}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}], "start": **********.006203, "duration": 0.04931, "duration_str": "49.31ms", "memory": 0, "memory_str": null, "filename": "AdminDomain.php:56", "source": {"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminDomain.php&line=56", "ajax": false, "filename": "AdminDomain.php", "line": "56"}, "connection": "wind_rich", "explain": null, "start_percent": 70.065, "width_percent": 2.632}, {"sql": "select * from `wr_admin_domains` where `status` = 1 and `admin_uid` = 1 and `wr_admin_domains`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 131}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}], "start": **********.058474, "duration": 0.04985, "duration_str": "49.85ms", "memory": 0, "memory_str": null, "filename": "AdminDomain.php:56", "source": {"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminDomain.php&line=56", "ajax": false, "filename": "AdminDomain.php", "line": "56"}, "connection": "wind_rich", "explain": null, "start_percent": 72.696, "width_percent": 2.661}, {"sql": "select * from `wr_admin_domains` where `status` = 1 and `admin_uid` = 1 and `wr_admin_domains`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 131}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}], "start": **********.1109421, "duration": 0.049460000000000004, "duration_str": "49.46ms", "memory": 0, "memory_str": null, "filename": "AdminDomain.php:56", "source": {"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminDomain.php&line=56", "ajax": false, "filename": "AdminDomain.php", "line": "56"}, "connection": "wind_rich", "explain": null, "start_percent": 75.357, "width_percent": 2.64}, {"sql": "select * from `wr_admin_domains` where `status` = 1 and `admin_uid` = 1 and `wr_admin_domains`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 131}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}], "start": **********.1631472, "duration": 0.04872, "duration_str": "48.72ms", "memory": 0, "memory_str": null, "filename": "AdminDomain.php:56", "source": {"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminDomain.php&line=56", "ajax": false, "filename": "AdminDomain.php", "line": "56"}, "connection": "wind_rich", "explain": null, "start_percent": 77.997, "width_percent": 2.6}, {"sql": "select * from `wr_ww_users_groups` where `wr_ww_users_groups`.`id` = 7 and `wr_ww_users_groups`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 158}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 28, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 29, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": **********.226407, "duration": 0.04908, "duration_str": "49.08ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:158", "source": {"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 158}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=158", "ajax": false, "filename": "WwLinkController.php", "line": "158"}, "connection": "wind_rich", "explain": null, "start_percent": 80.597, "width_percent": 2.62}, {"sql": "select `merge_name`, `id` from `wr_area` where 0 = 1 and `wr_area`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 166}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": **********.2789502, "duration": 0.04908, "duration_str": "49.08ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:166", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 166}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=166", "ajax": false, "filename": "WwLinkController.php", "line": "166"}, "connection": "wind_rich", "explain": null, "start_percent": 83.217, "width_percent": 2.62}, {"sql": "select * from `wr_ww_users_groups` where `wr_ww_users_groups`.`id` = 2 and `wr_ww_users_groups`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 158}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 28, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 29, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": **********.331457, "duration": 0.04818, "duration_str": "48.18ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:158", "source": {"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 158}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=158", "ajax": false, "filename": "WwLinkController.php", "line": "158"}, "connection": "wind_rich", "explain": null, "start_percent": 85.836, "width_percent": 2.572}, {"sql": "select `merge_name`, `id` from `wr_area` where `id` in ('1', '192', '2648', '3642') and `wr_area`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "192", "2648", "3642"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 166}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": **********.381747, "duration": 0.05964, "duration_str": "59.64ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:166", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 166}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=166", "ajax": false, "filename": "WwLinkController.php", "line": "166"}, "connection": "wind_rich", "explain": null, "start_percent": 88.408, "width_percent": 3.183}, {"sql": "select `merge_name`, `id` from `wr_area` where `id` in ('1', '192', '2648', '3642') and `wr_area`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "192", "2648", "3642"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 166}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": **********.443738, "duration": 0.05086, "duration_str": "50.86ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:166", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 166}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=166", "ajax": false, "filename": "WwLinkController.php", "line": "166"}, "connection": "wind_rich", "explain": null, "start_percent": 91.591, "width_percent": 2.715}, {"sql": "select `merge_name`, `id` from `wr_area` where `id` in ('1', '192', '2648', '3642') and `wr_area`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "192", "2648", "3642"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 166}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": **********.497051, "duration": 0.04947, "duration_str": "49.47ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:166", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 166}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=166", "ajax": false, "filename": "WwLinkController.php", "line": "166"}, "connection": "wind_rich", "explain": null, "start_percent": 94.306, "width_percent": 2.64}, {"sql": "select `merge_name`, `id` from `wr_area` where `id` in ('1', '192', '2648', '3642') and `wr_area`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "192", "2648", "3642"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 166}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": **********.548781, "duration": 0.05722, "duration_str": "57.22ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:166", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 166}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=166", "ajax": false, "filename": "WwLinkController.php", "line": "166"}, "connection": "wind_rich", "explain": null, "start_percent": 96.946, "width_percent": 3.054}]}, "models": {"data": {"App\\Models\\AdminUser": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminUser.php&line=1", "ajax": false, "filename": "AdminUser.php", "line": "?"}}, "App\\Models\\WwLink": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwLink.php&line=1", "ajax": false, "filename": "WwLink.php", "line": "?"}}, "App\\Models\\TencentAdAccount": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FTencentAdAccount.php&line=1", "ajax": false, "filename": "TencentAdAccount.php", "line": "?"}}, "App\\Models\\AdminDomain": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminDomain.php&line=1", "ajax": false, "filename": "AdminDomain.php", "line": "?"}}, "App\\Models\\WwTpl": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwTpl.php&line=1", "ajax": false, "filename": "WwTpl.php", "line": "?"}}, "App\\Models\\WwUsersGroup": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwUsersGroup.php&line=1", "ajax": false, "filename": "WwUsersGroup.php", "line": "?"}}, "Dcat\\Admin\\Models\\Role": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ShieldPolicy": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FShieldPolicy.php&line=1", "ajax": false, "filename": "ShieldPolicy.php", "line": "?"}}, "Dcat\\Admin\\Models\\Administrator": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FAdministrator.php&line=1", "ajax": false, "filename": "Administrator.php", "line": "?"}}, "App\\Models\\Announcement": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAnnouncement.php&line=1", "ajax": false, "filename": "Announcement.php", "line": "?"}}}, "count": 35, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://sen.test/ztfz/posting_link?_pjax=%23pjax-container", "action_name": "dcat.admin.posting_link.index", "controller_action": "App\\Admin\\Controllers\\WwLinkController@index", "uri": "GET ztfz/posting_link", "controller": "App\\Admin\\Controllers\\WwLinkController@index<a href=\"phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=63\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Admin\\Controllers", "prefix": "/ztfz", "file": "<a href=\"phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=63\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Admin/Controllers/WwLinkController.php:63-79</a>", "middleware": "admin.app:admin, web, admin", "duration": "2.87s", "peak_memory": "8MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1283501146 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_pjax</span>\" => \"<span class=sf-dump-str title=\"15 characters\">#pjax-container</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1283501146\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1169943961 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1169943961\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-892268808 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjdrV2MrSnl4RWdJYnVnWlVCb1ROUWc9PSIsInZhbHVlIjoidHZZZFFOYThyQWZMNmFTWGVjRmxzRDVzdnQ4WVRKbXRNRldUQUtMbDZxOEk1dWN6R0dOQTdKN0ZBMEgrYlRVTTl0U2Y1aHBNU2Z0cEtQMS9HUkJobC9LN0o4OXJ3UVh3d0lhS1BQbzlGUXljTzVGUktkdjJ4NHpsODRCTFQ3UTkiLCJtYWMiOiJjZmViNzFhOTI5MjEzNDFlYjBlZjc1ZTBiNTA3MWI2NDc5MzVmZDI5YTZjMzdkZjZmNjlkNDNkMDBmODkyM2ViIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImpQeVhPSjl5eUtmYTZ0SDBIdzVwWHc9PSIsInZhbHVlIjoiM05CdnQ2c3FvRGRYNy9jank3YVVmUFFBeU5PNjR4M1RzNkFqR2RTa0pIUDdOMWVQTnY0YjVOQ1FkY1ZtVEhqN244VU1RQ1grUGNlT1QxdkgzaGFMRjdTdHpzWVA0d1dnMlhqMC93NUJiQ2gxMDR6cnBrKysydXA1Ym95UTg3eTYiLCJtYWMiOiJhYTRjNTMzYTZiYjU3ZThmNGUzZTgxNDY4YWVmMzRmMmFlOGVhNDdkODNmOWU1ODA0NjViN2ZjMTM1N2QyOGM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">zh-CN,zh;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">text/html, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">0esJyfQEGYXbNzItiQn8t75dzSvXZmiKqhcwIHBs</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-pjax</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-pjax-container</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">#pjax-container</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">sen.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-892268808\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1988809512 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0esJyfQEGYXbNzItiQn8t75dzSvXZmiKqhcwIHBs</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Sc1mCMq4OSwvqqSk8mbaIttmOBzjSrhvaXfSjoYm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1988809512\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-291422022 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Aug 2025 03:55:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-pjax-url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">/ztfz/posting_link?_pjax=%23pjax-container</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-291422022\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-394887842 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>prev</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://sen.test/ztfz/posting_link</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">admin.prev.url</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0esJyfQEGYXbNzItiQn8t75dzSvXZmiKqhcwIHBs</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://sen.test/ztfz</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-394887842\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://sen.test/ztfz/posting_link?_pjax=%23pjax-container", "action_name": "dcat.admin.posting_link.index", "controller_action": "App\\Admin\\Controllers\\WwLinkController@index"}, "badge": null}}