<?php

	namespace App\Admin\Forms\WwUser;

	use App\Jobs\WwUser\CheckLicenseTimeJob;
    use App\Models\AdminSubUserAuthCorp;
    use App\Models\AdminUser;
    use App\Models\WwCorpInfo;
    use App\Services\Corp\WwProvApiService;
    use App\Services\Tools\LogService;
    use Dcat\Admin\Admin;
    use Dcat\Admin\Widgets\Form;

    class checkLicenseTimeForm extends Form
	{
		/**
		 * Handle the form request.
		 *
		 * @param array $input
		 *
		 * @return mixed
		 */
		public function handle(array $input)
		{
            LogService::inputLog('Tools','销售客服-刷新许可证', $input, Admin::user()->id, Admin::user()->username);
			$corpId = $input['corp_id'];
			/** @var WwCorpInfo $corpInfo */
			$corpInfo = WwCorpInfo::query()->find($corpId);
//            WwProvApiService::checkLicenseTime($corpInfo);
            CheckLicenseTimeJob::dispatch($corpInfo);
            return $this->response()->alert()->success('提示')->detail('刷新完成。')->refresh();
		}

		/**
		 * Build a form here.
		 */
		public function form()
		{
            $list = AdminSubUserAuthCorp::getCorpAuthListBuyAdminUid(Admin::user()->id);
			$this->select('corp_id','选择企业微信')->options($list)->required();
		}

		/**
		 * The data of the form.
		 *
		 * @return array
		 */
		public function default()
		{
			return [

			];
		}
	}
