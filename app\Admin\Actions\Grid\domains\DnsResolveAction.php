<?php

namespace App\Admin\Actions\Grid\domains;

use App\Models\AdminDomain;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;

class DnsResolveAction extends RowAction
{

    /**
     * @return string
     */
    public function title(): string
    {
        return '<i class="fa fa-search"></i> 获取解析值';
    }

    /**
     * Handle the action request.
     *
     *
     * @return Response
     */
    public function handle(): Response
    {
        $key = $this->getKey();

        $domain = optional(AdminDomain::find($key))->domain;
        try {
            // 清理域名，移除协议和路径
            $cleanDomain = parse_url($domain, PHP_URL_HOST) ?: $domain;
            $cleanDomain = trim($cleanDomain);

            // 获取IP地址
            dns_get_record($cleanDomain, DNS_A); // 触发无缓存查询（清除可能存在的缓存）
            $ip = gethostbyname($cleanDomain);

            // 检查是否解析成功（gethostbyname失败时返回原域名）
            if ($ip === $cleanDomain && !filter_var($ip, FILTER_VALIDATE_IP)) {
                return $this->response()
                    ->error('域名解析失败：' . $cleanDomain);
            }

            // 检查IP属于哪个IP组
            $ipGroup = AdminDomain::getIpGroup($ip);
            $ipGroupText = $ipGroup ? "<br>所属IP组: <strong style='color: #1890ff;'>{$ipGroup}</strong>" : "<br>所属IP组: <span style='color: #999;'>未知</span>";

            return $this->response()
                ->alert()
                ->success('获取成功')
                ->detail("域名: {$cleanDomain}<br>IP地址: <strong style='color: #52c41a;'>{$ip}</strong>{$ipGroupText}<br>");

        } catch (\Exception $e) {
            return $this->response()
                ->error('解析过程中发生错误：' . $e->getMessage());
        }
    }



    /**
     * @return false
     */
    public function confirm(): bool
    {
        // 不需要确认对话框
        return false;
    }
}
