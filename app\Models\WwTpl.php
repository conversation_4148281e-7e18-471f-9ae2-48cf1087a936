<?php

    namespace App\Models;

    use Dcat\Admin\Admin;
    use Dcat\Admin\Traits\HasDateTimeFormatter;
    use Illuminate\Database\Eloquent\Model;
    use Illuminate\Database\Eloquent\Relations\BelongsTo;
    use Illuminate\Database\Eloquent\SoftDeletes;

    /**
     * @property mixed $admin_uid
     * @property mixed $view_file
     * @property mixed $type
     * @property mixed $name
     * @property mixed $product
     */
    class WwTpl extends Model
    {
        use HasDateTimeFormatter;
        use SoftDeletes;

        protected $table = 'ww_tpl';

        public function adminInfo(): BelongsTo
        {
            return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
        }

        public static function getAdTplList(): array
        {
            if (Admin::user()->isAdministrator()) {//如果是管理员，那么显示全部模板
                return WwTpl::query()->pluck("name", "id")->toArray();
            } else {
                /** @var AdminUser $adminUser */
                $adminUser = Admin::user();
                if (Admin::user()->isRole('customer')) { //如果是主账户，显示自身以及子账户全部模板
                    return WwTpl::query()->where("type", 1)->whereIn("admin_uid", AdminSubUser::getALLAdminUids($adminUser->id))->pluck("name", "id")->toArray();
                } else {
                    //如果是子账户，显示自身模板，以及父级授权展示的模板
                    $tplIdData = AdminSubUser::query()->where('admin_uid', $adminUser->id)->value('tpl_ids');
                    $tplIds = json_decode($tplIdData, true);
                    if (!$tplIds) $tplIds = [];
                    if (in_array(-1, $tplIds)) {
                        //判断授权是否是-1，代表全部，如果是-1，那么展示主账户全部+自己的全部
                        return WwTpl::query()->where("type", 1)->whereIn("admin_uid", [$adminUser->id, $adminUser->parent_id])->pluck("name", "id")->toArray();
                    } else {
                        //如果授权不是全部，那么展示授权的ID，以及自己的全部
                        return WwTpl::query()->where("type", 1)->where(function ($query) use ($tplIds, $adminUser) {
                            $query->whereIn('id', $tplIds)
                                ->orWhere("admin_uid", $adminUser->id);
                        })->pluck("name", "id")->toArray();
                    }
                }
            }
        }
    }
