<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\ExportOpenId;
use App\Jobs\ExportOpenId\ProcessExportOpenIdJob;
use App\Models\AdminUser;
use App\Models\ExportOpenId as ExportOpenIdModel;
use App\Services\Tools\UtilsService;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Show;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Request;

class ExportOpenIdController extends AdminController
{

    public function index(Content $content)
    {
        return $content
            ->header('导出OpenId')
            ->body($this->grid());
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new ExportOpenId(['adminInfo']), function (Grid $grid) {
            /** 对话框新增 */
            $grid->disableCreateButton();
            $grid->tools(function (Grid\Tools $tools) {
                $className = collect(Request::segments())->last();
                $tools->append(UtilsService::dialogForm('添加',Request::url().'/create',"create-{$className}"));
            });
            $grid->model()->orderBy('id', 'desc');
            // $grid->disableActions();
            $grid->disableBatchActions();
            // $grid->disableCreateButton();
            $grid->disableViewButton();
            $grid->column('id')->sortable();
            $grid->column('adminInfo.username', '用户')->copyable();
            //$grid->column('action');
            $grid->column('title', '任务名称')->copyable();
            $grid->column('data_type', '数据类型')->display(function ($dataType) {
                switch ($dataType) {
                    case 0:
                        return '当前数据';
                        break;
                    case 1:
                        return '历史数据';
                        break;
                    default:
                        return '';
                }
            });
            $grid->column('status', '状态')->using(ExportOpenIdModel::STATUS)->label([
                0 => 'default',
                1 => 'warning',
                2 => 'success',
                3 => 'danger',
            ]);
            $grid->column("file_name", '文件名')->copyable();
            $grid->column("file_path", '文件路径')->downloadable();
            $grid->column('时间范围')->display(function () {
                $dataRange = $this->start_date . ' 至 ' . $this->end_date;
                return $dataRange;
            });
//            $grid->column("start_date", '开始时间');
//            $grid->column("end_date", '结束时间');
            // $grid->column("job_id", '队列ID')->copyable();
            $grid->column('created_at')->sortable();
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                $adminUser = AdminUser::query()->where('parent_id', 0)->pluck('username', 'id');
                $filter->equal('admin_uid', "用户")->select($adminUser)->width(2);
                $filter->like("title", '任务名称')->width(2);
                $filter->equal("data_type", '数据类型')->select([0 => '当前数据', 1 => '历史数据'])->width(2);
                $filter->like("file_name", '文件名')->width(2);
                $filter->equal("status", '状态')->select(ExportOpenIdModel::STATUS)->width(2);
                $filter->between("created_at")->datetime()->width(4);
                $filter->between("updated_at")->datetime()->width(4);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new ExportOpenId(), function (Show $show) {
            if (!AdminUser::isAdmin($show->model())) {
                $show->field('N')->value("无数据");
            } else {
                $show->field('id');
            }
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new ExportOpenId(), function (Form $form) {
            $form->hidden('id');
            $adminUsers = AdminUser::query()->pluck('username', 'id');
            $form->select('admin_uid', '用户')
                ->options($adminUsers)
                ->loads(['corp_id','ww_user_group_id'], ['api/getCorpList', 'api/getGroupList'])
                ->help('此处选择的是主账号')->required();
            $form->text('title', '任务名称')->required();
            $form->text('file_name', '文件名称')->required();

            $form->select('data_type', '数据类型')
                ->default(1)
                ->options([0 => '当前数据', 1 => '历史数据'])
                ->required()
                ->when([0, 1], function ($form) {
                })->help('超过20万条数据，建议按5-10为一批次分开导出。');

            $form->select('corp_id', '选择企微')
                ->help('选择企微主体，导出该主体下的所有数据');
            $form->select("ww_user_group_id",'销售分组')->help("销售分组");
            $form->text('ww_user_wind_label')->help("请输入准确的标签，完全匹配判断");;

            $form->datetimeRange('start_date','end_date', '进粉时间')
                ->default([
                    'start_date' => now()->startOfDay(),
                    'end_date' => now()->endOfDay(),
                ])->required();





            // $form->radio('status','状态')->options(ExportTaskModel::STATUS)->required();
            $form->disableViewButton();
            $form->disableViewCheck();
            $form->disableEditingCheck();
            $form->disableDeleteButton();
        })->saving(function (Form $form) {

            // 如果没有填写title 和 file_name，则自动生成

            $name = 'export_openid-' . date('YmdHis');
            if ($form->title === null) {
                $form->title = $name;
            }

            if ($form->file_name === null) {
                $form->file_name = $name;
            }

            if ($form->start_date && !$form->end_date) {
                return $form->response()->alert()->error('提示')->detail('请选择结束时间');
            }
            if (!$form->start_date && $form->end_date) {
                return $form->response()->alert()->error('提示')->detail('请选择开始时间');
            }
            $dataCount = self::getDataCount($form->input());

            if($dataCount > 200000){
                return $form->response()->alert()->error('提示')->detail('数据超过20万条，建议分开导出。');
            }
            $checkTask = ExportOpenIdModel::query()->where('status', 1)->exists();
            if($checkTask) {
                return $form->response()->alert()->error('提示')->detail('有正在执行的导出任务。');
            }
        })->saved(function (Form $form, $result) {
            if ($form->isCreating()) {
                $id = $result;
            } else {
                $id = $form->getKey();
            }
            if (!$id) {
                return $form->response()->alert()->error('提示')->detail('导出任务创建失败，请稍后重试-1');
            }
            $task = ExportOpenIdModel::query()->where('id', $id)->first();
            if (!$task) {
                return $form->response()->alert()->error('提示')->detail('导出任务创建失败，请稍后重试-2');
            }

            if($task['status'] == 0){
                Log::info('导出openid任务，进入队列执行');
                ProcessExportOpenIdJob::dispatch($task);
            }

        });
    }


    /**
     * 获取数据总数
     * @param $form
     * @return int
     */
    public static function getDataCount($form)
    {
        $date = [$form['start_date'], $form['end_date']];

        switch ($form['data_type']) {
            case 0:
                $table = 'ww_user_add_record';
                break;
            case 1:
                $table = 'ww_user_add_record_delete';
                break;
            default:
                $table = 'ww_user_add_record_delete';
        }

        $adminUid = $form['admin_uid'];
        $adminUser = AdminUser::query()->where('id', $adminUid)->first();

        if ($adminUser['parent_id'] == 0) {
            $adminUids = AdminUser::query()
                ->where('id', $adminUid)
                ->orWhere('parent_id', $adminUid)
                ->pluck('id')
                ->toArray();
        } else {
            $adminUids = array($adminUser['id']);
        }

        // 使用 JOIN 替代双重查询
        return DB::table($table . ' as records')
            ->join('wechat_users as users', 'records.user_id', '=', 'users.id')
            ->whereIn('records.admin_uid', $adminUids)
            ->when($date[0] && $date[1], function ($query) use ($date) {
                return $query->whereBetween('records.external_contact_created_at', $date);
            })
            ->when($form['corp_id'], function ($query) use ($form) {
                return $query->where('records.corp_id', $form['corp_id']);
            })
            ->when($form['ww_user_group_id'], function ($query) use ($form) {
                return $query->where('records.ww_user_group_id', $form['ww_user_group_id']);
            })
            ->when($form['ww_user_wind_label'] !== null, function ($query) use ($form) {
                return $query->where('records.ww_user_wind_label', (string)$form['ww_user_wind_label']);
            })
            ->count();
    }

}
