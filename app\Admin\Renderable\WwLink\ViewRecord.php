<?php

	namespace App\Admin\Renderable\WwLink;

    use App\Admin\Repositories\LinkViewRecord;
	use Dcat\Admin\Grid;
    use Dcat\Admin\Grid\LazyRenderable;

    /**
	 * @property mixed|null $link_id
	 */
	class ViewRecord extends LazyRenderable
	{
		public function grid(): Grid
		{
			return Grid::make(new LinkViewRecord(), function (Grid $grid) {//pressRecords
				$grid->setName($this->link_id);
				$grid->showRefreshButton();
				$grid->disableRowSelector();
				$grid->model()->where("ww_link_id", $this->link_id)->orderByDesc('id');
				$grid->column('id', 'ID')->sortable();
				$grid->column('ip');
				$grid->column('area', '地域');
				$grid->column('isp', '服务商');
				$grid->column('created_at', '访问时间');
				$grid->column('view_count', '访问次数');
				$grid->column('ww_user_name', '销售姓名')->ClickCopy(10, "...");;
//				$grid->column('click_id', '访客ID')->display(function ($value) {
//					return empty($value) ? "无" : $value;
//				});
                $grid->column('click_id', '访客ID')->display(function ($value) {
                    if (!empty($value)) {
                        $checkIsJson = json_decode($value, true);
                        if (isset($checkIsJson['tid'])) {
                            $len = strlen($checkIsJson['tid']);
                            if ($len > 20) {
                                $val = substr($checkIsJson['tid'], 0, 20) .'...';
                                $fullValue = $checkIsJson['tid'];
                                return "<span title='{$fullValue}'>{$val}</span>";
                            }
                            return $checkIsJson['tid'];
                        }
                        if (isset($checkIsJson['impress_id'])) {
                            $len = strlen($checkIsJson['impress_id']);
                            if ($len > 20) {
                                $val = substr($checkIsJson['impress_id'], 0, 20) .'...';
                                $fullValue = $checkIsJson['impress_id'];
                                return "<span title='{$fullValue}'>{$val}</span>";
                            }
                            return $checkIsJson['impress_id'];
                        }
                    }
                    if (strlen($value) > 20) {
                        $val = substr($value, 0, 20) .'...';
                        $fullValue = $value;
                        return "<span title='{$fullValue}'>{$val}</span>";
                    }
                    return $value ?? '无';
                });
				$grid->column('add_ww_user_id', '加粉')->display(function ($value) {
					if ($value) {
						return '加粉';
					}
					return '未添加';
				});
				$grid->column('add_time', '加粉时间');

				$grid->paginate(10);
				$grid->disableActions();

				$grid->filter(function (Grid\Filter $filter) {
					$filter->panel();
					$filter->expand();
					$filter->where('加粉', function ($query) {
						if ($this->input == 1) {
							$query->whereNotNull('add_time');
						} else if ($this->input == 2) {
							$query->whereNull('add_time');
						}
					})->width(2)->select([
						0 => '全部',
						1 => '加粉',
						2 => '未加粉',
					]);
                    $filter->between('created_at', '访问时间')->width(4)->datetime();
                    $filter->between('add_time', '加粉时间')->width(4)->datetime();
//                    $filter->like('ip')->width(2);

                });


				// 如果数量过少 则在尾部添加空白 以修复 datetime 选择器显示不全的问题
				$grid->footer(function ($collection){
					if ($collection->count() <= 3) {
						return <<<HTML
						<div style="margin-top:100px;"></div>
						HTML;
					} else {
						return '';
					}
				});
			});
		}
	}
