{"__meta": {"id": "01K1J49JW441DK4HKF188ZEED4", "datetime": "2025-08-01 14:14:46", "utime": **********.91778, "method": "GET", "uri": "/ztfz/posting_link", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754028883.734934, "end": **********.9178, "duration": 3.182865858078003, "duration_str": "3.18s", "measures": [{"label": "Booting", "start": 1754028883.734934, "relative_start": 0, "end": **********.18544, "relative_end": **********.18544, "duration": 0.*****************, "duration_str": "451ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.185458, "relative_start": 0.*****************, "end": **********.917802, "relative_end": 2.1457672119140625e-06, "duration": 2.***************, "duration_str": "2.73s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.225384, "relative_start": 0.****************, "end": **********.231854, "relative_end": **********.231854, "duration": 0.0064699649810791016, "duration_str": "6.47ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: admin::grid.displayer.switch", "start": **********.888605, "relative_start": 2.****************, "end": **********.888605, "relative_end": **********.888605, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.switch", "start": **********.890135, "relative_start": 2.***************, "end": **********.890135, "relative_end": **********.890135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.switch", "start": **********.891733, "relative_start": 2.***************, "end": **********.891733, "relative_end": **********.891733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.switch", "start": **********.893489, "relative_start": 2.158554792404175, "end": **********.893489, "relative_end": **********.893489, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.switch", "start": **********.894812, "relative_start": 2.1598780155181885, "end": **********.894812, "relative_end": **********.894812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.input", "start": **********.163749, "relative_start": 2.4288148880004883, "end": **********.163749, "relative_end": **********.163749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.165798, "relative_start": 2.430863857269287, "end": **********.165798, "relative_end": **********.165798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.input", "start": **********.168298, "relative_start": 2.433363914489746, "end": **********.168298, "relative_end": **********.168298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.169225, "relative_start": 2.434290885925293, "end": **********.169225, "relative_end": **********.169225, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.input", "start": **********.171008, "relative_start": 2.4360740184783936, "end": **********.171008, "relative_end": **********.171008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.171895, "relative_start": 2.4369609355926514, "end": **********.171895, "relative_end": **********.171895, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.input", "start": **********.17359, "relative_start": 2.4386558532714844, "end": **********.17359, "relative_end": **********.17359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.175183, "relative_start": 2.440248966217041, "end": **********.175183, "relative_end": **********.175183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.input", "start": **********.177534, "relative_start": 2.4426000118255615, "end": **********.177534, "relative_end": **********.177534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.178692, "relative_start": 2.443758010864258, "end": **********.178692, "relative_end": **********.178692, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.textarea", "start": **********.552533, "relative_start": 2.817598819732666, "end": **********.552533, "relative_end": **********.552533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.553313, "relative_start": 2.8183789253234863, "end": **********.553313, "relative_end": **********.553313, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.textarea", "start": **********.554283, "relative_start": 2.8193488121032715, "end": **********.554283, "relative_end": **********.554283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.554727, "relative_start": 2.8197929859161377, "end": **********.554727, "relative_end": **********.554727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.textarea", "start": **********.55555, "relative_start": 2.8206160068511963, "end": **********.55555, "relative_end": **********.55555, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.555982, "relative_start": 2.8210480213165283, "end": **********.555982, "relative_end": **********.555982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.textarea", "start": **********.55676, "relative_start": 2.8218259811401367, "end": **********.55676, "relative_end": **********.55676, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.557421, "relative_start": 2.8224868774414062, "end": **********.557421, "relative_end": **********.557421, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.textarea", "start": **********.559282, "relative_start": 2.824347972869873, "end": **********.559282, "relative_end": **********.559282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.559864, "relative_start": 2.82492995262146, "end": **********.559864, "relative_end": **********.559864, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.fixed-table", "start": **********.570212, "relative_start": 2.835277795791626, "end": **********.570212, "relative_end": **********.570212, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.table-toolbar", "start": **********.571485, "relative_start": 2.8365509510040283, "end": **********.571485, "relative_end": **********.571485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.batch-actions", "start": **********.57248, "relative_start": 2.837545871734619, "end": **********.57248, "relative_end": **********.57248, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.button", "start": **********.574022, "relative_start": 2.839087963104248, "end": **********.574022, "relative_end": **********.574022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.form", "start": **********.596026, "relative_start": 2.8610918521881104, "end": **********.596026, "relative_end": **********.596026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.fields", "start": **********.597255, "relative_start": 2.862320899963379, "end": **********.597255, "relative_end": **********.597255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.display", "start": **********.598422, "relative_start": 2.863487958908081, "end": **********.598422, "relative_end": **********.598422, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.599379, "relative_start": 2.8644449710845947, "end": **********.599379, "relative_end": **********.599379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.file", "start": **********.600543, "relative_start": 2.8656089305877686, "end": **********.600543, "relative_end": **********.600543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.601578, "relative_start": 2.8666439056396484, "end": **********.601578, "relative_end": **********.601578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.602206, "relative_start": 2.867271900177002, "end": **********.602206, "relative_end": **********.602206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.hidden", "start": **********.603415, "relative_start": 2.868480920791626, "end": **********.603415, "relative_end": **********.603415, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.hidden", "start": **********.604177, "relative_start": 2.8692429065704346, "end": **********.604177, "relative_end": **********.604177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.column-selector", "start": **********.605705, "relative_start": 2.8707709312438965, "end": **********.605705, "relative_end": **********.605705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.checkbox", "start": **********.606228, "relative_start": 2.8712940216064453, "end": **********.606228, "relative_end": **********.606228, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.checkbox", "start": **********.608502, "relative_start": 2.873567819595337, "end": **********.608502, "relative_end": **********.608502, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.container", "start": **********.613479, "relative_start": 2.878544807434082, "end": **********.613479, "relative_end": **********.613479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.615651, "relative_start": 2.8807168006896973, "end": **********.615651, "relative_end": **********.615651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.617043, "relative_start": 2.8821089267730713, "end": **********.617043, "relative_end": **********.617043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.618347, "relative_start": 2.8834128379821777, "end": **********.618347, "relative_end": **********.618347, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.618921, "relative_start": 2.8839869499206543, "end": **********.618921, "relative_end": **********.618921, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.619855, "relative_start": 2.884920835494995, "end": **********.619855, "relative_end": **********.619855, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.620908, "relative_start": 2.8859739303588867, "end": **********.620908, "relative_end": **********.620908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.622451, "relative_start": 2.887516975402832, "end": **********.622451, "relative_end": **********.622451, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.623822, "relative_start": 2.888887882232666, "end": **********.623822, "relative_end": **********.623822, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.625509, "relative_start": 2.8905749320983887, "end": **********.625509, "relative_end": **********.625509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.62687, "relative_start": 2.8919358253479004, "end": **********.62687, "relative_end": **********.62687, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.627388, "relative_start": 2.892453908920288, "end": **********.627388, "relative_end": **********.627388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.628196, "relative_start": 2.8932619094848633, "end": **********.628196, "relative_end": **********.628196, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.628663, "relative_start": 2.8937289714813232, "end": **********.628663, "relative_end": **********.628663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.629162, "relative_start": 2.894227981567383, "end": **********.629162, "relative_end": **********.629162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.629916, "relative_start": 2.894981861114502, "end": **********.629916, "relative_end": **********.629916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.630348, "relative_start": 2.895413875579834, "end": **********.630348, "relative_end": **********.630348, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.630826, "relative_start": 2.8958919048309326, "end": **********.630826, "relative_end": **********.630826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.631574, "relative_start": 2.896639823913574, "end": **********.631574, "relative_end": **********.631574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.631996, "relative_start": 2.897061824798584, "end": **********.631996, "relative_end": **********.631996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.632668, "relative_start": 2.8977339267730713, "end": **********.632668, "relative_end": **********.632668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.63311, "relative_start": 2.8981759548187256, "end": **********.63311, "relative_end": **********.63311, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.63359, "relative_start": 2.898655891418457, "end": **********.63359, "relative_end": **********.63359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.table-pagination", "start": **********.636228, "relative_start": 2.901293992996216, "end": **********.636228, "relative_end": **********.636228, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.pagination", "start": **********.638066, "relative_start": 2.9031319618225098, "end": **********.638066, "relative_end": **********.638066, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.dropdown", "start": **********.639978, "relative_start": 2.905043840408325, "end": **********.639978, "relative_end": **********.639978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::layouts.content", "start": **********.64291, "relative_start": 2.907975912094116, "end": **********.64291, "relative_end": **********.64291, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.breadcrumb", "start": **********.644144, "relative_start": 2.909209966659546, "end": **********.644144, "relative_end": **********.644144, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.alerts", "start": **********.645468, "relative_start": 2.910533905029297, "end": **********.645468, "relative_end": **********.645468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.exception", "start": **********.646858, "relative_start": 2.911923885345459, "end": **********.646858, "relative_end": **********.646858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.toastr", "start": **********.647915, "relative_start": 2.912980794906616, "end": **********.647915, "relative_end": **********.647915, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::layouts.page", "start": **********.649481, "relative_start": 2.9145469665527344, "end": **********.649481, "relative_end": **********.649481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::layouts.container", "start": **********.65101, "relative_start": 2.9160759449005127, "end": **********.65101, "relative_end": **********.65101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.sidebar", "start": **********.652115, "relative_start": 2.9171810150146484, "end": **********.652115, "relative_end": **********.652115, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.866165, "relative_start": 3.1312308311462402, "end": **********.866165, "relative_end": **********.866165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.867901, "relative_start": 3.132966995239258, "end": **********.867901, "relative_end": **********.867901, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.868583, "relative_start": 3.1336488723754883, "end": **********.868583, "relative_end": **********.868583, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.869094, "relative_start": 3.134159803390503, "end": **********.869094, "relative_end": **********.869094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.869585, "relative_start": 3.134650945663452, "end": **********.869585, "relative_end": **********.869585, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.870064, "relative_start": 3.135129928588867, "end": **********.870064, "relative_end": **********.870064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.870525, "relative_start": 3.1355907917022705, "end": **********.870525, "relative_end": **********.870525, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.870963, "relative_start": 3.136029005050659, "end": **********.870963, "relative_end": **********.870963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.871576, "relative_start": 3.1366419792175293, "end": **********.871576, "relative_end": **********.871576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.87226, "relative_start": 3.1373260021209717, "end": **********.87226, "relative_end": **********.87226, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.872834, "relative_start": 3.137899875640869, "end": **********.872834, "relative_end": **********.872834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.873372, "relative_start": 3.1384379863739014, "end": **********.873372, "relative_end": **********.873372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.874144, "relative_start": 3.1392099857330322, "end": **********.874144, "relative_end": **********.874144, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.875967, "relative_start": 3.1410329341888428, "end": **********.875967, "relative_end": **********.875967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.877074, "relative_start": 3.1421399116516113, "end": **********.877074, "relative_end": **********.877074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.877724, "relative_start": 3.142789840698242, "end": **********.877724, "relative_end": **********.877724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.878375, "relative_start": 3.1434409618377686, "end": **********.878375, "relative_end": **********.878375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.879105, "relative_start": 3.1441709995269775, "end": **********.879105, "relative_end": **********.879105, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.879828, "relative_start": 3.1448938846588135, "end": **********.879828, "relative_end": **********.879828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.880388, "relative_start": 3.145453929901123, "end": **********.880388, "relative_end": **********.880388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.880911, "relative_start": 3.145977020263672, "end": **********.880911, "relative_end": **********.880911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.881429, "relative_start": 3.1464948654174805, "end": **********.881429, "relative_end": **********.881429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.881974, "relative_start": 3.1470398902893066, "end": **********.881974, "relative_end": **********.881974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.882475, "relative_start": 3.147540807723999, "end": **********.882475, "relative_end": **********.882475, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.882987, "relative_start": 3.148052930831909, "end": **********.882987, "relative_end": **********.882987, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.883502, "relative_start": 3.1485679149627686, "end": **********.883502, "relative_end": **********.883502, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.884035, "relative_start": 3.1491010189056396, "end": **********.884035, "relative_end": **********.884035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.884538, "relative_start": 3.149603843688965, "end": **********.884538, "relative_end": **********.884538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.885051, "relative_start": 3.1501169204711914, "end": **********.885051, "relative_end": **********.885051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.88558, "relative_start": 3.1506459712982178, "end": **********.88558, "relative_end": **********.88558, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.886091, "relative_start": 3.1511569023132324, "end": **********.886091, "relative_end": **********.886091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.886765, "relative_start": 3.1518309116363525, "end": **********.886765, "relative_end": **********.886765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.887383, "relative_start": 3.152448892593384, "end": **********.887383, "relative_end": **********.887383, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.888072, "relative_start": 3.1531379222869873, "end": **********.888072, "relative_end": **********.888072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.888643, "relative_start": 3.1537089347839355, "end": **********.888643, "relative_end": **********.888643, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.889223, "relative_start": 3.1542890071868896, "end": **********.889223, "relative_end": **********.889223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.889848, "relative_start": 3.154913902282715, "end": **********.889848, "relative_end": **********.889848, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.890395, "relative_start": 3.155460834503174, "end": **********.890395, "relative_end": **********.890395, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.891851, "relative_start": 3.156916856765747, "end": **********.891851, "relative_end": **********.891851, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.892906, "relative_start": 3.1579718589782715, "end": **********.892906, "relative_end": **********.892906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.893724, "relative_start": 3.158789873123169, "end": **********.893724, "relative_end": **********.893724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.894524, "relative_start": 3.159590005874634, "end": **********.894524, "relative_end": **********.894524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.89529, "relative_start": 3.160355806350708, "end": **********.89529, "relative_end": **********.89529, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.896038, "relative_start": 3.1611039638519287, "end": **********.896038, "relative_end": **********.896038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.896775, "relative_start": 3.1618409156799316, "end": **********.896775, "relative_end": **********.896775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.897504, "relative_start": 3.162569999694824, "end": **********.897504, "relative_end": **********.897504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.898293, "relative_start": 3.1633589267730713, "end": **********.898293, "relative_end": **********.898293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.898933, "relative_start": 3.16399884223938, "end": **********.898933, "relative_end": **********.898933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.899646, "relative_start": 3.1647119522094727, "end": **********.899646, "relative_end": **********.899646, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.900342, "relative_start": 3.16540789604187, "end": **********.900342, "relative_end": **********.900342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.901052, "relative_start": 3.1661179065704346, "end": **********.901052, "relative_end": **********.901052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.90178, "relative_start": 3.1668457984924316, "end": **********.90178, "relative_end": **********.90178, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.902483, "relative_start": 3.167548894882202, "end": **********.902483, "relative_end": **********.902483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.903299, "relative_start": 3.168365001678467, "end": **********.903299, "relative_end": **********.903299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.903869, "relative_start": 3.1689348220825195, "end": **********.903869, "relative_end": **********.903869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.904394, "relative_start": 3.169459819793701, "end": **********.904394, "relative_end": **********.904394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.904935, "relative_start": 3.1700007915496826, "end": **********.904935, "relative_end": **********.904935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.905445, "relative_start": 3.17051100730896, "end": **********.905445, "relative_end": **********.905445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.905954, "relative_start": 3.1710197925567627, "end": **********.905954, "relative_end": **********.905954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.906452, "relative_start": 3.171517848968506, "end": **********.906452, "relative_end": **********.906452, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.906981, "relative_start": 3.1720468997955322, "end": **********.906981, "relative_end": **********.906981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.90891, "relative_start": 3.173975944519043, "end": **********.90891, "relative_end": **********.90891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.909711, "relative_start": 3.174776792526245, "end": **********.909711, "relative_end": **********.909711, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.91034, "relative_start": 3.175405979156494, "end": **********.91034, "relative_end": **********.91034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.910846, "relative_start": 3.1759119033813477, "end": **********.910846, "relative_end": **********.910846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.navbar", "start": **********.911911, "relative_start": 3.1769769191741943, "end": **********.911911, "relative_end": **********.911911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: announcements.index", "start": **********.913119, "relative_start": 3.178184986114502, "end": **********.913119, "relative_end": **********.913119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.navbar-user-panel", "start": **********.914267, "relative_start": 3.179332971572876, "end": **********.914267, "relative_end": **********.914267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 35085552, "peak_usage_str": "33MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.20", "PHP Version": "8.0.2", "Environment": "local", "Debug Mode": "Enabled", "URL": "sen.test", "Timezone": "PRC", "Locale": "zh_CN"}}, "views": {"count": 143, "nb_templates": 143, "templates": [{"name": "5x admin::grid.displayer.switch", "param_count": null, "params": [], "start": **********.888493, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/switch.blade.phpadmin::grid.displayer.switch", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Fswitch.blade.php&line=1", "ajax": false, "filename": "switch.blade.php", "line": "?"}, "render_count": 5, "name_original": "admin::grid.displayer.switch"}, {"name": "5x admin::grid.displayer.editinline.input", "param_count": null, "params": [], "start": **********.163602, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/editinline/input.blade.phpadmin::grid.displayer.editinline.input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Feditinline%2Finput.blade.php&line=1", "ajax": false, "filename": "input.blade.php", "line": "?"}, "render_count": 5, "name_original": "admin::grid.displayer.editinline.input"}, {"name": "10x admin::grid.displayer.editinline.template", "param_count": null, "params": [], "start": **********.16564, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/editinline/template.blade.phpadmin::grid.displayer.editinline.template", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Feditinline%2Ftemplate.blade.php&line=1", "ajax": false, "filename": "template.blade.php", "line": "?"}, "render_count": 10, "name_original": "admin::grid.displayer.editinline.template"}, {"name": "5x admin::grid.displayer.editinline.textarea", "param_count": null, "params": [], "start": **********.552454, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/editinline/textarea.blade.phpadmin::grid.displayer.editinline.textarea", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Feditinline%2Ftextarea.blade.php&line=1", "ajax": false, "filename": "textarea.blade.php", "line": "?"}, "render_count": 5, "name_original": "admin::grid.displayer.editinline.textarea"}, {"name": "1x admin::grid.fixed-table", "param_count": null, "params": [], "start": **********.57013, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/fixed-table.blade.phpadmin::grid.fixed-table", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Ffixed-table.blade.php&line=1", "ajax": false, "filename": "fixed-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.fixed-table"}, {"name": "1x admin::grid.table-toolbar", "param_count": null, "params": [], "start": **********.57141, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/table-toolbar.blade.phpadmin::grid.table-toolbar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Ftable-toolbar.blade.php&line=1", "ajax": false, "filename": "table-toolbar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.table-toolbar"}, {"name": "1x admin::grid.batch-actions", "param_count": null, "params": [], "start": **********.572405, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/batch-actions.blade.phpadmin::grid.batch-actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fbatch-actions.blade.php&line=1", "ajax": false, "filename": "batch-actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.batch-actions"}, {"name": "1x admin::filter.button", "param_count": null, "params": [], "start": **********.573785, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/button.blade.phpadmin::filter.button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::filter.button"}, {"name": "1x admin::widgets.form", "param_count": null, "params": [], "start": **********.595938, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/form.blade.phpadmin::widgets.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::widgets.form"}, {"name": "1x admin::form.fields", "param_count": null, "params": [], "start": **********.597165, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/fields.blade.phpadmin::form.fields", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ffields.blade.php&line=1", "ajax": false, "filename": "fields.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.fields"}, {"name": "1x admin::form.display", "param_count": null, "params": [], "start": **********.598343, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/display.blade.phpadmin::form.display", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fdisplay.blade.php&line=1", "ajax": false, "filename": "display.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.display"}, {"name": "2x admin::form.help-block", "param_count": null, "params": [], "start": **********.599304, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/help-block.blade.phpadmin::form.help-block", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fhelp-block.blade.php&line=1", "ajax": false, "filename": "help-block.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::form.help-block"}, {"name": "1x admin::form.file", "param_count": null, "params": [], "start": **********.600469, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/file.blade.phpadmin::form.file", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ffile.blade.php&line=1", "ajax": false, "filename": "file.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.file"}, {"name": "1x admin::form.error", "param_count": null, "params": [], "start": **********.601504, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/error.blade.phpadmin::form.error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.error"}, {"name": "2x admin::form.hidden", "param_count": null, "params": [], "start": **********.603336, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/hidden.blade.phpadmin::form.hidden", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fhidden.blade.php&line=1", "ajax": false, "filename": "hidden.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::form.hidden"}, {"name": "1x admin::grid.column-selector", "param_count": null, "params": [], "start": **********.605625, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/column-selector.blade.phpadmin::grid.column-selector", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fcolumn-selector.blade.php&line=1", "ajax": false, "filename": "column-selector.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.column-selector"}, {"name": "2x admin::widgets.checkbox", "param_count": null, "params": [], "start": **********.606136, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/checkbox.blade.phpadmin::widgets.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::widgets.checkbox"}, {"name": "1x admin::filter.container", "param_count": null, "params": [], "start": **********.613327, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/container.blade.phpadmin::filter.container", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fcontainer.blade.php&line=1", "ajax": false, "filename": "container.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::filter.container"}, {"name": "9x admin::filter.where", "param_count": null, "params": [], "start": **********.615532, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/where.blade.phpadmin::filter.where", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fwhere.blade.php&line=1", "ajax": false, "filename": "where.blade.php", "line": "?"}, "render_count": 9, "name_original": "admin::filter.where"}, {"name": "5x admin::filter.text", "param_count": null, "params": [], "start": **********.616931, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/text.blade.phpadmin::filter.text", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Ftext.blade.php&line=1", "ajax": false, "filename": "text.blade.php", "line": "?"}, "render_count": 5, "name_original": "admin::filter.text"}, {"name": "4x admin::filter.select", "param_count": null, "params": [], "start": **********.620791, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/select.blade.phpadmin::filter.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 4, "name_original": "admin::filter.select"}, {"name": "4x admin::scripts.select", "param_count": null, "params": [], "start": **********.622345, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/scripts/select.blade.phpadmin::scripts.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fscripts%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 4, "name_original": "admin::scripts.select"}, {"name": "1x admin::grid.table-pagination", "param_count": null, "params": [], "start": **********.636145, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/table-pagination.blade.phpadmin::grid.table-pagination", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Ftable-pagination.blade.php&line=1", "ajax": false, "filename": "table-pagination.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.table-pagination"}, {"name": "1x admin::grid.pagination", "param_count": null, "params": [], "start": **********.63799, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/pagination.blade.phpadmin::grid.pagination", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fpagination.blade.php&line=1", "ajax": false, "filename": "pagination.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.pagination"}, {"name": "1x admin::widgets.dropdown", "param_count": null, "params": [], "start": **********.639904, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/dropdown.blade.phpadmin::widgets.dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fdropdown.blade.php&line=1", "ajax": false, "filename": "dropdown.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::widgets.dropdown"}, {"name": "1x admin::layouts.content", "param_count": null, "params": [], "start": **********.642826, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/layouts/content.blade.phpadmin::layouts.content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Flayouts%2Fcontent.blade.php&line=1", "ajax": false, "filename": "content.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::layouts.content"}, {"name": "1x admin::partials.breadcrumb", "param_count": null, "params": [], "start": **********.644066, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/breadcrumb.blade.phpadmin::partials.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.breadcrumb"}, {"name": "1x admin::partials.alerts", "param_count": null, "params": [], "start": **********.645381, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/alerts.blade.phpadmin::partials.alerts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Falerts.blade.php&line=1", "ajax": false, "filename": "alerts.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.alerts"}, {"name": "1x admin::partials.exception", "param_count": null, "params": [], "start": **********.646774, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/exception.blade.phpadmin::partials.exception", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fexception.blade.php&line=1", "ajax": false, "filename": "exception.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.exception"}, {"name": "1x admin::partials.toastr", "param_count": null, "params": [], "start": **********.647836, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/toastr.blade.phpadmin::partials.toastr", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Ftoastr.blade.php&line=1", "ajax": false, "filename": "toastr.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.toastr"}, {"name": "1x admin::layouts.page", "param_count": null, "params": [], "start": **********.649399, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/layouts/page.blade.phpadmin::layouts.page", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Flayouts%2Fpage.blade.php&line=1", "ajax": false, "filename": "page.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::layouts.page"}, {"name": "1x admin::layouts.container", "param_count": null, "params": [], "start": **********.650933, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\resources\\views/vendor/admin/layouts/container.blade.phpadmin::layouts.container", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fresources%2Fviews%2Fvendor%2Fadmin%2Flayouts%2Fcontainer.blade.php&line=1", "ajax": false, "filename": "container.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::layouts.container"}, {"name": "1x admin::partials.sidebar", "param_count": null, "params": [], "start": **********.652041, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/sidebar.blade.phpadmin::partials.sidebar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.sidebar"}, {"name": "65x admin::partials.menu", "param_count": null, "params": [], "start": **********.866078, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/menu.blade.phpadmin::partials.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 65, "name_original": "admin::partials.menu"}, {"name": "1x admin::partials.navbar", "param_count": null, "params": [], "start": **********.911824, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/navbar.blade.phpadmin::partials.navbar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fnavbar.blade.php&line=1", "ajax": false, "filename": "navbar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.navbar"}, {"name": "1x announcements.index", "param_count": null, "params": [], "start": **********.913041, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\resources\\views/announcements/index.blade.phpannouncements.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fresources%2Fviews%2Fannouncements%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "announcements.index"}, {"name": "1x admin::partials.navbar-user-panel", "param_count": null, "params": [], "start": **********.914191, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/navbar-user-panel.blade.phpadmin::partials.navbar-user-panel", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fnavbar-user-panel.blade.php&line=1", "ajax": false, "filename": "navbar-user-panel.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.navbar-user-panel"}]}, "queries": {"count": 37, "nb_statements": 36, "nb_visible_statements": 37, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 1.9597300000000002, "accumulated_duration_str": "1.96s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Admin.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", "line": 145}, {"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 36}], "start": **********.284002, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "wind_rich", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `wr_admin_users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Admin.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", "line": 145}, {"index": 18, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.2971501, "duration": 0.24541, "duration_str": "245ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wind_rich", "explain": null, "start_percent": 0, "width_percent": 12.523}, {"sql": "insert into `wr_operation_logs` (`admin_uid`, `user_name`, `path`, `method`, `ip`, `input`, `updated_at`, `created_at`) values (1, 'ZtFzSuperAdminPro', 'ztfz/posting_link', 'GET', '127.0.0.1', '[]', '2025-08-01 14:14:44', '2025-08-01 14:14:44')", "type": "query", "params": [], "bindings": [1, "ZtFzSuperAdminPro", "ztfz/posting_link", "GET", "127.0.0.1", "[]", "2025-08-01 14:14:44", "2025-08-01 14:14:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 51}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.550317, "duration": 0.0525, "duration_str": "52.5ms", "memory": 0, "memory_str": null, "filename": "OpRecord.php:51", "source": {"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FHttp%2FMiddleware%2FOpRecord.php&line=51", "ajax": false, "filename": "OpRecord.php", "line": "51"}, "connection": "wind_rich", "explain": null, "start_percent": 12.523, "width_percent": 2.679}, {"sql": "select * from `wr_announcements` where `wr_announcements`.`deleted_at` is null order by `id` desc limit 7", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Announcement.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\Announcement.php", "line": 24}, {"index": 15, "namespace": null, "name": "app/Admin/bootstrap.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\bootstrap.php", "line": 62}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Admin.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", "line": 164}, {"index": 17, "namespace": null, "name": "app/Admin/bootstrap.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\bootstrap.php", "line": 80}, {"index": 19, "namespace": "middleware", "name": "admin.bootstrap", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\Bootstrap.php", "line": 14}], "start": **********.615238, "duration": 0.04743, "duration_str": "47.43ms", "memory": 0, "memory_str": null, "filename": "Announcement.php:24", "source": {"index": 14, "namespace": null, "name": "app/Models/Announcement.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\Announcement.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAnnouncement.php&line=24", "ajax": false, "filename": "Announcement.php", "line": "24"}, "connection": "wind_rich", "explain": null, "start_percent": 15.202, "width_percent": 2.42}, {"sql": "select `wr_admin_roles`.*, `wr_admin_role_users`.`user_id` as `pivot_user_id`, `wr_admin_role_users`.`role_id` as `pivot_role_id`, `wr_admin_role_users`.`created_at` as `pivot_created_at`, `wr_admin_role_users`.`updated_at` as `pivot_updated_at` from `wr_admin_roles` inner join `wr_admin_role_users` on `wr_admin_roles`.`id` = `wr_admin_role_users`.`role_id` where `wr_admin_role_users`.`user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/HasPermissions.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\HasPermissions.php", "line": 88}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/HasPermissions.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\HasPermissions.php", "line": 76}, {"index": 21, "namespace": "middleware", "name": "admin.permission", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\Permission.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 23, "namespace": "middleware", "name": "admin.bootstrap", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\Bootstrap.php", "line": 19}], "start": **********.79845, "duration": 0.04998, "duration_str": "49.98ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:88", "source": {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/HasPermissions.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\HasPermissions.php", "line": 88}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FHasPermissions.php&line=88", "ajax": false, "filename": "HasPermissions.php", "line": "88"}, "connection": "wind_rich", "explain": null, "start_percent": 17.622, "width_percent": 2.55}, {"sql": "select `id`, `parent_id` from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 52}, {"index": 17, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 283}, {"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 327}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.9337602, "duration": 0.048420000000000005, "duration_str": "48.42ms", "memory": 0, "memory_str": null, "filename": "AdminSubUser.php:52", "source": {"index": 16, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUser.php&line=52", "ajax": false, "filename": "AdminSubUser.php", "line": "52"}, "connection": "wind_rich", "explain": null, "start_percent": 20.172, "width_percent": 2.471}, {"sql": "select `id` from `wr_admin_users` where `parent_id` = 1 and `status` = 1 and `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 57}, {"index": 14, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 283}, {"index": 17, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 327}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.9865499, "duration": 0.04851, "duration_str": "48.51ms", "memory": 0, "memory_str": null, "filename": "AdminSubUser.php:57", "source": {"index": 13, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUser.php&line=57", "ajax": false, "filename": "AdminSubUser.php", "line": "57"}, "connection": "wind_rich", "explain": null, "start_percent": 22.643, "width_percent": 2.475}, {"sql": "select `username`, `id` from `wr_admin_users` where `id` in (1) and `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 284}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 327}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 1008}], "start": **********.039196, "duration": 0.0521, "duration_str": "52.1ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:284", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 284}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=284", "ajax": false, "filename": "WwLinkController.php", "line": "284"}, "connection": "wind_rich", "explain": null, "start_percent": 25.118, "width_percent": 2.659}, {"sql": "select `id`, `parent_id` from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 298}, {"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 327}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.100132, "duration": 0.052649999999999995, "duration_str": "52.65ms", "memory": 0, "memory_str": null, "filename": "AdminSubUser.php:35", "source": {"index": 16, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUser.php&line=35", "ajax": false, "filename": "AdminSubUser.php", "line": "35"}, "connection": "wind_rich", "explain": null, "start_percent": 27.777, "width_percent": 2.687}, {"sql": "select `id` from `wr_admin_users` where `parent_id` = 1 and `status` = 1 and `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 39}, {"index": 14, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 298}, {"index": 17, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 327}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.157865, "duration": 0.05269, "duration_str": "52.69ms", "memory": 0, "memory_str": null, "filename": "AdminSubUser.php:39", "source": {"index": 13, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUser.php&line=39", "ajax": false, "filename": "AdminSubUser.php", "line": "39"}, "connection": "wind_rich", "explain": null, "start_percent": 30.463, "width_percent": 2.689}, {"sql": "select `ww_user_group_ids` from `wr_admin_sub_users` where `admin_uid` = 1 and `wr_admin_sub_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 300}, {"index": 19, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 1008}], "start": **********.215381, "duration": 0.047630000000000006, "duration_str": "47.63ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:300", "source": {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 300}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=300", "ajax": false, "filename": "WwLinkController.php", "line": "300"}, "connection": "wind_rich", "explain": null, "start_percent": 33.152, "width_percent": 2.43}, {"sql": "select `id`, `parent_id` from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 319}, {"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 327}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.266625, "duration": 0.04778, "duration_str": "47.78ms", "memory": 0, "memory_str": null, "filename": "AdminSubUser.php:35", "source": {"index": 16, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUser.php&line=35", "ajax": false, "filename": "AdminSubUser.php", "line": "35"}, "connection": "wind_rich", "explain": null, "start_percent": 35.582, "width_percent": 2.438}, {"sql": "select `id` from `wr_admin_users` where `parent_id` = 1 and `status` = 1 and `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 39}, {"index": 14, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 319}, {"index": 17, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 327}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.318502, "duration": 0.04690999999999999, "duration_str": "46.91ms", "memory": 0, "memory_str": null, "filename": "AdminSubUser.php:39", "source": {"index": 13, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUser.php&line=39", "ajax": false, "filename": "AdminSubUser.php", "line": "39"}, "connection": "wind_rich", "explain": null, "start_percent": 38.021, "width_percent": 2.394}, {"sql": "select `title`, `id` from `wr_ww_users_groups` where `admin_uid` in (1) and `type` = 1 and `admin_uid` in (1) and `wr_ww_users_groups`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 321}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 327}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 1008}], "start": **********.368399, "duration": 0.04721, "duration_str": "47.21ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:321", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 321}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=321", "ajax": false, "filename": "WwLinkController.php", "line": "321"}, "connection": "wind_rich", "explain": null, "start_percent": 40.414, "width_percent": 2.409}, {"sql": "select count(*) as aggregate from `wr_ww_link` where `wr_ww_link`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.421033, "duration": 0.04801, "duration_str": "48.01ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 42.823, "width_percent": 2.45}, {"sql": "select * from `wr_ww_link` where `wr_ww_link`.`deleted_at` is null order by `id` desc limit 20 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.473073, "duration": 0.047119999999999995, "duration_str": "47.12ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 45.273, "width_percent": 2.404}, {"sql": "select * from `wr_ad_account` where `wr_ad_account`.`account_id` in ('********', '********', '********', '********', '********') and `wr_ad_account`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["********", "********", "********", "********", "********"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.528106, "duration": 0.051, "duration_str": "51ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 47.677, "width_percent": 2.602}, {"sql": "select * from `wr_ww_tpl` where `wr_ww_tpl`.`id` in (1, 3) and `wr_ww_tpl`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.584425, "duration": 0.04776, "duration_str": "47.76ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 50.28, "width_percent": 2.437}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` in (1, 4) and `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.635283, "duration": 0.04734, "duration_str": "47.34ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 52.717, "width_percent": 2.416}, {"sql": "select * from `wr_ww_users_groups` where `wr_ww_users_groups`.`id` in (1, 6) and `wr_ww_users_groups`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.686963, "duration": 0.04804, "duration_str": "48.04ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 55.133, "width_percent": 2.451}, {"sql": "select * from `wr_shield_policies` where `wr_shield_policies`.`id` in (1, 2) and `wr_shield_policies`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.7402332, "duration": 0.0475, "duration_str": "47.5ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 57.584, "width_percent": 2.424}, {"sql": "select * from `wr_ww_tpl` where `wr_ww_tpl`.`id` in (2, 4) and `wr_ww_tpl`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 29, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 31, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 32, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.79356, "duration": 0.04809, "duration_str": "48.09ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 28, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 60.008, "width_percent": 2.454}, {"sql": "select * from `wr_admin_domains` where `status` = 1 and `admin_uid` = 1 and `wr_admin_domains`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 153}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}], "start": **********.897786, "duration": 0.047259999999999996, "duration_str": "47.26ms", "memory": 0, "memory_str": null, "filename": "AdminDomain.php:56", "source": {"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminDomain.php&line=56", "ajax": false, "filename": "AdminDomain.php", "line": "56"}, "connection": "wind_rich", "explain": null, "start_percent": 62.462, "width_percent": 2.412}, {"sql": "select * from `wr_admin_domains` where `status` = 1 and `admin_uid` = 1 and `wr_admin_domains`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 153}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}], "start": **********.950138, "duration": 0.048799999999999996, "duration_str": "48.8ms", "memory": 0, "memory_str": null, "filename": "AdminDomain.php:56", "source": {"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminDomain.php&line=56", "ajax": false, "filename": "AdminDomain.php", "line": "56"}, "connection": "wind_rich", "explain": null, "start_percent": 64.873, "width_percent": 2.49}, {"sql": "select * from `wr_admin_domains` where `status` = 1 and `admin_uid` = 1 and `wr_admin_domains`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 153}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}], "start": **********.003127, "duration": 0.04963, "duration_str": "49.63ms", "memory": 0, "memory_str": null, "filename": "AdminDomain.php:56", "source": {"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminDomain.php&line=56", "ajax": false, "filename": "AdminDomain.php", "line": "56"}, "connection": "wind_rich", "explain": null, "start_percent": 67.363, "width_percent": 2.532}, {"sql": "select * from `wr_admin_domains` where `status` = 1 and `admin_uid` = 1 and `wr_admin_domains`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 153}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}], "start": **********.0571551, "duration": 0.04751, "duration_str": "47.51ms", "memory": 0, "memory_str": null, "filename": "AdminDomain.php:56", "source": {"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminDomain.php&line=56", "ajax": false, "filename": "AdminDomain.php", "line": "56"}, "connection": "wind_rich", "explain": null, "start_percent": 69.896, "width_percent": 2.424}, {"sql": "select * from `wr_admin_domains` where `status` = 1 and `admin_uid` = 1 and `wr_admin_domains`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 153}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}], "start": **********.110339, "duration": 0.04743, "duration_str": "47.43ms", "memory": 0, "memory_str": null, "filename": "AdminDomain.php:56", "source": {"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminDomain.php&line=56", "ajax": false, "filename": "AdminDomain.php", "line": "56"}, "connection": "wind_rich", "explain": null, "start_percent": 72.32, "width_percent": 2.42}, {"sql": "select * from `wr_ww_users_groups` where `wr_ww_users_groups`.`id` = 7 and `wr_ww_users_groups`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 180}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 28, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 29, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": **********.182134, "duration": 0.04784, "duration_str": "47.84ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:180", "source": {"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=180", "ajax": false, "filename": "WwLinkController.php", "line": "180"}, "connection": "wind_rich", "explain": null, "start_percent": 74.74, "width_percent": 2.441}, {"sql": "select `merge_name`, `id` from `wr_area` where 0 = 1 and `wr_area`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 188}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": **********.23315, "duration": 0.05575, "duration_str": "55.75ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:188", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=188", "ajax": false, "filename": "WwLinkController.php", "line": "188"}, "connection": "wind_rich", "explain": null, "start_percent": 77.182, "width_percent": 2.845}, {"sql": "select * from `wr_ww_users_groups` where `wr_ww_users_groups`.`id` = 2 and `wr_ww_users_groups`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 180}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 28, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 29, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": **********.294618, "duration": 0.0506, "duration_str": "50.6ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:180", "source": {"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=180", "ajax": false, "filename": "WwLinkController.php", "line": "180"}, "connection": "wind_rich", "explain": null, "start_percent": 80.026, "width_percent": 2.582}, {"sql": "select `merge_name`, `id` from `wr_area` where `id` in ('1', '192', '2648', '3642') and `wr_area`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "192", "2648", "3642"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 188}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": **********.348003, "duration": 0.04904, "duration_str": "49.04ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:188", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=188", "ajax": false, "filename": "WwLinkController.php", "line": "188"}, "connection": "wind_rich", "explain": null, "start_percent": 82.608, "width_percent": 2.502}, {"sql": "select `merge_name`, `id` from `wr_area` where `id` in ('1', '192', '2648', '3642') and `wr_area`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "192", "2648", "3642"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 188}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": **********.4000359, "duration": 0.04904, "duration_str": "49.04ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:188", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=188", "ajax": false, "filename": "WwLinkController.php", "line": "188"}, "connection": "wind_rich", "explain": null, "start_percent": 85.111, "width_percent": 2.502}, {"sql": "select `merge_name`, `id` from `wr_area` where `id` in ('1', '192', '2648', '3642') and `wr_area`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "192", "2648", "3642"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 188}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": **********.4517949, "duration": 0.047270000000000006, "duration_str": "47.27ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:188", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=188", "ajax": false, "filename": "WwLinkController.php", "line": "188"}, "connection": "wind_rich", "explain": null, "start_percent": 87.613, "width_percent": 2.412}, {"sql": "select `merge_name`, `id` from `wr_area` where `id` in ('1', '192', '2648', '3642') and `wr_area`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "192", "2648", "3642"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 188}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": **********.501775, "duration": 0.04725, "duration_str": "47.25ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:188", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=188", "ajax": false, "filename": "WwLinkController.php", "line": "188"}, "connection": "wind_rich", "explain": null, "start_percent": 90.025, "width_percent": 2.411}, {"sql": "select * from `wr_admin_menu` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 115}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/MenuCache.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\MenuCache.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 99}], "start": **********.653948, "duration": 0.05075, "duration_str": "50.75ms", "memory": 0, "memory_str": null, "filename": "ModelTree.php:123", "source": {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FModelTree.php&line=123", "ajax": false, "filename": "ModelTree.php", "line": "123"}, "connection": "wind_rich", "explain": null, "start_percent": 92.436, "width_percent": 2.59}, {"sql": "select `wr_admin_permissions`.*, `wr_admin_permission_menu`.`menu_id` as `pivot_menu_id`, `wr_admin_permission_menu`.`permission_id` as `pivot_permission_id`, `wr_admin_permission_menu`.`created_at` as `pivot_created_at`, `wr_admin_permission_menu`.`updated_at` as `pivot_updated_at` from `wr_admin_permissions` inner join `wr_admin_permission_menu` on `wr_admin_permissions`.`id` = `wr_admin_permission_menu`.`permission_id` where `wr_admin_permission_menu`.`menu_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 115}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 98}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/MenuCache.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\MenuCache.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 99}], "start": **********.711879, "duration": 0.04821, "duration_str": "48.21ms", "memory": 0, "memory_str": null, "filename": "ModelTree.php:123", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FModelTree.php&line=123", "ajax": false, "filename": "ModelTree.php", "line": "123"}, "connection": "wind_rich", "explain": null, "start_percent": 95.026, "width_percent": 2.46}, {"sql": "select `wr_admin_roles`.*, `wr_admin_role_menu`.`menu_id` as `pivot_menu_id`, `wr_admin_role_menu`.`role_id` as `pivot_role_id`, `wr_admin_role_menu`.`created_at` as `pivot_created_at`, `wr_admin_role_menu`.`updated_at` as `pivot_updated_at` from `wr_admin_roles` inner join `wr_admin_role_menu` on `wr_admin_roles`.`id` = `wr_admin_role_menu`.`role_id` where `wr_admin_role_menu`.`menu_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 115}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 98}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/MenuCache.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\MenuCache.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 99}], "start": **********.76722, "duration": 0.04927, "duration_str": "49.27ms", "memory": 0, "memory_str": null, "filename": "ModelTree.php:123", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FModelTree.php&line=123", "ajax": false, "filename": "ModelTree.php", "line": "123"}, "connection": "wind_rich", "explain": null, "start_percent": 97.486, "width_percent": 2.514}]}, "models": {"data": {"Dcat\\Admin\\Models\\Role": {"value": 156, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Dcat\\Admin\\Models\\Menu": {"value": 61, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FMenu.php&line=1", "ajax": false, "filename": "Menu.php", "line": "?"}}, "Dcat\\Admin\\Models\\Permission": {"value": 56, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\AdminUser": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminUser.php&line=1", "ajax": false, "filename": "AdminUser.php", "line": "?"}}, "App\\Models\\WwLink": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwLink.php&line=1", "ajax": false, "filename": "WwLink.php", "line": "?"}}, "App\\Models\\TencentAdAccount": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FTencentAdAccount.php&line=1", "ajax": false, "filename": "TencentAdAccount.php", "line": "?"}}, "App\\Models\\AdminDomain": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminDomain.php&line=1", "ajax": false, "filename": "AdminDomain.php", "line": "?"}}, "App\\Models\\WwTpl": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwTpl.php&line=1", "ajax": false, "filename": "WwTpl.php", "line": "?"}}, "App\\Models\\WwUsersGroup": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwUsersGroup.php&line=1", "ajax": false, "filename": "WwUsersGroup.php", "line": "?"}}, "App\\Models\\ShieldPolicy": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FShieldPolicy.php&line=1", "ajax": false, "filename": "ShieldPolicy.php", "line": "?"}}, "Dcat\\Admin\\Models\\Administrator": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FAdministrator.php&line=1", "ajax": false, "filename": "Administrator.php", "line": "?"}}, "App\\Models\\Announcement": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAnnouncement.php&line=1", "ajax": false, "filename": "Announcement.php", "line": "?"}}}, "count": 305, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://sen.test/ztfz/posting_link", "action_name": "dcat.admin.posting_link.index", "controller_action": "App\\Admin\\Controllers\\WwLinkController@index", "uri": "GET ztfz/posting_link", "controller": "App\\Admin\\Controllers\\WwLinkController@index<a href=\"phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=85\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Admin\\Controllers", "prefix": "/ztfz", "file": "<a href=\"phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=85\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Admin/Controllers/WwLinkController.php:85-101</a>", "middleware": "admin.app:admin, web, admin", "duration": "3.2s", "peak_memory": "2MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1063519847 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1063519847\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1205810787 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1205810787\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1424691992 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjJ1U21ldmQ1Z1BqQUZsUTNLMFM0cnc9PSIsInZhbHVlIjoiQm9XSC9yazdIQXlaMDVrV01GOU9xVE5aZWYvK2IwL0F4ZytOKzhIMTFGbTVTWGZQa2o1R1FHd1VTNmR4amhDL3h4U01CUmY5Y25JTkF2OFNkcHhVRGFIOWhHUXFRVis3R1EwaVpTRStVQldDQkdCNG5LQVdOaEkrWHY1bDRObzIiLCJtYWMiOiJmNmRiYmU0YzdlNDk4M2QzNDFmNmY5NzBmMTFjNDkwOWYyNjRhZDk1ODk1MTcwODgzZmVjMzJlMmViZTBkODBjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InhUNkc3NWNZR2V3dnRUT29nOTB6TWc9PSIsInZhbHVlIjoiTy9odkd3clRhQS9lektVU0YzejVsVXUzQ2FBajV0QVBVbmZGU1dHZFo0WHRUcEkyNTY4eDl0NW5UMzZtK1pSR0ZuNjdMYUg1dHd5ZjRqN0lTK2UwRlNLVVpRVmtiTVdXTmppMzFiSlRFc1FtSVZRNmdyWk9FVGY0bGt5RTNaZFoiLCJtYWMiOiI4ODJmNjVlNTJiOTJlYTExNzYxYjlhZGNlZTI1MmNkYzQwODEzN2YzZTA1MzY3ZWMxMjg5NGY1NmQ1YTMxY2UzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">zh-CN,zh;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">sen.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1424691992\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1729905365 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0esJyfQEGYXbNzItiQn8t75dzSvXZmiKqhcwIHBs</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Sc1mCMq4OSwvqqSk8mbaIttmOBzjSrhvaXfSjoYm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1729905365\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-143830151 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Aug 2025 06:14:44 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-143830151\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-619238565 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>prev</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://sen.test/ztfz/posting_link</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">admin.prev.url</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0esJyfQEGYXbNzItiQn8t75dzSvXZmiKqhcwIHBs</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://sen.test/ztfz/sale</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-619238565\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://sen.test/ztfz/posting_link", "action_name": "dcat.admin.posting_link.index", "controller_action": "App\\Admin\\Controllers\\WwLinkController@index"}, "badge": null}}