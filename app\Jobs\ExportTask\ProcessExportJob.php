<?php

namespace App\Jobs\ExportTask;

use App\Models\AdminUser;
use App\Models\ExportTask;
use App\Models\WwUserAddRecord;
use App\Models\WwUserAddRecordDelete;
use App\Services\Ocpx\OcpxSubmitService;
use Dcat\EasyExcel\Excel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class ProcessExportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $task;

    public function __construct($task)
    {
        $this->task = $task;
    }

//    public function middleware()
//    {
//        $taskId = $this->task['id'] ?? '';
//        if (empty($taskId)) {
//            return [];
//        }
//        return [(new WithoutOverlapping($taskId))->releaseAfter(10)->expireAfter(15)];
//    }

    public function handle()
    {
        $task = $this->task;

        if (!$task) {
            return false;
        }
        $jobId = $this->job->uuid() ?? null;
        ExportTask::query()->withTrashed()->where('id', $task['id'])->update(['status' => 1, 'job_id' => $jobId]);
        $fileName = $task['file_name'] . '.csv';

        // 预定义映射关系
        $deleteMap = [
            0 => "否",
            1 => "客户已删",
            2 => "销售已删"
        ];

        $siteSetMap = [
            'SITE_SET_MOBILE_UNION' => '优量汇',
            'SITE_SET_KUAISHOU' => '快手',
            'SITE_SET_WECHAT' => '微信公众号与小程序',
            'SITE_SET_MOBILE_INNER' => 'QQ、腾讯看点、腾讯音乐 (待废弃)',
            'SITE_SET_TENCENT_NEWS' => '腾讯新闻',
            'SITE_SET_TENCENT_VIDEO' => '腾讯视频',
            'SITE_SET_MOBILE_YYB' => '应用宝',
            'SITE_SET_PCQQ' => 'QQ、QQ 空间、腾讯音乐、PC 版位',
            'SITE_SET_KANDIAN' => 'QQ 浏览器（原腾讯看点）',
            'SITE_SET_QQ_MUSIC_GAME' => 'QQ、腾讯音乐及游戏',
            'SITE_SET_MOMENTS' => '微信朋友圈',
            'SITE_SET_MINI_GAME_WECHAT' => '微信生态内的小游戏场景',
            'SITE_SET_MINI_GAME_QQ' => '手机 QQ 生态内的小游戏场景',
            'SITE_SET_MOBILE_GAME' => '集合腾讯游戏和优量汇联盟生态的手机端游戏',
            'SITE_SET_QQSHOPPING' => 'QQ 购物',
            'SITE_SET_CHANNELS' => '微信视频号',
            'SITE_SET_WECHAT_PLUGIN' => '微信新闻插件',
            'SITE_SET_SEARCH_SCENE' => '搜索场景',
        ];

        $actionList = array_flip(OcpxSubmitService::ACTION_LIST);
        $host = '域名';

        // 获取管理员信息
        $adminUser = AdminUser::query()->find($task['admin_uid']);
        $adminUsername = $adminUser->username;

        // 获取管理员UID列表
        $adminUids = $adminUser->parent_id == 0
            ? AdminUser::query()
                ->where('id', $adminUser->id)
                ->orWhere('parent_id', $adminUser->id)
                ->pluck('id')
                ->toArray()
            : [$adminUser->id];

        // 选择模型
        $model = match ($task['data_type']) {
            0 => new WwUserAddRecord(),
            1 => new WwUserAddRecordDelete(),
            default => new WwUserAddRecordDelete(),
        };

        // 日期范围
        $date = [$task['start_date'], $task['end_date']];

        // 使用游标分页
        $lastId = 0;
        $chunkSize = 1000;

        Excel::export()->chunk(function ($chunkIndex) use (
            $model, $adminUids, $date, $task, $adminUser,
            $adminUsername, $deleteMap, $siteSetMap, $actionList,
            $host, &$lastId, $chunkSize
        ) {
            // 限制最大分页数（200次）
            if ($chunkIndex > 500) {
                return false;
            }

            // 使用游标分页查询
            $query = $model::query()
                ->with([
                    'corpInfoWithTrashed',
                    'wwUserInfoWithTrashed',
                    'linkInfo',
                    'adminInfo'
                ])
                ->whereIn('admin_uid', $adminUids)
                ->whereBetween('external_contact_created_at', $date)
                ->where('id', '>', $lastId)
                ->orderBy('id')
                ->limit($chunkSize);

            if ($task['corp_id']) {
                $query->where('corp_id', $task['corp_id']);
            }
//            if ($task['ww_user_group_id']) {
//                $query->where('ww_user_group_id', $task['ww_user_group_id']);
//            }
//            if ($task['ww_user_wind_label']) {
//                $query->where('ww_user_wind_label', $task['ww_user_wind_label']);
//            }
//            if ($task['ip']) {
//                $query->where('ip', $task['ip']);
//            }
            $addRecords = $query->get();

            if ($addRecords->isEmpty()) {
                return false;
            }

            // 更新最后ID
            $lastId = $addRecords->last()->id;

            $list = [];
            foreach ($addRecords as $key => $row) {
                $item = [
                    '所属账号' => $adminUser->id == $row->admin_uid
                        ? '主账号'
                        : str_replace($adminUsername, '', optional($row->adminInfo)->username),
                    'ID' => $row->id,
                    '企微' => optional($row->corpInfoWithTrashed)->corp_name,
                    '销售名称' => optional($row->wwUserInfoWithTrashed)->name,
                    '销售ID' => optional($row->wwUserInfoWithTrashed)->user_id,
                    '销售分组' => $row->ww_user_group_name ?? "",
                    '风起标签' => $row->ww_user_wind_label ?? "",
                    'IP' => $row->ip ?? "",
                    '地区' => $row->area,
                    'ua' => getPhoneType($row->ua),
                    '访问次数' => $row->view_count,
                    '进粉时间' => $row->external_contact_created_at,
                    '开口' => $row->customer_start_chat ? "是" : "未开",
                    '访问时间' => $row->link_view_created_at,
                    '名称' => $row->name,
                    '删除' => $deleteMap[$row->is_delete] ?? '未知',
                    '客户微信备注' => $row->follow_user_remark ?? '',
                    '客户标签' => $row->follow_user_remark_tags_string ?? '',
                    '广告名称' => $row->adgroup_name ?? '',
                    '创意名称' => $row->dynamic_creative_name ?? '',
                    '版位' => $siteSetMap[$row->site_set_name] ?? '未知',
                ];

                // 链接信息处理
                if ($row->linkInfo) {
                    $linkInfo = $row->linkInfo;
                    $item['链接'] = $linkInfo->remark . "「" . $linkInfo->id . "」";
                    $item['账户ID'] = $linkInfo->account_id;
                    $item['链接地址'] = $host .
                        ($linkInfo->media_type == '腾讯广告' ? "/cdw?ad_id=" : "/wl?ad_id=") .
                        $linkInfo->link_ad_id;
                }

                // OCPX结果处理
                if ($row->ocpx_result_string) {
                    $item['上报结果'] = strtr($row->ocpx_result_string, $actionList);
                }

                $list[] = $item;
            }

            // 释放内存
            unset($addRecords);
            gc_collect_cycles();

            return $list;
        })->csv()->disk('oss')->store('/export_task/' . $fileName);

        if ($task) {
            $ossFilePath = env('CDN_URL') . '/export_task/' . $fileName;
            ExportTask::query()->withTrashed()->where('id', $task['id'])->update([
                'status' => 2,
                'file_path' => $ossFilePath
            ]);
        }
    }
}
