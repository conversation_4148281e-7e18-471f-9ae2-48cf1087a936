<?php

namespace App\Http\Controllers\WorkWeixin3rd;

use App\Jobs\AddExternalContactJob;
use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Models\AdminSubUserAuthCorp;
use App\Models\LinkViewRecord;
use App\Models\LinkViewRecordDelete;
use App\Models\WwCorpInfo;
use App\Models\WwUserAddRecord;
use App\Models\WwUserAddRecordEx;
use App\Services\Ocpx\OcpxSubmitService;
use App\Services\Corp\WwCorpApiService;
use App\Services\NotifySendService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 好友变动通知
 */
class ChangeExternalContact
{
    public function handler($message, $param): bool
    {
        $message = json_decode($message, true);
        //这里是智投新增的参数，用户把sid和suite_id传到系统内
        $message['wind_param'] = $param;
        // Log::info('ChangeExternalContact - Event - MESSAGE：' . json_encode($message));

        //Event - 自建应用的通知方式
        //InfoType - 第三方应用的通知方式
        /**
         * 这里是兼容了第三方应用和代开发应用（代开发算是自建，服务商代替客户创建的自建，权限基本一致）
         * Event是 服务商代开发的回调参数，用来判断回调类型 https://developer.work.weixin.qq.com/document/path/96361
         * InfoType是 第三方应用的回调参数，用来判断回调类型 https://developer.work.weixin.qq.com/document/path/92277
         */
        $infoType = $message['Event'] ?? "";
        if (empty($infoType)) {
            $infoType = $message['InfoType'] ?? "";
        }
        /**
         * 回调类型是change_external_contact，代表是企业微信外部联系人（企业客户）的回调，详见上方注释文档
         */
        if ($infoType == 'change_external_contact') {
            switch ($message['ChangeType']) {
                case 'add_external_contact'://添加企业客户事件\
                case 'add_half_external_contact'://外部联系人免验证添加成员事件
                    Log::info('添加企业客户事件');
                    $this->add_external_contact_by_job($message);
                    break;
                case 'del_follow_user'://删除跟进成员事件，外部联系人删除销售时
                    $this->del_follow_user($message);
                    break;
                case 'del_external_contact'://删除企业客户事件，销售删除外部联系人时
                    $this->del_external_contact($message);
                    break;
                case 'edit_external_contact'://编辑企业客户事件 - 成员编辑外部联系人的备注信息(不包括备注手机号码)或企业标签
                    $this->edit_external_contact($message);
                    break;
            }
        }
        return true;
    }


    /**
     * 添加外部联系人事件处理队列
     * @param $message
     * @return bool
     */
    public function add_external_contact_by_job($message): bool
    {
        Log::info('添加外部联系人事件处理队列-add_external_contact_by_job');
        // Log::info('ChangeExternalContact - Event - MESSAGE：' . json_encode($message));
        //计算消息中 'State' 字段的 CRC32 哈希值，并对 10 取模，得到一个 0 到 9 之间的索引值 ,这个索引值将用于确定要使用的队列名称。
        $state = $message['State'] ?? "";
        if (empty($state)) {
            $jobIndex = 10;
            return false;
        } else {
            $jobIndex = crc32($state) % 10;
        }
        //初始化队列名称为
        $job = 'add_external_contact';

        //检查计算得到的索引值是否不等于 0。如果不等于 0，则将索引值添加到队列名称的末尾，形成一个更具体的队列名称。例如，如果索引值为 3，则队列名称将变为 'add_external_contact_3'。
        if ($jobIndex) { //如果不等于0，那么就加上JobIndex
            $job = 'add_external_contact_' . $jobIndex;
        }
        Log::info("扔进去队列" . $job);
        AddExternalContactJob::dispatch($message)->onQueue($job);
        return true;
    }


    public function add_external_contact($message): bool
    {
        //			Log::info('change_external_contact - add_external_contact - MESSAGE：' . json_encode($message));
        //获取State的点击记录
        /**
         * 获取企业微信的State，给用户展示二维码或者获客链接的时候，我们系统加上的唯一标识，用于溯源这条数据是通过哪个链接，哪个viewRecord进来的
         */
        $state = $message['State'] ?? "";

        /** @var WwCorpInfo $corpInfo */
        $corpInfo = WwCorpInfo::getCorpInfoFromWwCallback($message);
        if (!$corpInfo) {
            NotifySendService::sendWorkWeixinForError("[企微回调][好友变更][严重错误] 回调无法获取到相关的企业信息，请立即查看，相关数据如下：" . json_encode($message));
            return false;
        }
        /**
         * 这里是判断了state的格式，智投2.0系统的前缀统一ztfz_t2，不是这个前缀的，说明不是通过新系统来的，不用处理，单独记录一下即可
         */
        if (!str_contains($state, 'zt_t2')) {
            //这段意思是如果是销售删除的，那么我们要恢复一下
            $addRecords = WwUserAddRecord::query()->where([
                'follow_user_id' => $message['UserID'],
                'external_userid' => $message['ExternalUserID'],
            ])->get();
            /** @var WwUserAddRecord $addRecord */
            foreach ($addRecords as $addRecord) {
                $addRecord->is_delete = 0;
                $addRecord->save();
            }
            if ($addRecords->isEmpty()) {
                $obj = new WwUserAddRecordEx();
                $obj->message = json_encode($message, JSON_UNESCAPED_UNICODE);

                $exUserInfo = WwCorpApiService::externalcontactGet($corpInfo, $message['ExternalUserID']);
                if (isset($exUserInfo['errcode']) && $exUserInfo['errcode'] == 0) {
                    $obj->name = $exUserInfo['external_contact']['name'];
                    $obj->external_userid = $exUserInfo['external_contact']['external_userid'];
                    $obj->ex_user_info = json_encode($exUserInfo, JSON_UNESCAPED_UNICODE);
                }
                $obj->save();
                //NotifySendService::sendWorkWeixinForError("[企微回调][好友变更][普通提醒] 回调的添加记录，不是通过智投方舟系统添加的，请立即查看，相关数据如下：" . json_encode($message));
            }
            return false;
        }
        /**
         * 通过State，溯源查一下，这次进粉的访问记录，访问记录包含了其他的关键信息，如链接ID等
         */
        /** @var LinkViewRecord $linkViewRecord */
        $linkViewRecord = LinkViewRecord::query()->where("state", $state)->first();
        if (!$linkViewRecord) {
            $linkViewRecord = LinkViewRecordDelete::query()->where("state", $state)->first();
        }
        if (!$linkViewRecord) {
            NotifySendService::sendWorkWeixinForError("[企微回调][好友变更][严重错误] 回调的添加记录，无法获取到LinkView记录，请立即查看，相关数据如下：" . json_encode($message));
            return false;
        }
        /**
         * 获取这次访问记录，当时展示的销售信息，一般这个销售信息就是本次回调的销售信息，但是这里没有判断，后续需要优化一下
         * TODO 判断一下企业微信回调的进粉销售与展示的销售是否一致，一般情况下都是一致的，这里单独判断一下，以防异常
         */
        $viewWwUserInfo = $linkViewRecord->wwUserInfo;

        /**
         * 通过企业微信的API，获取外部联系人的信息
         */
        $exUserInfo = WwCorpApiService::externalcontactGet($corpInfo, $message['ExternalUserID']);
        if (isset($exUserInfo['errcode']) && $exUserInfo['errcode'] == 0) {
            //获取用户信息
            $addRecord = WwUserAddRecord::query()->where([
                'admin_uid' => $linkViewRecord->admin_uid,
                'ww_link_id' => $linkViewRecord->ww_link_id,
                'external_userid' => $message['ExternalUserID'],
                'ww_user_id' => $viewWwUserInfo->id
            ])->first();
            //如果信息不存在，那么创建一条新的记录
            if (!$addRecord) {
                $addRecord = new WwUserAddRecord();
                $addRecord->admin_uid = $linkViewRecord->admin_uid;
                $addRecord->corp_id = $viewWwUserInfo->corp_id;
                $addRecord->ww_user_id = $linkViewRecord->ww_user_id;
                $addRecord->link_view_record_id = $linkViewRecord->id;
                $addRecord->user_id = $linkViewRecord->user_id;
                $addRecord->ww_link_id = $linkViewRecord->ww_link_id;
                $addRecord->click_id = $linkViewRecord->click_id;
                $addRecord->page_type = $linkViewRecord->page_type;
                $addRecord->ip = $linkViewRecord->ip;
                $addRecord->prov = $linkViewRecord->prov;
                $addRecord->city = $linkViewRecord->city;
                $addRecord->district = $linkViewRecord->district;
                $addRecord->area = $linkViewRecord->area;
                $addRecord->ua = $linkViewRecord->ua;
                $addRecord->view_count = $linkViewRecord->view_count;
                $addRecord->link_view_created_at = $linkViewRecord->created_at;
                $addRecord->date = date("Y-m-d");
                $addRecord->state = $state;
                $addRecord->external_userid = $exUserInfo['external_contact']['external_userid'];
                $addRecord->name = $exUserInfo['external_contact']['name'];
                $addRecord->avatar = $exUserInfo['external_contact']['avatar'] ?? "";
                $addRecord->type = $exUserInfo['external_contact']['type'];
                $addRecord->gender = $exUserInfo['external_contact']['gender'] ?? "";
                $addRecord->follow_user_id = $viewWwUserInfo->open_user_id;

                $addRecord->ocpx_result = '[]';
                $addRecord->label = '[]';

                $addRecord->ww_user_group_id = $linkViewRecord->ww_user_group_id;
                if ($linkViewRecord->wwUserGroup) {
                    $addRecord->ww_user_group_name = $linkViewRecord->wwUserGroup->title;
                }

                $addRecord->save();

                //只有第一次进粉才进行OCPX的上报
                $ocpxObj = new OcpxSubmitService();
                $ocpxObj->up($addRecord, 'add_external_contact', 1);
            }
            //判断最新的跟进信息，follow_user是外部联系人添加的企业微信销售，比如小张（外部联系人）添加了公司的A、B两个销售，那么follow_user中会有2条记录，一般都是1条，因为一般只添加这个企业微信下的1个销售
            foreach ($exUserInfo['follow_user'] as $followUser) {
                if (!isset($followUser['userid'])) {
                    continue;
                }
                //因为follow_user有可能是多条， 这里做一个判断，只处理当前进粉的销售对应的信息
                if ($followUser['userid'] == $viewWwUserInfo->open_user_id || $followUser['userid'] == $viewWwUserInfo->user_id) {
                    if ($followUser['userid'] == $viewWwUserInfo->user_id) {
                        $addRecord->follow_user_id = $viewWwUserInfo->user_id;
                    }
                    $addRecord->follow_user_remark = $followUser['remark'] ?? "";
                    $addRecord->follow_user_add_way = $followUser['add_way'] ?? "";
                    $addRecord->follow_user_created_at = date("Y-m-d H:i:s", $followUser['createtime']);

                    $remarkMobileSys = json_decode($addRecord->follow_user_remark_mobiles_sys, true);
                    if (!$remarkMobileSys) {
                        $remarkMobileSys = [];
                    }
                    $remarkMobileSys = array_merge($remarkMobileSys, ($followUser['remark_mobiles'] ?? []));
                    $patternForPhone = '/1[3456789]\d{9}/'; //pattern for chinese mobile phone，移动电话（手机）的正则表达式
                    preg_match($patternForPhone, $addRecord->follow_user_remark, $phones);
                    if (isset($phones[0])) {
                        $remarkMobileSys[] = $phones[0];
                    }
                    $addRecord->follow_user_remark_mobiles_sys = json_encode($remarkMobileSys);
                    $addRecord->follow_user_remark_mobiles = json_encode($followUser['remark_mobiles'] ?? []);
                    $addRecord->follow_user_remark_tags = json_encode($followUser['tags'] ?? []);
                    if (!empty($followUser['tags'])) {
                        $addRecord->follow_user_remark_tags_string = implode(",", array_column($followUser['tags'], "tag_name"));
                    }
                    $addRecord->external_contact_created_at = date("Y-m-d H:i:s", $followUser['createtime']);
                }
            }
            $addRecord->is_delete = 0;
            $addRecord->save();

            $linkViewRecord->add_ww_user_id = $addRecord->ww_user_id;
            $linkViewRecord->add_time = date("Y-m-d H:i:s", time());
            $linkViewRecord->save();

            //如果有标签，需要配置自动打标签
            if (!empty($addRecord->linkInfo->ww_label)) {
                $labels = explode(PHP_EOL, $addRecord->linkInfo->ww_label);
                if (!empty($labels)) {
                    foreach ($labels as $key => $value) {
                        if (empty(trim($value))) {
                            unset($labels[$key]);
                            continue;
                        }
                        $labels[$key] = trim($value);
                    }
                }
                if (!empty($labels)) {
                    //获取企业下的全部标签
                    $allLabels = WwCorpApiService::getCorpTagList($corpInfo);
                    function getLabelId($data, $label)
                    {
                        if (isset($data['tag_group'])) {
                            foreach ($data['tag_group'] as $tagGroup) {
                                if (!empty($tagGroup['tag'])) {
                                    foreach ($tagGroup['tag'] as $tag) {
                                        if ($tag['name'] == $label) {
                                            return $tag['id'];
                                        }
                                    }
                                }
                            }
                        }
                        return 0;
                    }

                    $labelData = [];
                    $labelIds = [];
                    foreach ($labels as $label) {
                        $labelId = getLabelId($allLabels, $label);
                        $labelData[$label] = $labelId;
                        if ($labelId) {
                            $labelIds[] = $labelId;
                        }
                    }

                    $labelReq = [$message['UserID'], $message['ExternalUserID'], $labelIds];
                    $labelResp = [];
                    if (!empty($labelIds)) {
                        $labelResp = WwCorpApiService::markTag($corpInfo, $message['UserID'], $message['ExternalUserID'], $labelIds);
                    }
                    if (!empty($addRecord->label_resp)) {
                        $labelResp = [$labelResp, json_decode($addRecord->label_resp, true)];
                    }
                    if (!empty($addRecord->label_req)) {
                        $labelReq = [$labelReq, json_decode($addRecord->label_req, true)];
                    }
                    if (!empty($addRecord->label)) {
                        $labelData = [$labelData, json_decode($addRecord->label, true)];
                    }
                    $addRecord->label_resp = json_encode($labelResp, JSON_UNESCAPED_UNICODE);
                    $addRecord->label_req = json_encode($labelReq, JSON_UNESCAPED_UNICODE);
                    $addRecord->label = json_encode($labelData, JSON_UNESCAPED_UNICODE);
                    $addRecord->save();
                    $this->edit_external_contact($message);
                }
            }
            if ($viewWwUserInfo && !empty($viewWwUserInfo->ww_corp_label_ids)) {
                $labelIds = json_decode($viewWwUserInfo->ww_corp_label_ids, true);
                $labelReq = [$message['UserID'], $message['ExternalUserID'], $labelIds];
                $labelResp = [];
                $labelData = $labelIds;
                if (!empty($labelIds)) {
                    $labelResp = WwCorpApiService::markTag($corpInfo, $message['UserID'], $message['ExternalUserID'], $labelIds);
                }
                if (!empty($addRecord->label_resp)) {
                    $labelResp = [$labelResp, json_decode($addRecord->label_resp, true)];
                }
                if (!empty($addRecord->label_req)) {
                    $labelReq = [$labelReq, json_decode($addRecord->label_req, true)];
                }
                if (!empty($addRecord->label)) {
                    $labelData = [$labelData, json_decode($addRecord->label, true)];
                }
                $addRecord->label_resp = json_encode($labelResp, JSON_UNESCAPED_UNICODE);
                $addRecord->label_req = json_encode($labelReq, JSON_UNESCAPED_UNICODE);
                $addRecord->label = json_encode($labelData, JSON_UNESCAPED_UNICODE);
                $addRecord->save();
                $this->edit_external_contact($message);
            }

        } else {
            NotifySendService::sendWorkWeixinForError("[企微回调][好友变更][严重错误] 回调无法获取到外部联系人信息，请立即查看，相关数据如下：" . json_encode([$message, $exUserInfo]));
            return false;
        }
        return true;
    }

    public function edit_external_contact($message): void
    {
        $addRecords = WwUserAddRecord::query()->where([
            'follow_user_id' => $message['UserID'],
            'external_userid' => $message['ExternalUserID'],
        ])->get();
        if ($addRecords->isEmpty()) {
            return;
        }
        /** @var WwCorpInfo $corpInfo */
        $corpInfo = WwCorpInfo::getCorpInfoFromWwCallback($message);
        if (!$corpInfo) {
            NotifySendService::sendWorkWeixinForError("[企微回调][好友编辑备注][严重错误] 回调无法获取到相关的企业信息，请立即查看，相关数据如下：" . json_encode($message));
            return;
        }
        $exUserInfo = WwCorpApiService::externalcontactGet($corpInfo, $message['ExternalUserID']);
        /** @var WwUserAddRecord $addRecord */
        foreach ($addRecords as $addRecord) {
            if (isset($exUserInfo['follow_user'])) {
                foreach ($exUserInfo['follow_user'] as $followUser) {
                    if (!isset($followUser['userid'])) {
                        continue;
                    }
                    if ($followUser['userid'] == $addRecord->follow_user_id) {
                        $addRecord->follow_user_remark = $followUser['remark'] ?? "";
                        $addRecord->follow_user_add_way = $followUser['add_way'] ?? "";
                        $addRecord->follow_user_created_at = date("Y-m-d H:i:s", $followUser['createtime']);

                        $remarkMobileSys = json_decode($addRecord->follow_user_remark_mobiles_sys, true);
                        if (!$remarkMobileSys) {
                            $remarkMobileSys = [];
                        }
                        $remarkMobileSys = array_merge($remarkMobileSys, ($followUser['remark_mobiles'] ?? []));
                        $patternForPhone = '/1[3456789]\d{9}/'; //pattern for chinese mobile phone，移动电话（手机）的正则表达式
                        preg_match($patternForPhone, $addRecord->follow_user_remark, $phones);
                        if (isset($phones[0])) {
                            $remarkMobileSys[] = $phones[0];
                        }
                        $addRecord->follow_user_remark_mobiles_sys = json_encode($remarkMobileSys);
                        $addRecord->follow_user_remark_mobiles = json_encode($followUser['remark_mobiles'] ?? []);
                        $addRecord->follow_user_remark_tags = json_encode($followUser['tags'] ?? []);
                        if (!empty($followUser['tags'])) {
                            $addRecord->follow_user_remark_tags_string = implode(",", array_column($followUser['tags'], "tag_name"));
                        } else {
                            $addRecord->follow_user_remark_tags_string = '';
                        }
                        $addRecord->save();
                    }
                }
            }

        }
    }

    /**
     * 客户删除销售
     * @param $message
     * @return bool
     */
    public function del_follow_user($message): bool
    {
        $addRecords = WwUserAddRecord::query()->where([
            'follow_user_id' => $message['UserID'],
            'external_userid' => $message['ExternalUserID'],
        ])->get();
        if ($addRecords->isNotEmpty()) {
            /** @var WwUserAddRecord $addRecord */
            foreach ($addRecords as $addRecord) {
                $addRecord->is_delete = 1; //客户删除 记录操作日志
                $addRecord->save();
                //$action, $relId, $actionType, $message, $ip, $adminUid
                AdminActionLogJob::dispatch(
                    'customer_delete_sale', //客户删除销售
                    $addRecord->id,
                    AdminActionLog::ACTION_TYPE['销售'],
                    '客户删除销售，加粉明细ID：' . $addRecord->id . '，销售ID：' . $addRecord->wwUserInfoWithTrashed->id . '，销售名称：' . $addRecord->wwUserInfoWithTrashed->name ?? '',
                    getIp(),
                    $addRecord->admin_uid
                )->onQueue('admin_action_log_job');
            }
        }
        return true;
    }

    /**
     * 销售删除客户
     * @param $message
     * @return bool
     */
    public function del_external_contact($message): bool
    {
        $addRecords = WwUserAddRecord::query()->where([
            'follow_user_id' => $message['UserID'],
            'external_userid' => $message['ExternalUserID'],
        ])->get();
        if ($addRecords->isNotEmpty()) {
            /** @var WwUserAddRecord $addRecord */
            foreach ($addRecords as $addRecord) {
                $addRecord->is_delete = 2; //销售删除  记录操作日志
                $addRecord->save();

                AdminActionLogJob::dispatch(
                    'sale_delete_customer', //销售删除客户
                    $addRecord->id,
                    AdminActionLog::ACTION_TYPE['销售'],
                    '销售删除客户，加粉明细ID：' . $addRecord->id . '，销售ID：' . $addRecord->wwUserInfoWithTrashed->id . '，销售名称：' . $addRecord->wwUserInfoWithTrashed->name ?? '',
                    getIp(),
                    $addRecord->admin_uid
                )->onQueue('admin_action_log_job');
            }
        }

        return true;
    }
}
