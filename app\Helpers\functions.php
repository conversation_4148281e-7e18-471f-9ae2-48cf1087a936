<?php /** @noinspection RegExpRedundantEscape */

	use Illuminate\Support\Facades\Log;

	/**
	 * Created by PhpStorm.
	 * User: Long
	 * Date: 2021/12/29
	 * Time: 11:48
	 */

    function getLabelIdInJob($data, $label)
    {
        if (isset($data['tag_group'])) {
            foreach ($data['tag_group'] as $tagGroup) {
                if (!empty($tagGroup['tag'])) {
                    foreach ($tagGroup['tag'] as $tag) {
                        if ($tag['name'] == $label) {
                            return $tag['id'];
                        }
                    }
                }
            }
        }
        return 0;
    }

	/**
	 * @param string $message
	 * @param array  $param
	 * @param null   $e
	 * @param null   $channel
	 * @return bool|string
	 */
	function makeErrorLog(string $message, array $param, $e = null, $channel = null,): bool|string
	{
		if (!is_null($e)) {
			$param = [
				'file'    => $e->getFile(),
				'line'    => $e->getLine(),
				'message' => $e->getMessage(),
				'param'   => $param
			];
		}
		if (empty($channel)) {
			Log::error($message, $param);
		} else {
			Log::channel($channel)->error($message, $param);
		}
		return json_encode($param, JSON_UNESCAPED_UNICODE);
	}

	/**
	 * 获取客户端真实IP
	 */
	function getIp(): bool|string
	{
		$ip = false;
		if (getenv('HTTP_CLIENT_IP') && strcasecmp(getenv('HTTP_CLIENT_IP'), 'unknown')) {
			$ip = getenv('HTTP_CLIENT_IP');
		} else {
			if (getenv('HTTP_X_FORWARDED_FOR') && strcasecmp(getenv('HTTP_X_FORWARDED_FOR'), 'unknown')) {
				$ip = getenv('HTTP_X_FORWARDED_FOR');
			} else {
				if (getenv('REMOTE_ADDR') && strcasecmp(getenv('REMOTE_ADDR'), 'unknown')) {
					$ip = getenv('REMOTE_ADDR');
				} else {
					if (isset($_SERVER['REMOTE_ADDR']) && $_SERVER['REMOTE_ADDR'] && strcasecmp($_SERVER['REMOTE_ADDR'], 'unknown')) {
						$ip = $_SERVER['REMOTE_ADDR'];
					}
				}
			}
		}
		return preg_match('/[\d\.]{7,15}/', $ip, $matches) ? $matches [0] : false;
	}

	function getLinkState(): string
	{
		return uniqid('zt_t2_', true);
	}

	function getPhoneType($userAgent): string
	{
		if (str_contains($userAgent, 'iPhone')) {
			return "iPhone";
		}
		if (str_contains($userAgent, 'iPad')) {
			return "iPad";
		}
		if (str_contains($userAgent, 'Mac')) {
			return "Mac";
		} elseif (str_contains($userAgent, 'iPad')) {
			return "Ipad";
		} elseif (str_contains($userAgent, 'Android')) {
			$androidModels = array(
				'Samsung' => array('Galaxy S', 'Galaxy Note'),
				'Huawei'  => array('P', 'Mate'),
				'Xiaomi'  => array('Mi', 'Redmi')
			);
			foreach ($androidModels as $brand => $models) {
				foreach ($models as $model) {
					if (str_contains($userAgent, $brand) && str_contains($userAgent, $model)) {
						return "{$brand} {$model}";
					}
				}
			}
			return "安卓";
		} else {
			return "其他";
		}
    }
