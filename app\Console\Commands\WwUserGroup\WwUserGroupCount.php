<?php

namespace App\Console\Commands\WwUserGroup;

use App\Jobs\WwUserGroup\WwUserGroupCountJob;
use App\Models\WwUsersGroup;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class WwUserGroupCount extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'WwUser:WwUserGroupCount';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '统计销售分组内销售数据';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $groups = WwUsersGroup::query()
            ->whereNull('deleted_at')
            ->orderByDesc('id')
            ->get();
        foreach ($groups as $group) {
            $job = 'ww_user_groups_count';
            WwUserGroupCountJob::dispatch($group)->onQueue($job);
        }
        return Command::SUCCESS;
    }
}
