<?php

	namespace App\Admin\Extensions\Tools\WwUserAddRecord;

	use App\Jobs\AdminActionLogJob;
    use App\Models\AdminActionLog;
    use App\Models\WwUserAddRecord;
    use App\Services\Ocpx\OcpxSubmitService;
    use Dcat\Admin\Actions\Response;
    use Dcat\Admin\Admin;
    use Dcat\Admin\Grid\BatchAction;

    class OcpxUploadsTools extends BatchAction
	{
        protected $title = '<button class="btn btn-primary ww_user_batch_btn" style="margin-left: 5px"><i class="feather icon-upload-cloud"></i>&nbsp&nbsp<span class="selected"></span>上报</button>';

        /**
         * 确认弹窗信息
         * @return string
         */
        public function confirm():string
		{
			return '您确定要上报吗';
		}

		// 处理请求
		public function handle(): Response
        {
			// 获取页面的分组
			$keys = $this->getKey();

			foreach ($keys as $id) {
				/** @var WwUserAddRecord $addRecord */
				$addRecord = WwUserAddRecord::query()->find($id);
				if (!$addRecord || $addRecord->admin_uid != Admin::user()->id) {
					continue;
				}
				//所有的手动上报都按照添加成功来进行操作
				$ocpxObj = new OcpxSubmitService();
				$ocpxObj->up($addRecord, 'hand_action', 0);

			}
            //添加操作日志队列
            AdminActionLogJob::dispatch(
                'batch_ocpx_upload',
                Admin::user()->id,
                AdminActionLog::ACTION_TYPE['加粉'],
                '批量手动上报「' . Admin::user()->username . '」，加粉明细ID：' . implode(',', $keys),
                getIp(),
                Admin::user()->id,
            )->onQueue('admin_action_log_job');
            return $this->response()->alert()->success('提示')->detail('上报成功。');
		}

	}
