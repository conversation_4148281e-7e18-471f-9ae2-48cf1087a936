<?php /** @noinspection PhpInconsistentReturnPointsInspection */

namespace App\Admin\Controllers;

use App\Admin\Actions\Grid\wwUserGroups\CopyUserGroupConfig;
use App\Admin\Forms\WwUserGroups\ImportUserGroupForm;
use App\Admin\Renderable\WwUserGroups\ViewGroupsUser;
use App\Admin\Repositories\WwUsersGroup;
use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Models\AdminSubUser;
use App\Models\AdminSubUserAuthCorp;
use App\Models\AdminUser;
use App\Models\ShieldPolicy;
use App\Models\WwLink;
use App\Models\WwUser;
use App\Services\Tools\UtilsService;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Show;
use Dcat\Admin\Widgets\Modal;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Request;

/**
 * @property $wwUsers
 */
class WwUsersGroupController extends AdminController
{
    private $ww_users_count;
    private $onlineWwUsers;


    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new WwUsersGroup(['adminInfo', 'wwUsers','onlineWwUsers']), function (Grid $grid) {
            $grid->paginate(AdminUser::getPaginate());
            if (AdminUser::isSystemOp()) {//如果是管理员，那么显示全部模板
                $grid->column("adminInfo.username", "客户");
            } else {
                /** @var AdminUser $adminUser */
                $adminUser = Admin::user();
                if (Admin::user()->isRole('customer')) { //如果是主账户，显示自身全部模板，以及子账户的全部模板
                    $grid->model()->whereIn("admin_uid", AdminSubUser::getAdminUids($adminUser->id))->orderByDesc("id");
                    $grid->column('归属账号')->display(function () use ($adminUser) {
                        if ($adminUser->id == $this->admin_uid) { //如果是自己创建的则直接显示主账号
                            return '主账号';
                        } else {
                            $username = $this->adminInfo->username;
                            return str_replace($adminUser->username, '', $username);
                        }
                    });
                } else { //如果是子账户，显示自身模板，以及父级授权展示的模板
                    $wwUserGroupData = AdminSubUser::query()->where('admin_uid', $adminUser->id)->value('ww_user_group_ids');
                    $wwUserGroupIds = json_decode($wwUserGroupData, true);
                    if (!$wwUserGroupIds) $wwUserGroupIds = [];
                    if (in_array(-1, $wwUserGroupIds)) {
                        //判断授权是否是-1，代表全部，如果是-1，那么展示主账户全部+自己的全部
                        $grid->model()->whereIn("admin_uid", [$adminUser->id, $adminUser->parent_id]);
                    } else {
                        //如果授权不是全部，那么展示授权的ID，以及自己的全部
                        $grid->model()->where(function ($query) use ($wwUserGroupIds, $adminUser) {
                            $query->whereIn('id', $wwUserGroupIds)
                                ->orWhere("admin_uid", $adminUser->id);
                        });
                    }
                }
            }

            $grid->disableViewButton();
            $grid->column('id')->sortable();
            $grid->column('title');
            $grid->column('type', '类型')
                ->using([0 => '屏蔽', 1 => '投放'])
                ->dot(
                    [
                        0 => 'danger',
                        1 => 'success',
                    ],
                    'primary' // 第二个参数为默认值
                );
//            $grid->column('ww_users_count')->display(function () {
//
//                if (isset($this->wwUsers)) {
//                    return count($this->wwUsers);
//                }
//                return 0;
//            });

            $grid->column('ww_users_online_count', '当前在线')->display(function ($value) {

                $wwUsersCount = 0;
                $onlineWwUsers = 0;
                if (isset($this->wwUsers)) {
                    $wwUsersCount =  count($this->wwUsers);
                }
                if (isset($this->onlineWwUsers)) {
                    $onlineWwUsers =  count($this->onlineWwUsers);
                }
                $max = $wwUsersCount; // 确保分母不为零
                $percentage = 0;
                if($max > 0){
                    $percentage = min(100, ($onlineWwUsers / $max) * 100);
                }
                $color = $percentage > 70 ? 'bg-success' : ($percentage > 30 ? 'bg-warning' : 'bg-danger');
                $animation = $percentage > 0 ? 'progress-bar-animated' : '';
                $percentage = number_format($percentage, 2, '.', '');
                return <<<HTML
<style>
    .custom-progress {
        height: 25px;
        border-radius: 12px; /* 圆角效果 */
        box-shadow: inset 0 1px 2px rgba(0,0,0,.1);
        background-color: #f5f5f5;
        overflow: hidden;
    }
    .custom-progress-bar {
        height: 100%;
        border-radius: 12px; /* 圆角效果 */
        transition: width 0.6s ease, background-color 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding-right: 10px;
        font-size: 12px;
        color: white;
        text-shadow: 0 0 2px rgba(0,0,0,0.5);
    }
</style>

<div class="progress-group">
    <div class="custom-progress">
        <div class="custom-progress-bar $color $animation"
             style="width: {$percentage}%">
             {$percentage}%
        </div>
    </div>
    <small class="d-flex justify-content-between mt-1">
        <span class="font-weight-bold" style="font-size: 15px; color: #0B8B06">在线: {$onlineWwUsers}</span>
        <span style="color: #ee00ee; font-size: 15px;">总数: {$max}</span>
    </small>
</div>
HTML;
            });
            $grid->column('销售信息')->display('查看')->modal(function ($modal) {
                // 设置弹窗标题
                $modal->title('查看销售');
                $modal->xl();
                $modal->icon('feather icon-eye');
                $wwUserIds = json_decode($this->ww_users_list, true);
                //如果存在就展示销售，不再存在就无
                if ($wwUserIds) {
                    return ViewGroupsUser::make(['id' => $wwUserIds]);
                }
                return '暂无销售';
            });
//                $modal = Modal::make()
//                    ->xl()
//                    ->title('导入复制销售分组')
//                    ->body(ImportUserGroupForm::make())
//                    ->button('<button class="btn btn-primary float-right" style="margin-left: 5px"><i class="feather icon-copy"></i>&nbsp&nbsp导入销售分组</button>');
//                if (Admin::user()->parent_id == 0) {
//                    $grid->tools($modal);
//                }
            /** 对话框新增 */
            $grid->disableCreateButton();
            $grid->tools(function (Grid\Tools $tools) {
                $className = collect(Request::segments())->last();
                $tools->append(UtilsService::dialogForm('添加', Request::url() . '/create', "create-{$className}"));
            });
            //复制分组按钮
//                $grid->actions(function (Grid\Displayers\Actions $actions) {
//                    $actions->prepend(new CopyUserGroupConfig());
//                });
            $grid->column('updated_at')->sortable();
            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                $filter->equal('id')->width(2);
                $filter->like('title')->width(2);
                $filter->equal('type', '类型')->select(
                    [
                        0 => '屏蔽',
                        1 => '投放',
                    ]
                )->width(2);
                if(AdminUser::isSystemOp()){
                    $adminUsers = AdminUser::query()->pluck('username', 'id');
                    $filter->equal('admin_uid','用户')->select($adminUsers)->width(2);
                }
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new WwUsersGroup(), function (Show $show) {
            if (!AdminUser::isAdmin($show->model())) {
                $show->field('N')->value("无数据");
            } else {
                $show->field('id');
                $show->field('title');
            }

        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new WwUsersGroup(['wwUsers']), function (Form $form) {
            $form->hidden('ww_users_list');
            if (!$form->isCreating() && !AdminUser::isAdmin($form->model())) {
                $form->disableDeleteButton();
                $form->disableViewButton();
                $form->disableViewCheck();
                $form->disableEditingCheck();
                $form->disableSubmitButton();
                $form->disableListButton();
                $form->disableResetButton();
                $form->display('不可操作')->value("不可操作");
            } else {
                $form->display('id');
                $form->hidden('admin_uid')->default(Admin::user()->id);
                $form->text('title')->required();
                $form->hidden('ww_users_count')->default(0);
                //整理这个企业下的销售数据
                $data = [];
                $corpAuthRecord = AdminSubUserAuthCorp::query()->with(["corpInfo", "adminInfo"])->whereIn('admin_uid', AdminSubUser::getAdminUids(Admin::user()->id))->get();
                $corpAuthIds = [];
                foreach ($corpAuthRecord as $corpAuth) {
                    $corpAuthIds[] = $corpAuth->id;
                    if (Admin::user()->isRole('customer')) {
                        $userName = $corpAuth->adminInfo->username;
                        $userName = str_replace(Admin::user()->username, "", $userName);
                        if (empty($userName)) $userName = '主账号';
                        $name = "「" . $userName . "」" . $corpAuth->corpInfo->corp_name;
                    } else {
                        $name = $corpAuth->corpInfo->corp_name;
                    }
                    $data[] = [
                        'id' => 'corp_' . $corpAuth->id,
                        'parent_id' => 0,
                        'name' => $name,
                        'order' => $corpAuth->id
                    ];
                }
                $wwUsers = WwUser::query()
                    ->select("id", "name", "corp_auth_id as parent_id", "id as order", "user_id", "type")
                    ->whereIn('admin_uid', AdminSubUser::getAdminUids(Admin::user()->id))
                    ->get();
                if (!empty($wwUsers)) {
                    foreach ($wwUsers as $wwUser) {
                        if (!in_array($wwUser->parent_id, $corpAuthIds)) {
                            continue;
                        }

                        if ($wwUser->type == 1) {
                            $pre = "<i class='fa fa-street-view'></i>&nbsp";
                        } else {
                            $pre = "<i class='fa fa-users'></i>&nbsp";
                        }
                        $userId = '';
                        if (!empty($wwUser->user_id)) {
                            $userId = "「" . $wwUser->user_id . "」";
                        }
                        $data[] = [
                            'id' => $wwUser->id,
                            'parent_id' => 'corp_' . $wwUser->parent_id,
                            'name' => $pre . $wwUser->name . $userId,
                            'order' => $wwUser->order,
                        ];
                    }
                }
                $form->radio('type')->options([0 => '屏蔽', 1 => '投放'])->required();
                $form->tree('wwUsers', '销售')
                    ->nodes($data) // 设置所有节点
                    ->expand(false)
                    ->customFormat(function ($v) { // 格式化外部注入的值
                        if (!$v) {
                            return [];
                        }
                        return array_column($v, 'id');
                    });
                $form->saving(function (Form $form) {
                    if ($form->isEditing()) {
                        //编辑分组 今日展示清0
                        WwUser::reSetTodayShowCount(Admin::user()->id);
                    }
                    $wwUsers = explode(",", $form->input('wwUsers'));
                    foreach ($wwUsers as $wwUser) {
                        if (str_contains($wwUser, "corp_")) {
                            return $form->response()->alert()->error('提示')->detail('企微不能为空，您可以导入销售后再创建分组，或者当前暂不选择直接创建一个空分组');
                        }
                    }
                    $wwUsers = array_filter($wwUsers);
                    $form->input("ww_users_count", count($wwUsers));
                    $form->input("ww_users_list", json_encode($wwUsers));
                });
                $form->saved(function (Form $form) {
                    $input = $form->input();
                    // 获取当前操作的记录ID
                    $recordId = $form->getKey(); // 主键ID
                    // 构建日志消息
                    $action = $form->isCreating() ? 'create' : 'update';
                    $message = self::buildUserGroupActionMessage($input, $recordId,$action);
                    AdminActionLogJob::dispatch(
                        'ww_user_group_'.$action,
                        $recordId,
                        AdminActionLog::ACTION_TYPE['销售组'],
                        $message,
                        getIp(),
                        Admin::user()->id, // 当前操作用户ID
                    )->onQueue('admin_action_log_job');
                });
//                $form->display('created_at');
//                $form->display('updated_at');

            }
        });
    }

    public function destroy($id)
    {
        $data = \App\Models\WwUsersGroup::query()->whereIn("id", explode(",", $id))->get();

        foreach ($data as $datum) {
            if (!AdminUser::isAdmin($datum)) {

//                return $this->form()->response()->alert()->error('提示')->detail('无权限操作');
                return $this->form()->response()->error('无权限操作');
            }
            $wwLinkCheck = WwLink::query()->where("ww_user_group_id", $datum->id)->first();
            if ($wwLinkCheck) {
//                return $this->form()->response()->alert()->error('提示')->detail("当前有投放链接使用：【{$datum->title}】分组，无法删除");
                return $this->form()->response()->error("当前有投放链接使用：【{$datum->title}】分组，无法删除");
            }
            $shieldPolicyCheck = ShieldPolicy::query()->where("ww_user_group_id", $datum->id)->first();
            if ($shieldPolicyCheck) {
//                return $this->form()->response()->alert()->error('提示')->detail("当前屏蔽规则使用：【{$datum->title}】分组，无法删除");
                return $this->form()->response()->error("当前屏蔽规则使用：【{$datum->title}】分组，无法删除");
            }
        }
        /** 记录删除操作日志 */
        $delIds = explode(",", $id);
        foreach ($delIds as $delId) {
            AdminActionLogJob::dispatch(
                'ww_user_group_delete',
                $delId,
                AdminActionLog::ACTION_TYPE['销售组'],
                '删除销售分组ID:'.$delId,
                getIp(),
                Admin::user()->id, // 当前操作用户ID
            )->onQueue('admin_action_log_job');
        }
        return $this->form()->destroy($id);
    }

    /**
     * 构建日志消息
     * @param $input
     * @param $recordId
     * @param $action
     * @return string
     */
    public static function buildUserGroupActionMessage($input,$recordId,$action): string
    {
        $admin_uid = $input['admin_uid'];
        $title = $input['title'];
        $type = $input['type'] == 1 ? '投放' : '屏蔽';
        $wwUsers = $input['wwUsers'];
        $action = $action == 'create' ? '创建': '修改';
        return <<<HTML
操作：$action<br>
ID：$recordId<br>
分组名称：$title<br>
类型：$type<br>
销售ID：$wwUsers<br>
管理员ID：$admin_uid<br>
HTML;
    }
}
