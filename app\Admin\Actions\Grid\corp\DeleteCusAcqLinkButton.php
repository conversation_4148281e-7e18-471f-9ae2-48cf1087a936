<?php

namespace App\Admin\Actions\Grid\corp;

use App\Jobs\Corp\DeleteCorpCusacqLinkJob;
use App\Models\AdminSubUserAuthCorp;
use App\Models\AdminUser;
use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Traits\HasPermissions;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;

class DeleteCusAcqLinkButton extends RowAction
{
    /**
     * @return string
     */
    protected $title = '删除获客助手链接';

    public function handle()
    {
        $corpAuthId = $this->getKey();
        if (!AdminUser::isSystemOp()) {
            return $this->response()->alert()->error('提示')->detail('无权限操作')->refresh();
        }
        $corpAuthInfo = AdminSubUserAuthCorp::query()->with("corpInfo")->find($corpAuthId);
        if (!$corpAuthInfo || !$corpAuthInfo->corpInfo) {
            return $this->response()->alert()->error('提示')->detail('未查询到数据')->refresh();
        }
        DeleteCorpCusacqLinkJob::dispatch($corpAuthInfo->corpInfo)->onQueue('delete_corp_cusacq_link');
        return $this->response()->alert()->success('提示')->detail('已转入后台删除')->refresh();
    }

    /**
     * @return string|array|void
     */
    public function confirm()
    {
        return ['确认清理该企微的获客助手链接？'];
    }

    protected function html()
    {
        $name = '<button class="' . $this->getElementClass() . ' btn btn-sm btn-outline-primary  "> 清理获客助手链接 </button>';
        return $name;
    }

    /**
     * @param Model|Authenticatable|HasPermissions|null $user
     *
     * @return bool
     */
    protected function authorize($user): bool
    {
        return true;
    }

    /**
     * @return array
     */
    protected function parameters()
    {
        return [];
    }
}
