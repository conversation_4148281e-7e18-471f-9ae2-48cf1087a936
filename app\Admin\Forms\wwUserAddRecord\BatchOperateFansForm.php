<?php

namespace App\Admin\Forms\wwUserAddRecord;

use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Models\AdminUser;
use App\Models\ShieldIp;
use App\Models\WechatUser;
use App\Models\WwUser;
use App\Models\WwUserAddRecord;
use App\Models\WwUsersOnlineLogs;
use App\Services\Ocpx\OcpxSubmitService;
use App\Services\Tools\LogService;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class BatchOperateFansForm extends Form implements LazyRenderable
{
    use LazyWidget;

    // 处理请求
    public function handle(array $input)
    {


        LogService::inputLog('Tools','加粉批量操作', $input, Admin::user()->id, Admin::user()->username);
        if(!$input['id']){
            return $this->response()->alert()->error('提示')->detail('请选择操作记录。');
        }
        // id转化为数组
        $id = explode(',', $input['id']);
        if (! $id) {
            return $this->response()->alert()->error('提示')->detail('参数错误。');
        }
        $wwAddRecord = WwUserAddRecord::whereIn('id',$id)->get();

        if ($wwAddRecord->isEmpty()) {
            return $this->response()->alert()->error('提示')->detail('操作记录不存在。');
        }

        switch ($input['operate_status']){

            case 0://手动上报
                foreach($wwAddRecord as $addRecord){
                    $result = $this->commonCondition($addRecord);
                    if($result!==true){
                        return $this->response()->alert()->error('提示')->detail($result);
                    }
                    //所有的手动上报都按照添加成功来进行操作
                    $ocpxObj = new OcpxSubmitService();
                    $ocpxObj->up($addRecord, 'hand_action', 0);
                    //记录操作日志
                    //添加操作日志队列
                    AdminActionLogJob::dispatch(
                        'batch_ocpx_upload',
                        $addRecord->id,
                        AdminActionLog::ACTION_TYPE['加粉'],
                        '批量手动上报「' . Admin::user()->username . '」，加粉明细ID：' . $addRecord->id,
                        getIp(),
                        Admin::user()->id,
                    )->onQueue('admin_action_log_job');
                }
                return $this->response()->alert()->success('上报完成，请查看上报结果')->refresh();
                break;
            case 1: //拉黑
                foreach($wwAddRecord as $addRecord){
                    $result = $this->commonCondition($addRecord);
                    if($result!==true){
                        return $this->response()->alert()->error('提示')->detail($result);
                    }
                    $wechatUser = WechatUser::query()->where("id",$addRecord->user_id)->first();
                    if(!$wechatUser){
                        return $this->response()->alert()->error('提示')->detail("ID为：【{$addRecord->id}】的加粉明细溯源失败，请联系运营。");
                    }
                    $wechatUser->is_black = - Admin::user()->id;
                    $wechatUser->save();

                    //同步拉黑IP
                    $shieldIpModel = new ShieldIp();
                    $checkShieldIp = $shieldIpModel->where('ip',$addRecord->ip)->where('admin_uid',Admin::user()->id)->first();
                    if(!$checkShieldIp){
                        $shieldIpModel->admin_uid = Admin::user()->id;
                        $shieldIpModel->ip = $addRecord->ip;
                        $shieldIpModel->remark = Admin::user()->username . '用户通过进粉记录拉黑';
                        $shieldIpModel->save();
                    }
                }
                return $this->response()->alert()->success('提示')->detail('拉黑成功。')->refresh();

            case 2://深度回传
                foreach ($wwAddRecord as $addRecord) {
                    $result = $this->commonCondition($addRecord);
                    if($result!==true){
                        return $this->response()->alert()->error('提示')->detail($result);
                    }
                    //所有的手动上报都按照添加成功来进行操作
                    $isLog = 0;
                    if($addRecord->linkInfo->media_type == '腾讯广告'){
                        $ocpxObj = new OcpxSubmitService();
                        $action = 'deep_action';
                        $ocpxObj->up($addRecord, $action, 0);
                        $isLog = 1;
                    }
                }
                //记录操作日志
                if($isLog){
                    //添加操作日志队列
                    AdminActionLogJob::dispatch(
                        'batch_deep_ocpx_upload',
                        Admin::user()->id,
                        AdminActionLog::ACTION_TYPE['加粉'],
                        '批量深度回传「' . Admin::user()->username . '」，加粉明细ID：' . implode(',', $id),
                        getIp(),
                        Admin::user()->id,
                    )->onQueue('admin_action_log_job');
                }
                return $this->response()->alert()->success('提示')->detail('操作成功。')->refresh();
        }

    }

    private function commonCondition($addRecord){
        $addRecord = WwUserAddRecord::query()->find($addRecord->id);
        if(!$addRecord){
            $msg = '加粉明细不存在，请刷新页面后重试。';
            return $msg;
        }
        if(!AdminUser::isAdmin($addRecord)){
            $msg = '无权限操作';
            return $msg;
        }
        return true;
    }

    public function form()
    {
        $this->radio('operate_status','操作')->options([0 => '上报',1 => '拉黑' ,2 => '深度回传'])->default(0)->required();
        $this->hidden('id')->value($this->payload['ids'] ?? '');
        $this->confirm('确认提交？');
    }

    // 返回表单数据，如不需要可以删除此方法
    public function default()
    {
        return [
            'password'         => '',
            'password_confirm' => '',
        ];
    }
}
