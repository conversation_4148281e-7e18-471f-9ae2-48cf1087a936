<?php
namespace App\Admin\Controllers;

use App\Models\AdminUser;
use Dcat\Admin\Admin;
use Dcat\Admin\Http\Controllers\AdminController;

class SwitchUserController extends AdminController
{
    public function switchUser($userId) {
        // 确保是管理员操作
        // 权限检查（仅超级管理员可切换）
        if (!AdminUser::isSystemOp()){
            return view('errors.system-error', [
                'message' => '非法操作-1'
            ]);
        }
        try {
            $userId = decrypt($userId);
        }catch (\Exception $e){
            return view('errors.system-error', [
                'message' => '非法操作-2'
            ]);
        }
        // 1. 切换用户会话
        auth('admin')->loginUsingId($userId);
        return redirect(admin_url('/'));
    }
}
