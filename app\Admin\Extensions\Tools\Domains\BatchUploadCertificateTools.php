<?php

namespace App\Admin\Extensions\Tools\Domains;

use Admin;
use App\Admin\Extensions\Grid\BatchActionPlus;
use App\Models\AdminDomain;
use App\Models\AdminUser;
use App\Services\AlibabaCloudService;
use App\Services\NotifySendService;
use Illuminate\Http\Request;

class BatchUploadCertificateTools extends BatchActionPlus
{
    public $title = '<button class="btn btn-primary" style="margin-left: 5px"><i class="fa fa-cloud-upload"></i>&nbsp;&nbsp;上传证书</button>';

    // 处理请求
    public function handle(Request $request)
    {
        $keys = $this->getKey();

        /** 查询未上传证书 */
        $domains = AdminDomain::with('cert')
            ->whereIn('upload_status',[0,3])
            ->whereIn('id', $keys)
            ->get();
        if ($domains->isEmpty()) {
            return $this->response()->alert()->error('提示')->detail('未找到待上传证书的域名。');
        }
//        /** 移除https */
//        $domains = $domains->pluck('domain')->map(function ($domain) {
//            return Str::replace(['https://', 'http://'], '', $domain);
//        });

        /** 是否解析到系统 */
//        foreach ($domains as $domain) {
//            $ip = gethostbyname($domain);
//            $ipGroup = AdminDomain::getIpGroup($ip);
//            if (!$ipGroup) {
//                if ($ip == $domain) {
//                    return $this->response()->alert()->error('提示')->detail("$domain 未解析到系统");
//                }
//                return $this->response()->alert()->error('提示')->detail("$domain 未解析到系统 IP：{$ip}");
//            }
//        }

        foreach ($domains as $domain) {
            //判断是否用户重新上传了证书
            if($domain->upload_status == 3){ //如果是证书重新上传状态
                if($domain->listener_status == 'Associated'){
                    return $this->response()->alert()->error('提示')->detail('请先解除之前上传的证书关联。');
                }
            }
            $cleanDomain = preg_replace('#^https?://#i', '', $domain->domain);

            $upLoad = AlibabaCloudService::uploadUserCertificate($cleanDomain,$domain->cert->pem,$domain->cert->key);
            if (!$upLoad['success']) {
                return $this->response()->alert()->error('上传失败'.$cleanDomain)->detail($upLoad['message']);
            }
            $upLoadData = $upLoad['data'];
            /** 更新证书字段 */
            $domain->ali_cert_id = $upLoadData['certId'];
            $domain->upload_status = 1;
            $domain->save();
            $getCertDetail = AlibabaCloudService::getUserCertificateDetail($upLoadData['certId']);
            if (!$getCertDetail['success']) {
                return $this->response()->alert()->error('获取证书详情失败'.$cleanDomain)->detail($upLoad['message']);
            }
            $getCertDetailData = $getCertDetail['data'];
            /** 更新过期时间 */
            $domain->ssl_ex_time = $getCertDetailData['NotAfter'];
            $domain->save();
            /** @var AdminUser $adminUser */
            $adminUser = Admin::user();
            /** 发送消息到运营群 */
            NotifySendService::sendWopForMessage(
                title: "域名管理-上传证书通知",
                contentLines:[
                    "操作者ID" => $adminUser->id,
                    "操作者姓名" => $adminUser->username,
                    "域名ID" => $domain->id,
                    "域名信息" => $cleanDomain,
                    "上传时间" => date("Y-m-d H:i:s")
                ]
            );
        }
        return $this->response()->alert()->success('完成')->detail("所有操作已完成")->refresh();



    }

    public function confirm(): array
    {
        return ['提示','上传证书前，请先在列表点击获取【DNS解析】，确认域名已经解析成功。'];
    }
    // 设置请求参数
    public function parameters()
    {

    }

}
