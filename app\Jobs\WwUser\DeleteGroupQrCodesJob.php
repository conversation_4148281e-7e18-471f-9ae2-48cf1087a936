<?php

namespace App\Jobs\WwUser;

use App\Models\WwUserQrcode;
use App\Models\WwUserQrCodesDelete;
use App\Services\Corp\WwCorpApiService;
use App\Services\NotifySendService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;


class DeleteGroupQrCodesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $qrCode;


    protected $corpInfo;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($qrCode,$corpInfo)
    {

        $this->qrCode = $qrCode;
        $this->corpInfo = $corpInfo;


    }


    /**
     * 防止任务重叠。 expireAfter 设置锁的最大持有时间（防止任务崩溃导致死锁）
     *
     * @return array
     */
    public function middleware()
    {
        $qrcodeId = $this->qrCode['id'];
        if (empty($qrcodeId)) {
            return [];
        }
        return [(new WithoutOverlapping($qrcodeId))->releaseAfter(10)->expireAfter(15)];
    }


    /**
     * Execute the job.
     *
     * @return mixed
     */
    public function handle()
    {

        $corpInfo = $this->corpInfo;
        $configId = $this->qrCode['config_id'];
        $id = $this->qrCode['id'];


        $deleteRes = WwCorpApiService::group_del_join_way($corpInfo, $configId);
        Log::info('删除企微【群二维码】码结果：' . json_encode($deleteRes));

        if (isset($deleteRes['errcode']) && ($deleteRes['errcode'] == 0 || $deleteRes['errcode'] == 41044)) {
            WwUserQrcode::query()->where('id', $id)->delete();//删除已使用的二维码
            try {
                unset($this->qrCode['corp_info']);
                WwUserQrCodesDelete::query()->insert($this->qrCode);
            } catch (\Exception $e) {
                Log::error("[定时删除销售【群二维码】队列失败-转移到冷库]：" . $e->getMessage());
                NotifySendService::sendWorkWeixinForError($e->getMessage());
            }
        } else {
            $message = '[定时删除销售【群二维码】队列失败]： ' . 'config_id：' . $configId . '，errcode：' . $deleteRes['errcode'] ?? '' . '，errmsg：' . $deleteRes['errmsg'] ?? '';
            NotifySendService::sendWorkWeixinForError($message);
        }
        return true;
    }
}
