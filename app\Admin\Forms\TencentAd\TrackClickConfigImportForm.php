<?php

namespace App\Admin\Forms\TencentAd;

use Admin;
use App\Jobs\TencentAd\TencentAdTrackClickConfigJob;
use App\Models\TencentAdAccount;
use App\Models\TenCentAdTrackClickConfig;
use App\Services\Tools\LogService;
use Dcat\Admin\Http\JsonResponse;
use Dcat\EasyExcel\Excel;
use Dcat\Admin\Widgets\Form;

class TrackClickConfigImportForm extends Form
{
    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return JsonResponse
     */
    public function handle(array $input)
    {
        LogService::inputLog('Tools','腾讯广告监测链接配置-导入账户ID配置', $input, Admin::user()->id, Admin::user()->username);
        // 获取上传的文件路径
        $filePath = storage_path('app/public' . $input['import_file']);
        $secondCategoryType = $input['second_category_type'] ?? 'WEB';
        // 如果用的是maatwebsite/excel或者其他, 把读取数据这一步改改就好了
        // 读取excel文件
        $data = Excel::import($filePath)->toArray();
        if (empty($data) || empty($data['Sheet1'])) {
            return $this->response()->alert()->error('提示')->detail('导入数据不能为空');
        }
        $accountIds = array_column($data['Sheet1'], '账户ID');
        $accountIds = array_filter($accountIds, function ($value) {
            return !empty($value);
        });
        $accountList = TencentAdAccount::query()->whereIn('account_id', $accountIds)->get();
        if ($accountList->isEmpty()) {
            return $this->response()->alert()->error('提示')->detail('未查询到账户数据');
        }
        foreach ($accountList as $accountInfo) {
            $feedbackName = 'ZTFZ-DN-智投方舟-' . $accountInfo->account_id;
            TencentAdTrackClickConfigJob::dispatch($accountInfo, $feedbackName,$secondCategoryType)->onQueue('ad_track_click_config');
        }
        return $this->response()->alert()->success('提示')->detail('已转入后台队列执行，稍后请在列表页查看')->refresh();
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        // 禁用重置表单按钮
        $this->disableResetButton();
        $this->select('second_category_type','营销载体类型')->help('默认为WEB类型，如客户有其他类型需求，则选择对应的营销载体类型')->options(TenCentAdTrackClickConfig::CATEGORY_TYPE)->required()->default('WEB');
        // 文件上传
        $this->file('import_file', ' ')
            ->disk('public')
            ->accept('xls,xlsx')
            ->uniqueName()
            ->autoUpload()
            ->required()
            ->move('/import')
            ->help('支持xls,xlsx<a href="https://wind-fly.oss-cn-hangzhou.aliyuncs.com/腾讯广告监测链接组账户ID模板.xlsx">下载模板</a>');


    }
}
