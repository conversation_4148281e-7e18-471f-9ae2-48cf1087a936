<?php

namespace App\Jobs\WwUser;

use App\Services\Corp\WwProvApiService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CheckLicenseTimeJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $corpInfo;


    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($corpInfo)
    {
        $this->corpInfo = $corpInfo;
    }


    /**
     * Execute the job.
     *
     * @return mixed
     */
    public function handle()
    {

        $corpInfo = $this->corpInfo;
        WwProvApiService::checkLicenseTime($corpInfo);
        return true;
    }

}
