<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class CountCorpsData extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;
    protected $table = 'count_corps_data';

//    protected $fillable = ['id'];


// 或者没有定义$fillable，而$guarded也没有设置？

}
