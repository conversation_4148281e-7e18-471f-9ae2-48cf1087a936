<?php

namespace App\Admin\Forms\Admin;

use App\Models\AdminUser;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class BatchSetWwUserOfflineCheckForm extends Form implements LazyRenderable
{
    use LazyWidget;
    // 处理请求
    public function handle(array $input)
    {
        // id转化为数组
        $id = explode(',', $input['id'] ?? null);
        if (!$id) {
            return $this->response()->alert()->error('提示')->detail('参数错误。');
        }

        $adminUsers = AdminUser::query()->find($id);

        if ($adminUsers->isEmpty()) {
            return $this->response()->alert()->error('提示')->detail('用户不存在。');
        }

        foreach ($adminUsers as $key => $adminUser) {

            if(isset($input['ww_user_offline_check'])){
                $adminUser->ww_user_offline_check = $input['ww_user_offline_check'];
            }
            $adminUser->save();
        }
        return $this->response()->alert()->success('提示')->detail('配置成功')->refresh();
    }

    public function form()
    {
        $this->switch('ww_user_offline_check', '销售批量下线检查')->help('开启后，销售批量下线时，会判断否是某些分组内的最后一个在线销售。');
        $this->hidden('id')->value($this->payload['ids'] ?? '');
    }
}
