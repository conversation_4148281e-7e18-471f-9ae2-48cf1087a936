<?php

namespace App\Console\Commands\Tads\Account;

use App\Models\TencentAdAccount;
use App\Services\TenCentAd\AdGroups\GetAdGroupsService;
use App\Services\TenCentAd\OauthAccountService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class GetAdAccountAdGroups extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'GetAdAccountAdGroups';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        /**
         * 获取过往三天消耗较大的账户
         * 获取这些账户下面的所有素材，并且对获取素材对应的落地页，以及素材对应的数据
         */
        $accountVccIds = DB::table("ad_account_vcc_daily")->where("ad_cost", ">", "200000")->where('date', ">", date("Y-m-d", time() - 86400 * 7))->pluck("account_id");
        $accountVccIds = [
            //********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********
            ********,********,********,********
        ];
        $accountInfos = TencentAdAccount::query()->whereIn("account_id", $accountVccIds)->get();
        /** @var TencentAdAccount $adAccount */
        $i        = 0;
        $sumCount = $accountInfos->count();
        foreach ($accountInfos as $adAccount) {
            //完善有消耗的行业
            if (!$adAccount->system_industry_id) {
                $accountInfoResp               = OauthAccountService::advertiserGet($adAccount);
                $accountInfo                   = $accountInfoResp['data']['list'][0] ?? [];
                $adAccount->system_industry_id = $accountInfo['system_industry_id'] ?? "";
                $adAccount->mdm_name           = $accountInfo['mdm_name'] ?? "";
                $adAccount->agency_account_id  = $accountInfo['agency_account_id'] ?? "";
                $adAccount->operators          = json_encode($accountInfo['operators'] ?? []);
                $adAccount->memo               = $accountInfo['memo'] ?? "";
                $adAccount->save();
            }
            $this->info($adAccount->account_id);
            $i++;
            $this->info('进度:' . round($i / $sumCount * 100, 2) . '%');
            //获取所有计划
            GetAdGroupsService::get($adAccount,false);
        }
        return Command::SUCCESS;
    }
}
