<?php

namespace App\Console\Commands\WwUser;

use App\Jobs\WwUser\DeleteGroupQrCodesJob;
use App\Models\WwUserQrcode;
use Illuminate\Console\Command;

class DeletGruopQrCode extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'WwUser:DeleteGruopQrCode';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '清理群二维码';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $where = [
            'is_used' => 1,//已使用的
            'ww_user_type' => 2,//类型是群二维码的
        ];
//        $deleteAt = date('Y-m-d H:i:s', strtotime('-1 hour'));
        WwUserQrcode::query()
            ->where($where)
//            ->where('updated_at','<=',$deleteAt)
            ->chunkById(1000, function ($qrCodeList) {
                if ($qrCodeList) {
                    foreach ($qrCodeList as $k => $qrCode) {
                        $corpInfo = $qrCode->corpInfo;
                        if ($corpInfo) {
                            $qrCode = $qrCode->toArray();
                            DeleteGroupQrCodesJob::dispatch($qrCode,$corpInfo)->onQueue('delete_group_qrcode');
                        }
                    }
                }
            });

        return Command::SUCCESS;
    }
}
