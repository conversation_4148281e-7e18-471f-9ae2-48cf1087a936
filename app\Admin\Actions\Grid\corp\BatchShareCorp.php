<?php

namespace App\Admin\Actions\Grid\corp;

use App\Admin\Extensions\Grid\BatchActionPlus;
use App\Admin\Forms\Corp\BatchShareCorpForm;

class BatchShareCorp extends BatchActionPlus
{
    /**
     * @return string
     */
    public $title = '批量共享';
    public $buttonText = '<button class="btn btn-primary ww_user_batch_btn"><i class="fa fa-share-alt"></i>&nbsp&nbsp<span class="selected"></span>批量共享</button>';

    public function form(): BatchShareCorpForm
    {
        // 实例化表单类
        return BatchShareCorpForm::make();
    }
}
