<?php

namespace App\Admin\Actions\Grid\adminUser;

use App\Admin\Extensions\Grid\BatchActionPlus;
use App\Admin\Forms\Admin\BatchSetAuditOcpxForm;

class BatchSetAuditOcpx extends BatchActionPlus

{
    /**
     * @return string
     */
    public $title = '设置屏蔽进粉手动上报';
    public $buttonText = '<button class="btn btn-primary ww_user_batch_btn">设置屏蔽进粉手动上报</button>';


    public function form(): BatchSetAuditOcpxForm
    {
        // 实例化表单类
        return BatchSetAuditOcpxForm::make();
    }
}
