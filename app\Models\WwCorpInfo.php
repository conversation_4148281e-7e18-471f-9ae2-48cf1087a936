<?php

	namespace App\Models;

	use Dcat\Admin\Traits\HasDateTimeFormatter;
    use Illuminate\Database\Eloquent\Builder;
    use Illuminate\Database\Eloquent\Model;
	use Illuminate\Database\Eloquent\Relations\BelongsTo;
	use Illuminate\Database\Eloquent\SoftDeletes;

	/**
	 * @property mixed|string       $suite_id
	 * @property mixed|string       $id
	 * @property false|mixed|string $auth_user_info
	 * @property false|mixed|string $dealer_corp_info
	 * @property int|mixed          $agent_is_customized_app
	 * @property mixed              $agent_auth_mode
	 * @property false|mixed|string $agent_privilege
	 * @property mixed              $agent_square_logo_url
	 * @property mixed              $agent_name
	 * @property mixed              $agent_agentid
	 * @property mixed|string       $corp_sub_industry
	 * @property mixed|string       $corp_industry
	 * @property mixed|string       $corp_scale
	 * @property mixed|string       $verified_end_time
	 * @property mixed|string       $subject_type
	 * @property mixed|string       $corp_full_name
	 * @property mixed|string       $corp_wxqrcode
	 * @property mixed|string       $corp_user_max
	 * @property mixed|string       $corp_square_logo_url
	 * @property mixed|string       $corp_round_logo_url
	 * @property mixed|string       $corp_type
	 * @property mixed|string       $corp_name
	 * @property mixed|string       $at_ex_time
	 * @property int|mixed          $state
	 * @property mixed|null         $deleted_at
	 * @property mixed              $secret
	 * @property mixed              $corp_id
	 * @property mixed              $access_token
	 * @property mixed              $customer_link_quota_balance
	 * @property mixed              $customer_link_quota_total
	 * @property mixed|string       $customer_link_quota_update_time
	 * @property false|mixed|string $customer_link_quota_list
	 * @property mixed|string       $license_check_time
	 * @property mixed|string       $trail_end_time
	 * @property mixed|string       $trail_start_time
	 * @property mixed              $license_status
	 * @property mixed              $created_at
     * @method isNotEmpty()
     */
	class WwCorpInfo extends Model
	{
		use HasDateTimeFormatter;
		use SoftDeletes;

		protected $table = 'ww_corp_info';

		public function suiteInfo(): BelongsTo
		{
			return $this->BelongsTo(WwAppList::class, 'suite_id', 'suite_id');
		}

        /**
         * @param $message
         *
         * @return Model|null
         */
        public static function getCorpInfoFromWwCallback($message): Model|null
        {
            /**
             * 这里是兼容了第三方应用和代开发应用（代开发算是自建，服务商代替客户创建的自建，权限基本一致）
             * ToUserName 服务商代开发的回调参数，表示是哪个企业微信主体，用来判断回调类型 https://developer.work.weixin.qq.com/document/path/96361
             * AuthCorpId 第三方应用的回调参数，表示是哪个企业微信主体，用来判断回调类型 https://developer.work.weixin.qq.com/document/path/92277
             */
            $corpId = $message['AuthCorpId'] ?? "";
            if (empty($corpId)) {
                $corpId = $message['ToUserName'];
            }
            $where = [
                'corp_id' => $corpId,
            ];
            //一般是自建应用的回调
            if(isset($message['wind_param']['suite_id'])){
                $where['suite_id'] = $message['wind_param']['suite_id'];
            }
            //一般是三方应用的回调
            if(isset($message['wind_param']['sid'])){
                /** @var WwAppList $wwApp */
                $wwApp = WwAppList::query()->where("md5_id", $message['wind_param']['sid'])->first();
                if($wwApp){
                    $where['suite_id'] = $wwApp->suite_id;
                }
            }
            return self::query()->where($where)->first();
        }
	}
