<?php

namespace App\Admin\Actions\Grid\adminUser;

use App\Admin\Extensions\Grid\BatchActionPlus;
use App\Admin\Forms\Admin\BatchUpdateShowWwUserPolicyForm;

class BatchUpdateShowWwUserPolicy extends BatchActionPlus
{
    /**
     * @return string
     */
    public $title = '修改去重策略';

    public $buttonText = '<button class="btn btn-primary ww_user_batch_btn"><i class="feather icon-refresh-ccw"></i>&nbsp&nbsp<span class="selected"></span>修改去重策略</button>';


    public function form(): BatchUpdateShowWwUserPolicyForm
    {
        // 实例化表单类
        return BatchUpdateShowWwUserPolicyForm::make();
    }
}
