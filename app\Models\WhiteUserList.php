<?php

	namespace App\Models;

	use Dcat\Admin\Traits\HasDateTimeFormatter;
	use Illuminate\Database\Eloquent\Model;
	use Illuminate\Database\Eloquent\SoftDeletes;

	/**
	 * @property mixed        $admin_uid
	 * @property mixed        $wechat_user_id
	 * @property mixed        $openid
	 * @property mixed|string $md5_id
	 */
	class WhiteUserList extends Model
	{
		use HasDateTimeFormatter;
		use SoftDeletes;

		protected $table = 'white_user_list';

		public static function getIsWhite($adminUid, $openId, $ip = '', $mediaType = ''): bool
		{
            //如果账户在管理员账户加白过，那么就全局生效
            if(WhiteUserList::query()->where("admin_uid", 1)->where("openid", $openId)->exists()){
                return true;
            }
            if($mediaType == '腾讯广告'){
                return WhiteUserList::query()->where("admin_uid", $adminUid)->where("openid", $openId)->exists();
            }else{
                //由于whiteLoginCallback方法获取不到$mediaType。只能进到这个判断，所以这里再判断一下$openId
                if(WhiteUserList::query()->where("admin_uid", $adminUid)->where("openid", $openId)->exists()){
                    return true;
                }else{
                    if(!$ip){
                        return false;
                    }
                    return WhiteUserList::query()->where("admin_uid", $adminUid)->where("ip", $ip)->exists();
                }
            }
		}
	}
