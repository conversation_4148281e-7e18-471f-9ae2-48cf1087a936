<?php

namespace App\Console\Commands\WwUser;

use App\Jobs\WwUserAutoOnLineJob;
use App\Models\AdminActionLog;
use App\Models\CorpLicenseOrders;
use App\Models\WwUser;
use App\Models\WwUsersOnlineLogs;
use App\Server\NotifySend;
use App\Server\ProviderServer;
use App\Services\NotifySendService;
use App\Services\ProviderService;
use Dcat\Admin\Admin;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CorpLicenseOrderCanCel extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'WwUser:CorpLicenseOrderCanCel';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '取消30分钟内未支付的订单';
    protected $canCelTime = 600;

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $orders = CorpLicenseOrders::query()
            ->where('is_wx_pay',0)
            ->get();
        if($orders->isNotEmpty()){
            foreach ($orders as $order){
                //取消20分钟内未支付的订单
                if(time() >= strtotime($order->created_at)  + $this->canCelTime){
                    $res = ProviderService::cancelOrder($order->corpInfo->corp_id, $order->order_id);
                    if(isset($res) && $res['errcode'] == 0){
                        $order->is_wx_pay = -1;
                        $order->save();
                        $message = '企微许可证订单，ID' . $order->id . '，客户超时未支付，订单已自动取消';
                        NotifySendService::sendWorkWeixinForError($message);
                    }else{
                        $message = '企微许可证订单，ID' . $order->id . '，客户超时未支付，订单取消失败，原因；' . $res['errmsg'] ?? '';
                        NotifySendService::sendWorkWeixinForError($message);
                    }
                }
            }
        }

        return Command::SUCCESS;
    }

}
