{"__meta": {"id": "01K1J1QS2QR7R6RHBKBN74XCVG", "datetime": "2025-08-01 13:30:06", "utime": **********.296559, "method": "GET", "uri": "/ztfz/sale", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754026202.8811, "end": **********.296574, "duration": 3.4154741764068604, "duration_str": "3.42s", "measures": [{"label": "Booting", "start": 1754026202.8811, "relative_start": 0, "end": **********.233686, "relative_end": **********.233686, "duration": 0.***************, "duration_str": "353ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.233703, "relative_start": 0.****************, "end": **********.296576, "relative_end": 1.9073486328125e-06, "duration": 3.***************, "duration_str": "3.06s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.270411, "relative_start": 0.****************, "end": **********.278244, "relative_end": **********.278244, "duration": 0.007833003997802734, "duration_str": "7.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: admin::grid.displayer.switch", "start": **********.933397, "relative_start": 2.****************, "end": **********.933397, "relative_end": **********.933397, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.switch", "start": **********.934523, "relative_start": 2.****************, "end": **********.934523, "relative_end": **********.934523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.switch", "start": **********.935034, "relative_start": 2.***************, "end": **********.935034, "relative_end": **********.935034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.radio", "start": **********.936351, "relative_start": 2.055251121520996, "end": **********.936351, "relative_end": **********.936351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.radio", "start": **********.936963, "relative_start": 2.05586314201355, "end": **********.936963, "relative_end": **********.936963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.938942, "relative_start": 2.0578420162200928, "end": **********.938942, "relative_end": **********.938942, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.radio", "start": **********.941735, "relative_start": 2.0606350898742676, "end": **********.941735, "relative_end": **********.941735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.radio", "start": **********.942103, "relative_start": 2.0610029697418213, "end": **********.942103, "relative_end": **********.942103, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.943305, "relative_start": 2.0622050762176514, "end": **********.943305, "relative_end": **********.943305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.radio", "start": **********.944349, "relative_start": 2.063249111175537, "end": **********.944349, "relative_end": **********.944349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.radio", "start": **********.944563, "relative_start": 2.063462972640991, "end": **********.944563, "relative_end": **********.944563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.945377, "relative_start": 2.064277172088623, "end": **********.945377, "relative_end": **********.945377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.fixed-table", "start": **********.952736, "relative_start": 2.0716359615325928, "end": **********.952736, "relative_end": **********.952736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.table-toolbar", "start": **********.95398, "relative_start": 2.0728800296783447, "end": **********.95398, "relative_end": **********.95398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.batch-actions", "start": **********.955077, "relative_start": 2.073976993560791, "end": **********.955077, "relative_end": **********.955077, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.button", "start": **********.956996, "relative_start": 2.0758960247039795, "end": **********.956996, "relative_end": **********.956996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.form", "start": **********.376904, "relative_start": 2.4958040714263916, "end": **********.376904, "relative_end": **********.376904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.fields", "start": **********.377901, "relative_start": 2.4968011379241943, "end": **********.377901, "relative_end": **********.377901, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select", "start": **********.379149, "relative_start": 2.498049020767212, "end": **********.379149, "relative_end": **********.379149, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.380162, "relative_start": 2.4990620613098145, "end": **********.380162, "relative_end": **********.380162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.38101, "relative_start": 2.4999101161956787, "end": **********.38101, "relative_end": **********.38101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select-script", "start": **********.381823, "relative_start": 2.500723123550415, "end": **********.381823, "relative_end": **********.381823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.382714, "relative_start": 2.5016140937805176, "end": **********.382714, "relative_end": **********.382714, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.hidden", "start": **********.383883, "relative_start": 2.5027830600738525, "end": **********.383883, "relative_end": **********.383883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.hidden", "start": **********.384598, "relative_start": 2.503498077392578, "end": **********.384598, "relative_end": **********.384598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.form", "start": **********.961302, "relative_start": 3.080202102661133, "end": **********.961302, "relative_end": **********.961302, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.fields", "start": **********.961728, "relative_start": 3.0806281566619873, "end": **********.961728, "relative_end": **********.961728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select", "start": **********.962207, "relative_start": 3.0811071395874023, "end": **********.962207, "relative_end": **********.962207, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.962643, "relative_start": 3.08154296875, "end": **********.962643, "relative_end": **********.962643, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.963019, "relative_start": 3.081918954849243, "end": **********.963019, "relative_end": **********.963019, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select-script", "start": **********.963429, "relative_start": 3.082329034805298, "end": **********.963429, "relative_end": **********.963429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.96378, "relative_start": 3.0826799869537354, "end": **********.96378, "relative_end": **********.96378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.multipleselect", "start": **********.964849, "relative_start": 3.0837490558624268, "end": **********.964849, "relative_end": **********.964849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.965521, "relative_start": 3.084421157836914, "end": **********.965521, "relative_end": **********.965521, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.965846, "relative_start": 3.0847461223602295, "end": **********.965846, "relative_end": **********.965846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select-script", "start": **********.966222, "relative_start": 3.0851221084594727, "end": **********.966222, "relative_end": **********.966222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.966563, "relative_start": 3.085463047027588, "end": **********.966563, "relative_end": **********.966563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select", "start": **********.967258, "relative_start": 3.086158037185669, "end": **********.967258, "relative_end": **********.967258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.967661, "relative_start": 3.0865609645843506, "end": **********.967661, "relative_end": **********.967661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.967988, "relative_start": 3.086888074874878, "end": **********.967988, "relative_end": **********.967988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select-script", "start": **********.96832, "relative_start": 3.0872199535369873, "end": **********.96832, "relative_end": **********.96832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.96865, "relative_start": 3.087550163269043, "end": **********.96865, "relative_end": **********.96865, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.file", "start": **********.96969, "relative_start": 3.088590145111084, "end": **********.96969, "relative_end": **********.96969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.970349, "relative_start": 3.0892491340637207, "end": **********.970349, "relative_end": **********.970349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.970696, "relative_start": 3.0895960330963135, "end": **********.970696, "relative_end": **********.970696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.keyvalue", "start": **********.971684, "relative_start": 3.0905840396881104, "end": **********.971684, "relative_end": **********.971684, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.textarea", "start": **********.973516, "relative_start": 3.0924160480499268, "end": **********.973516, "relative_end": **********.973516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.974906, "relative_start": 3.093806028366089, "end": **********.974906, "relative_end": **********.974906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.975504, "relative_start": 3.0944039821624756, "end": **********.975504, "relative_end": **********.975504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.textarea", "start": **********.976179, "relative_start": 3.095078945159912, "end": **********.976179, "relative_end": **********.976179, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.976577, "relative_start": 3.0954771041870117, "end": **********.976577, "relative_end": **********.976577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.976896, "relative_start": 3.0957961082458496, "end": **********.976896, "relative_end": **********.976896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.listbox", "start": **********.977807, "relative_start": 3.0967071056365967, "end": **********.977807, "relative_end": **********.977807, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.978516, "relative_start": 3.0974161624908447, "end": **********.978516, "relative_end": **********.978516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.978864, "relative_start": 3.097764015197754, "end": **********.978864, "relative_end": **********.978864, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.radio", "start": **********.979816, "relative_start": 3.0987160205841064, "end": **********.979816, "relative_end": **********.979816, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.radio", "start": **********.979998, "relative_start": 3.09889817237854, "end": **********.979998, "relative_end": **********.979998, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.980982, "relative_start": 3.099882125854492, "end": **********.980982, "relative_end": **********.980982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.981351, "relative_start": 3.1002509593963623, "end": **********.981351, "relative_end": **********.981351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.radio", "start": **********.981877, "relative_start": 3.1007771492004395, "end": **********.981877, "relative_end": **********.981877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.radio", "start": **********.982055, "relative_start": 3.100955009460449, "end": **********.982055, "relative_end": **********.982055, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.982776, "relative_start": 3.1016759872436523, "end": **********.982776, "relative_end": **********.982776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.983106, "relative_start": 3.102005958557129, "end": **********.983106, "relative_end": **********.983106, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.983989, "relative_start": 3.102889060974121, "end": **********.983989, "relative_end": **********.983989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.984612, "relative_start": 3.1035120487213135, "end": **********.984612, "relative_end": **********.984612, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.98493, "relative_start": 3.103830099105835, "end": **********.98493, "relative_end": **********.98493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.985496, "relative_start": 3.104396104812622, "end": **********.985496, "relative_end": **********.985496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.985841, "relative_start": 3.104741096496582, "end": **********.985841, "relative_end": **********.985841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.986165, "relative_start": 3.105065107345581, "end": **********.986165, "relative_end": **********.986165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.number", "start": **********.986936, "relative_start": 3.1058361530303955, "end": **********.986936, "relative_end": **********.986936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.987541, "relative_start": 3.106441020965576, "end": **********.987541, "relative_end": **********.987541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.98786, "relative_start": 3.106760025024414, "end": **********.98786, "relative_end": **********.98786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.number", "start": **********.98886, "relative_start": 3.107759952545166, "end": **********.98886, "relative_end": **********.98886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.989326, "relative_start": 3.1082260608673096, "end": **********.989326, "relative_end": **********.989326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.989991, "relative_start": 3.108891010284424, "end": **********.989991, "relative_end": **********.989991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.990736, "relative_start": 3.109636068344116, "end": **********.990736, "relative_end": **********.990736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.99117, "relative_start": 3.110069990158081, "end": **********.99117, "relative_end": **********.99117, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.99152, "relative_start": 3.110419988632202, "end": **********.99152, "relative_end": **********.99152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.992027, "relative_start": 3.110927104949951, "end": **********.992027, "relative_end": **********.992027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.992425, "relative_start": 3.1113250255584717, "end": **********.992425, "relative_end": **********.992425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.992735, "relative_start": 3.1116349697113037, "end": **********.992735, "relative_end": **********.992735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.checkbox", "start": **********.993923, "relative_start": 3.112823009490967, "end": **********.993923, "relative_end": **********.993923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.checkbox", "start": **********.994411, "relative_start": 3.1133110523223877, "end": **********.994411, "relative_end": **********.994411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.995605, "relative_start": 3.1145050525665283, "end": **********.995605, "relative_end": **********.995605, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.995949, "relative_start": 3.114849090576172, "end": **********.995949, "relative_end": **********.995949, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.996551, "relative_start": 3.1154510974884033, "end": **********.996551, "relative_end": **********.996551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.996903, "relative_start": 3.1158030033111572, "end": **********.996903, "relative_end": **********.996903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.997234, "relative_start": 3.1161341667175293, "end": **********.997234, "relative_end": **********.997234, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.997722, "relative_start": 3.116621971130371, "end": **********.997722, "relative_end": **********.997722, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.998094, "relative_start": 3.1169941425323486, "end": **********.998094, "relative_end": **********.998094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.998414, "relative_start": 3.117314100265503, "end": **********.998414, "relative_end": **********.998414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.998867, "relative_start": 3.117767095565796, "end": **********.998867, "relative_end": **********.998867, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.999276, "relative_start": 3.118175983428955, "end": **********.999276, "relative_end": **********.999276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.999585, "relative_start": 3.1184849739074707, "end": **********.999585, "relative_end": **********.999585, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.hidden", "start": **********.000059, "relative_start": 3.1189589500427246, "end": **********.000059, "relative_end": **********.000059, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.hidden", "start": **********.000503, "relative_start": 3.119403123855591, "end": **********.000503, "relative_end": **********.000503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.column-selector", "start": **********.001353, "relative_start": 3.120253086090088, "end": **********.001353, "relative_end": **********.001353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.checkbox", "start": **********.001524, "relative_start": 3.1204240322113037, "end": **********.001524, "relative_end": **********.001524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.checkbox", "start": **********.002815, "relative_start": 3.1217150688171387, "end": **********.002815, "relative_end": **********.002815, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.container", "start": **********.006423, "relative_start": 3.1253230571746826, "end": **********.006423, "relative_end": **********.006423, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.009471, "relative_start": 3.128371000289917, "end": **********.009471, "relative_end": **********.009471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.010942, "relative_start": 3.1298420429229736, "end": **********.010942, "relative_end": **********.010942, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.012338, "relative_start": 3.1312379837036133, "end": **********.012338, "relative_end": **********.012338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.01331, "relative_start": 3.1322100162506104, "end": **********.01331, "relative_end": **********.01331, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.014292, "relative_start": 3.1331920623779297, "end": **********.014292, "relative_end": **********.014292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.015444, "relative_start": 3.1343441009521484, "end": **********.015444, "relative_end": **********.015444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.015939, "relative_start": 3.1348390579223633, "end": **********.015939, "relative_end": **********.015939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.016832, "relative_start": 3.1357321739196777, "end": **********.016832, "relative_end": **********.016832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.017428, "relative_start": 3.1363279819488525, "end": **********.017428, "relative_end": **********.017428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.018266, "relative_start": 3.1371660232543945, "end": **********.018266, "relative_end": **********.018266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.018672, "relative_start": 3.1375720500946045, "end": **********.018672, "relative_end": **********.018672, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.019349, "relative_start": 3.138249158859253, "end": **********.019349, "relative_end": **********.019349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.019706, "relative_start": 3.138606071472168, "end": **********.019706, "relative_end": **********.019706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.020276, "relative_start": 3.1391761302948, "end": **********.020276, "relative_end": **********.020276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.021357, "relative_start": 3.1402571201324463, "end": **********.021357, "relative_end": **********.021357, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.021826, "relative_start": 3.140726089477539, "end": **********.021826, "relative_end": **********.021826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.022279, "relative_start": 3.141179084777832, "end": **********.022279, "relative_end": **********.022279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.023246, "relative_start": 3.142146110534668, "end": **********.023246, "relative_end": **********.023246, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.023677, "relative_start": 3.1425771713256836, "end": **********.023677, "relative_end": **********.023677, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.024473, "relative_start": 3.1433730125427246, "end": **********.024473, "relative_end": **********.024473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.024985, "relative_start": 3.1438851356506348, "end": **********.024985, "relative_end": **********.024985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.025475, "relative_start": 3.1443750858306885, "end": **********.025475, "relative_end": **********.025475, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.02616, "relative_start": 3.1450600624084473, "end": **********.02616, "relative_end": **********.02616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.026523, "relative_start": 3.145423173904419, "end": **********.026523, "relative_end": **********.026523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.026948, "relative_start": 3.145848035812378, "end": **********.026948, "relative_end": **********.026948, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.027622, "relative_start": 3.146522045135498, "end": **********.027622, "relative_end": **********.027622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.027967, "relative_start": 3.146867036819458, "end": **********.027967, "relative_end": **********.027967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.028401, "relative_start": 3.147300958633423, "end": **********.028401, "relative_end": **********.028401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.029073, "relative_start": 3.14797306060791, "end": **********.029073, "relative_end": **********.029073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.029417, "relative_start": 3.1483170986175537, "end": **********.029417, "relative_end": **********.029417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.029792, "relative_start": 3.1486921310424805, "end": **********.029792, "relative_end": **********.029792, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.030417, "relative_start": 3.1493170261383057, "end": **********.030417, "relative_end": **********.030417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.030744, "relative_start": 3.149644136428833, "end": **********.030744, "relative_end": **********.030744, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.031316, "relative_start": 3.1502161026000977, "end": **********.031316, "relative_end": **********.031316, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.031691, "relative_start": 3.1505911350250244, "end": **********.031691, "relative_end": **********.031691, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.032098, "relative_start": 3.150998115539551, "end": **********.032098, "relative_end": **********.032098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.table-pagination", "start": **********.033802, "relative_start": 3.1527020931243896, "end": **********.033802, "relative_end": **********.033802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.pagination", "start": **********.035395, "relative_start": 3.154294967651367, "end": **********.035395, "relative_end": **********.035395, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.dropdown", "start": **********.037257, "relative_start": 3.1561570167541504, "end": **********.037257, "relative_end": **********.037257, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.tab", "start": **********.0433, "relative_start": 3.1621999740600586, "end": **********.0433, "relative_end": **********.0433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::layouts.content", "start": **********.04464, "relative_start": 3.1635401248931885, "end": **********.04464, "relative_end": **********.04464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.breadcrumb", "start": **********.045629, "relative_start": 3.1645290851593018, "end": **********.045629, "relative_end": **********.045629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.alerts", "start": **********.046858, "relative_start": 3.1657581329345703, "end": **********.046858, "relative_end": **********.046858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.exception", "start": **********.047747, "relative_start": 3.166646957397461, "end": **********.047747, "relative_end": **********.047747, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.toastr", "start": **********.048589, "relative_start": 3.1674890518188477, "end": **********.048589, "relative_end": **********.048589, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::layouts.page", "start": **********.049971, "relative_start": 3.1688711643218994, "end": **********.049971, "relative_end": **********.049971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::layouts.container", "start": **********.051301, "relative_start": 3.170201063156128, "end": **********.051301, "relative_end": **********.051301, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.sidebar", "start": **********.052316, "relative_start": 3.1712160110473633, "end": **********.052316, "relative_end": **********.052316, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.25536, "relative_start": 3.3742599487304688, "end": **********.25536, "relative_end": **********.25536, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.257542, "relative_start": 3.3764419555664062, "end": **********.257542, "relative_end": **********.257542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.258474, "relative_start": 3.3773741722106934, "end": **********.258474, "relative_end": **********.258474, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.259103, "relative_start": 3.3780031204223633, "end": **********.259103, "relative_end": **********.259103, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.259567, "relative_start": 3.378467082977295, "end": **********.259567, "relative_end": **********.259567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.260003, "relative_start": 3.3789031505584717, "end": **********.260003, "relative_end": **********.260003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.260426, "relative_start": 3.379326105117798, "end": **********.260426, "relative_end": **********.260426, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.260792, "relative_start": 3.3796920776367188, "end": **********.260792, "relative_end": **********.260792, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.261389, "relative_start": 3.380289077758789, "end": **********.261389, "relative_end": **********.261389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.262019, "relative_start": 3.3809189796447754, "end": **********.262019, "relative_end": **********.262019, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.262512, "relative_start": 3.3814120292663574, "end": **********.262512, "relative_end": **********.262512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.262976, "relative_start": 3.381875991821289, "end": **********.262976, "relative_end": **********.262976, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.26343, "relative_start": 3.3823301792144775, "end": **********.26343, "relative_end": **********.26343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.263901, "relative_start": 3.382801055908203, "end": **********.263901, "relative_end": **********.263901, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.26437, "relative_start": 3.383270025253296, "end": **********.26437, "relative_end": **********.26437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.264821, "relative_start": 3.383721113204956, "end": **********.264821, "relative_end": **********.264821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.26527, "relative_start": 3.3841700553894043, "end": **********.26527, "relative_end": **********.26527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.265716, "relative_start": 3.3846161365509033, "end": **********.265716, "relative_end": **********.265716, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.266161, "relative_start": 3.385061025619507, "end": **********.266161, "relative_end": **********.266161, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.26662, "relative_start": 3.3855199813842773, "end": **********.26662, "relative_end": **********.26662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.267066, "relative_start": 3.3859660625457764, "end": **********.267066, "relative_end": **********.267066, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.267511, "relative_start": 3.38641095161438, "end": **********.267511, "relative_end": **********.267511, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.267964, "relative_start": 3.386863946914673, "end": **********.267964, "relative_end": **********.267964, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.268431, "relative_start": 3.387331008911133, "end": **********.268431, "relative_end": **********.268431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.268895, "relative_start": 3.3877949714660645, "end": **********.268895, "relative_end": **********.268895, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.269373, "relative_start": 3.388273000717163, "end": **********.269373, "relative_end": **********.269373, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.269809, "relative_start": 3.38870906829834, "end": **********.269809, "relative_end": **********.269809, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.270258, "relative_start": 3.389158010482788, "end": **********.270258, "relative_end": **********.270258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.270704, "relative_start": 3.389604091644287, "end": **********.270704, "relative_end": **********.270704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.271239, "relative_start": 3.390139102935791, "end": **********.271239, "relative_end": **********.271239, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.271834, "relative_start": 3.3907339572906494, "end": **********.271834, "relative_end": **********.271834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.272676, "relative_start": 3.391576051712036, "end": **********.272676, "relative_end": **********.272676, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.273556, "relative_start": 3.3924560546875, "end": **********.273556, "relative_end": **********.273556, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.274231, "relative_start": 3.3931310176849365, "end": **********.274231, "relative_end": **********.274231, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.274838, "relative_start": 3.393738031387329, "end": **********.274838, "relative_end": **********.274838, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.275516, "relative_start": 3.394416093826294, "end": **********.275516, "relative_end": **********.275516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.276069, "relative_start": 3.3949689865112305, "end": **********.276069, "relative_end": **********.276069, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.276561, "relative_start": 3.395461082458496, "end": **********.276561, "relative_end": **********.276561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.277057, "relative_start": 3.3959569931030273, "end": **********.277057, "relative_end": **********.277057, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.277524, "relative_start": 3.3964240550994873, "end": **********.277524, "relative_end": **********.277524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.277993, "relative_start": 3.39689302444458, "end": **********.277993, "relative_end": **********.277993, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.278528, "relative_start": 3.397428035736084, "end": **********.278528, "relative_end": **********.278528, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.279013, "relative_start": 3.3979129791259766, "end": **********.279013, "relative_end": **********.279013, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.279501, "relative_start": 3.3984010219573975, "end": **********.279501, "relative_end": **********.279501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.279965, "relative_start": 3.398864984512329, "end": **********.279965, "relative_end": **********.279965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.280439, "relative_start": 3.399338960647583, "end": **********.280439, "relative_end": **********.280439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.280954, "relative_start": 3.3998539447784424, "end": **********.280954, "relative_end": **********.280954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.281365, "relative_start": 3.4002649784088135, "end": **********.281365, "relative_end": **********.281365, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.281825, "relative_start": 3.4007251262664795, "end": **********.281825, "relative_end": **********.281825, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.28229, "relative_start": 3.4011900424957275, "end": **********.28229, "relative_end": **********.28229, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.282763, "relative_start": 3.401663064956665, "end": **********.282763, "relative_end": **********.282763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.283227, "relative_start": 3.4021270275115967, "end": **********.283227, "relative_end": **********.283227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.283682, "relative_start": 3.4025821685791016, "end": **********.283682, "relative_end": **********.283682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.284195, "relative_start": 3.403095006942749, "end": **********.284195, "relative_end": **********.284195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.284672, "relative_start": 3.4035720825195312, "end": **********.284672, "relative_end": **********.284672, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.28514, "relative_start": 3.4040400981903076, "end": **********.28514, "relative_end": **********.28514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.285606, "relative_start": 3.404505968093872, "end": **********.285606, "relative_end": **********.285606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.286068, "relative_start": 3.404968023300171, "end": **********.286068, "relative_end": **********.286068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.28653, "relative_start": 3.4054300785064697, "end": **********.28653, "relative_end": **********.28653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.286986, "relative_start": 3.405886173248291, "end": **********.286986, "relative_end": **********.286986, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.287457, "relative_start": 3.4063570499420166, "end": **********.287457, "relative_end": **********.287457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.288015, "relative_start": 3.4069149494171143, "end": **********.288015, "relative_end": **********.288015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.288765, "relative_start": 3.4076650142669678, "end": **********.288765, "relative_end": **********.288765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.28938, "relative_start": 3.40828013420105, "end": **********.28938, "relative_end": **********.28938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.290238, "relative_start": 3.4091379642486572, "end": **********.290238, "relative_end": **********.290238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.navbar", "start": **********.29136, "relative_start": 3.410259962081909, "end": **********.29136, "relative_end": **********.29136, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: announcements.index", "start": **********.292207, "relative_start": 3.411107063293457, "end": **********.292207, "relative_end": **********.292207, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.navbar-user-panel", "start": **********.293227, "relative_start": 3.4121270179748535, "end": **********.293227, "relative_end": **********.293227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 36068432, "peak_usage_str": "34MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.20", "PHP Version": "8.0.2", "Environment": "local", "Debug Mode": "Enabled", "URL": "sen.test", "Timezone": "PRC", "Locale": "zh_CN"}}, "views": {"count": 216, "nb_templates": 216, "templates": [{"name": "3x admin::grid.displayer.switch", "param_count": null, "params": [], "start": **********.933305, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/switch.blade.phpadmin::grid.displayer.switch", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Fswitch.blade.php&line=1", "ajax": false, "filename": "switch.blade.php", "line": "?"}, "render_count": 3, "name_original": "admin::grid.displayer.switch"}, {"name": "3x admin::grid.displayer.editinline.radio", "param_count": null, "params": [], "start": **********.936263, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/editinline/radio.blade.phpadmin::grid.displayer.editinline.radio", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Feditinline%2Fradio.blade.php&line=1", "ajax": false, "filename": "radio.blade.php", "line": "?"}, "render_count": 3, "name_original": "admin::grid.displayer.editinline.radio"}, {"name": "5x admin::widgets.radio", "param_count": null, "params": [], "start": **********.936887, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/radio.blade.phpadmin::widgets.radio", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fradio.blade.php&line=1", "ajax": false, "filename": "radio.blade.php", "line": "?"}, "render_count": 5, "name_original": "admin::widgets.radio"}, {"name": "3x admin::grid.displayer.editinline.template", "param_count": null, "params": [], "start": **********.938823, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/editinline/template.blade.phpadmin::grid.displayer.editinline.template", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Feditinline%2Ftemplate.blade.php&line=1", "ajax": false, "filename": "template.blade.php", "line": "?"}, "render_count": 3, "name_original": "admin::grid.displayer.editinline.template"}, {"name": "1x admin::grid.fixed-table", "param_count": null, "params": [], "start": **********.952659, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/fixed-table.blade.phpadmin::grid.fixed-table", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Ffixed-table.blade.php&line=1", "ajax": false, "filename": "fixed-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.fixed-table"}, {"name": "1x admin::grid.table-toolbar", "param_count": null, "params": [], "start": **********.953891, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/table-toolbar.blade.phpadmin::grid.table-toolbar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Ftable-toolbar.blade.php&line=1", "ajax": false, "filename": "table-toolbar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.table-toolbar"}, {"name": "1x admin::grid.batch-actions", "param_count": null, "params": [], "start": **********.954984, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/batch-actions.blade.phpadmin::grid.batch-actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fbatch-actions.blade.php&line=1", "ajax": false, "filename": "batch-actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.batch-actions"}, {"name": "1x admin::filter.button", "param_count": null, "params": [], "start": **********.956903, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/button.blade.phpadmin::filter.button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::filter.button"}, {"name": "2x admin::widgets.form", "param_count": null, "params": [], "start": **********.376837, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/form.blade.phpadmin::widgets.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::widgets.form"}, {"name": "2x admin::form.fields", "param_count": null, "params": [], "start": **********.377829, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/fields.blade.phpadmin::form.fields", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ffields.blade.php&line=1", "ajax": false, "filename": "fields.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::form.fields"}, {"name": "3x admin::form.select", "param_count": null, "params": [], "start": **********.379074, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/select.blade.phpadmin::form.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 3, "name_original": "admin::form.select"}, {"name": "20x admin::form.error", "param_count": null, "params": [], "start": **********.380095, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/error.blade.phpadmin::form.error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 20, "name_original": "admin::form.error"}, {"name": "20x admin::form.help-block", "param_count": null, "params": [], "start": **********.380939, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/help-block.blade.phpadmin::form.help-block", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fhelp-block.blade.php&line=1", "ajax": false, "filename": "help-block.blade.php", "line": "?"}, "render_count": 20, "name_original": "admin::form.help-block"}, {"name": "4x admin::form.select-script", "param_count": null, "params": [], "start": **********.381756, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/select-script.blade.phpadmin::form.select-script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fselect-script.blade.php&line=1", "ajax": false, "filename": "select-script.blade.php", "line": "?"}, "render_count": 4, "name_original": "admin::form.select-script"}, {"name": "12x admin::scripts.select", "param_count": null, "params": [], "start": **********.382649, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/scripts/select.blade.phpadmin::scripts.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fscripts%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 12, "name_original": "admin::scripts.select"}, {"name": "4x admin::form.hidden", "param_count": null, "params": [], "start": **********.383815, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/hidden.blade.phpadmin::form.hidden", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fhidden.blade.php&line=1", "ajax": false, "filename": "hidden.blade.php", "line": "?"}, "render_count": 4, "name_original": "admin::form.hidden"}, {"name": "1x admin::form.multipleselect", "param_count": null, "params": [], "start": **********.964786, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/multipleselect.blade.phpadmin::form.multipleselect", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fmultipleselect.blade.php&line=1", "ajax": false, "filename": "multipleselect.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.multipleselect"}, {"name": "1x admin::form.file", "param_count": null, "params": [], "start": **********.969626, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/file.blade.phpadmin::form.file", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ffile.blade.php&line=1", "ajax": false, "filename": "file.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.file"}, {"name": "1x admin::form.keyvalue", "param_count": null, "params": [], "start": **********.971622, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/keyvalue.blade.phpadmin::form.keyvalue", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fkeyvalue.blade.php&line=1", "ajax": false, "filename": "keyvalue.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.keyvalue"}, {"name": "2x admin::form.textarea", "param_count": null, "params": [], "start": **********.973418, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/textarea.blade.phpadmin::form.textarea", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ftextarea.blade.php&line=1", "ajax": false, "filename": "textarea.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::form.textarea"}, {"name": "1x admin::form.listbox", "param_count": null, "params": [], "start": **********.97774, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/listbox.blade.phpadmin::form.listbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Flistbox.blade.php&line=1", "ajax": false, "filename": "listbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.listbox"}, {"name": "2x admin::form.radio", "param_count": null, "params": [], "start": **********.979753, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/radio.blade.phpadmin::form.radio", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fradio.blade.php&line=1", "ajax": false, "filename": "radio.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::form.radio"}, {"name": "7x admin::form.input", "param_count": null, "params": [], "start": **********.983919, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/input.blade.phpadmin::form.input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Finput.blade.php&line=1", "ajax": false, "filename": "input.blade.php", "line": "?"}, "render_count": 7, "name_original": "admin::form.input"}, {"name": "2x admin::form.number", "param_count": null, "params": [], "start": **********.986872, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/number.blade.phpadmin::form.number", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fnumber.blade.php&line=1", "ajax": false, "filename": "number.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::form.number"}, {"name": "1x admin::form.checkbox", "param_count": null, "params": [], "start": **********.993857, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/checkbox.blade.phpadmin::form.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.checkbox"}, {"name": "3x admin::widgets.checkbox", "param_count": null, "params": [], "start": **********.994349, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/checkbox.blade.phpadmin::widgets.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 3, "name_original": "admin::widgets.checkbox"}, {"name": "1x admin::grid.column-selector", "param_count": null, "params": [], "start": **********.001274, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/column-selector.blade.phpadmin::grid.column-selector", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fcolumn-selector.blade.php&line=1", "ajax": false, "filename": "column-selector.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.column-selector"}, {"name": "1x admin::filter.container", "param_count": null, "params": [], "start": **********.006351, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/container.blade.phpadmin::filter.container", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fcontainer.blade.php&line=1", "ajax": false, "filename": "container.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::filter.container"}, {"name": "14x admin::filter.where", "param_count": null, "params": [], "start": **********.009218, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/where.blade.phpadmin::filter.where", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fwhere.blade.php&line=1", "ajax": false, "filename": "where.blade.php", "line": "?"}, "render_count": 14, "name_original": "admin::filter.where"}, {"name": "6x admin::filter.text", "param_count": null, "params": [], "start": **********.01085, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/text.blade.phpadmin::filter.text", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Ftext.blade.php&line=1", "ajax": false, "filename": "text.blade.php", "line": "?"}, "render_count": 6, "name_original": "admin::filter.text"}, {"name": "8x admin::filter.select", "param_count": null, "params": [], "start": **********.013198, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/select.blade.phpadmin::filter.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 8, "name_original": "admin::filter.select"}, {"name": "1x admin::grid.table-pagination", "param_count": null, "params": [], "start": **********.033733, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/table-pagination.blade.phpadmin::grid.table-pagination", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Ftable-pagination.blade.php&line=1", "ajax": false, "filename": "table-pagination.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.table-pagination"}, {"name": "1x admin::grid.pagination", "param_count": null, "params": [], "start": **********.035326, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/pagination.blade.phpadmin::grid.pagination", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fpagination.blade.php&line=1", "ajax": false, "filename": "pagination.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.pagination"}, {"name": "1x admin::widgets.dropdown", "param_count": null, "params": [], "start": **********.03719, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/dropdown.blade.phpadmin::widgets.dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fdropdown.blade.php&line=1", "ajax": false, "filename": "dropdown.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::widgets.dropdown"}, {"name": "1x admin::widgets.tab", "param_count": null, "params": [], "start": **********.043221, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/tab.blade.phpadmin::widgets.tab", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Ftab.blade.php&line=1", "ajax": false, "filename": "tab.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::widgets.tab"}, {"name": "1x admin::layouts.content", "param_count": null, "params": [], "start": **********.044571, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/layouts/content.blade.phpadmin::layouts.content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Flayouts%2Fcontent.blade.php&line=1", "ajax": false, "filename": "content.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::layouts.content"}, {"name": "1x admin::partials.breadcrumb", "param_count": null, "params": [], "start": **********.045563, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/breadcrumb.blade.phpadmin::partials.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.breadcrumb"}, {"name": "1x admin::partials.alerts", "param_count": null, "params": [], "start": **********.046794, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/alerts.blade.phpadmin::partials.alerts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Falerts.blade.php&line=1", "ajax": false, "filename": "alerts.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.alerts"}, {"name": "1x admin::partials.exception", "param_count": null, "params": [], "start": **********.047679, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/exception.blade.phpadmin::partials.exception", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fexception.blade.php&line=1", "ajax": false, "filename": "exception.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.exception"}, {"name": "1x admin::partials.toastr", "param_count": null, "params": [], "start": **********.048523, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/toastr.blade.phpadmin::partials.toastr", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Ftoastr.blade.php&line=1", "ajax": false, "filename": "toastr.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.toastr"}, {"name": "1x admin::layouts.page", "param_count": null, "params": [], "start": **********.049907, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/layouts/page.blade.phpadmin::layouts.page", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Flayouts%2Fpage.blade.php&line=1", "ajax": false, "filename": "page.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::layouts.page"}, {"name": "1x admin::layouts.container", "param_count": null, "params": [], "start": **********.051231, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\resources\\views/vendor/admin/layouts/container.blade.phpadmin::layouts.container", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fresources%2Fviews%2Fvendor%2Fadmin%2Flayouts%2Fcontainer.blade.php&line=1", "ajax": false, "filename": "container.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::layouts.container"}, {"name": "1x admin::partials.sidebar", "param_count": null, "params": [], "start": **********.052241, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/sidebar.blade.phpadmin::partials.sidebar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.sidebar"}, {"name": "65x admin::partials.menu", "param_count": null, "params": [], "start": **********.255289, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/menu.blade.phpadmin::partials.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 65, "name_original": "admin::partials.menu"}, {"name": "1x admin::partials.navbar", "param_count": null, "params": [], "start": **********.291288, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/navbar.blade.phpadmin::partials.navbar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fnavbar.blade.php&line=1", "ajax": false, "filename": "navbar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.navbar"}, {"name": "1x announcements.index", "param_count": null, "params": [], "start": **********.292141, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\resources\\views/announcements/index.blade.phpannouncements.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fresources%2Fviews%2Fannouncements%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "announcements.index"}, {"name": "1x admin::partials.navbar-user-panel", "param_count": null, "params": [], "start": **********.293151, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/navbar-user-panel.blade.phpadmin::partials.navbar-user-panel", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fnavbar-user-panel.blade.php&line=1", "ajax": false, "filename": "navbar-user-panel.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.navbar-user-panel"}]}, "queries": {"count": 43, "nb_statements": 42, "nb_visible_statements": 43, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 2.42186, "accumulated_duration_str": "2.42s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Admin.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", "line": 145}, {"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 36}], "start": **********.31725, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "wind_rich", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `wr_admin_users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Admin.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", "line": 145}, {"index": 18, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.326737, "duration": 0.26741000000000004, "duration_str": "267ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wind_rich", "explain": null, "start_percent": 0, "width_percent": 11.042}, {"sql": "insert into `wr_operation_logs` (`admin_uid`, `user_name`, `path`, `method`, `ip`, `input`, `updated_at`, `created_at`) values (1, 'ZtFzSuperAdminPro', 'ztfz/sale', 'GET', '127.0.0.1', '[]', '2025-08-01 13:30:03', '2025-08-01 13:30:03')", "type": "query", "params": [], "bindings": [1, "ZtFzSuperAdminPro", "ztfz/sale", "GET", "127.0.0.1", "[]", "2025-08-01 13:30:03", "2025-08-01 13:30:03"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 51}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.5995662, "duration": 0.*****************, "duration_str": "133ms", "memory": 0, "memory_str": null, "filename": "OpRecord.php:51", "source": {"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FHttp%2FMiddleware%2FOpRecord.php&line=51", "ajax": false, "filename": "OpRecord.php", "line": "51"}, "connection": "wind_rich", "explain": null, "start_percent": 11.042, "width_percent": 5.477}, {"sql": "select * from `wr_announcements` where `wr_announcements`.`deleted_at` is null order by `id` desc limit 7", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Announcement.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\Announcement.php", "line": 24}, {"index": 15, "namespace": null, "name": "app/Admin/bootstrap.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\bootstrap.php", "line": 62}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Admin.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", "line": 164}, {"index": 17, "namespace": null, "name": "app/Admin/bootstrap.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\bootstrap.php", "line": 80}, {"index": 19, "namespace": "middleware", "name": "admin.bootstrap", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\Bootstrap.php", "line": 14}], "start": **********.740933, "duration": 0.05802, "duration_str": "58.02ms", "memory": 0, "memory_str": null, "filename": "Announcement.php:24", "source": {"index": 14, "namespace": null, "name": "app/Models/Announcement.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\Announcement.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAnnouncement.php&line=24", "ajax": false, "filename": "Announcement.php", "line": "24"}, "connection": "wind_rich", "explain": null, "start_percent": 16.518, "width_percent": 2.396}, {"sql": "select `wr_admin_roles`.*, `wr_admin_role_users`.`user_id` as `pivot_user_id`, `wr_admin_role_users`.`role_id` as `pivot_role_id`, `wr_admin_role_users`.`created_at` as `pivot_created_at`, `wr_admin_role_users`.`updated_at` as `pivot_updated_at` from `wr_admin_roles` inner join `wr_admin_role_users` on `wr_admin_roles`.`id` = `wr_admin_role_users`.`role_id` where `wr_admin_role_users`.`user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/HasPermissions.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\HasPermissions.php", "line": 88}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/HasPermissions.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\HasPermissions.php", "line": 76}, {"index": 21, "namespace": "middleware", "name": "admin.permission", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\Permission.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 23, "namespace": "middleware", "name": "admin.bootstrap", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\Bootstrap.php", "line": 19}], "start": **********.922526, "duration": 0.06363, "duration_str": "63.63ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:88", "source": {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/HasPermissions.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\HasPermissions.php", "line": 88}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FHasPermissions.php&line=88", "ajax": false, "filename": "HasPermissions.php", "line": "88"}, "connection": "wind_rich", "explain": null, "start_percent": 18.914, "width_percent": 2.627}, {"sql": "select * from `wr_admin_sub_user_auth_corps` where `wr_admin_sub_user_auth_corps`.`deleted_at` is null order by `id` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 452}, {"index": 18, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 530}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.0277019, "duration": 0.07065, "duration_str": "70.65ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:117", "source": {"index": 14, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=117", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "117"}, "connection": "wind_rich", "explain": null, "start_percent": 21.541, "width_percent": 2.917}, {"sql": "select `id`, `corp_name` from `wr_ww_corp_info` where `wr_ww_corp_info`.`id` in (1, 3, 5, 6) and `wr_ww_corp_info`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, {"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 452}, {"index": 23, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 530}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.102996, "duration": 0.057030000000000004, "duration_str": "57.03ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:117", "source": {"index": 19, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=117", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "117"}, "connection": "wind_rich", "explain": null, "start_percent": 24.458, "width_percent": 2.355}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 452}, {"index": 27, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 530}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}], "start": **********.162116, "duration": 0.06506999999999999, "duration_str": "65.07ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 26.813, "width_percent": 2.687}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 452}, {"index": 27, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 530}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}], "start": **********.2292309, "duration": 0.05239, "duration_str": "52.39ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 29.5, "width_percent": 2.163}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 9 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 452}, {"index": 27, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 530}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}], "start": **********.284032, "duration": 0.05564, "duration_str": "55.64ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 31.663, "width_percent": 2.297}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 452}, {"index": 27, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 530}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}], "start": **********.343833, "duration": 0.059359999999999996, "duration_str": "59.36ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 33.961, "width_percent": 2.451}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 452}, {"index": 27, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 530}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}], "start": **********.405312, "duration": 0.047439999999999996, "duration_str": "47.44ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 36.412, "width_percent": 1.959}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 452}, {"index": 27, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 530}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}], "start": **********.454788, "duration": 0.04808, "duration_str": "48.08ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 38.371, "width_percent": 1.985}, {"sql": "select `title`, `id` from `wr_ww_users_groups` where `wr_ww_users_groups`.`deleted_at` is null order by `id` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 501}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 530}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 1008}], "start": **********.512824, "duration": 0.04811, "duration_str": "48.11ms", "memory": 0, "memory_str": null, "filename": "WwUserController.php:501", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 501}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwUserController.php&line=501", "ajax": false, "filename": "WwUserController.php", "line": "501"}, "connection": "wind_rich", "explain": null, "start_percent": 40.356, "width_percent": 1.986}, {"sql": "select `username`, `id` from `wr_admin_users` where `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 518}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 530}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 1008}], "start": **********.563476, "duration": 0.05124, "duration_str": "51.24ms", "memory": 0, "memory_str": null, "filename": "WwUserController.php:518", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 518}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwUserController.php&line=518", "ajax": false, "filename": "WwUserController.php", "line": "518"}, "connection": "wind_rich", "explain": null, "start_percent": 42.342, "width_percent": 2.116}, {"sql": "select count(*) as aggregate from `wr_ww_users` where `type` = 1 and `wr_ww_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.6182008, "duration": 0.04784, "duration_str": "47.84ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 44.458, "width_percent": 1.975}, {"sql": "select * from `wr_ww_users` where `type` = 1 and `wr_ww_users`.`deleted_at` is null order by `id` desc limit 20 offset 0", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.6687639, "duration": 0.04993, "duration_str": "49.93ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 46.433, "width_percent": 2.062}, {"sql": "select * from `wr_ww_corp_info` where `wr_ww_corp_info`.`id` in (1, 3) and `wr_ww_corp_info`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.721886, "duration": 0.04883, "duration_str": "48.83ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 48.495, "width_percent": 2.016}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` in (1, 4) and `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.774076, "duration": 0.04684000000000001, "duration_str": "46.84ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 50.511, "width_percent": 1.934}, {"sql": "select `wr_ww_users_groups`.*, `wr_ww_users_groups_rel`.`ww_user_id` as `pivot_ww_user_id`, `wr_ww_users_groups_rel`.`ww_group_id` as `pivot_ww_group_id`, `wr_ww_users_groups_rel`.`created_at` as `pivot_created_at`, `wr_ww_users_groups_rel`.`updated_at` as `pivot_updated_at` from `wr_ww_users_groups` inner join `wr_ww_users_groups_rel` on `wr_ww_users_groups`.`id` = `wr_ww_users_groups_rel`.`ww_group_id` where `wr_ww_users_groups_rel`.`ww_user_id` in (5, 6, 13) and `wr_ww_users_groups`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.824414, "duration": 0.04746, "duration_str": "47.46ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 52.445, "width_percent": 1.96}, {"sql": "select * from `wr_ww_user_qrcodes` where `is_used` = 0 and `wr_ww_user_qrcodes`.`ww_user_id` in (5, 6, 13)", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.8780248, "duration": 0.045520000000000005, "duration_str": "45.52ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 54.405, "width_percent": 1.88}, {"sql": "select * from `wr_admin_sub_user_auth_corps` where `wr_admin_sub_user_auth_corps`.`deleted_at` is null order by `id` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.963774, "duration": 0.047420000000000004, "duration_str": "47.42ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:117", "source": {"index": 14, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=117", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "117"}, "connection": "wind_rich", "explain": null, "start_percent": 56.284, "width_percent": 1.958}, {"sql": "select `id`, `corp_name` from `wr_ww_corp_info` where `wr_ww_corp_info`.`id` in (1, 3, 5, 6) and `wr_ww_corp_info`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, {"index": 20, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.015053, "duration": 0.05307, "duration_str": "53.07ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:117", "source": {"index": 19, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=117", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "117"}, "connection": "wind_rich", "explain": null, "start_percent": 58.242, "width_percent": 2.191}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.0718172, "duration": 0.04683, "duration_str": "46.83ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 60.434, "width_percent": 1.934}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.120896, "duration": 0.046520000000000006, "duration_str": "46.52ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 62.367, "width_percent": 1.921}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 9 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.1695962, "duration": 0.04822, "duration_str": "48.22ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 64.288, "width_percent": 1.991}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.2199879, "duration": 0.047270000000000006, "duration_str": "47.27ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 66.279, "width_percent": 1.952}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.269382, "duration": 0.04641, "duration_str": "46.41ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 68.231, "width_percent": 1.916}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.317921, "duration": 0.04942, "duration_str": "49.42ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 70.147, "width_percent": 2.041}, {"sql": "select * from `wr_admin_sub_user_auth_corps` where `wr_admin_sub_user_auth_corps`.`deleted_at` is null order by `id` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.385122, "duration": 0.04947, "duration_str": "49.47ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:117", "source": {"index": 14, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=117", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "117"}, "connection": "wind_rich", "explain": null, "start_percent": 72.188, "width_percent": 2.043}, {"sql": "select `id`, `corp_name` from `wr_ww_corp_info` where `wr_ww_corp_info`.`id` in (1, 3, 5, 6) and `wr_ww_corp_info`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, {"index": 20, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.437212, "duration": 0.04592, "duration_str": "45.92ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:117", "source": {"index": 19, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=117", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "117"}, "connection": "wind_rich", "explain": null, "start_percent": 74.231, "width_percent": 1.896}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.4854221, "duration": 0.046450000000000005, "duration_str": "46.45ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 76.127, "width_percent": 1.918}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.534001, "duration": 0.04739, "duration_str": "47.39ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 78.045, "width_percent": 1.957}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 9 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.583784, "duration": 0.04828, "duration_str": "48.28ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 80.001, "width_percent": 1.994}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.634119, "duration": 0.04874, "duration_str": "48.74ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 81.995, "width_percent": 2.013}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.685183, "duration": 0.045329999999999995, "duration_str": "45.33ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 84.007, "width_percent": 1.872}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.732569, "duration": 0.04952, "duration_str": "49.52ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 85.879, "width_percent": 2.045}, {"sql": "select `id` from `wr_ww_users_groups` where `admin_uid` = 1 and `wr_ww_users_groups`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/WwUsersGroup.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\WwUsersGroup.php", "line": 155}, {"index": 14, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 234}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.801577, "duration": 0.05268, "duration_str": "52.68ms", "memory": 0, "memory_str": null, "filename": "WwUsersGroup.php:155", "source": {"index": 13, "namespace": null, "name": "app/Models/WwUsersGroup.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\WwUsersGroup.php", "line": 155}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwUsersGroup.php&line=155", "ajax": false, "filename": "WwUsersGroup.php", "line": "155"}, "connection": "wind_rich", "explain": null, "start_percent": 87.924, "width_percent": 2.175}, {"sql": "select `ww_user_group_ids` from `wr_admin_sub_users` where `admin_uid` = 1 and `wr_admin_sub_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/WwUsersGroup.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\WwUsersGroup.php", "line": 156}, {"index": 17, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 234}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.8578439, "duration": 0.046380000000000005, "duration_str": "46.38ms", "memory": 0, "memory_str": null, "filename": "WwUsersGroup.php:156", "source": {"index": 16, "namespace": null, "name": "app/Models/WwUsersGroup.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\WwUsersGroup.php", "line": 156}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwUsersGroup.php&line=156", "ajax": false, "filename": "WwUsersGroup.php", "line": "156"}, "connection": "wind_rich", "explain": null, "start_percent": 90.099, "width_percent": 1.915}, {"sql": "select `title`, `id` from `wr_ww_users_groups` where `id` in (1, 2) and `wr_ww_users_groups`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/WwUsersGroup.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\WwUsersGroup.php", "line": 161}, {"index": 14, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 234}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.9071949, "duration": 0.047450000000000006, "duration_str": "47.45ms", "memory": 0, "memory_str": null, "filename": "WwUsersGroup.php:161", "source": {"index": 13, "namespace": null, "name": "app/Models/WwUsersGroup.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\WwUsersGroup.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwUsersGroup.php&line=161", "ajax": false, "filename": "WwUsersGroup.php", "line": "161"}, "connection": "wind_rich", "explain": null, "start_percent": 92.014, "width_percent": 1.959}, {"sql": "select * from `wr_admin_menu` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 115}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/MenuCache.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\MenuCache.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 99}], "start": **********.053947, "duration": 0.05011, "duration_str": "50.11ms", "memory": 0, "memory_str": null, "filename": "ModelTree.php:123", "source": {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FModelTree.php&line=123", "ajax": false, "filename": "ModelTree.php", "line": "123"}, "connection": "wind_rich", "explain": null, "start_percent": 93.973, "width_percent": 2.069}, {"sql": "select `wr_admin_permissions`.*, `wr_admin_permission_menu`.`menu_id` as `pivot_menu_id`, `wr_admin_permission_menu`.`permission_id` as `pivot_permission_id`, `wr_admin_permission_menu`.`created_at` as `pivot_created_at`, `wr_admin_permission_menu`.`updated_at` as `pivot_updated_at` from `wr_admin_permissions` inner join `wr_admin_permission_menu` on `wr_admin_permissions`.`id` = `wr_admin_permission_menu`.`permission_id` where `wr_admin_permission_menu`.`menu_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 115}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 98}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/MenuCache.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\MenuCache.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 99}], "start": **********.110223, "duration": 0.047729999999999995, "duration_str": "47.73ms", "memory": 0, "memory_str": null, "filename": "ModelTree.php:123", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FModelTree.php&line=123", "ajax": false, "filename": "ModelTree.php", "line": "123"}, "connection": "wind_rich", "explain": null, "start_percent": 96.042, "width_percent": 1.971}, {"sql": "select `wr_admin_roles`.*, `wr_admin_role_menu`.`menu_id` as `pivot_menu_id`, `wr_admin_role_menu`.`role_id` as `pivot_role_id`, `wr_admin_role_menu`.`created_at` as `pivot_created_at`, `wr_admin_role_menu`.`updated_at` as `pivot_updated_at` from `wr_admin_roles` inner join `wr_admin_role_menu` on `wr_admin_roles`.`id` = `wr_admin_role_menu`.`role_id` where `wr_admin_role_menu`.`menu_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 115}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 98}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/MenuCache.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\MenuCache.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 99}], "start": **********.1649969, "duration": 0.048119999999999996, "duration_str": "48.12ms", "memory": 0, "memory_str": null, "filename": "ModelTree.php:123", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FModelTree.php&line=123", "ajax": false, "filename": "ModelTree.php", "line": "123"}, "connection": "wind_rich", "explain": null, "start_percent": 98.013, "width_percent": 1.987}]}, "models": {"data": {"Dcat\\Admin\\Models\\Role": {"value": 156, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Dcat\\Admin\\Models\\Menu": {"value": 61, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FMenu.php&line=1", "ajax": false, "filename": "Menu.php", "line": "?"}}, "Dcat\\Admin\\Models\\Permission": {"value": 56, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\AdminUser": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminUser.php&line=1", "ajax": false, "filename": "AdminUser.php", "line": "?"}}, "App\\Models\\AdminSubUserAuthCorp": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=1", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "?"}}, "App\\Models\\WwCorpInfo": {"value": 14, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwCorpInfo.php&line=1", "ajax": false, "filename": "WwCorpInfo.php", "line": "?"}}, "App\\Models\\WwUsersGroup": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwUsersGroup.php&line=1", "ajax": false, "filename": "WwUsersGroup.php", "line": "?"}}, "App\\Models\\WwUser": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwUser.php&line=1", "ajax": false, "filename": "WwUser.php", "line": "?"}}, "Dcat\\Admin\\Models\\Administrator": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FAdministrator.php&line=1", "ajax": false, "filename": "Administrator.php", "line": "?"}}, "App\\Models\\Announcement": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAnnouncement.php&line=1", "ajax": false, "filename": "Announcement.php", "line": "?"}}}, "count": 334, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://sen.test/ztfz/sale", "action_name": "dcat.admin.sale.index", "controller_action": "App\\Admin\\Controllers\\WwUserController@index", "uri": "GET ztfz/sale", "controller": "App\\Admin\\Controllers\\WwUserController@index<a href=\"phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwUserController.php&line=65\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Admin\\Controllers", "prefix": "/ztfz", "file": "<a href=\"phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwUserController.php&line=65\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Admin/Controllers/WwUserController.php:65-101</a>", "middleware": "admin.app:admin, web, admin", "duration": "3.43s", "peak_memory": "6MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-4225769 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-4225769\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1117368476 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1117368476\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1972416214 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImVlcGNyMEU3T2xwYy9FNXJiRW96eVE9PSIsInZhbHVlIjoiOVFqZUU3U0VmZDZwZlpQaXRsc3RPM0YyZkh4d3VZeHlIcnBnZzN1RjlMNkozVWd6UjFpYlFBd1FvUXZ3a0pJYzF1Z1dYby95SThtbDErV1hoNGlUR09Fc1Ixcng0aWRwdk0wUnN1N0JlRFpmeEdWU0tHbUJnSXFmVGFHWDlpN1YiLCJtYWMiOiIxZGQwODQ5ODZhYmFiMTUxNmFkYWY4NTYyZmNhZTM1NDFkZDgxZmMwNGE5NWQ3YzZiMmM4NzQ3ZTA4NDA5YjZiIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InQrcldFcytlL2pCcDlscVYwUjZVNGc9PSIsInZhbHVlIjoiakh3VW50M0hjS3NjR0ZiSXpZQ240MnUvMmkvaU9oUVo4WnJ2bEtaTW4wMmZSWTlPL0NrTk1yaHQ4Mmt0RERHbzVlWTcvUWNaUkM0MUl3MUwwK1ZEdlhhWXA3cS8zeXErK0E4N094d1NTRW1oV0pvSTNqWkp1RWFRc2c0bXAzekkiLCJtYWMiOiIyODUyNzgyNDkyMjAwMTA3NmQ5YzZkMmUzZWFlNzQyYjAyMDE1OTFlZDdhNjVjNjg3NDY4ODM1OWU4ODhmZjgyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">zh-CN,zh;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">sen.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1972416214\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-363604825 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0esJyfQEGYXbNzItiQn8t75dzSvXZmiKqhcwIHBs</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Sc1mCMq4OSwvqqSk8mbaIttmOBzjSrhvaXfSjoYm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-363604825\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1152037799 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Aug 2025 05:30:06 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1152037799\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-929241828 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>prev</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://sen.test/ztfz/sale</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">admin.prev.url</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0esJyfQEGYXbNzItiQn8t75dzSvXZmiKqhcwIHBs</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://sen.test/ztfz/sale</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-929241828\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://sen.test/ztfz/sale", "action_name": "dcat.admin.sale.index", "controller_action": "App\\Admin\\Controllers\\WwUserController@index"}, "badge": null}}