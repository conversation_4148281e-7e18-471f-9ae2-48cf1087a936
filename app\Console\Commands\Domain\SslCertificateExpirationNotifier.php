<?php

namespace App\Console\Commands\Domain;
use App\Models\AdminDomain;
use App\Services\NotifySendService;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;

class SslCertificateExpirationNotifier extends Command
{
    protected $signature = 'ssl:notify-expiry';

    protected $description = '检查并通知管理员关于即将过期的SSL证书';


    public function handle(): int
    {
        /** 三天前日期 */
        $subDay = now()->subDays(3)->startOfDay();
        /** 当前日期 */
        $currentDateTime = now()->startOfDay();

        /** 查询三天内过期的 */
        $res = AdminDomain::query()
            ->with('adminInfo')
            ->whereBetween('ssl_ex_time', [$subDay, $currentDateTime])
            ->get();

        /** 是否为空 */
        if ($res->isEmpty()) {
            $this->info('没有即将过期的证书');
            return 0;
        }
        /** 转换为 Carbon 对象 */
        $carbonCurrentDate = Carbon::parse($currentDateTime);
        foreach ($res as $item) {
            /** 时间差 */
            $humanDiff = $carbonCurrentDate->diffForHumans($item->ssl_ex_time);
            /** 用户名 */
            $userName = $item->adminInfo->username;
            /** 发送通知 */
            NotifySendService::sendCustomerForMessageByRobots($item->adminInfo, '[证书到期提醒]' . PHP_EOL . "用户：【 $userName 】".PHP_EOL .$item->domain . ' ' . $humanDiff . '到期');
        }
        return 0;
    }

}
