{"__meta": {"id": "01K1J4J3F3Q2EAXCE7CJRVW7A4", "datetime": "2025-08-01 14:19:26", "utime": **********.05285, "method": "GET", "uri": "/ztfz/posting_link", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754029161.529662, "end": **********.05287, "duration": 4.523208141326904, "duration_str": "4.52s", "measures": [{"label": "Booting", "start": 1754029161.529662, "relative_start": 0, "end": **********.091816, "relative_end": **********.091816, "duration": 1.****************, "duration_str": "1.56s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.091841, "relative_start": 1.****************, "end": **********.052873, "relative_end": 2.86102294921875e-06, "duration": 2.***************, "duration_str": "2.96s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.149942, "relative_start": 1.****************, "end": **********.157355, "relative_end": **********.157355, "duration": 0.007413148880004883, "duration_str": "7.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: admin::grid.displayer.switch", "start": **********.960144, "relative_start": 3.****************, "end": **********.960144, "relative_end": **********.960144, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.switch", "start": **********.962261, "relative_start": 3.****************, "end": **********.962261, "relative_end": **********.962261, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.switch", "start": **********.963259, "relative_start": 3.****************, "end": **********.963259, "relative_end": **********.963259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.switch", "start": **********.964265, "relative_start": 3.434603214263916, "end": **********.964265, "relative_end": **********.964265, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.switch", "start": **********.965217, "relative_start": 3.4355552196502686, "end": **********.965217, "relative_end": **********.965217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.input", "start": 1754029165.252222, "relative_start": 3.722560167312622, "end": 1754029165.252222, "relative_end": 1754029165.252222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": 1754029165.253374, "relative_start": 3.723712205886841, "end": 1754029165.253374, "relative_end": 1754029165.253374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.input", "start": 1754029165.254703, "relative_start": 3.725041151046753, "end": 1754029165.254703, "relative_end": 1754029165.254703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": 1754029165.255219, "relative_start": 3.7255570888519287, "end": 1754029165.255219, "relative_end": 1754029165.255219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.input", "start": 1754029165.256156, "relative_start": 3.726494073867798, "end": 1754029165.256156, "relative_end": 1754029165.256156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": 1754029165.256643, "relative_start": 3.7269811630249023, "end": 1754029165.256643, "relative_end": 1754029165.256643, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.input", "start": 1754029165.25752, "relative_start": 3.727858066558838, "end": 1754029165.25752, "relative_end": 1754029165.25752, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": 1754029165.257979, "relative_start": 3.7283170223236084, "end": 1754029165.257979, "relative_end": 1754029165.257979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.input", "start": 1754029165.25941, "relative_start": 3.729748010635376, "end": 1754029165.25941, "relative_end": 1754029165.25941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": 1754029165.260439, "relative_start": 3.7307770252227783, "end": 1754029165.260439, "relative_end": 1754029165.260439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.textarea", "start": 1754029165.631018, "relative_start": 4.101356029510498, "end": 1754029165.631018, "relative_end": 1754029165.631018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": 1754029165.631897, "relative_start": 4.1022350788116455, "end": 1754029165.631897, "relative_end": 1754029165.631897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.textarea", "start": 1754029165.632914, "relative_start": 4.103252172470093, "end": 1754029165.632914, "relative_end": 1754029165.632914, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": 1754029165.633457, "relative_start": 4.103795051574707, "end": 1754029165.633457, "relative_end": 1754029165.633457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.textarea", "start": 1754029165.634485, "relative_start": 4.104823112487793, "end": 1754029165.634485, "relative_end": 1754029165.634485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": 1754029165.634977, "relative_start": 4.105315208435059, "end": 1754029165.634977, "relative_end": 1754029165.634977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.textarea", "start": 1754029165.635884, "relative_start": 4.106222152709961, "end": 1754029165.635884, "relative_end": 1754029165.635884, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": 1754029165.636348, "relative_start": 4.106686115264893, "end": 1754029165.636348, "relative_end": 1754029165.636348, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.textarea", "start": 1754029165.637219, "relative_start": 4.107557058334351, "end": 1754029165.637219, "relative_end": 1754029165.637219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": 1754029165.637686, "relative_start": 4.1080241203308105, "end": 1754029165.637686, "relative_end": 1754029165.637686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.fixed-table", "start": 1754029165.651324, "relative_start": 4.121662139892578, "end": 1754029165.651324, "relative_end": 1754029165.651324, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.table-toolbar", "start": 1754029165.652699, "relative_start": 4.123037099838257, "end": 1754029165.652699, "relative_end": 1754029165.652699, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.batch-actions", "start": 1754029165.65378, "relative_start": 4.124118089675903, "end": 1754029165.65378, "relative_end": 1754029165.65378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.button", "start": 1754029165.655245, "relative_start": 4.125583171844482, "end": 1754029165.655245, "relative_end": 1754029165.655245, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.form", "start": 1754029165.681329, "relative_start": 4.15166711807251, "end": 1754029165.681329, "relative_end": 1754029165.681329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.fields", "start": 1754029165.682994, "relative_start": 4.153331995010376, "end": 1754029165.682994, "relative_end": 1754029165.682994, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.display", "start": 1754029165.684619, "relative_start": 4.154957056045532, "end": 1754029165.684619, "relative_end": 1754029165.684619, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": 1754029165.686057, "relative_start": 4.156395196914673, "end": 1754029165.686057, "relative_end": 1754029165.686057, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.file", "start": 1754029165.687828, "relative_start": 4.158166170120239, "end": 1754029165.687828, "relative_end": 1754029165.687828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": 1754029165.689396, "relative_start": 4.159734010696411, "end": 1754029165.689396, "relative_end": 1754029165.689396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": 1754029165.690384, "relative_start": 4.160722017288208, "end": 1754029165.690384, "relative_end": 1754029165.690384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.hidden", "start": 1754029165.692077, "relative_start": 4.162415027618408, "end": 1754029165.692077, "relative_end": 1754029165.692077, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.hidden", "start": 1754029165.694126, "relative_start": 4.164463996887207, "end": 1754029165.694126, "relative_end": 1754029165.694126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.column-selector", "start": 1754029165.696534, "relative_start": 4.166872024536133, "end": 1754029165.696534, "relative_end": 1754029165.696534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.checkbox", "start": 1754029165.697124, "relative_start": 4.167462110519409, "end": 1754029165.697124, "relative_end": 1754029165.697124, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.checkbox", "start": 1754029165.698758, "relative_start": 4.169095993041992, "end": 1754029165.698758, "relative_end": 1754029165.698758, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.container", "start": 1754029165.701681, "relative_start": 4.172019004821777, "end": 1754029165.701681, "relative_end": 1754029165.701681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": 1754029165.703146, "relative_start": 4.1734840869903564, "end": 1754029165.703146, "relative_end": 1754029165.703146, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": 1754029165.704155, "relative_start": 4.174493074417114, "end": 1754029165.704155, "relative_end": 1754029165.704155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": 1754029165.705068, "relative_start": 4.175406217575073, "end": 1754029165.705068, "relative_end": 1754029165.705068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": 1754029165.705506, "relative_start": 4.175844192504883, "end": 1754029165.705506, "relative_end": 1754029165.705506, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": 1754029165.706186, "relative_start": 4.1765241622924805, "end": 1754029165.706186, "relative_end": 1754029165.706186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": 1754029165.707147, "relative_start": 4.17748498916626, "end": 1754029165.707147, "relative_end": 1754029165.707147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": 1754029165.708384, "relative_start": 4.178722143173218, "end": 1754029165.708384, "relative_end": 1754029165.708384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": 1754029165.710676, "relative_start": 4.181014060974121, "end": 1754029165.710676, "relative_end": 1754029165.710676, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": 1754029165.711334, "relative_start": 4.181672096252441, "end": 1754029165.711334, "relative_end": 1754029165.711334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": 1754029165.712132, "relative_start": 4.182470083236694, "end": 1754029165.712132, "relative_end": 1754029165.712132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": 1754029165.712597, "relative_start": 4.182934999465942, "end": 1754029165.712597, "relative_end": 1754029165.712597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": 1754029165.713316, "relative_start": 4.183654069900513, "end": 1754029165.713316, "relative_end": 1754029165.713316, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": 1754029165.713792, "relative_start": 4.1841301918029785, "end": 1754029165.713792, "relative_end": 1754029165.713792, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": 1754029165.714335, "relative_start": 4.184673070907593, "end": 1754029165.714335, "relative_end": 1754029165.714335, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": 1754029165.715149, "relative_start": 4.1854870319366455, "end": 1754029165.715149, "relative_end": 1754029165.715149, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": 1754029165.715605, "relative_start": 4.185943126678467, "end": 1754029165.715605, "relative_end": 1754029165.715605, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": 1754029165.716142, "relative_start": 4.1864800453186035, "end": 1754029165.716142, "relative_end": 1754029165.716142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": 1754029165.716932, "relative_start": 4.187270164489746, "end": 1754029165.716932, "relative_end": 1754029165.716932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": 1754029165.717402, "relative_start": 4.187740087509155, "end": 1754029165.717402, "relative_end": 1754029165.717402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": 1754029165.718091, "relative_start": 4.188429117202759, "end": 1754029165.718091, "relative_end": 1754029165.718091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": 1754029165.718534, "relative_start": 4.1888720989227295, "end": 1754029165.718534, "relative_end": 1754029165.718534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": 1754029165.719025, "relative_start": 4.1893630027771, "end": 1754029165.719025, "relative_end": 1754029165.719025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.table-pagination", "start": 1754029165.722057, "relative_start": 4.192395210266113, "end": 1754029165.722057, "relative_end": 1754029165.722057, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.pagination", "start": 1754029165.724812, "relative_start": 4.195150136947632, "end": 1754029165.724812, "relative_end": 1754029165.724812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.dropdown", "start": 1754029165.728301, "relative_start": 4.198639154434204, "end": 1754029165.728301, "relative_end": 1754029165.728301, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::layouts.content", "start": 1754029165.73003, "relative_start": 4.200368165969849, "end": 1754029165.73003, "relative_end": 1754029165.73003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.breadcrumb", "start": 1754029165.731267, "relative_start": 4.2016050815582275, "end": 1754029165.731267, "relative_end": 1754029165.731267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.alerts", "start": 1754029165.732485, "relative_start": 4.202823162078857, "end": 1754029165.732485, "relative_end": 1754029165.732485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.exception", "start": 1754029165.733596, "relative_start": 4.203934192657471, "end": 1754029165.733596, "relative_end": 1754029165.733596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.toastr", "start": 1754029165.734626, "relative_start": 4.2049641609191895, "end": 1754029165.734626, "relative_end": 1754029165.734626, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::layouts.page", "start": 1754029165.736259, "relative_start": 4.206597089767456, "end": 1754029165.736259, "relative_end": 1754029165.736259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::layouts.container", "start": 1754029165.738126, "relative_start": 4.2084641456604, "end": 1754029165.738126, "relative_end": 1754029165.738126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.sidebar", "start": 1754029165.739358, "relative_start": 4.209696054458618, "end": 1754029165.739358, "relative_end": 1754029165.739358, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.972429, "relative_start": 4.442767143249512, "end": 1754029165.972429, "relative_end": 1754029165.972429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.975233, "relative_start": 4.445571184158325, "end": 1754029165.975233, "relative_end": 1754029165.975233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.977482, "relative_start": 4.44782018661499, "end": 1754029165.977482, "relative_end": 1754029165.977482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.97886, "relative_start": 4.449198007583618, "end": 1754029165.97886, "relative_end": 1754029165.97886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.979873, "relative_start": 4.450211048126221, "end": 1754029165.979873, "relative_end": 1754029165.979873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.980727, "relative_start": 4.4510650634765625, "end": 1754029165.980727, "relative_end": 1754029165.980727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.981521, "relative_start": 4.451858997344971, "end": 1754029165.981521, "relative_end": 1754029165.981521, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.982193, "relative_start": 4.452531099319458, "end": 1754029165.982193, "relative_end": 1754029165.982193, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.983534, "relative_start": 4.453872203826904, "end": 1754029165.983534, "relative_end": 1754029165.983534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.984898, "relative_start": 4.455236196517944, "end": 1754029165.984898, "relative_end": 1754029165.984898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.985676, "relative_start": 4.456014156341553, "end": 1754029165.985676, "relative_end": 1754029165.985676, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.986309, "relative_start": 4.456647157669067, "end": 1754029165.986309, "relative_end": 1754029165.986309, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.986914, "relative_start": 4.457252025604248, "end": 1754029165.986914, "relative_end": 1754029165.986914, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.987492, "relative_start": 4.457830190658569, "end": 1754029165.987492, "relative_end": 1754029165.987492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.988108, "relative_start": 4.458446025848389, "end": 1754029165.988108, "relative_end": 1754029165.988108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.988696, "relative_start": 4.459034204483032, "end": 1754029165.988696, "relative_end": 1754029165.988696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.989275, "relative_start": 4.459613084793091, "end": 1754029165.989275, "relative_end": 1754029165.989275, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.989866, "relative_start": 4.460204124450684, "end": 1754029165.989866, "relative_end": 1754029165.989866, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.990431, "relative_start": 4.460769176483154, "end": 1754029165.990431, "relative_end": 1754029165.990431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.991032, "relative_start": 4.46136999130249, "end": 1754029165.991032, "relative_end": 1754029165.991032, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.991662, "relative_start": 4.462000131607056, "end": 1754029165.991662, "relative_end": 1754029165.991662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.992257, "relative_start": 4.462595224380493, "end": 1754029165.992257, "relative_end": 1754029165.992257, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.994358, "relative_start": 4.464696168899536, "end": 1754029165.994358, "relative_end": 1754029165.994358, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.995536, "relative_start": 4.465874195098877, "end": 1754029165.995536, "relative_end": 1754029165.995536, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.996301, "relative_start": 4.466639041900635, "end": 1754029165.996301, "relative_end": 1754029165.996301, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.99695, "relative_start": 4.467288017272949, "end": 1754029165.99695, "relative_end": 1754029165.99695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.997558, "relative_start": 4.467896223068237, "end": 1754029165.997558, "relative_end": 1754029165.997558, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.998147, "relative_start": 4.468485116958618, "end": 1754029165.998147, "relative_end": 1754029165.998147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.998736, "relative_start": 4.469074010848999, "end": 1754029165.998736, "relative_end": 1754029165.998736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": 1754029165.999363, "relative_start": 4.469701051712036, "end": 1754029165.999363, "relative_end": 1754029165.999363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.000007, "relative_start": 4.4703450202941895, "end": **********.000007, "relative_end": **********.000007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.000659, "relative_start": 4.470997095108032, "end": **********.000659, "relative_end": **********.000659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.001187, "relative_start": 4.471525192260742, "end": **********.001187, "relative_end": **********.001187, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.001763, "relative_start": 4.472101211547852, "end": **********.001763, "relative_end": **********.001763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.002309, "relative_start": 4.472647190093994, "end": **********.002309, "relative_end": **********.002309, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.002916, "relative_start": 4.473254203796387, "end": **********.002916, "relative_end": **********.002916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.003489, "relative_start": 4.473827123641968, "end": **********.003489, "relative_end": **********.003489, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.004074, "relative_start": 4.474412202835083, "end": **********.004074, "relative_end": **********.004074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.004643, "relative_start": 4.474981069564819, "end": **********.004643, "relative_end": **********.004643, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.005195, "relative_start": 4.4755330085754395, "end": **********.005195, "relative_end": **********.005195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.005773, "relative_start": 4.476111173629761, "end": **********.005773, "relative_end": **********.005773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.006366, "relative_start": 4.476704120635986, "end": **********.006366, "relative_end": **********.006366, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.006942, "relative_start": 4.477280139923096, "end": **********.006942, "relative_end": **********.006942, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.007525, "relative_start": 4.477863073348999, "end": **********.007525, "relative_end": **********.007525, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.008081, "relative_start": 4.478419065475464, "end": **********.008081, "relative_end": **********.008081, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.008705, "relative_start": 4.479043006896973, "end": **********.008705, "relative_end": **********.008705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.010726, "relative_start": 4.481064081192017, "end": **********.010726, "relative_end": **********.010726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.011971, "relative_start": 4.482309103012085, "end": **********.011971, "relative_end": **********.011971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.012892, "relative_start": 4.483230113983154, "end": **********.012892, "relative_end": **********.012892, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.013539, "relative_start": 4.483877182006836, "end": **********.013539, "relative_end": **********.013539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.014119, "relative_start": 4.484457015991211, "end": **********.014119, "relative_end": **********.014119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.014848, "relative_start": 4.4851861000061035, "end": **********.014848, "relative_end": **********.014848, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.015877, "relative_start": 4.486215114593506, "end": **********.015877, "relative_end": **********.015877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.016866, "relative_start": 4.487204074859619, "end": **********.016866, "relative_end": **********.016866, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.017801, "relative_start": 4.4881391525268555, "end": **********.017801, "relative_end": **********.017801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.018668, "relative_start": 4.489006042480469, "end": **********.018668, "relative_end": **********.018668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.019535, "relative_start": 4.489873170852661, "end": **********.019535, "relative_end": **********.019535, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.036447, "relative_start": 4.506785154342651, "end": **********.036447, "relative_end": **********.036447, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.037503, "relative_start": 4.507841110229492, "end": **********.037503, "relative_end": **********.037503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.038217, "relative_start": 4.508555173873901, "end": **********.038217, "relative_end": **********.038217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.038881, "relative_start": 4.509219169616699, "end": **********.038881, "relative_end": **********.038881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.039652, "relative_start": 4.509990215301514, "end": **********.039652, "relative_end": **********.039652, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.040261, "relative_start": 4.510599136352539, "end": **********.040261, "relative_end": **********.040261, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.040819, "relative_start": 4.511157035827637, "end": **********.040819, "relative_end": **********.040819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.041357, "relative_start": 4.511695146560669, "end": **********.041357, "relative_end": **********.041357, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.navbar", "start": **********.042834, "relative_start": 4.513172149658203, "end": **********.042834, "relative_end": **********.042834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: announcements.index", "start": **********.045247, "relative_start": 4.51558518409729, "end": **********.045247, "relative_end": **********.045247, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.navbar-user-panel", "start": **********.04726, "relative_start": 4.5175981521606445, "end": **********.04726, "relative_end": **********.04726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 34974560, "peak_usage_str": "33MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.20", "PHP Version": "8.0.2", "Environment": "local", "Debug Mode": "Enabled", "URL": "sen.test", "Timezone": "PRC", "Locale": "zh_CN"}}, "views": {"count": 143, "nb_templates": 143, "templates": [{"name": "5x admin::grid.displayer.switch", "param_count": null, "params": [], "start": **********.960013, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/switch.blade.phpadmin::grid.displayer.switch", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Fswitch.blade.php&line=1", "ajax": false, "filename": "switch.blade.php", "line": "?"}, "render_count": 5, "name_original": "admin::grid.displayer.switch"}, {"name": "5x admin::grid.displayer.editinline.input", "param_count": null, "params": [], "start": 1754029165.252118, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/editinline/input.blade.phpadmin::grid.displayer.editinline.input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Feditinline%2Finput.blade.php&line=1", "ajax": false, "filename": "input.blade.php", "line": "?"}, "render_count": 5, "name_original": "admin::grid.displayer.editinline.input"}, {"name": "10x admin::grid.displayer.editinline.template", "param_count": null, "params": [], "start": 1754029165.253285, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/editinline/template.blade.phpadmin::grid.displayer.editinline.template", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Feditinline%2Ftemplate.blade.php&line=1", "ajax": false, "filename": "template.blade.php", "line": "?"}, "render_count": 10, "name_original": "admin::grid.displayer.editinline.template"}, {"name": "5x admin::grid.displayer.editinline.textarea", "param_count": null, "params": [], "start": 1754029165.630934, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/editinline/textarea.blade.phpadmin::grid.displayer.editinline.textarea", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Feditinline%2Ftextarea.blade.php&line=1", "ajax": false, "filename": "textarea.blade.php", "line": "?"}, "render_count": 5, "name_original": "admin::grid.displayer.editinline.textarea"}, {"name": "1x admin::grid.fixed-table", "param_count": null, "params": [], "start": 1754029165.65123, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/fixed-table.blade.phpadmin::grid.fixed-table", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Ffixed-table.blade.php&line=1", "ajax": false, "filename": "fixed-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.fixed-table"}, {"name": "1x admin::grid.table-toolbar", "param_count": null, "params": [], "start": 1754029165.652608, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/table-toolbar.blade.phpadmin::grid.table-toolbar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Ftable-toolbar.blade.php&line=1", "ajax": false, "filename": "table-toolbar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.table-toolbar"}, {"name": "1x admin::grid.batch-actions", "param_count": null, "params": [], "start": 1754029165.653703, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/batch-actions.blade.phpadmin::grid.batch-actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fbatch-actions.blade.php&line=1", "ajax": false, "filename": "batch-actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.batch-actions"}, {"name": "1x admin::filter.button", "param_count": null, "params": [], "start": 1754029165.655157, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/button.blade.phpadmin::filter.button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::filter.button"}, {"name": "1x admin::widgets.form", "param_count": null, "params": [], "start": 1754029165.681205, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/form.blade.phpadmin::widgets.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::widgets.form"}, {"name": "1x admin::form.fields", "param_count": null, "params": [], "start": 1754029165.682875, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/fields.blade.phpadmin::form.fields", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ffields.blade.php&line=1", "ajax": false, "filename": "fields.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.fields"}, {"name": "1x admin::form.display", "param_count": null, "params": [], "start": 1754029165.684496, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/display.blade.phpadmin::form.display", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fdisplay.blade.php&line=1", "ajax": false, "filename": "display.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.display"}, {"name": "2x admin::form.help-block", "param_count": null, "params": [], "start": 1754029165.68594, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/help-block.blade.phpadmin::form.help-block", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fhelp-block.blade.php&line=1", "ajax": false, "filename": "help-block.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::form.help-block"}, {"name": "1x admin::form.file", "param_count": null, "params": [], "start": 1754029165.687714, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/file.blade.phpadmin::form.file", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ffile.blade.php&line=1", "ajax": false, "filename": "file.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.file"}, {"name": "1x admin::form.error", "param_count": null, "params": [], "start": 1754029165.68928, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/error.blade.phpadmin::form.error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.error"}, {"name": "2x admin::form.hidden", "param_count": null, "params": [], "start": 1754029165.691954, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/hidden.blade.phpadmin::form.hidden", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fhidden.blade.php&line=1", "ajax": false, "filename": "hidden.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::form.hidden"}, {"name": "1x admin::grid.column-selector", "param_count": null, "params": [], "start": 1754029165.696451, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/column-selector.blade.phpadmin::grid.column-selector", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fcolumn-selector.blade.php&line=1", "ajax": false, "filename": "column-selector.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.column-selector"}, {"name": "2x admin::widgets.checkbox", "param_count": null, "params": [], "start": 1754029165.697046, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/checkbox.blade.phpadmin::widgets.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::widgets.checkbox"}, {"name": "1x admin::filter.container", "param_count": null, "params": [], "start": 1754029165.701589, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/container.blade.phpadmin::filter.container", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fcontainer.blade.php&line=1", "ajax": false, "filename": "container.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::filter.container"}, {"name": "9x admin::filter.where", "param_count": null, "params": [], "start": 1754029165.703069, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/where.blade.phpadmin::filter.where", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fwhere.blade.php&line=1", "ajax": false, "filename": "where.blade.php", "line": "?"}, "render_count": 9, "name_original": "admin::filter.where"}, {"name": "5x admin::filter.text", "param_count": null, "params": [], "start": 1754029165.70407, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/text.blade.phpadmin::filter.text", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Ftext.blade.php&line=1", "ajax": false, "filename": "text.blade.php", "line": "?"}, "render_count": 5, "name_original": "admin::filter.text"}, {"name": "4x admin::filter.select", "param_count": null, "params": [], "start": 1754029165.707034, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/select.blade.phpadmin::filter.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 4, "name_original": "admin::filter.select"}, {"name": "4x admin::scripts.select", "param_count": null, "params": [], "start": 1754029165.70827, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/scripts/select.blade.phpadmin::scripts.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fscripts%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 4, "name_original": "admin::scripts.select"}, {"name": "1x admin::grid.table-pagination", "param_count": null, "params": [], "start": 1754029165.721935, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/table-pagination.blade.phpadmin::grid.table-pagination", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Ftable-pagination.blade.php&line=1", "ajax": false, "filename": "table-pagination.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.table-pagination"}, {"name": "1x admin::grid.pagination", "param_count": null, "params": [], "start": 1754029165.724692, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/pagination.blade.phpadmin::grid.pagination", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fpagination.blade.php&line=1", "ajax": false, "filename": "pagination.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.pagination"}, {"name": "1x admin::widgets.dropdown", "param_count": null, "params": [], "start": 1754029165.728155, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/dropdown.blade.phpadmin::widgets.dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fdropdown.blade.php&line=1", "ajax": false, "filename": "dropdown.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::widgets.dropdown"}, {"name": "1x admin::layouts.content", "param_count": null, "params": [], "start": 1754029165.729936, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/layouts/content.blade.phpadmin::layouts.content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Flayouts%2Fcontent.blade.php&line=1", "ajax": false, "filename": "content.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::layouts.content"}, {"name": "1x admin::partials.breadcrumb", "param_count": null, "params": [], "start": 1754029165.731172, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/breadcrumb.blade.phpadmin::partials.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.breadcrumb"}, {"name": "1x admin::partials.alerts", "param_count": null, "params": [], "start": 1754029165.732391, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/alerts.blade.phpadmin::partials.alerts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Falerts.blade.php&line=1", "ajax": false, "filename": "alerts.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.alerts"}, {"name": "1x admin::partials.exception", "param_count": null, "params": [], "start": 1754029165.73352, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/exception.blade.phpadmin::partials.exception", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fexception.blade.php&line=1", "ajax": false, "filename": "exception.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.exception"}, {"name": "1x admin::partials.toastr", "param_count": null, "params": [], "start": 1754029165.73455, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/toastr.blade.phpadmin::partials.toastr", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Ftoastr.blade.php&line=1", "ajax": false, "filename": "toastr.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.toastr"}, {"name": "1x admin::layouts.page", "param_count": null, "params": [], "start": 1754029165.736166, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/layouts/page.blade.phpadmin::layouts.page", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Flayouts%2Fpage.blade.php&line=1", "ajax": false, "filename": "page.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::layouts.page"}, {"name": "1x admin::layouts.container", "param_count": null, "params": [], "start": 1754029165.738019, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\resources\\views/vendor/admin/layouts/container.blade.phpadmin::layouts.container", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fresources%2Fviews%2Fvendor%2Fadmin%2Flayouts%2Fcontainer.blade.php&line=1", "ajax": false, "filename": "container.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::layouts.container"}, {"name": "1x admin::partials.sidebar", "param_count": null, "params": [], "start": 1754029165.739275, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/sidebar.blade.phpadmin::partials.sidebar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.sidebar"}, {"name": "65x admin::partials.menu", "param_count": null, "params": [], "start": 1754029165.972299, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/menu.blade.phpadmin::partials.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 65, "name_original": "admin::partials.menu"}, {"name": "1x admin::partials.navbar", "param_count": null, "params": [], "start": **********.042647, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/navbar.blade.phpadmin::partials.navbar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fnavbar.blade.php&line=1", "ajax": false, "filename": "navbar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.navbar"}, {"name": "1x announcements.index", "param_count": null, "params": [], "start": **********.045058, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\resources\\views/announcements/index.blade.phpannouncements.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fresources%2Fviews%2Fannouncements%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "announcements.index"}, {"name": "1x admin::partials.navbar-user-panel", "param_count": null, "params": [], "start": **********.047134, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/navbar-user-panel.blade.phpadmin::partials.navbar-user-panel", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fnavbar-user-panel.blade.php&line=1", "ajax": false, "filename": "navbar-user-panel.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.navbar-user-panel"}]}, "queries": {"count": 37, "nb_statements": 36, "nb_visible_statements": 37, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 2.0298700000000003, "accumulated_duration_str": "2.03s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Admin.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", "line": 145}, {"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 36}], "start": **********.279318, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "wind_rich", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `wr_admin_users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Admin.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", "line": 145}, {"index": 18, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.29807, "duration": 0.25212, "duration_str": "252ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wind_rich", "explain": null, "start_percent": 0, "width_percent": 12.42}, {"sql": "insert into `wr_operation_logs` (`admin_uid`, `user_name`, `path`, `method`, `ip`, `input`, `updated_at`, `created_at`) values (1, 'ZtFzSuperAdminPro', 'ztfz/posting_link', 'GET', '127.0.0.1', '[]', '2025-08-01 14:19:23', '2025-08-01 14:19:23')", "type": "query", "params": [], "bindings": [1, "ZtFzSuperAdminPro", "ztfz/posting_link", "GET", "127.0.0.1", "[]", "2025-08-01 14:19:23", "2025-08-01 14:19:23"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 51}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.561678, "duration": 0.053689999999999995, "duration_str": "53.69ms", "memory": 0, "memory_str": null, "filename": "OpRecord.php:51", "source": {"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FHttp%2FMiddleware%2FOpRecord.php&line=51", "ajax": false, "filename": "OpRecord.php", "line": "51"}, "connection": "wind_rich", "explain": null, "start_percent": 12.42, "width_percent": 2.645}, {"sql": "select * from `wr_announcements` where `wr_announcements`.`deleted_at` is null order by `id` desc limit 7", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Announcement.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\Announcement.php", "line": 24}, {"index": 15, "namespace": null, "name": "app/Admin/bootstrap.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\bootstrap.php", "line": 62}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Admin.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", "line": 164}, {"index": 17, "namespace": null, "name": "app/Admin/bootstrap.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\bootstrap.php", "line": 80}, {"index": 19, "namespace": "middleware", "name": "admin.bootstrap", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\Bootstrap.php", "line": 14}], "start": **********.6338742, "duration": 0.0489, "duration_str": "48.9ms", "memory": 0, "memory_str": null, "filename": "Announcement.php:24", "source": {"index": 14, "namespace": null, "name": "app/Models/Announcement.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\Announcement.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAnnouncement.php&line=24", "ajax": false, "filename": "Announcement.php", "line": "24"}, "connection": "wind_rich", "explain": null, "start_percent": 15.065, "width_percent": 2.409}, {"sql": "select `wr_admin_roles`.*, `wr_admin_role_users`.`user_id` as `pivot_user_id`, `wr_admin_role_users`.`role_id` as `pivot_role_id`, `wr_admin_role_users`.`created_at` as `pivot_created_at`, `wr_admin_role_users`.`updated_at` as `pivot_updated_at` from `wr_admin_roles` inner join `wr_admin_role_users` on `wr_admin_roles`.`id` = `wr_admin_role_users`.`role_id` where `wr_admin_role_users`.`user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/HasPermissions.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\HasPermissions.php", "line": 88}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/HasPermissions.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\HasPermissions.php", "line": 76}, {"index": 21, "namespace": "middleware", "name": "admin.permission", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\Permission.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 23, "namespace": "middleware", "name": "admin.bootstrap", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\Bootstrap.php", "line": 19}], "start": **********.815629, "duration": 0.04984, "duration_str": "49.84ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:88", "source": {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/HasPermissions.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\HasPermissions.php", "line": 88}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FHasPermissions.php&line=88", "ajax": false, "filename": "HasPermissions.php", "line": "88"}, "connection": "wind_rich", "explain": null, "start_percent": 17.475, "width_percent": 2.455}, {"sql": "select `id`, `parent_id` from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 52}, {"index": 17, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 259}, {"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 303}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.9918408, "duration": 0.05099, "duration_str": "50.99ms", "memory": 0, "memory_str": null, "filename": "AdminSubUser.php:52", "source": {"index": 16, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUser.php&line=52", "ajax": false, "filename": "AdminSubUser.php", "line": "52"}, "connection": "wind_rich", "explain": null, "start_percent": 19.93, "width_percent": 2.512}, {"sql": "select `id` from `wr_admin_users` where `parent_id` = 1 and `status` = 1 and `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 57}, {"index": 14, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 259}, {"index": 17, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 303}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.051365, "duration": 0.04918, "duration_str": "49.18ms", "memory": 0, "memory_str": null, "filename": "AdminSubUser.php:57", "source": {"index": 13, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUser.php&line=57", "ajax": false, "filename": "AdminSubUser.php", "line": "57"}, "connection": "wind_rich", "explain": null, "start_percent": 22.442, "width_percent": 2.423}, {"sql": "select `username`, `id` from `wr_admin_users` where `id` in (1) and `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 260}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 303}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 1008}], "start": **********.104909, "duration": 0.04957, "duration_str": "49.57ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:260", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 260}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=260", "ajax": false, "filename": "WwLinkController.php", "line": "260"}, "connection": "wind_rich", "explain": null, "start_percent": 24.865, "width_percent": 2.442}, {"sql": "select `id`, `parent_id` from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 274}, {"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 303}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.167813, "duration": 0.05515, "duration_str": "55.15ms", "memory": 0, "memory_str": null, "filename": "AdminSubUser.php:35", "source": {"index": 16, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUser.php&line=35", "ajax": false, "filename": "AdminSubUser.php", "line": "35"}, "connection": "wind_rich", "explain": null, "start_percent": 27.307, "width_percent": 2.717}, {"sql": "select `id` from `wr_admin_users` where `parent_id` = 1 and `status` = 1 and `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 39}, {"index": 14, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 274}, {"index": 17, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 303}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.227879, "duration": 0.049909999999999996, "duration_str": "49.91ms", "memory": 0, "memory_str": null, "filename": "AdminSubUser.php:39", "source": {"index": 13, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUser.php&line=39", "ajax": false, "filename": "AdminSubUser.php", "line": "39"}, "connection": "wind_rich", "explain": null, "start_percent": 30.024, "width_percent": 2.459}, {"sql": "select `ww_user_group_ids` from `wr_admin_sub_users` where `admin_uid` = 1 and `wr_admin_sub_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 276}, {"index": 19, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 303}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 1008}], "start": **********.283438, "duration": 0.05046, "duration_str": "50.46ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:276", "source": {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=276", "ajax": false, "filename": "WwLinkController.php", "line": "276"}, "connection": "wind_rich", "explain": null, "start_percent": 32.482, "width_percent": 2.486}, {"sql": "select `id`, `parent_id` from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 295}, {"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 303}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.338036, "duration": 0.05049, "duration_str": "50.49ms", "memory": 0, "memory_str": null, "filename": "AdminSubUser.php:35", "source": {"index": 16, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUser.php&line=35", "ajax": false, "filename": "AdminSubUser.php", "line": "35"}, "connection": "wind_rich", "explain": null, "start_percent": 34.968, "width_percent": 2.487}, {"sql": "select `id` from `wr_admin_users` where `parent_id` = 1 and `status` = 1 and `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 39}, {"index": 14, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 295}, {"index": 17, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 303}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.393348, "duration": 0.05253, "duration_str": "52.53ms", "memory": 0, "memory_str": null, "filename": "AdminSubUser.php:39", "source": {"index": 13, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUser.php&line=39", "ajax": false, "filename": "AdminSubUser.php", "line": "39"}, "connection": "wind_rich", "explain": null, "start_percent": 37.456, "width_percent": 2.588}, {"sql": "select `title`, `id` from `wr_ww_users_groups` where `admin_uid` in (1) and `type` = 1 and `admin_uid` in (1) and `wr_ww_users_groups`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 297}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 303}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 1008}], "start": **********.450669, "duration": 0.056909999999999995, "duration_str": "56.91ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:297", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 297}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=297", "ajax": false, "filename": "WwLinkController.php", "line": "297"}, "connection": "wind_rich", "explain": null, "start_percent": 40.043, "width_percent": 2.804}, {"sql": "select count(*) as aggregate from `wr_ww_link` where `wr_ww_link`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.5166018, "duration": 0.04957, "duration_str": "49.57ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 42.847, "width_percent": 2.442}, {"sql": "select * from `wr_ww_link` where `wr_ww_link`.`deleted_at` is null order by `id` desc limit 20 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.569868, "duration": 0.0518, "duration_str": "51.8ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 45.289, "width_percent": 2.552}, {"sql": "select * from `wr_ad_account` where `wr_ad_account`.`account_id` in ('********', '********', '********', '********', '********') and `wr_ad_account`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["********", "********", "********", "********", "********"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.6275742, "duration": 0.0496, "duration_str": "49.6ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 47.841, "width_percent": 2.444}, {"sql": "select * from `wr_ww_tpl` where `wr_ww_tpl`.`id` in (1, 3) and `wr_ww_tpl`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.6826189, "duration": 0.04936, "duration_str": "49.36ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 50.285, "width_percent": 2.432}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` in (1, 4) and `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.734682, "duration": 0.04845, "duration_str": "48.45ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 52.716, "width_percent": 2.387}, {"sql": "select * from `wr_ww_users_groups` where `wr_ww_users_groups`.`id` in (1, 6) and `wr_ww_users_groups`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.786942, "duration": 0.0505, "duration_str": "50.5ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 55.103, "width_percent": 2.488}, {"sql": "select * from `wr_shield_policies` where `wr_shield_policies`.`id` in (1, 2) and `wr_shield_policies`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.84094, "duration": 0.049640000000000004, "duration_str": "49.64ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 57.591, "width_percent": 2.445}, {"sql": "select * from `wr_ww_tpl` where `wr_ww_tpl`.`id` in (2, 4) and `wr_ww_tpl`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 29, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 31, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 32, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.894512, "duration": 0.05017, "duration_str": "50.17ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 28, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 60.036, "width_percent": 2.472}, {"sql": "select * from `wr_admin_domains` where `status` = 1 and `admin_uid` = 1 and `wr_admin_domains`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 129}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}], "start": **********.9673889, "duration": 0.06814, "duration_str": "68.14ms", "memory": 0, "memory_str": null, "filename": "AdminDomain.php:56", "source": {"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminDomain.php&line=56", "ajax": false, "filename": "AdminDomain.php", "line": "56"}, "connection": "wind_rich", "explain": null, "start_percent": 62.508, "width_percent": 3.357}, {"sql": "select * from `wr_admin_domains` where `status` = 1 and `admin_uid` = 1 and `wr_admin_domains`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 129}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}], "start": 1754029165.0416229, "duration": 0.049530000000000005, "duration_str": "49.53ms", "memory": 0, "memory_str": null, "filename": "AdminDomain.php:56", "source": {"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminDomain.php&line=56", "ajax": false, "filename": "AdminDomain.php", "line": "56"}, "connection": "wind_rich", "explain": null, "start_percent": 65.865, "width_percent": 2.44}, {"sql": "select * from `wr_admin_domains` where `status` = 1 and `admin_uid` = 1 and `wr_admin_domains`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 129}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}], "start": 1754029165.0960472, "duration": 0.04965, "duration_str": "49.65ms", "memory": 0, "memory_str": null, "filename": "AdminDomain.php:56", "source": {"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminDomain.php&line=56", "ajax": false, "filename": "AdminDomain.php", "line": "56"}, "connection": "wind_rich", "explain": null, "start_percent": 68.305, "width_percent": 2.446}, {"sql": "select * from `wr_admin_domains` where `status` = 1 and `admin_uid` = 1 and `wr_admin_domains`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 129}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}], "start": 1754029165.14846, "duration": 0.04862, "duration_str": "48.62ms", "memory": 0, "memory_str": null, "filename": "AdminDomain.php:56", "source": {"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminDomain.php&line=56", "ajax": false, "filename": "AdminDomain.php", "line": "56"}, "connection": "wind_rich", "explain": null, "start_percent": 70.751, "width_percent": 2.395}, {"sql": "select * from `wr_admin_domains` where `status` = 1 and `admin_uid` = 1 and `wr_admin_domains`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 129}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}], "start": 1754029165.1998959, "duration": 0.04906, "duration_str": "49.06ms", "memory": 0, "memory_str": null, "filename": "AdminDomain.php:56", "source": {"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminDomain.php&line=56", "ajax": false, "filename": "AdminDomain.php", "line": "56"}, "connection": "wind_rich", "explain": null, "start_percent": 73.146, "width_percent": 2.417}, {"sql": "select * from `wr_ww_users_groups` where `wr_ww_users_groups`.`id` = 7 and `wr_ww_users_groups`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 156}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 28, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 29, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": 1754029165.263248, "duration": 0.04871, "duration_str": "48.71ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:156", "source": {"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 156}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=156", "ajax": false, "filename": "WwLinkController.php", "line": "156"}, "connection": "wind_rich", "explain": null, "start_percent": 75.563, "width_percent": 2.4}, {"sql": "select `merge_name`, `id` from `wr_area` where 0 = 1 and `wr_area`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 164}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": 1754029165.3147738, "duration": 0.04957, "duration_str": "49.57ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:164", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=164", "ajax": false, "filename": "WwLinkController.php", "line": "164"}, "connection": "wind_rich", "explain": null, "start_percent": 77.963, "width_percent": 2.442}, {"sql": "select * from `wr_ww_users_groups` where `wr_ww_users_groups`.`id` = 2 and `wr_ww_users_groups`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 156}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 28, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 29, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": 1754029165.3677812, "duration": 0.04925, "duration_str": "49.25ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:156", "source": {"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 156}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=156", "ajax": false, "filename": "WwLinkController.php", "line": "156"}, "connection": "wind_rich", "explain": null, "start_percent": 80.405, "width_percent": 2.426}, {"sql": "select `merge_name`, `id` from `wr_area` where `id` in ('1', '192', '2648', '3642') and `wr_area`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "192", "2648", "3642"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 164}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": 1754029165.4193492, "duration": 0.04925, "duration_str": "49.25ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:164", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=164", "ajax": false, "filename": "WwLinkController.php", "line": "164"}, "connection": "wind_rich", "explain": null, "start_percent": 82.831, "width_percent": 2.426}, {"sql": "select `merge_name`, `id` from `wr_area` where `id` in ('1', '192', '2648', '3642') and `wr_area`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "192", "2648", "3642"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 164}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": 1754029165.4714692, "duration": 0.049100000000000005, "duration_str": "49.1ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:164", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=164", "ajax": false, "filename": "WwLinkController.php", "line": "164"}, "connection": "wind_rich", "explain": null, "start_percent": 85.257, "width_percent": 2.419}, {"sql": "select `merge_name`, `id` from `wr_area` where `id` in ('1', '192', '2648', '3642') and `wr_area`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "192", "2648", "3642"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 164}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": 1754029165.523586, "duration": 0.049479999999999996, "duration_str": "49.48ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:164", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=164", "ajax": false, "filename": "WwLinkController.php", "line": "164"}, "connection": "wind_rich", "explain": null, "start_percent": 87.676, "width_percent": 2.438}, {"sql": "select `merge_name`, `id` from `wr_area` where `id` in ('1', '192', '2648', '3642') and `wr_area`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "192", "2648", "3642"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 164}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": 1754029165.576801, "duration": 0.04936, "duration_str": "49.36ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:164", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=164", "ajax": false, "filename": "WwLinkController.php", "line": "164"}, "connection": "wind_rich", "explain": null, "start_percent": 90.114, "width_percent": 2.432}, {"sql": "select * from `wr_admin_menu` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 115}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/MenuCache.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\MenuCache.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 99}], "start": 1754029165.741234, "duration": 0.04985, "duration_str": "49.85ms", "memory": 0, "memory_str": null, "filename": "ModelTree.php:123", "source": {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FModelTree.php&line=123", "ajax": false, "filename": "ModelTree.php", "line": "123"}, "connection": "wind_rich", "explain": null, "start_percent": 92.545, "width_percent": 2.456}, {"sql": "select `wr_admin_permissions`.*, `wr_admin_permission_menu`.`menu_id` as `pivot_menu_id`, `wr_admin_permission_menu`.`permission_id` as `pivot_permission_id`, `wr_admin_permission_menu`.`created_at` as `pivot_created_at`, `wr_admin_permission_menu`.`updated_at` as `pivot_updated_at` from `wr_admin_permissions` inner join `wr_admin_permission_menu` on `wr_admin_permissions`.`id` = `wr_admin_permission_menu`.`permission_id` where `wr_admin_permission_menu`.`menu_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 115}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 98}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/MenuCache.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\MenuCache.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 99}], "start": 1754029165.800495, "duration": 0.05039, "duration_str": "50.39ms", "memory": 0, "memory_str": null, "filename": "ModelTree.php:123", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FModelTree.php&line=123", "ajax": false, "filename": "ModelTree.php", "line": "123"}, "connection": "wind_rich", "explain": null, "start_percent": 95.001, "width_percent": 2.482}, {"sql": "select `wr_admin_roles`.*, `wr_admin_role_menu`.`menu_id` as `pivot_menu_id`, `wr_admin_role_menu`.`role_id` as `pivot_role_id`, `wr_admin_role_menu`.`created_at` as `pivot_created_at`, `wr_admin_role_menu`.`updated_at` as `pivot_updated_at` from `wr_admin_roles` inner join `wr_admin_role_menu` on `wr_admin_roles`.`id` = `wr_admin_role_menu`.`role_id` where `wr_admin_role_menu`.`menu_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 115}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 98}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/MenuCache.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\MenuCache.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 99}], "start": 1754029165.85863, "duration": 0.05108, "duration_str": "51.08ms", "memory": 0, "memory_str": null, "filename": "ModelTree.php:123", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FModelTree.php&line=123", "ajax": false, "filename": "ModelTree.php", "line": "123"}, "connection": "wind_rich", "explain": null, "start_percent": 97.484, "width_percent": 2.516}]}, "models": {"data": {"Dcat\\Admin\\Models\\Role": {"value": 156, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Dcat\\Admin\\Models\\Menu": {"value": 61, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FMenu.php&line=1", "ajax": false, "filename": "Menu.php", "line": "?"}}, "Dcat\\Admin\\Models\\Permission": {"value": 56, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\AdminUser": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminUser.php&line=1", "ajax": false, "filename": "AdminUser.php", "line": "?"}}, "App\\Models\\WwLink": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwLink.php&line=1", "ajax": false, "filename": "WwLink.php", "line": "?"}}, "App\\Models\\TencentAdAccount": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FTencentAdAccount.php&line=1", "ajax": false, "filename": "TencentAdAccount.php", "line": "?"}}, "App\\Models\\AdminDomain": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminDomain.php&line=1", "ajax": false, "filename": "AdminDomain.php", "line": "?"}}, "App\\Models\\WwTpl": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwTpl.php&line=1", "ajax": false, "filename": "WwTpl.php", "line": "?"}}, "App\\Models\\WwUsersGroup": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwUsersGroup.php&line=1", "ajax": false, "filename": "WwUsersGroup.php", "line": "?"}}, "App\\Models\\ShieldPolicy": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FShieldPolicy.php&line=1", "ajax": false, "filename": "ShieldPolicy.php", "line": "?"}}, "Dcat\\Admin\\Models\\Administrator": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FAdministrator.php&line=1", "ajax": false, "filename": "Administrator.php", "line": "?"}}, "App\\Models\\Announcement": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAnnouncement.php&line=1", "ajax": false, "filename": "Announcement.php", "line": "?"}}}, "count": 305, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://sen.test/ztfz/posting_link", "action_name": "dcat.admin.posting_link.index", "controller_action": "App\\Admin\\Controllers\\WwLinkController@index", "uri": "GET ztfz/posting_link", "controller": "App\\Admin\\Controllers\\WwLinkController@index<a href=\"phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=61\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Admin\\Controllers", "prefix": "/ztfz", "file": "<a href=\"phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=61\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Admin/Controllers/WwLinkController.php:61-77</a>", "middleware": "admin.app:admin, web, admin", "duration": "4.54s", "peak_memory": "2MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1067576908 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1067576908\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-80319204 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-80319204\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-820556971 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ik43a1doOVhQc1pyMWkxV2p4QWNPaEE9PSIsInZhbHVlIjoiR0FFVEppYzdxQUVBSVlwb3kzdTZsR3BWbVRNS0g4aHFhYjl4KzNJSFBqa1VjT2lQR2tLakVvblZFd3U5RmxQUDIyT0E3L25qaENaTE92UElNK1ZZbkRpSk1jNWhrejNFcDFxbWNaU3N2bngrSUp4N1BEYVNOVDg1T1A1UHF1ZHUiLCJtYWMiOiJjYzE0NDVlNTg5Mjg4YTc3YjkxNTg4NWU4NWJkZTgxNDhkNDhlNjgxMmY1NjM4ODk3ZTZkZDcxM2Y5MTA5NmZhIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik9iNzNFdkxyZGNCQVU3K0ZqdGpsRXc9PSIsInZhbHVlIjoiRjNmZXNsVGozTFlqbnhwcENqZEdZY292ditGaHJhNEp5dXR4TlhTLys5SGNJVmQvcDdoVGUreFhKQm5xV2FOT0ZGeFEwd29XVVZlTEVsRG1YdGYvMmNBem1CTGhBZG13elFFRzZSMC9NL1JrL2VlQm8vTkRXV3pOV005WEFzQTUiLCJtYWMiOiIwN2UwOGJlNTdiNDAyMDkxOTAwYmRkNGZmZTU4MzRkOTY1MjE4NDY0MjdmMWM5YjFlYTVmZTYyYzU0NmY1YTM4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">zh-CN,zh;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">sen.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-820556971\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1712928511 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0esJyfQEGYXbNzItiQn8t75dzSvXZmiKqhcwIHBs</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Sc1mCMq4OSwvqqSk8mbaIttmOBzjSrhvaXfSjoYm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1712928511\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-753499485 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Aug 2025 06:19:23 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-753499485\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1193210267 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>prev</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://sen.test/ztfz/posting_link</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">admin.prev.url</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0esJyfQEGYXbNzItiQn8t75dzSvXZmiKqhcwIHBs</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://sen.test/ztfz/posting_link</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1193210267\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://sen.test/ztfz/posting_link", "action_name": "dcat.admin.posting_link.index", "controller_action": "App\\Admin\\Controllers\\WwLinkController@index"}, "badge": null}}