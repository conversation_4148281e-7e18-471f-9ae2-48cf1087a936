<?php

namespace App\Console\Commands\Admin;

use App\Models\AdminUser;
use App\Models\LinkViewRecord;
use App\Models\WwLink;
use App\Models\WwUserAddRecord;
use App\Models\WwUserAddRecordDelete;
use Dcat\EasyExcel\Excel;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class BillDetails extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'Admin:BillDetails';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $adminUid  = 170;//河南蚂蚁互动文化传媒有限公司
//        $adminUid  = 148;//腾扬
        //$adminUid  = 189;//每悦
        $adminUids = AdminUser::query()->where("id", $adminUid)->orWhere("parent_id", $adminUid)->pluck("id");
        $adminInfo = AdminUser::query()->where("id", $adminUid)->orWhere("parent_id", $adminUid)->pluck("username", "id")->toArray();
        //寻找这些账户的账户 ID计费
        $accountVccList  = DB::table("ad_account_vcc_daily")->whereIn("admin_uid", $adminUids)->get();
        $accountAsLinkId = [];

        $data = [];
        foreach ($accountVccList as $vccItem) {
            $this->info($vccItem->date . "-" . $vccItem->account_id);
            $username = '';
            if (isset($adminInfo[$vccItem->admin_uid])) {
                $username = $adminInfo[$vccItem->admin_uid];
            }
            $tempData['归属账号'] = $username . "(" . $vccItem->admin_uid . ")";
            $tempData['日期']     = $vccItem->date;
            $tempData['账户 ID']  = $vccItem->account_id;
            $tempData['消耗']     = round($vccItem->ad_cost / 100, 4);
            $tempData['广告点击'] = $vccItem->valid_click_count;
            if (isset($accountAsLinkId[$vccItem->account_id])) {
                $tempData['投放链接']     = $accountAsLinkId[$vccItem->account_id]->id;
                $tempData['投放链接备注'] = $accountAsLinkId[$vccItem->account_id]->remark;
                $tempData['屏蔽'] = $accountAsLinkId[$vccItem->account_id]->need_shield;
            } else {
                $link = WwLink::withTrashed()->where("account_id", $vccItem->account_id)->where("admin_uid", $vccItem->admin_uid)->first();
                if ($link) {
                    $accountAsLinkId[$vccItem->account_id] = $link;
                    $tempData['投放链接']                  = $link->id;
                    $tempData['投放链接备注']              = $link->remark;
                    $tempData['屏蔽']              = $link->need_shield;
                }
            }
            //今日的展示量一共多少
            $tempData['今日展示量-热'] = LinkViewRecord::query()->where("ww_link_id", $tempData['投放链接'])->whereBetween("created_at", [
                $vccItem->date . " 00:00:00",
                $vccItem->date . " 23:59:59"
            ])->count();
            $tempData['今日展示量-冷'] = DB::table('link_view_record_delete')->where("ww_link_id", $tempData['投放链接'])->whereBetween("created_at", [
                $vccItem->date . " 00:00:00",
                $vccItem->date . " 23:59:59"
            ])->count();
            $tempData['展示量-总']     = $tempData['今日展示量-热'] + $tempData['今日展示量-冷'];

            //今日的非屏展示量一共多少
            $tempData['今日非屏展示量-热'] = LinkViewRecord::query()->where("ww_link_id", $tempData['投放链接'])->whereBetween("created_at", [
                $vccItem->date . " 00:00:00",
                $vccItem->date . " 23:59:59"
            ])->where("need_shield",0)->count();
            $tempData['今日非屏展示量-冷'] = DB::table('link_view_record_delete')->where("ww_link_id", $tempData['投放链接'])->whereBetween("created_at", [
                $vccItem->date . " 00:00:00",
                $vccItem->date . " 23:59:59"
            ])->where("need_shield",0)->count();
            $tempData['非屏展示量-总']     = $tempData['今日非屏展示量-热'] + $tempData['今日非屏展示量-冷'];
            //今日进粉量一共多少
            $tempData['今日进粉量-热'] = WwUserAddRecord::query()->where("admin_uid", $vccItem->admin_uid)->where("ww_link_id", $tempData['投放链接'])->whereBetween("created_at", [
                $vccItem->date . " 00:00:00",
                $vccItem->date . " 23:59:59"
            ])->count();
            $tempData['今日进粉量-冷'] = WwUserAddRecordDelete::query()->where("admin_uid", $vccItem->admin_uid)->where("ww_link_id", $tempData['投放链接'])->whereBetween("created_at", [
                $vccItem->date . " 00:00:00",
                $vccItem->date . " 23:59:59"
            ])->count();
            $tempData['进粉量-总']     = $tempData['今日进粉量-热'] + $tempData['今日进粉量-冷'];
            $data[]                    = $tempData;
        }
        $disk = Storage::disk('oss');
        $file = "BillDetails/" . $adminUid . "-" . current($adminInfo) . "-" . date("YmdHis") . '.xlsx';
        Excel::export($data)->disk($disk)->store($file);
        $this->info("https://wind-fly.oss-cn-hangzhou.aliyuncs.com/" . $file);
        return Command::SUCCESS;
    }
}
