<?php

	namespace App\Admin\Extensions\Filter;


	use Dcat\Admin\Grid\Filter\AbstractFilter;
    use Dcat\Admin\Grid\Filter\Presenter\DateTime;
    use Illuminate\Support\Arr;

    class DateRange extends AbstractFilter
	{
		// 自定义你的过滤器显示模板
		protected $view = 'wra.grid.filter.dateRange';

		// 这个方法用于生成过滤器字段的唯一id
		// 通过这个唯一id则可以用js代码对其进行操作
		public function formatId($columns)
		{
			$id   = str_replace('.', '_', $columns);
			$prefix = $this->parent->grid()->makeName('filter-column-');

			return ['start' => "{$prefix}{$id}_start", 'end' => "{$prefix}{$id}_end"];
		}

		// form表单name属性格式化
		protected function formatName($column)
		{
			$columns = explode('.', $column);

			if (count($columns) == 1) {
				$name = $columns[0];
			} else {
				$name = array_shift($columns);

				foreach ($columns as $column) {
					$name .= "[$column]";
				}
			}

			return ['start' => "{$name}[start]", 'end' => "{$name}[end]"];
		}

		/**
		 * @param  array  $options
		 * @return $this
		 */
		public function datetime($options = [])
		{
			DateTime::requireAssets();

			$options['format'] = Arr::get($options, 'format', 'YYYY-MM-DD HH:mm:ss');
			$options['locale'] = Arr::get($options, 'locale', config('app.locale'));

			return $this->addVariables([
				'dateOptions' => $options,
			]);
		}

		// 创建条件
		// 这里构建的条件支持`Laravel query builder`即可。
		public function condition($inputs)
		{
			if (!Arr::has($inputs, $this->column)) {
				return;
			}

			$this->value = Arr::get($inputs, $this->column);

			$value = array_filter($this->value, function ($val) {
				return $val !== '';
			});

			if (empty($value)) {
				return;
			}

			if (!isset($value['start']) && isset($value['end'])) {
				// 这里返回的数组相当于
				// $query->where($this->column, '<=', $value['end']);
				return $this->buildCondition($this->column, '<=', $value['end']);
			}

			if (!isset($value['end']) && isset($value['start'])) {
				// 这里返回的数组相当于
				// $query->where($this->column, '>=', $value['end']);
				return $this->buildCondition($this->column, '>=', $value['start']);
			}

			$this->query = 'whereBetween';

			// 这里返回的数组相当于
			// $query->whereBetween($this->column, $value['end']);
			return $this->buildCondition($this->column, $this->value);
		}
	}
