<?php

namespace App\Admin\Forms\ShieldPolicy;

use Admin;
use App\Models\AdminUser;
use App\Models\ShieldPolicy;
use App\Services\Tools\LogService;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Illuminate\Support\Facades\Log;

class UpdateShieldPolicyGroupAndAuditTplForm extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input)
    {
        LogService::inputLog('Tools','屏蔽规则-修改屏蔽分组/页面', $input, Admin::user()->id, Admin::user()->username);
        if(!AdminUser::isSystemOp()){
            return $this->response()->alert()->error('提示')->detail('无权限操作');
        }
        if(!$input['shield_id']){
            return $this->response()->alert()->error('提示')->detail('请选择屏蔽策略');
        }
        $shieldPolicy = ShieldPolicy::query()->where('id', $input['shield_id'])->first();
        if(!$shieldPolicy){
            return $this->response()->alert()->error('提示')->detail('屏蔽策略不存在');
        }

        if(!$input['ww_user_group_id'] && !$input['tpl_audit_id']){
            return $this->response()->alert()->warning('提示')->detail('无任何修改')->refresh();
        }
        if($input['ww_user_group_id']){
            $shieldPolicy->ww_user_group_id = $input['ww_user_group_id'];
        }
        if($input['tpl_audit_id']){
            $shieldPolicy->tpl_audit_id = $input['tpl_audit_id'];
        }
        $shieldPolicy->save();
        Log::info('修改屏蔽规则分组和页面，用户ID：'. Admin::user()->id . '，修改了屏蔽规则ID：' . $shieldPolicy->id . '，的屏蔽分组ID为：' . $input['ww_user_group_id'] . '，屏蔽页面ID为，' . $input['tpl_audit_id']);
        return $this->response()->alert()->success('提示')->detail('修改成功')->refresh();
    }

    public function form()
    {
        $this->hidden("shield_id");
        $adminUsers = AdminUser::query()->pluck('username', 'id');
        $this->select('admin_uid', '用户')
            ->loads(
                ['ww_user_group_id','tpl_audit_id'],
                ['api/getShieldPolicieWwGroupList','api/getShieldPolicieAuditTpl'])
            ->options($adminUsers)
            ->help('选择要修改为哪个用户的屏蔽分组或屏蔽页面')
            ->required();
        $this->select('ww_user_group_id','屏蔽分组');
        $this->select('tpl_audit_id','屏蔽页面');
        $this->confirm('确定提交码？');

    }

    public function default()
    {
        return [
            // 展示上个页面传递过来的值
            'shield_id' => $this->payload['shield_id'] ?? '',
        ];
    }
}
