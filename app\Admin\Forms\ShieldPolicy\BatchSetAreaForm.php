<?php

namespace App\Admin\Forms\ShieldPolicy;

use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Models\AdminUser;
use App\Models\Area;
use App\Models\ShieldPolicy;
use App\Services\Tools\LogService;
use App\Services\Tools\UtilsService;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class BatchSetAreaForm extends Form implements LazyRenderable
{
    use LazyWidget;
    // 处理请求
    public function handle(array $input)
    {
        LogService::inputLog('Tools','屏蔽规则-批量设置屏蔽地域', $input, Admin::user()->id, Admin::user()->username);
        if (!$input['id']) {
            return $this->response()->error('请选择要操作的数据');
        }

        $ids = explode(',', $input['id']);

        if(empty(json_decode($input['block_area'],true))){
            return $this->response()->error('请选择屏蔽地区');
        }
        $shieldPolicy = ShieldPolicy::query()->find($ids);
        if ($shieldPolicy->isEmpty()) {
            return $this->response()->error('屏蔽规则不存在');
        }
        foreach ($shieldPolicy as $sPolicy) {
            if(!AdminUser::isAdmin($sPolicy)){
                return $this->response()->error('无操作权限')->refresh();
            }
            $sPolicy->block_area = $input['block_area'];
            $sPolicy->save();
            $area = Area::query()->whereIn('id', json_decode($sPolicy->block_area))->get()->pluck('name')->toArray();
            //添加操作日志队列
            AdminActionLogJob::dispatch(
                'batch_set_shield_policy_block',
                $sPolicy->id,
                AdminActionLog::ACTION_TYPE['防水墙'],
                '「' . $sPolicy->name. '」批量设置屏蔽地域：' . implode(',' , $area) ,
                getIp(),
                Admin::user()->id,
            )->onQueue('admin_action_log_job');
        }
        return $this->response()->success('配置成功')->refresh();
    }

    public function form()
    {

        //警告
        $this->html(
            <<<HTML
<div class="alert alert-warning alert-dismissable" style="color: red">
            <h4><i class="fa fa-warning"></i>&nbsp; 警告</h4>
        <i class="fa fa-warning"></i>&nbsp; 批量设置将完全覆盖现有的屏蔽地域设置！
</div>
HTML
        );
        $this->html('<div id="search_block_area"></div>', '快捷搜索选择地区');
        /** 搜搜节点组件 */
        UtilsService::searchJsTree('search_block_area','block_area',' ',' ');

        $this->hidden('id')->value($this->payload['ids'] ?? ''); // 批量操作的屏蔽规则ID
        $areaData = Area::query()->select("id", "name", "pid as parent_id", "id as order")->whereIn("level", [1, 2])->get()->toArray();
        $this->tree('block_area')
            ->nodes($areaData) // 设置所有节点
            ->expand(false)
            ->treeState(false) # 允许单独选择父节点
            ->customFormat(function ($v) { // 格式化外部注入的值
                if (!$v) {
                    return [];
                }
                return $v;
            })->saveAsJson();
       $this->confirm('确认提交？批量设置将完全覆盖现有的屏蔽地域设置。请谨慎操作！');
    }

}
