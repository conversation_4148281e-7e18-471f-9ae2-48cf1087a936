<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;

use Illuminate\Database\Eloquent\Model;

/**
 * @property mixed|string $disable_message
 * @property mixed|string $disable_code
 * @property mixed|string $last_modified_time
 * @property mixed|string $created_time
 * @property mixed|string $owner_account_id
 * @property mixed|string $page_status
 * @property mixed|string $page_url
 * @property mixed|string $page_name
 * @property mixed|string $page_id
 * @property mixed|string $page_type
 * @property mixed        $account_id
 */
class AdAccountPage extends Model
{
	use HasDateTimeFormatter;
    protected $table = 'ad_account_pages';

}
