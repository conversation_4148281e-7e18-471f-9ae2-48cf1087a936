<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\ExportTask;
use App\Jobs\ExportTask\ProcessExportJob;
use App\Models\AdminUser;
use App\Models\ExportTask as ExportTaskModel;
use App\Models\WwUserAddRecord;
use App\Models\WwUserAddRecordDelete;
use App\Services\Tools\UtilsService;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Show;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Request;

class ExportTaskController extends AdminController
{

    public function index(Content $content)
    {
        return $content
            ->header('导出任务')
            ->body($this->grid());
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new ExportTask(['adminInfo']), function (Grid $grid) {
            /** 对话框新增 */
            $grid->disableCreateButton();
            $grid->tools(function (Grid\Tools $tools) {
                $className = collect(Request::segments())->last();
                $tools->append(UtilsService::dialogForm('添加',Request::url().'/create',"create-{$className}"));
            });
            $grid->model()->orderBy('id', 'desc');
//            $grid->disableActions();
            $grid->disableBatchActions();
//            $grid->disableCreateButton();
            $grid->disableViewButton();
            $grid->column('id')->sortable();
            $grid->column('adminInfo.username', '用户')->copyable();
            //$grid->column('action');
            $grid->column('title', '任务名称')->copyable();
            $grid->column('data_type', '数据类型')->display(function ($dataType) {
                switch ($dataType) {
                    case 0:
                        return '进粉记录';
                        break;
                    case 1:
                        return '历史进粉记录';
                        break;
                    default:
                        return '';
                }
            });
            $grid->column('status', '状态')->using(ExportTaskModel::STATUS)->label([
                0 => 'default',
                1 => 'warning',
                2 => 'success',
                3 => 'danger',
            ]);
            $grid->column("file_name", '文件名')->copyable();
            $grid->column("file_path", '文件路径')->downloadable();
            $grid->column("start_date", '开始时间');
            $grid->column("end_date", '结束时间');
            $grid->column('created_at')->sortable();
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                $adminUser = AdminUser::query()->where('parent_id', 0)->pluck('username', 'id');
                $filter->equal('admin_uid', "用户")->select($adminUser)->width(2);
                $filter->like("title", '任务名称')->width(2);
                $filter->equal("data_type", '数据类型')->select([0 => '进粉记录', 1 => '历史进粉记录'])->width(2);
                $filter->like("file_name", '文件名')->width(2);
                $filter->equal("status", '状态')->select(ExportTaskModel::STATUS)->width(2);
                $filter->between("created_at")->datetime()->width(4);
                $filter->between("updated_at")->datetime()->width(4);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new ExportTask(), function (Show $show) {
            if (!AdminUser::isAdmin($show->model())) {
                $show->field('N')->value("无数据");
            } else {
                $show->field('id');
            }
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new ExportTask(), function (Form $form) {
            $form->hidden('id');
//            $adminUsers = AdminUser::query()->where('parent_id', 0)->pluck('username', 'id');
            $adminUsers = AdminUser::query()->pluck('username', 'id');
            $form->select('admin_uid', '用户')
                ->options($adminUsers)
                ->load('corp_id', '/api/getCorpList')
                ->required();

            $form->select('data_type', '数据类型')
                ->options([0 => '进粉记录', 1 => '历史进粉记录'])
                ->required()
                ->when([0, 1], function ($form) {
                    $form->select('corp_id', '选择企微')->help('可选择对应企微主体的进粉记录，非必选项。');
                })->help('超过20万条数据，建议按5-10为一批次分开导出。');

            $form->text('title', '任务名称')->required();
            $form->text('file_name', '文件名称')->required();
            $form->datetime('start_date', '开始时间')->required();
            $form->datetime('end_date', '结束时间')->required();
//            $form->select('job_index','选择导出队列')->options([0 => '默认队列', 1 => '队列1', 2 => '队列2', 3 => '队列3'])->default(0)->help('如需同时导出多个任务，请选择不同导出队列。')->required();
            $form->radio('status','状态')->options(ExportTaskModel::STATUS)->required();
            $form->disableViewButton();
            $form->disableViewCheck();
            $form->disableEditingCheck();
            $form->disableDeleteButton();
        })->saving(function (Form $form) {
            $dataCount = self::getDataCount($form->input());
            if($dataCount > 200000){
                return $form->response()->alert()->error('提示')->detail('数据超过20万条，建议分开导出。');
            }
            $checkTask = ExportTaskModel::query()->where('status',1)->exists();
            if($checkTask) {
                return $form->response()->alert()->error('提示')->detail('有正在执行的导出任务。');
            }

        })->saved(function (Form $form, $result) {

            if ($form->isCreating()) {
                $id = $result;
            } else {
                $id = $form->getKey();
            }
            if (!$id) {
                return $form->response()->alert()->error('提示')->detail('导出任务创建失败，请稍后重试-1');
            }
            $task = ExportTaskModel::query()->where('id', $id)->first();
            if (!$task) {
                return $form->response()->alert()->error('提示')->detail('导出任务创建失败，请稍后重试-2');
            }

            if($task['status'] == 0){
                Log::info('导出任务，进入队列执行');
                ProcessExportJob::dispatch($task)->onQueue('export_task');
            }


        });
    }


    /**
     * 获取数据总数
     * @param $form
     * @return int
     */
    public static function getDataCount($form)
    {
        $date = [
            $form['start_date'],
            $form['end_date'],
        ];
        switch ($form['data_type']) {
            case 0:
                $model = new WwUserAddRecord();
                break;
            case 1:
                $model = new WwUserAddRecordDelete();
                break;
            default:
                $model = new WwUserAddRecordDelete();
        }
        $adminUid = $form['admin_uid'];
        $adminUser = AdminUser::query()->where('id', $adminUid)->first();
        if ($adminUser['parent_id'] == 0) { //如果是主账号
            $adminUids = AdminUser::query()
                ->where('id', $adminUid)
                ->orWhere('parent_id', $adminUid)
                ->pluck('id')
                ->toArray();
        } else {
            $adminUids = array($adminUser['id']);
        }
        $query = $model::query()
            ->whereIn('admin_uid', $adminUids)
            ->whereBetween('external_contact_created_at', $date);
        if ($form['corp_id']) {
            $query->where('corp_id', $form['corp_id']);
        }
        return  $query->count();
    }

}
