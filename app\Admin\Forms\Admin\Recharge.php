<?php

	namespace App\Admin\Forms\Admin;

    use App\Models\AdminBill;
    use App\Models\AdminUser;
    use Dcat\Admin\Widgets\Form;

    class Recharge extends Form
	{
		/**
		 * Handle the form request.
		 *
		 * @param array $input
		 *
		 * @return mixed
		 */
		public function handle(array $input)
		{
            $where = [
                'admin_uid' => $input['admin_uid'],
                'date' => $input['date']
            ];
            /** @var AdminBill $newAdminBill */
            $newAdminBill = AdminBill::query()->where($where)->first();
            if(!$newAdminBill){
                return $this->response()->alert()->error('提示')->detail('用户账单不存在');
            }
            $newAdminBill->add_price = $input['money'] * 100;
            $newAdminBill->save();
            return $this->response()->alert()->success('提示')->detail('充值完成')->refresh();
		}

		/**
		 * Build a form here.
		 */
		public function form()
		{
            $admin = AdminUser::query()->where("parent_id",0)->pluck("username","id");
            $this->select('admin_uid','客户')->options($admin);
			$this->date('date', '充值日期')->default(now())->required();
            $this->number('money', '充值金额')->min(0);
		}

		/**
		 * The data of the form.
		 *
		 * @return array
		 */
		public function default()
		{
			return [

			];
		}
	}
