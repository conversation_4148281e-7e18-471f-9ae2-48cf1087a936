<?php

    namespace App\Models;

    use Dcat\Admin\Admin;
    use Dcat\Admin\Traits\HasDateTimeFormatter;
use Dcat\Admin\Traits\ModelTree;
use Illuminate\Database\Eloquent\HigherOrderBuilderProxy;
    use Illuminate\Database\Eloquent\Model;
    use Illuminate\Database\Eloquent\Relations\BelongsToMany;
    use Illuminate\Database\Eloquent\SoftDeletes;

    /**
     * @property mixed id
     * @property mixed|string password
     * @property mixed name
     * @property mixed username
     * @property mixed|string $open_unionid
     * @property mixed $open_openid
     * @property mixed $open_scope
     * @property mixed $open_refresh_token
     * @property mixed|string $open_expires_in
     * @property mixed $open_access_token
     * @property mixed|string $avatar
     * @property mixed|string $status
     * @property mixed|string $sales_name
     * @property false|mixed|string $sales_wechat_ids
     * @property mixed $parent_id
     * @property mixed $white_qrcode
     * @property mixed $encrypt_id
     * @property mixed $created_at
     * @property mixed $ad_price_rate
     * @property mixed $show_ww_user_policy
     * @property mixed $today_add_record 进粉记录默认今天
     * @property mixed $action_log_table 操作记录表
     */
    class AdminUser extends Model
    {
        use HasDateTimeFormatter;
        use SoftDeletes;
        use ModelTree;

        protected $table = 'admin_users';
        protected $orderColumn = 'id';

        protected $casts = [
            'block_area' => 'json',
        ];

        const SHOW_WW_USER_POLICY = [
            1 => '策略一：寻找今日是否加过企业微信下的销售，如果加过，那么就展示该销售',
            2 => '策略二：七天内同一个广告主体展示同一个销售，如销售已删除、不在线、权重为0，则寻找其他销售',
            3 => '策略三：同IP当天内，如销售已删除、不在线、权重为0，则寻找其他销售，则寻找其他销售',
            4 => '策略四：同IP当天内，展示过企微分组下某一销售后，当天内只展示该销售，如销售已删除、不在线、权重为0，则寻找其他销售，则寻找其他销售。',
            5 => '策略五：同用户当天内，展示过企微分组下某一销售后，当天内只展示该销售，如销售已删除、不在线、权重为0，则寻找其他销售，则寻找其他销售。',
        ];

        /**
         * A user has and belongs to many roles.
         *
         * @return BelongsToMany
         */
        public function roles(): BelongsToMany
        {
            $pivotTable = config('admin.database.role_users_table');

            $relatedModel = config('admin.database.roles_model');

            return $this->belongsToMany($relatedModel, $pivotTable, 'user_id', 'role_id')->withTimestamps();
        }

        /**
         * 是否是超管或者运营
         * @param $model
         * @return bool
         */
        public static function isAdmin($model)
        {
            if (Admin::user()->isAdministrator()  || Admin::user()->isRole("wop")) {
                return true;
            }

            $userIds = AdminSubUser::getAdminUids(Admin::user()->id);
            if (in_array($model->admin_uid, $userIds)) {
                return true;
            }
            return false;
        }

        /**
         * 超管 系统运营 系统运营子账户 返回true
         * @return bool
         */
        public static function isSystemOp(){
            return Admin::user()->isAdministrator() || Admin::user()->isRole("wop") || Admin::user()->isRole("wop_subAccout");
        }

        public static function getPaginate(){
            $paginate = Admin::user()->paginate;
            if(!$paginate){
                $paginate = 20;
            }
            return $paginate;
        }

        /**
         * 屏蔽页进粉只打销售标签的用户ID配置
         * @return array
         */
        public static function auditPageOnlySetWwUserTag()
        {
            return [
                189,//南京每悦科技有限公司
            ];
        }

        /**
         * A1的总账号包含的 IDs
         * @return int[]
         */
        public static function getA1AdminUids(): array
        {
            return [
                135,136,137,//天使筑梦
                209,//烟海文化
                //142,//金都传媒
            ];
        }

        /**
         * 根据主账号ID获取所有用户ID包括主账号自己
         * @param $parentId
         * @return array
         */
        public static function getAdminUidsByParentId(array $parentId): array
        {
            if(!$parentId){
                return [];
            }
            $adminUids = AdminUser::query()
                ->whereIn('parent_id',$parentId)
                ->orWhereIn('id',$parentId)
                ->pluck('id')
                ->toArray();
            return $adminUids ?? [];
        }

        /**
         * 通过admin uid获取所有用户ID,包括主账号以及其他子账户
         * @param $adminUid
         * @return array
         */
        public static function getAllAdminUidsByAdminUid($adminUid): array
        {
            $adminUser = AdminUser::query()->where('id',$adminUid)->select("id", "parent_id")->first();
            if ($adminUser->parent_id != 0) {
                return self::query()
                    ->where("parent_id",$adminUser->parent_id)
                    ->orWhere('id',$adminUser->parent_id)
                    ->pluck("id")->toArray();
            } else {
                return AdminUser::query()
                    ->where("parent_id", $adminUser->id)
                    ->orWhere('id',$adminUser->id)
                    ->pluck("id")->toArray();
            }
        }
        /**
         * 通过admin_uids获取所有用户ID,包括主账号以及其他子账户
         * @param $adminUid
         * @return array
         */
        public static function getAllAdminUidsByAdminUids($adminUids): array
        {;
            $returnAdminUids = [];
            $adminUserList = AdminUser::query()
                ->whereIn('id',$adminUids)
                ->select("id", "parent_id")
                ->get();
            foreach ($adminUserList as $adminUser) {
                if ($adminUser->parent_id != 0) { //如果是子账号
                    $returnAdminUids[] = $adminUser->id;
                } else { //如果是主账号
                    $parentAdminUids =  AdminUser::query()
                        ->where("parent_id", $adminUser->id)
                        ->orWhere('id',$adminUser->id)
                        ->pluck("id")
                        ->toArray();
                    $returnAdminUids = array_merge($returnAdminUids,$parentAdminUids);
                }
            }
            $returnAdminUids = array_unique($returnAdminUids);
            return $returnAdminUids ?? [];
        }

        /**
         * 获取当前用户的所有子账户ID （包括当前用户）
         * @return array
         */
        public function getAllChildrenIds()
        {
            $allChildren = [];
            $children = AdminUser::query()
                ->where('parent_id', $this->id)
                ->pluck('id')
                ->toArray();

            if (!empty($children)) {
                foreach ($children as $childId) {
                    $allChildren[] = $childId;
                    $allChildren = array_merge($allChildren, AdminUser::query()->find($childId)->getAllChildrenIds());
                }
            }

            $allChildren[] = $this->id;
            return array_unique($allChildren);
        }


    }
