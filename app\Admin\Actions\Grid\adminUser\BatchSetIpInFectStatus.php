<?php

namespace App\Admin\Actions\Grid\adminUser;

use App\Admin\Extensions\Grid\BatchActionPlus;
use App\Admin\Forms\Admin\BatchSetIpInFectStatusForm;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\BatchAction;
use Dcat\Admin\Widgets\Modal;
use Illuminate\Http\Request;

class BatchSetIpInFectStatus extends BatchActionPlus
{
    /**
     * @return string
     */
    public $title = '批量修改状态';
    public $buttonText = '<button class="btn btn-primary ww_user_batch_btn"><i class="feather icon-refresh-ccw"></i>&nbsp&nbsp<span class="selected"></span>批量修改状态</button>';

    public function form(): BatchSetIpInFectStatusForm
    {
        // 实例化表单类
        return BatchSetIpInFectStatusForm::make();
    }
}
