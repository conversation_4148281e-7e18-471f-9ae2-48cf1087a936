<?php

namespace App\Console\Commands\WwUser;

use App\Models\WwCorpInfo;
use App\Models\WwUser;
use App\Services\Corp\WwProvApiService;
use App\Services\NotifySendService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CheckWwUserLicenseNotifySend extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'WwUser:CheckWwUserLicenseNotifySend';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '检查企微许可证状态发送通知';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        Log::info('检查企微许可证状态发送通知');
        /** @var WwCorpInfo $corpList 获取所有企微信息 */
        $corpList = WwCorpInfo::query()
            ->orderBy('id', 'desc')
            ->get();
        $i = 0;
        /** 如果企微信息不为空 */
        if ($corpList->isNotEmpty()) {
            /** 企微总数量 */
            $count = count($corpList->toArray());

            foreach ($corpList as $corpInfo) {
                /** 已激活的帐户列表 */
                $activatedAccountList = WwProvApiService::get_list_actived_account($corpInfo);
                /** 激活账户列表不为空 */
                if (!empty($activatedAccountList)) {
                    Log::info('检查许可到期时间：企微许可证试用期小于当前时间，且【有】购买许可证的订单');
                    /** api返回账户列表 */
                    foreach ($activatedAccountList as $datum) {

                        /** 查询api返回符合条件的销售 */
                        $wwUsers = WwUser::query()
                            ->with('adminInfo')
                            ->where("corp_id", $corpInfo->id)
                            ->where("open_user_id", $datum['userid'])
                            ->get();
                        /** 没有跳过 */
                        if ($wwUsers->isEmpty()) {
                            continue;
                        }
                        /** @var WwUser $wwUser */
                        foreach ($wwUsers as $wwUser) {
                            /** @var WwUser $wwUser */
                            $exDays = ($datum['expire_time'] - time()) / 86400;
                            /** 计算状态 */
                            if ($exDays > 3) {
                                $exStatus = 1;
                            } elseif ($exDays > 0) {
                                $exStatus = 2;
                            } else {
                                $exStatus = 0;
                            }
                            /** 获取许可证到期状态  1 有效 2预警 0无效 */
                            if ($exStatus == 2 || $exStatus == 0) {

                                $message = match ($exStatus) {
                                    2 => '即将到期',
                                    0 => '已经到期'
                                };

                                NotifySendService::sendCustomerForMessageByRobots($wwUser->adminInfo,
                                    "$corpInfo->corp_name 企微许可证提醒 {$message}！");
                            }

                        }
                    }
                } else {
                    /** 查询所有企微的试用时间 */
                    Log::info('检查企微许可到期时间,【没有】购买许可证的订单');
                    /** 获取许可证到期状态 */
                    $exStatus = WwUser::checkWwUsersLicenseStatus($corpInfo->trail_end_time);
                    $wwUsers = WwUser::query()
                        ->with('adminInfo')
                        ->where("corp_id", $corpInfo->id);
                    foreach ($wwUsers as $wwUser) {
                        /** 获取许可证到期状态  1 有效 2预警 0无效 */
                        if ($exStatus == 2 || $exStatus == 0) {
                            $message = match ($exStatus) {
                                2 => '即将到期',
                                0 => '已经到期'
                            };
                            NotifySendService::sendCustomerForMessageByRobots($wwUser->adminInfo,
                                "$corpInfo->corp_name 企微许可证提醒 {$message}！");
                        }
                    }
                }

                $i++;
                $this->info('执行进度：' . round($i / $count * 100, 2) . '%');
            }
        }
        return 0;
    }
}
