<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ExportOpenId extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;
    protected $table = 'export_openid';

    const STATUS = [
        0 => '等待导出',
        1 => '导出中',
        2 => '导出成功',
        3 => '导出失败'
    ];

    public function adminInfo(): BelongsTo
    {
        return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
    }
}
