<?php
namespace App\Models;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property mixed $use_time
 * @property mixed $resp_user_id
 * @property mixed $errcode
 */
class CorpLicenseActiveCode extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'corp_license_active_codes';

}
