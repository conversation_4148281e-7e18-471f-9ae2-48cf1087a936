<?php

namespace App\Admin\Forms\WwUser;

use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Models\AdminUser;
use App\Models\WwUser;
use App\Services\Tools\LogService;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class BatchSetWelcomeMessageForm extends Form implements LazyRenderable
{
    use LazyWidget;

    // 处理请求
    public function handle(array $input)
    {
        LogService::inputLog('Tools','销售客服管理 -配置欢迎语', $input, Admin::user()->id, Admin::user()->username);
        if(!$input['id']){
            return $this->response()->alert()->error('提示')->detail('请选择销售客服。');
        }
        // id转化为数组
        $id = explode(',', $input['id']);
        if (! $id) {
            return $this->response()->alert()->error('提示')->detail('参数错误。');
        }


        $wwUsers = WwUser::query()->find($id);

        if ($wwUsers->isEmpty()) {
            return $this->response()->alert()->error('提示')->detail('销售不存在。');
        }
        foreach($wwUsers as $wwUser){
            if(!AdminUser::isAdmin($wwUser)){
                return $this->response()->alert()->error('提示')->detail('无操作权限。');
            }
            if (!$input['welcome_message_content']) {
                return $this->response()->alert()->error('提示')->detail('请输入欢迎语内容');
            }
            /** 处理欢迎语开始 */
            $res = [];
            $res['text'] = [
                'content' => $input['welcome_message_content'],
            ];
            // 根据附件类型决定存储哪种附件
            $attachmentType = array_filter($input['welcome_message_type'])[0] ?? false;
            if ($attachmentType === 'image' && $input['welcome_message_image_url']) {
                $res['attachments'] = [
                    [
                        'msgtype' => 'image',
                        'image' => [
                            'pic_url' => $input['welcome_message_image_url'],
                        ]
                    ]
                ];
            }
            if ($attachmentType === 'link' && $input['welcome_message_link_url']) {
                $res['attachments'] = [
                    [
                        'msgtype' => 'link',
                        'link' => [
                            'title' => $input['welcome_message_link_title'] ?: '',
                            'url' => $input['welcome_message_link_url'],
                        ]
                    ]
                ];
            }
            /** 处理欢迎语结束 */
            $wwUser->welcome_message = json_encode($res,JSON_UNESCAPED_UNICODE);
            $wwUser->save();

            // 构建用户友好的日志内容
            $logContent = '【' . $wwUser->name . '】，设置欢迎语：文字内容："' . $input['welcome_message_content'] . '"';
            if ($attachmentType === 'image' && $input['welcome_message_image_url']) {
                $logContent .= '，附件类型：图片，图片地址：' . $input['welcome_message_image_url'];
            } elseif ($attachmentType === 'link' && $input['welcome_message_link_url']) {
                $logContent .= '，附件类型：链接，链接地址：' . $input['welcome_message_link_url'];
                if ($input['welcome_message_link_title']) {
                    $logContent .= '，链接标题：' . $input['welcome_message_link_title'];
                }
            }

            //添加操作日志队列
            AdminActionLogJob::dispatch(
                'batch_set_welcome_message',
                $wwUser->id,
                AdminActionLog::ACTION_TYPE['销售'],
                $logContent,
                getIp(),
                Admin::user()->id
            )->onQueue('admin_action_log_job');

        }
        return $this->response()->alert()->success('提示')->detail('配置成功')->refresh();
    }

    public function form()
    {
        $this->text('welcome_message_content','欢迎语文字内容')->required();
        $this->checkbox('welcome_message_type', '欢迎语附件')->options([
            'link' => '链接卡片',
        ])->when(['image'], function () {
            //  $this->image('welcome_message_image_url', '图片地址')->url('users/files?corp_id=' . $this->model()->corp_id);
            $this->text('welcome_message_image_url', '图片地址');

        })->when(['link'], function (){
            $this->text('welcome_message_link_url', '链接地址');
            $this->text('welcome_message_link_title', '链接标题');
        });

        $this->hidden('id')->value($this->payload['ids'] ?? ''); // 批量操作时使用BatchActionPlus 就可以$this->payload['ids'] 获取批量选中的ids
        $this->confirm('确认提交？');
    }

}
