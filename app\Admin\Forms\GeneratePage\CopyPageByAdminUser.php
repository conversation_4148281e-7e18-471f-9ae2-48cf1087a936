<?php

namespace App\Admin\Forms\GeneratePage;

use App\Models\AdminUser;
use App\Models\GeneratePage;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class CopyPageByAdminUser extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input)
    {
        if(!AdminUser::isSystemOp()){
            return $this->response()->alert()->error('提示')->detail('无权限操作');
        }
        if(!$input['html_id']){
            return $this->response()->alert()->error('提示')->detail('请选择要复制的页面');
        }
        $html = GeneratePage::query()->where('id', $input['html_id'])->first();
        if(!$html){
            return $this->response()->alert()->error('提示')->detail('要复制的页面不存在');
        }
        $checkName = GeneratePage::where('name',$input['name'])->first();
        if($checkName){
            return $this->response()->alert()->error('提示')->detail('页面名称已存在');
        }
        $copy = [
            'admin_uid' => $input['admin_uid'],
            'name' => $input['name'],
//            'type' => $html->type,
//            'add_method' => $html->add_method,
            'button_color' => $html->button_color,
            'font_color' => $html->font_color,
            'file_type' => $html->file_type,
            'ask_content' => $html->ask_content,
            'continue_content' => $html->continue_content ?? '',
            'end_content' => $html->end_content,
            'top_content' => $html->top_content,
            'ask_name' => $html->ask_name ?? '',
            'submit_button_title' => $html->submit_button_title ?? '',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ];
        GeneratePage::query()->insert($copy);
        return $this->response()->alert()->success('提示')->detail('复制成功')->refresh();
    }

    public function form()
    {
        $this->hidden("html_id");
        $adminUsers = AdminUser::query()->pluck('username', 'id');
        $this->select('admin_uid', '用户')->options($adminUsers)->help('复制页面仅复制基本数据，图片、页面存放目录、下载地址不会复制，请复制完之后编辑选择存放目录后，保存生成新的页面文件。')->required();
        $name = date('Ymd_');
        $name = preg_replace('/^.{2}/', '', $name);
        $this->text('name', '页面名称')->default($name)->required()->help('页面名称不可重复');

    }

    public function default()
    {
        return [
            // 展示上个页面传递过来的值
            'html_id' => $this->payload['html_id'] ?? '',
        ];
    }
}
