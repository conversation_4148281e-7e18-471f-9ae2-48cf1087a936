<?php

namespace App\Admin\Actions\Grid\wwUser;

use App\Admin\Extensions\Grid\BatchActionPlus;
use App\Admin\Forms\WwUser\BatchSetAutoUpOrOffineForm;

class BatchSetAutoUpOrOffine extends BatchActionPlus
{

    /**
     * @return string
     */
    public $title = '自动上下线';

    public $buttonText = '<button class="btn btn-primary ww_user_batch_btn"><i class="feather icon-minimize-2"></i>&nbsp;自动上下线</button>';

    public function form(): BatchSetAutoUpOrOffineForm
    {
        // 表单渲染时
        return BatchSetAutoUpOrOffineForm::make();
    }
}
