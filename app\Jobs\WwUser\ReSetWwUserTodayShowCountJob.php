<?php

namespace App\Jobs\WwUser;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ReSetWwUserTodayShowCountJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $wwUser;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($wwUser)
    {
        //
        $this->wwUser = $wwUser;
    }




    /**
     * Execute the job.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->wwUser->today_show_count = 0;
        $this->wwUser->save();
        return true;
    }
}
