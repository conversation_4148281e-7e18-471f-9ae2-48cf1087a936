<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property mixed        $admin_uid
 * @property mixed|string $product
 * @property mixed|string $mdm
 * @property mixed        $shield_policy_id
 */
class SwitchNativePage extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'switch_native_page';

    const STATUS = [
        0 => '等待切换',
        1 => '切换中',
        2 => '切换完成',
        3 => '切换失败',
    ];

    public function adminInfo(): BelongsTo
    {
        return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
    }



}
