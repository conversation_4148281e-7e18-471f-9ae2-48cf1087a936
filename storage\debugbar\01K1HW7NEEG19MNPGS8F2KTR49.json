{"__meta": {"id": "01K1HW7NEEG19MNPGS8F2KTR49", "datetime": "2025-08-01 11:53:55", "utime": **********.407346, "method": "GET", "uri": "/ztfz/auth/login", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754020434.630094, "end": **********.407363, "duration": 0.7772688865661621, "duration_str": "777ms", "measures": [{"label": "Booting", "start": 1754020434.630094, "relative_start": 0, "end": **********.027845, "relative_end": **********.027845, "duration": 0.****************, "duration_str": "398ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.027862, "relative_start": 0.****************, "end": **********.407365, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "380ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.067705, "relative_start": 0.****************, "end": **********.074567, "relative_end": **********.074567, "duration": 0.006862163543701172, "duration_str": "6.86ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: wra.login", "start": **********.384991, "relative_start": 0.***************, "end": **********.384991, "relative_end": **********.384991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::layouts.full-content", "start": **********.394293, "relative_start": 0.****************, "end": **********.394293, "relative_end": **********.394293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.alerts", "start": **********.39593, "relative_start": 0.****************, "end": **********.39593, "relative_end": **********.39593, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.exception", "start": **********.397381, "relative_start": 0.767287015914917, "end": **********.397381, "relative_end": **********.397381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.toastr", "start": **********.398663, "relative_start": 0.7685689926147461, "end": **********.398663, "relative_end": **********.398663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::layouts.full-page", "start": **********.400292, "relative_start": 0.770197868347168, "end": **********.400292, "relative_end": **********.400292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 26816808, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.20", "PHP Version": "8.0.2", "Environment": "local", "Debug Mode": "Enabled", "URL": "sen.test", "Timezone": "PRC", "Locale": "zh_CN"}}, "views": {"count": 6, "nb_templates": 6, "templates": [{"name": "wra.login", "param_count": null, "params": [], "start": **********.384787, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\resources\\views/wra/login.blade.phpwra.login", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fresources%2Fviews%2Fwra%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}}, {"name": "admin::layouts.full-content", "param_count": null, "params": [], "start": **********.394211, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/layouts/full-content.blade.phpadmin::layouts.full-content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Flayouts%2Ffull-content.blade.php&line=1", "ajax": false, "filename": "full-content.blade.php", "line": "?"}}, {"name": "admin::partials.alerts", "param_count": null, "params": [], "start": **********.395843, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/alerts.blade.phpadmin::partials.alerts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Falerts.blade.php&line=1", "ajax": false, "filename": "alerts.blade.php", "line": "?"}}, {"name": "admin::partials.exception", "param_count": null, "params": [], "start": **********.397272, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/exception.blade.phpadmin::partials.exception", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fexception.blade.php&line=1", "ajax": false, "filename": "exception.blade.php", "line": "?"}}, {"name": "admin::partials.toastr", "param_count": null, "params": [], "start": **********.398583, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/toastr.blade.phpadmin::partials.toastr", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Ftoastr.blade.php&line=1", "ajax": false, "filename": "toastr.blade.php", "line": "?"}}, {"name": "admin::layouts.full-page", "param_count": null, "params": [], "start": **********.40021, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/layouts/full-page.blade.phpadmin::layouts.full-page", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Flayouts%2Ffull-page.blade.php&line=1", "ajax": false, "filename": "full-page.blade.php", "line": "?"}}]}, "queries": {"count": 2, "nb_statements": 1, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.*****************, "accumulated_duration_str": "233ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 51}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.124778, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "OpRecord.php:51", "source": {"index": 10, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FHttp%2FMiddleware%2FOpRecord.php&line=51", "ajax": false, "filename": "OpRecord.php", "line": "51"}, "connection": "wind_rich", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "insert into `wr_operation_logs` (`admin_uid`, `user_name`, `path`, `method`, `ip`, `input`, `updated_at`, `created_at`) values (0, '', 'ztfz/auth/login', 'GET', '127.0.0.1', '[]', '2025-08-01 11:53:55', '2025-08-01 11:53:55')", "type": "query", "params": [], "bindings": [0, "", "ztfz/auth/login", "GET", "127.0.0.1", "[]", "2025-08-01 11:53:55", "2025-08-01 11:53:55"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 51}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.1347098, "duration": 0.*****************, "duration_str": "233ms", "memory": 0, "memory_str": null, "filename": "OpRecord.php:51", "source": {"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FHttp%2FMiddleware%2FOpRecord.php&line=51", "ajax": false, "filename": "OpRecord.php", "line": "51"}, "connection": "wind_rich", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://sen.test/ztfz/auth/login", "action_name": "dcat.admin.", "controller_action": "App\\Admin\\Controllers\\AuthController@getLogin", "uri": "GET ztfz/auth/login", "controller": "App\\Admin\\Controllers\\AuthController@getLogin<a href=\"phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FHttp%2FControllers%2FAuthController.php&line=36\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/ztfz", "file": "<a href=\"phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FHttp%2FControllers%2FAuthController.php&line=36\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/dcat/laravel-admin/src/Http/Controllers/AuthController.php:36-43</a>", "middleware": "admin.app:admin, web, admin", "duration": "778ms", "peak_memory": "2MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1599439226 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1599439226\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1420857424 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1420857424\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-338926004 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"631 characters\">XSRF-TOKEN=eyJpdiI6IkVCTVkvSUl4VzcwWHYvRkVnZWtVT1E9PSIsInZhbHVlIjoiYUhaejJOdEpuL3ZWNmZRdmI3akpTdi9ObERidU9vOURjOVlpcElFZXN5ekVPZW9mbkJjUldCejBkZjNnK0ZoOSIsIm1hYyI6Ijg5NzhlMzBhZjc5YjUxZDc3OTJiYTYyY2FjY2M2MmU2ZGY2MThlZDEwNzIxNDZiYzJkMmYxNWU2ZGNhYTI1ZjgiLCJ0YWciOiIifQ%3D%3D; laravel_session=eyJpdiI6ImZIZ0R4cldMeUYvZUIxeUFjNG5yc0E9PSIsInZhbHVlIjoiWkt0RzNZaW1CVkliWWhuampyb0JuK0hsaUJlVHREdm5zY3Z2RTNGTzl3dUQvZ1c5VlJicENGTG94cU45a2N1cEVQOEtjRk5DcG9reGdFUjRvNjduSWNFU2hpWnYrMUtIUTNncHNQMmVycmdPU2tiK1hBdk1GdUxxU3FrM2tWSTUiLCJtYWMiOiJjOTIzNjYwNmVkYWQzZjNmY2JhMjdkOGU1MDYxMzVhODdjZWZiYWMyZWViNDgxYTBlNDFhOTI0MGNiYjBhOTFjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">zh-CN,zh;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">sen.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-338926004\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-97111519 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">uyEAzLeesCBRScai53ESzn27IqQX3yEsZcU5V6gY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-97111519\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1702438121 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Aug 2025 03:53:55 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1702438121\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-105775746 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>prev</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://sen.test/ztfz/auth/login</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">admin.prev.url</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Ia65Wa6468qgGAUSdE8rbvZeydWIRojrJeZ6TDDr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-105775746\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://sen.test/ztfz/auth/login", "action_name": "dcat.admin.", "controller_action": "App\\Admin\\Controllers\\AuthController@getLogin"}, "badge": null}}