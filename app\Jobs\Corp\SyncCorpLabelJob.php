<?php


namespace App\Jobs\Corp;

use App\Models\AdminSubUserAuthCorp;
use App\Models\CorpLabels;
use App\Services\Corp\WwCorpApiService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 同步企微标签队列
 */
class SyncCorpLabelJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $corpAuthId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($corpAuthId)
    {
        $this->corpAuthId = $corpAuthId;
    }

    /**
     * Execute the job.
     *
     * @return mixed
     */
    public function handle()
    {
        /** @var AdminSubUserAuthCorp $corpAuthInfo */
        $corpAuthInfo = AdminSubUserAuthCorp::query()->with("corpInfo")->find($this->corpAuthId);

        $date = date('Y-m-d H:i:s');
        $corpInfo = $corpAuthInfo->corpInfo;
        $data = WwCorpApiService::getCorpTagList($corpInfo);

        if (isset($data['tag_group'])) {
            $newLabelIds = [];
            $inserts = []; //新增的标签
            $updates = []; //更新的标签
            //已经存在的企微标签
            $existingTags = CorpLabels::query()
                ->select(['id', 'ww_corp_label_id', 'ww_corp_label_name', 'group_name'])
                ->where('corp_id', $corpAuthInfo->corp_id)
                ->get()
                ->keyBy('ww_corp_label_id');

            foreach ($data['tag_group'] as $tagGroup) {
                if (!empty($tagGroup['tag'])) {
                    foreach ($tagGroup['tag'] as $tag) {
                        $newLabelIds[] = $tag['id'];
                        //如果已经存在，则更新一下数据
                        if (isset($existingTags[$tag['id']])) {
                            // 更新现有标签
                            $updates[] = [
                                'id' => $existingTags[$tag['id']]->id,
                                'ww_corp_label_name' => $tag['name'] ?? '',
                                'group_id' => $tagGroup['group_id'] ?? '',
                                'group_name' => $tagGroup['group_name'] ?? '',
                                'updated_at' => $date
                            ];
                            continue;
                        }


                        //新增的标签，放到$inserts数组，循环后统一处理
                        $inserts[] = [
                            'corp_id' => $corpAuthInfo->corp_id,
                            'ww_corp_label_id' => $tag['id'],
                            'ww_corp_label_name' => $tag['name'] ?? '',
                            'group_id' => $tagGroup['group_id'],
                            'group_name' => $tagGroup['group_name'] ?? '',
                            'created_at' => $date,
                            'updated_at' => $date,
                        ];
                    }
                }
            }


            // 循环结束后一次性插入
            if (!empty($inserts)) {
                $inserts = array_chunk($inserts, 500); // 每次处理500条
                foreach ($inserts as $inset) {
                    CorpLabels::query()->insert($inset);
                }

            }
            //循环结束后 一次性更新
            if ($updates) {
                foreach ($updates as $update) {
                    CorpLabels::where('id', $update['id'])->update(
                        [
                            'ww_corp_label_name' => $update['ww_corp_label_name'],
                            'group_name' => $update['group_name'],
                            'updated_at' => $update['updated_at']
                        ]);
                }
            }
            //客户企微那边删除的标签，本地数据库也同步删除
            $oldLbaelIds = array_keys($existingTags->toArray());
            $diffLabelIds = array_diff($oldLbaelIds, $newLabelIds);
            if ($diffLabelIds) {
                CorpLabels::query()
                    ->where('corp_id', $corpAuthInfo->corp_id)
                    ->whereIn('ww_corp_label_id', $diffLabelIds)
                    ->delete();
            }
        }
        return true;
    }

}
