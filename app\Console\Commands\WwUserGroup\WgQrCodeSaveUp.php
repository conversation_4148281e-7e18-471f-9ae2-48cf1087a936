<?php

namespace App\Console\Commands\WwUserGroup;

use App\Models\WwUser;
use App\Models\WwUserQrcode;
use App\Services\Corp\WwCorpApiService;
use App\Services\NotifySendService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Zxing\QrReader;

class WgQrCodeSaveUp extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'WwUserGroup:WgQrCodeSaveUp';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Log::info('创建销售【群二维码定时】');
        //获取需要二维码的销售
        WwUser::query()->with("corpInfo")->where("type", 2)->where("add_method", 2)->chunkById(100, function ($rows) {
            $qrcodeNumMax = 1600;
            /** @var WwUser $wwUser */
            foreach ($rows as $wwUser) {
                //判断是否可见，只有可见的才会去生成
                if(!$wwUser->subscribe){
                    continue;
                }
                $count = WwUserQrcode::query()->where("is_used", 0)->where("ww_user_id", $wwUser->id)->count();
                if ($count < $qrcodeNumMax) {
                    for ($i = 0; $i < 30; $i++) { //一般一次生成30个
                        $state      = getLinkState();
                        $contactWay = WwCorpApiService::group_chat_add_join_way($wwUser->corpInfo, $wwUser->open_user_id, $state);
                        if (isset($contactWay['config_id'])) {
                            $contactWayData = WwCorpApiService::get_group_chat_contact_way($wwUser->corpInfo,$contactWay['config_id']);
                            if (isset($contactWayData['join_way']['config_id']) && isset($contactWayData['join_way']['qr_code'])) {
                                $qrcodeObj                = new WwUserQrcode();
                                $qrcodeObj->ww_user_id    = $wwUser->id;
                                $qrcodeObj->admin_uid     = $wwUser->admin_uid;
                                $qrcodeObj->ww_app_id     = $wwUser->ww_app_id;
                                $qrcodeObj->corp_id       = $wwUser->corp_id;
                                $qrcodeObj->corp_auth_id  = $wwUser->corp_auth_id;
                                $qrcodeObj->is_used       = 0;
                                $qrcodeObj->ww_user_type  = $wwUser->type;
                                $qrcodeObj->qrcode_type   = 1;
                                $qrcodeObj->qrcode_scene  = 2;
                                $qrcodeObj->qrcode_remark = '';
                                $qrcodeObj->skip_verify   = 1;
                                $qrcodeObj->state         = $state;
                                $qrcodeObj->config_id     = $contactWayData['join_way']['config_id'];
                                $qrcodeObj->qr_code       = $contactWayData['join_way']['qr_code'];

                                //以下字段是群码字段，使用默认值即可
                                $qrcodeObj->auto_create_room = 0;
                                $qrcodeObj->room_base_name   = '';
                                $qrcodeObj->room_base_id     = 0;
                                try {
                                    $link = $qrcodeObj->qr_code;
                                    $path = '/wwUser/qrcode/' . uniqid() . ".png";
                                    Storage::put($path, file_get_contents($link));
                                    $qrcode = new QrReader(Storage::path($path));
                                    $text = $qrcode->text(); //return decoded text from QR Code
                                    $qrcodeObj->qr_code_link  = $text;
                                    Storage::delete($path);
                                } catch (\Exception $exception) {
//                                    makeErrorLog('[系统任务][销售群二维码储备][新建失败-读取]' . $exception->getMessage(), ['ww_user_id' => $wwUser->id]);
                                    NotifySendService::sendWorkWeixinForError("[系统任务][销售群二维码储备][新建失败-获取] 销售：" . $wwUser->id . " 新建创建群二维码失败，失败原因是：" . $exception->getMessage());
                                    break;
                                }
                                $qrcodeObj->save();
                            }else{
                                $qrcodeErrMessage = isset($contactWay['errmsg']) ? explode(",", $contactWay['errmsg'])[0] : "";
//                                makeErrorLog('[系统任务][销售群二维码储备][新建失败-获取]' . $qrcodeErrMessage, ['ww_user_id' => $wwUser->id]);
                                NotifySendService::sendWorkWeixinForError("[系统任务][销售群二维码储备][新建失败-获取] 销售：" . $wwUser->id . " 新建创建群二维码失败，失败原因是：" . $qrcodeErrMessage);
                                break;
                            }
                        } else {
                            $qrcodeErrMessage = isset($contactWay['errmsg']) ? explode(",", $contactWay['errmsg'])[0] : "";
//                            makeErrorLog('[系统任务][销售群二维码储备][新建失败]' . $qrcodeErrMessage, ['ww_user_id' => $wwUser->id]);
//                            NotifySend::sendWorkWeixinForError("[系统任务][销售群二维码储备][新建失败] 销售：" . $wwUser->id . " 新建创建群二维码失败，失败原因是：" . $qrcodeErrMessage);
                            break;
                        }
                    }
                }
            }
        });
        return Command::SUCCESS;
    }
}
