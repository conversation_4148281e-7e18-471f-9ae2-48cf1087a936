<?php

    namespace App\Console\Commands\Tads;

    use App\Models\TencentAdAccount;

    use App\Services\TenCentAd\DmpService;
    use App\Services\TenCentAd\OauthAccountService;
    use Illuminate\Console\Command;

    class GetAccountList extends Command
    {
        /**
         * The name and signature of the console command.
         *
         * @var string
         */
        protected $signature = 'Tads:GetAccountList';

        /**
         * The console command description.
         *
         * @var string
         */
        protected $description = 'Command description';

        /**
         * Execute the console command.
         *
         * @return int
         */
        public function handle()
        {
            $accountInfos = TencentAdAccount::query()->find([6459, 6460, 6461, 6462, 6463, 6464, 6465, 6466, 6474, 14484]);// 众诺
            // $accountInfos = AdAccount::query()->find([10713, 10714, 10772, 10773, 10774, 10775, 10776, 10777, 10778, 10779, 10780, 10781, 10782, 10783, 10784]);//A1
            $accountInfos = TencentAdAccount::query()->find([19214,19215,19217]);// 众诺
            /** @var TencentAdAccount $accountInfo */
            foreach ($accountInfos as $accountInfo) {
                $adminUid = $accountInfo->admin_uid;
                $this->info($accountInfo->id);
                $accountIdData = [];
                OauthAccountService::organization_account_relation($accountInfo, $accountIdData);
                if (empty($accountIdData)) {
                    $this->info('请检查账户权限，未拉取到任何子户');
                    continue;
                }
                $this->info("账户数：" . count($accountIdData));
                $accountIds = [];
                foreach ($accountIdData as $item) {
                    $accountIds[] = $item['account_id'];
                }
                $adAccountIds = TencentAdAccount::query()->where("account_id", $accountIds)->pluck("account_id", "account_id")->toArray();
                //            $noMyIds = [********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,54339679,54339686,55406378,55467607,55467608,55467609,55467611,55467618,55467619,57697717,57697718,57697719,57697720,56318918,56318915,56318983,56318927,56318982,56318941,56318991,56318986,56318989,56318922,56318935,56318946,56318993,56319005,56319001,56318955,56318997,56318937,56318951,56319008,56319033,56319039,56319042,56319036,56319035,56980733,56980725,56980717,56980712,56980708,56980697,56980686,56980684,56980676,56980675,56980669,56980667,56980661,56980658,56980654,56980648,56980641,56980636,56980629,56980621,56980607,56980594,56980587,56980579,56980570,56980565,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********];
                $noMyIds = [];
                $count = count($accountIdData);
                $i = 0;
                foreach ($accountIdData as $accountIdDatum) {
                    $i++;
                    if (in_array($accountIdDatum['account_id'], $adAccountIds)) {
                        $this->info("已存在-1-".round($i/$count*100,2)."%");
                        continue;
                    }
                    if (in_array($accountIdDatum['account_id'], $noMyIds)) {
                        $this->info("外放户跳过".round($i/$count*100,2)."%");
                        continue;
                    }
                    $lastAccount = TencentAdAccount::query()->where("account_id", $accountIdDatum['account_id'])->first();
                    if ($lastAccount) {
                        $this->info("已存在-2-".round($i/$count*100,2)."%");
                        continue;
                    }
                    $adAccount = new TencentAdAccount();
                    $adAccount->account_id = $accountIdDatum['account_id'];
                    $adAccount->access_token = $accountInfo->access_token;
                    $adAccount->refresh_token = $accountInfo->refresh_token;
                    $adAccount->access_token_expires_in = $accountInfo->access_token_expires_in;
                    $adAccount->refresh_token_expires_in = $accountInfo->refresh_token_expires_in;
                    $adAccount->scope_list = $accountInfo->scope_list;
                    $adAccount->account_role_type = $accountInfo->account_role_type;
                    $adAccount->wechat_account_id = $accountInfo->wechat_account_id;
                    $adAccount->account_type = 'ACCOUNT_TYPE_ADVERTISER';
                    $adAccount->role_type = $accountInfo->role_type;
                    $adAccount->account_name = $accountInfo->account_name;
                    $adAccount->login_name = $accountInfo->login_name;
                    $adAccount->admin_uid = $adminUid;
                    $adAccount->corporation_name = $accountIdDatum['corporation_name'];
                    $adAccount->save();
                    $this->info(round($i/$count*100,2)."%");
                    DmpService::autoPush($adAccount);
                }
            }
            return Command::SUCCESS;
        }
    }
