<?php

namespace App\Admin\Controllers;

use Admin;
use App\Admin\Actions\Grid\wwUserAddRecord\BatchOperateAddFans;
use App\Admin\Actions\Grid\wwUserAddRecord\OcpxUploadGridAction;
use App\Admin\Extensions\Tools\WwUserAddRecord\AddRecordExportTool;
use App\Admin\Repositories\WwUserAddRecord;
use App\Models\AdminDomain;
use App\Models\AdminSubUser;
use App\Models\AdminSubUserAuthCorp;
use App\Models\AdminUser;
use App\Models\ShieldIp;
use App\Models\WwLink;
use App\Models\WwUser;
use App\Models\WwUsersGroup;
use App\Services\Ocpx\OcpxSubmitService;
use App\Services\Tools\UtilsService;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Show;
use Dcat\Admin\Widgets\Tooltip;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Vtiful\Kernel\Excel;

/**
 * @property WwLink $linkInfo
 * @property        $input
 * @property $admin_uid
 * @property $adminInfo
 * @property $id
 */
class WwUserAddRecordController extends AdminController
{
    private const SITE_SET_MAPPING = [
        'SITE_SET_MOBILE_UNION' => '优量汇',
        'SITE_SET_KUAISHOU' => '快手',
        'SITE_SET_WECHAT' => '微信公众号与小程序',
        'SITE_SET_MOBILE_INNER' => 'QQ、腾讯看点、腾讯音乐 (待废弃)',
        'SITE_SET_TENCENT_NEWS' => '腾讯新闻',
        'SITE_SET_TENCENT_VIDEO' => '腾讯视频',
        'SITE_SET_MOBILE_YYB' => '应用宝',
        'SITE_SET_PCQQ' => 'QQ、QQ 空间、腾讯音乐、PC 版位',
        'SITE_SET_KANDIAN' => 'QQ 浏览器（原腾讯看点）',
        'SITE_SET_QQ_MUSIC_GAME' => 'QQ、腾讯音乐及游戏',
        'SITE_SET_MOMENTS' => '微信朋友圈',
        'SITE_SET_MINI_GAME_WECHAT' => '微信生态内的小游戏场景',
        'SITE_SET_MINI_GAME_QQ' => '手机 QQ 生态内的小游戏场景',
        'SITE_SET_MOBILE_GAME' => '集合腾讯游戏和优量汇联盟生态的手机端游戏',
        'SITE_SET_QQSHOPPING' => 'QQ 购物',
        'SITE_SET_CHANNELS' => '微信视频号',
        'SITE_SET_WECHAT_PLUGIN' => '微信新闻插件',
        'SITE_SET_SEARCH_SCENE' => '搜索场景',
    ];

    /**
     * 判断是否默认展示当天的进粉记录
     *
     * @param Content $content
     *
     * @return Content|RedirectResponse
     */

    public function index(Content $content): Content|RedirectResponse
    {
        /** @var AdminUser $adminUser */
        $adminUser = Admin::user();
        if ($adminUser->today_add_record == 1) {
            /** 通过重定向方式，将请求跳转到带有当天筛选条件的页面 */
            $this->redirectToday();
        }
        return $content
            ->header('加粉明细')
            ->body($this->grid());
    }


    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid(): Grid
    {
        return Grid::make(new WwUserAddRecord([ 'wwUserInfoWithTrashed', 'linkInfo', 'adminInfo', 'corpInfoWithTrashed']), function (Grid $grid) {
            /** @var AdminUser $adminUser */
            $adminUser = Admin::user();

            $grid->paginate(AdminUser::getPaginate());
            $grid->disableCreateButton();
            $grid->disableViewButton();
            $grid->disableDeleteButton();
            $grid->disableEditButton();
            $grid->disableBatchDelete();
            $grid->showColumnSelector();

            /** 自带导出工具 */
            /*$grid->export()->csv()->rows(function ($rows) {
                return $this->exportData($rows);
            })->chunkSize(1000);*/

            if (!Admin::user()->isRole('wop')) {
                /** 工具栏 */
                $this->GridTools($grid);
                /** 行操作栏 */
                $this->GridActions($grid);
            } else {
                $grid->disableActions();
            }

            if (!AdminUser::isSystemOp()) {
                $grid->model()->whereIn("admin_uid", AdminSubUser::getAdminUids($adminUser->id));
                /** 归属账号 */
                $this->addBelongAdminColumn($grid);
            } else {
                /** 归属账号 */
                $this->addBelongAdminColumn($grid);
                $grid->column("click_id", "广告标识符");
            }

            $grid->model()->orderByDesc("id");

            $hideColumn = [];
            if (!Admin::user()->isRole('customer')) { //如果是子账户，查看是否需要隐藏列
                $hideColumnData = AdminSubUser::query()->where('admin_uid', $adminUser->id)->value('hide_add_record_column');
                $hideColumnKeys = json_decode($hideColumnData, true);
                if (!$hideColumnKeys) $hideColumnKeys = [];
                $hideColumn = $hideColumnKeys;
            }

            $grid->fixColumns(0, -1);
            $grid->column('id')->sortable();

            if (!in_array('corp_name', $hideColumn)) {
                $grid->column('corpInfoWithTrashed.corp_name', '企微');
            }
            if (!in_array('ww_user_name', $hideColumn)) {
                $grid->column('wwUserInfoWithTrashed.name', '销售名称')->ClickCopy(8);
            }
            if (!in_array('ww_user_id', $hideColumn)) {
                $grid->column('wwUserInfoWithTrashed.user_id', "销售ID")->ClickCopy(5, '');
            }
            $grid->column("ww_user_group_name", "销售分组")->ClickCopy(8);
            $grid->column('name');
            $grid->column('avatar')->image("", 30);
            $grid->column('follow_user_remark_tags_string', '客户标签')->display(function ($value) {
                $tag = str_replace(",", "<br/>", htmlspecialchars($value));
                $tag = str_replace(PHP_EOL, "", $tag);
                Tooltip::make('.block_area_help_message' . $this->id)->title($tag);

                $devTag = $tag;
                $len    = 20;

                // 使用mb_strlen和mb_substr处理多字节字符
                if (mb_strlen($devTag, 'UTF-8') > $len) {
                    $devTag = mb_substr($devTag, 0, $len, 'UTF-8') . '...';
                }
                return '<div class="block_area_help_message' . $this->id . '">' . $devTag . '</div>';
            });

            $grid->column('customer_start_chat', '开口')->using([
                0 => '否',
                1 => '是'
            ]);
            $grid->column('is_delete')->using([
                0 => '正常',
                1 => '客户删除',
                2 => '销售删除',
            ]);
            $grid->column('follow_user_remark', '微信备注');

            $grid->column("ww_user_wind_label", "智投标签")->help("销售配置的智投标签")->ClickCopy(8);
            $grid->column('linkInfo.remark', "链接")->display(function ($value) {
                if(!isset($value)){
                    return '无';
                }
                return $value . "「ID:" . $this->linkInfo->id . "」";
            });
            $grid->column('linkInfo.account_id', "账户ID")->ClickCopy(9);
            $grid->column('linkInfo.link_ad_id', '链接')->display(function ($value) {
                $host = AdminDomain::getDomainHost();
                if (empty($host)) {
                    return '未配置域名';
                }
                if(isset($this->linkInfo->media_type)){
                    if ($this->linkInfo->media_type == '腾讯广告') {
                        return $host . "/wec?adt=" . $value;
                    } else {
                        return $host . "/ordinary?adt=" . $value;
                    }
                }
                return '无';

            })->ClickCopy(0, "复制");
            $grid->column('ip')->display(function ($ip) {
                if (in_array($ip, ShieldIp::getIpList())) {
                    Tooltip::make('.black_ip' . $this->id)->title('该ip已被拉黑');
                    return '<span class="black_ip' . $this->id . '" style="text-decoration: line-through; color: red;">' . $ip . '</span>';
                }
                return $ip;
            });
            $grid->column('area');
            $grid->column('ua')->display(function ($value) {
                return getPhoneType($value);
            });
            $grid->column('view_count');
            $grid->column('link_view_created_at', '访问时间');
            $grid->column('external_contact_created_at', '加粉时间')->sortable();



            $grid->column('ocpx_result_string', '上报结果')->display(function ($value) {
                $list = json_decode($value, true);
                if (empty($list)) {
                    return "无";
                }
                $string = [];
                foreach ($list as $k => $v) {
                    $string[] = OcpxSubmitService::ACTION_LIST[$k] . "：" . $v;
                }
                return implode("<br/>", $string);
            });
            $grid->column('adgroup_name', '广告名称')->copyable();
            //$grid->column('dynamic_creative_id','创意ID')->copyable();
            $grid->column('dynamic_creative_name', '创意名称')->copyable();
            $grid->column('site_set_name', '版位')->using(self::SITE_SET_MAPPING)->copyable();
            $grid->filter(function (Grid\Filter $filter) use ($adminUser) {
                $filter->expand(); // 默认展开筛选条件
                $filter->panel();
                //所有带「所属账户」的列表，主账户都增加对应筛选
                if ($adminUser->parent_id == 0) { //主账号
                    if (AdminUser::isSystemOp()) {
                        $filter->equal("admin_uid", '操作账号')->select(AdminUser::query()->pluck("username", 'id')->toArray())->width(2);
                    } else {
                        $adminIds = AdminSubUser::getALLAdminUids($adminUser->id);
                        $filter->equal("admin_uid", '操作账号')->select(AdminUser::query()->whereIn("id", $adminIds)->pluck("username", 'id')->toArray())->width(2);
                    }
                }
                $filter->equal('id')->width(2);
                $corpAuthRecord = AdminSubUserAuthCorp::getCorpAuthListBuyAdminUid($adminUser->id);
                $filter->equal("corp_id", "企业微信")->select($corpAuthRecord)->width(2);

                $filter->where('ww_user_id', function ($query) use ($adminUser) {
                    if (AdminUser::isSystemOp()) {
                        $wwUsers = WwUser::withTrashed()->orderByDesc('id')->where("name", $this->input)->pluck('id');
                    } else {
                        $wwUsers = WwUser::withTrashed()->orderByDesc('id')->whereIn("admin_uid", AdminSubUser::getAdminUids($adminUser->id))->where("name", $this->input)->pluck('id');
                    }
                    $query->whereIn("ww_user_id", $wwUsers);
                }, '销售名称')->width(2);
                $filter->where('ww_user_user_id', function ($query) use ($adminUser) {
                    if (AdminUser::isSystemOp()) {
                        $wwUsers = WwUser::withTrashed()->orderByDesc('id')->where("user_id", $this->input)->pluck('id');
                    } else {
                        $wwUsers = WwUser::withTrashed()->orderByDesc('id')->whereIn("admin_uid", AdminSubUser::getAdminUids($adminUser->id))->where("user_id", $this->input)->pluck('id');
                    }
                    $query->whereIn("ww_user_id", $wwUsers);
                }, '销售ID')->width(2);

                $filter->in('ww_user_group_id', '销售分组')->multipleSelect(WwUsersGroup::getMyAdGroupList())->width(2);
                $filter->equal('name', '客户名称')->width(2);
                $filter->like("area", '客户地域')->width(2);
                $filter->equal('is_delete', '好友状态')->select([
                    0 => '正常',
                    1 => '客户删除',
                    2 => '销售删除',
                ])->width(2);
                $filter->equal('customer_start_chat', '开口')->select([
                    0 => '否',
                    1 => '是',
                ])->width(2);
                $filter->where('account_id', function ($query) use($adminUser) {
                    $input = trim($this->input);
                    if (empty($keyWord)) {
                        return;
                    }
                    $keyWords = UtilsService::splitKeywords($input);
                    if(AdminUser::isSystemOp()){
                        $wwLinkIds = WwLink::withTrashed()->whereIn("account_id",$keyWords)->pluck("id");
                    }else{
                        $wwLinkIds = WwLink::withTrashed()
                            ->whereIn("admin_uid",AdminSubUser::getAdminUids($adminUser->id))
                            ->whereIn("account_id",$keyWords)
                            ->pluck("id");
                    }
                    $query->whereIn("ww_link_id", $wwLinkIds);
                }, '账户ID')->width(2);
                $filter->like("adgroup_name","广告名称")->width(2);
                $filter->like("follow_user_remark_tags_string", '客户标签')->width(2);
                $filter->equal("ww_user_wind_label", "智投标签")->width(2);

                $filter->where('linkInfoRemark', function ($query) use($adminUser)  {
                    if(AdminUser::isSystemOp()){
                        $wwLinkIds = WwLink::query()
                            ->where("remark",'like', "%$this->input%")
                            ->pluck("id");
                    }else{
                        $wwLinkIds = WwLink::query()
                            ->whereIn("admin_uid",AdminSubUser::getAdminUids($adminUser->id))
                            ->where("remark",'like', "%$this->input%")
                            ->pluck("id");
                    }
                    $query->whereIn("ww_link_id", $wwLinkIds);
                }, '链接备注')->width(2);
                $filter->equal("ip", "IP")->width(2);

                $ocpxResultList = [
                    '成功'     => '成功',
                    '部分成功' => '部分成功',
                    '同行为'   => '同行为',
                    '未配置'   => '未配置',
                    '过滤'     => '过滤',
                    '不上报'   => '不上报',
                    '失败'     => '失败',
                ];
                $filter->where('ocpx_result_string', function ($query) use ($ocpxResultList) {
                    $query->where('ocpx_result_string', 'like', "%$this->input%");
                }, "上报结果")->width(2)->select($ocpxResultList);

                $filter->dateRange("external_contact_created_at", "加粉时间")->datetime()->width(4);
                $filter->whereBetween("link_view_created_at", function ($query) {
                    if (isset($this->input['start'])) {
                        $query->where('link_view_created_at', '>=', $this->input['start'] . " 00:00:00");
                    }
                    if (isset($this->input['end'])) {
                        $query->where('link_view_created_at', '<=', $this->input['end'] . " 23:59:59");
                    }
                }, '访问时间')->datetime(['format' => 'YYYY-MM-DD'])->width(4);

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail(mixed $id): Show
    {
        return Show::make($id, new WwUserAddRecord(), function (Show $show) {
            if (!AdminUser::isAdmin($show->model())) {
                $show->field('N')->value("无数据");
            } else {
                $show->field('id');
            }
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form(): Form
    {
        return Form::make(new WwUserAddRecord(), function (Form $form) {
            if (!$form->isCreating() && !AdminUser::isAdmin($form->model())) {
                $form->display('N')->value("无数据");
            } else {
                $form->display('id');
            }
        });
    }

    private function exportData($rows): array
    {
        /** @var AdminUser $adminUser */
        $adminUser = Admin::user(); // 获取当前登录用户
        foreach ($rows as $key => $row) {
            // 处理归属账号逻辑
            if ($adminUser->id == $row->admin_uid) {
                $rows[$key]['归属账号'] = '主账号';
            } else {
                $username               = $row->adminInfo ? $row->adminInfo->username : '';
                $rows[$key]['归属账号'] = str_replace($adminUser->username, '', $username);
            }
            if (AdminUser::isSystemOp()) {
                $rows[$key]['广告标识符'] = $row->click_id;
            }
            $rows[$key]['corpInfoWithTrashed.corp_name'] = ($row->corpInfoWithTrashed) ? $row->corpInfoWithTrashed->corp_name : "";
            $rows[$key]['wwUserInfoWithTrashed.name']    = ($row->wwUserInfoWithTrashed) ? $row->wwUserInfoWithTrashed->name : "";
            $rows[$key]['wwUserInfoWithTrashed.user_id'] = ($row->wwUserInfoWithTrashed) ? $row->wwUserInfoWithTrashed->user_id : "";
            if ($row->linkInfo) {
                $rows[$key]['linkInfo.remark']     = $row->linkInfo->remark . "「" . $row->linkInfo->id . "」";
                $rows[$key]['linkInfo.account_id'] = $row->linkInfo->account_id;
                $host                              = '域名';
                if ($row->linkInfo->media_type == '腾讯广告') {
                    $link = $host . "/wec?adt=" . $row->linkInfo->link_ad_id;
                } else {
                    $link = $host . "/ordinary?adt=" . $row->linkInfo->link_ad_id;
                }
                $rows[$key]['linkInfo.link_ad_id'] = $link;
            }
            $rows[$key]['ua']                  = getPhoneType($row['ua']);
            $rows[$key]['customer_start_chat'] = ($row['customer_start_chat']) ? "是" : "未开";
            $rows[$key]['is_delete']           = match ($row['is_delete']) {
                0       => "正常",
                1       => "客户删除",
                2       => "销售删除",
                default => '未知',
            };
            $ocpxString                        = $row['ocpx_result_string'];
            foreach (OcpxSubmitService::ACTION_LIST as $k => $v) {
                $ocpxString = str_replace($k, $v, $ocpxString);
            }
            $rows[$key]['ocpx_result_string'] = $ocpxString;
            $rows[$key]['site_set_name'] = self::SITE_SET_MAPPING[$row['site_set_name']] ?? '未知';
        }
        return $rows;
    }

    public function destroy($id): Response|JsonResponse
    {
        return $this->form()->response()->error("无权限操作");
    }

    /**
     * 工具栏
     * @param $grid
     * @return void
     */
    private function GridTools($grid): void
    {
        $grid->tools(function ($tools) {
            /** 上报 */
//            $tools->append(new OcpxUploadsTools());
//            /** 拉黑 */
//            $tools->append(new WwUsersToBlack());

            /** 其他操作 */
            $tools->append(new BatchOperateAddFans());
            /** 添加自定义导出工具 */
            $tools->append(new AddRecordExportTool());
            /** 深度回传 */
//            $tools->append(new OcpxDeepUploadsTools());
        });
    }

    /**
     * 行操作栏
     * @param $grid
     * @return void
     */
    private function GridActions($grid): void
    {
        $grid->actions(function (Grid\Displayers\Actions $actions) {
            /** 单个手动上报 */
            $actions->prepend(new OcpxUploadGridAction());
        });
    }


    /**
     * 使用xlswriter自定义导出功能
     *
     * @return BinaryFileResponse
     */
    public function xlsExport(): BinaryFileResponse
    {
        $exportType = request('_export_', 'all');
        $selectedIds = request('__rows__', '');

        // 处理选中的ID
        if ($selectedIds) {
            $selectedIds = explode(',', $selectedIds);
        } else {
            $selectedIds = [];
        }

        // 配置excel
        $config = [
            'path' => storage_path('app/exports/') // xlsx文件保存路径
        ];

        // 确保目录存在
        if (!file_exists($config['path'])) {
            mkdir($config['path'], 0755, true);
        }

        $excel = new Excel($config);

        // 生成文件名
        $filename = '进粉记录-' . date('Y-m-d-H-i-s') . mt_rand(1000000,9999999) . '.xlsx';

        // 获取数据
        $query = $this->buildExportQuery($exportType, $selectedIds);

        // 设置表头
        $headers = $this->getExportHeaders();

        // 创建工作表并设置表头
        $filePath = $excel->fileName($filename, 'sheet1')
            ->header($headers);

        // 分批获取数据并写入
        $this->writeDataToExcel($excel, $query);

        $outputPath = $filePath->output();

        // 返回文件下载响应
        return response()->download($outputPath, $filename)->deleteFileAfterSend();
    }

    /**
     * 构建导出查询
     */
    private function buildExportQuery($exportType, $selectedIds = [])
    {
        /** @var AdminUser $adminUser */
        $adminUser = Admin::user();
        // 使用模型直接构建查询
        $model = \App\Models\WwUserAddRecord::with(['wwUserInfoWithTrashed', 'linkInfo', 'adminInfo', 'corpInfoWithTrashed']);

        // 应用相同的权限过滤
        if (!AdminUser::isSystemOp()) {
            $model = $model->whereIn("admin_uid", AdminSubUser::getAdminUids($adminUser->id));

        }

        // 应用当前筛选条件
        $this->applyFilters($model);

        switch ($exportType) {
            case 'page:1':
                // 当前页
                $model = $model->limit(AdminUser::getPaginate());
                break;
            case 'selected:__rows__':
                // 选择的行
                if (!empty($selectedIds)) {
                    $model = $model->whereIn('id', $selectedIds);
                }
                break;
            case 'all':
            default:
                // 全部数据，不做额外限制
                break;
        }

        return $model->orderByDesc('id');
    }

    /**
     * 应用当前页面的筛选条件
     */
    private function applyFilters($model): void
    {
        $filters = request()->all();

        // 应用各种筛选条件
        if (isset($filters['id']) && $filters['id']) {
            $model->where('id', $filters['id']);
        }

        if (isset($filters['corp_id']) && $filters['corp_id']) {
            $model->where('corp_id', $filters['corp_id']);
        }

        if (isset($filters['external_contact_created_at'])) {
            $dateRange = $filters['external_contact_created_at'];
            if (isset($dateRange['start']) && $dateRange['start']) {
                $model->where('external_contact_created_at', '>=', $dateRange['start']);
            }
            if (isset($dateRange['end']) && $dateRange['end']) {
                $model->where('external_contact_created_at', '<=', $dateRange['end']);
            }
        }

        // 更多筛选条件可以在这里添加...

    }

    /**
     * 获取导出表头
     */
    private function getExportHeaders(): array
    {
        $headers = [
            'ID',
            '归属账号',
            '企微',
            '销售名称',
            '销售ID',
            '销售分组',
            '智投标签',
            '链接',
            '账户ID',
            '链接地址',
            'IP',
            '客户地域',
            '设备类型',
            '访问次数',
            '加粉时间',
            '开口',
            '访问时间',
            '客户名称',
            '好友状态',
            '客户微信备注',
            '客户标签',
            '上报结果',
            '广告名称',
            '创意名称',
            '版位'
        ];

        if (AdminUser::isSystemOp()) {
            array_splice($headers, 1, 0, ['广告标识符']); // 在第二位插入广告标识
        }

        return $headers;
    }

    /**
     * 写入数据到Excel
     */
    private function writeDataToExcel($excel, $query): void
    {
        $chunk_size = 1000;
        $adminUser = Admin::user();
        $allData = [];

        $query->chunk($chunk_size, function ($records) use (&$allData, $adminUser) {
            foreach ($records as $record) {
                $row = [
                    $record->id,
                    $this->getAccountName($record, $adminUser),
                    $record->corpInfoWithTrashed ? $record->corpInfoWithTrashed->corp_name : '',
                    $record->wwUserInfoWithTrashed ? $record->wwUserInfoWithTrashed->name : '',
                    $record->wwUserInfoWithTrashed ? $record->wwUserInfoWithTrashed->user_id : '',
                    $record->ww_user_group_name ?? '',
                    $record->ww_user_wind_label ?? '',
                    $this->getLinkInfo($record),
                    $record->linkInfo ? $record->linkInfo->account_id : '',
                    $this->getLinkUrl($record),
                    $record->ip ?? '',
                    $record->area ?? '',
                    getPhoneType($record->ua ?? ''),
                    $record->view_count ?? 0,
                    $record->external_contact_created_at ?? '',
                    $record->customer_start_chat ? '是' : '否',
                    $record->link_view_created_at ?? '',
                    $record->name ?? '',
                    $this->getDeleteStatus($record->is_delete),
                    $record->follow_user_remark ?? '',
                    str_replace(',', ' ', $record->follow_user_remark_tags_string ?? ''),
                    $this->getOcpxResult($record->ocpx_result_string),
                    $record->adgroup_name ?? '',
                    $record->dynamic_creative_name ?? '',
                    $this->getSiteSetName($record->site_set_name ?? '')
                ];

                if (AdminUser::isSystemOp()) {
                    array_splice($row, 1, 0, [$record->click_id]); // 在第二位插入广告标识符
                }

                $allData[] = $row;
            }
        });

        // 一次性写入所有数据
        if (!empty($allData)) {
            $excel->data($allData);
        }
    }

    /**
     * 获取账号名称
     */
    private function getAccountName($record, $adminUser): array|string
    {
        if ($adminUser->id == $record->admin_uid) {
            return '主账号';
        } else {
            $username = $record->adminInfo ? $record->adminInfo->username : '';
            return str_replace($adminUser->username, '', $username);
        }
    }

    /**
     * 获取链接信息
     */
    private function getLinkInfo($record): string
    {
        if (!$record->linkInfo) {
            return '无';
        }
        return $record->linkInfo->remark . '「ID:' . $record->linkInfo->id . '」';
    }

    /**
     * 获取链接URL
     */
    private function getLinkUrl($record): string
    {
        if (!$record->linkInfo) {
            return '无';
        }

        $host = AdminDomain::getDomainHost();
        if (empty($host)) {
            return '未配置域名';
        }

        if ($record->linkInfo->media_type == '腾讯广告') {
            return $host . "/wec?adt=" . $record->linkInfo->link_ad_id;
        } else {
            return $host . "/wl?adt=" . $record->linkInfo->link_ad_id;
        }
    }

    /**
     * 获取删除状态
     */
    private function getDeleteStatus($status): string
    {
        return match ($status) {
            0 => '正常',
            1 => '客户删除',
            2 => '销售删除',
            default => '未知',
        };
    }

    /**
     * 获取上报结果
     */
    private function getOcpxResult($result): string
    {
        if (empty($result)) {
            return '无';
        }

        $list = json_decode($result, true);
        if (empty($list)) {
            return '无';
        }

        $string = [];
        foreach ($list as $k => $v) {
            $actionName = OcpxSubmitService::ACTION_LIST[$k] ?? $k;
            $string[] = $actionName . '：' . $v;
        }

        return implode(' ', $string);
    }

    /**
     * 获取版位名称
     */
    private function getSiteSetName($siteSet): string
    {
        return self::SITE_SET_MAPPING[$siteSet] ?? $siteSet;
    }

    /**
     * 归属账号列
     * @param $grid
     * @return void
     */
    private function addBelongAdminColumn($grid): void
    {
        /** @var AdminUser $adminUser */
        $adminUser = Admin::user();
        $grid->column('归属账号')->display(function () use ($adminUser) {
            if ($adminUser->id == $this->admin_uid) { //如果是自己创建的则直接显示主账号
                return '主账号';
            } else {
                $username = $this->adminInfo?->username;
                return str_replace($adminUser->username, '', $username);
            }
        });
    }

    /**
     * 通过重定向方式，将请求跳转到带有当天筛选条件的页面
     * @return void
     */
    private function redirectToday(): void
    {
        if (!request()->has('reset')) {
            $today = now()->format('Y-m-d');
            /**  获取当前路由地址 */
            $currentUrl = url()->current();

            /** 查询参数 */
            $query = [
                'external_contact_created_at' => [
                    'start' => $today . ' 00:00:00',
                    'end' => $today . ' 23:59:59'
                ],
                'reset' => 1
            ];
            /** 重定向 */
            redirect()->to($currentUrl . '?' . http_build_query($query));
        }
    }
}
