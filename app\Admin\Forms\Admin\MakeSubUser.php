<?php

namespace App\Admin\Forms\Admin;

use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Models\AdminRoleUser;
use App\Models\AdminSubUser;
use App\Models\AdminUser;
use Dcat\Admin\Admin;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Widgets\Form;

class MakeSubUser extends Form
{
    const ROLE_TYPE_VIEW = [
        '7s3sYo9mFKuwMGyU9oWo' => '运营'
    ];
    const ROLE_TYPE = [
        '7s3sYo9mFKuwMGyU9oWo' => 3,//customer_op
    ];

    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return JsonResponse
     */
    public function handle(array $input): JsonResponse
    {
        // dump($input);
        if (empty($input['username'])) {
            $this->response()->alert()->error('请填写子账号用户名.');
        }
        if (empty($input['password'])) {
            $this->response()->alert()->error('请填写子账号密码.');
        }
        $username = Admin::user()->username . $input['username'];

        $lastAdmin = AdminUser::query()->withTrashed()->where("username", $username)->first();
        if ($lastAdmin) {
            return $this->response()->withValidation([
                'username' => '账号已被占用。'
            ])->alert()->error("账号已占用。");
        }


        //为他新建用户
        $adminUser = new AdminUser();
        $adminUser->username = $username;
        $adminUser->name = $username;
        $adminUser->password = bcrypt($input['password']);
        $adminUser->parent_id = Admin::user()->id;
        $adminUser->save();

        //获取上级用户信息 并同步更新：去重策略以及企微机器人等信息
        $parentAdminUser = AdminUser::query()->where('id', $adminUser->parent_id)->first();
        if ($parentAdminUser) {
            $adminUser->wechat = $parentAdminUser->wechat;
            $adminUser->dingding = $parentAdminUser->dingding;
            $adminUser->feishu = $parentAdminUser->feishu;
            $adminUser->repeat_ww_link = $parentAdminUser->repeat_ww_link;
            $adminUser->today_add_record = $parentAdminUser->today_add_record;
            $adminUser->show_ww_user_policy = $parentAdminUser->show_ww_user_policy;
            /** 操作日志表跟随主账号 */
            $adminUser->action_log_table = Admin::user()->action_log_table;
            $adminUser->save();
        }

        //创建子账户授权
        $subAdmin = new AdminSubUser();
        $subAdmin->admin_uid = $adminUser->id;
        $subAdmin->parent_id = $adminUser->parent_id;
        $subAdmin->save();

        //赋予权限
        $roleUser = new AdminRoleUser();
        $roleUser->role_id = self::ROLE_TYPE[$input['role']];
        $roleUser->user_id = $adminUser->id;
        $roleUser->save();

        AdminActionLogJob::dispatch(
            'add_sub_user',
            $adminUser->id,
            AdminActionLog::ACTION_TYPE['账号'],
            "新增子账号，ID：" . $adminUser->id."，账号：" . $adminUser->username,
            getIp(),
            Admin::user()->id
        )->onQueue('admin_action_log_job');

        return $this
            ->response()
            ->alert()
            ->success('新增子账号【' . $adminUser->username . '】成功')
            ->refresh();
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        $this->text('username')->prepend(Admin::user()->username)->required()->help("子账号自动带有主账号的用户民前缀，例如：主账号是“智投方舟”，填写的子账号用户名是：测试子账号，则新建的子账号用户名为：“智投方舟测试子账号”");
        $this->password('password')->minLength(6)->maxLength(20)->required()->help("密码长度最小6位，最大20位");
        $this->select("role", '角色')->options(self::ROLE_TYPE_VIEW)->required()->help("角色说明：<br/>可授权企业微信、广告账户、新建落地页，管理员可转移广告账户至子账号、授权使用主账号的企业微信主体");
        $this->confirm('确认提交？');
    }
}
