<?php

	namespace App\Admin\Renderable\WwUserGroups;

    use App\Admin\Repositories\WwUser;
    use App\Models\AdminUser;
    use Dcat\Admin\Admin;
    use Dcat\Admin\Grid;
    use Dcat\Admin\Grid\LazyRenderable;

    /**
	 * @property mixed|null $link_id
	 */
	class ViewGroupsUser extends LazyRenderable
	{
		public function grid(): Grid
		{
			return Grid::make(new WwUser(['corpInfo']), function (Grid $grid) {//pressRecords
				$grid->showRefreshButton();
				$grid->disableRowSelector();
                $grid->paginate(10);
                $grid->disableActions();
				$grid->model()->whereIn("id", $this->id)->orderByDesc('id');
                /** @var AdminUser $adminUser */
                $adminUser = Admin::user();
                if (Admin::user()->isRole('customer') || AdminUser::isSystemOp()) { //如果是主账户，显示自身全部销售，以及子账户的全部销售
                    $grid->column('归属账号')->display(function () use ($adminUser) {
                        if ($adminUser->id == $this->admin_uid) { //如果是自己创建的则直接显示主账号
                            return '主账号';
                        } else {
                            $username = $this->adminInfo->username ?? '';
                            return str_replace($adminUser->username, '', $username);
                        }
                    });
                }
				$grid->column('id', 'ID')->sortable();
                $grid->column('corpInfo.corp_name','企业名称');
                $grid->column('name','昵称');
                $grid->column('user_id','销售ID');


                $grid->filter(function (Grid\Filter $filter) {
                    $filter->panel();
                    $filter->expand();
                    $filter->equal('id')->width(2);
                    $filter->like('corpInfo.corp_name','企业名称')->width(2);
                    $filter->like('name','昵称')->width(2);
                    $filter->equal('user_id','销售ID')->width(2);

                });
			});
		}
	}
