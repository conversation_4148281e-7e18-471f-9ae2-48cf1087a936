<?php

	namespace App\Http\Controllers;

	use App\Models\WwAppList;
	use App\Models\WwCorpInfo;
	use EasyWeChat\Kernel\Exceptions\InvalidArgumentException;
	use EasyWeChat\OpenWork\Application;
	use Illuminate\Support\Facades\Cache;
    use Illuminate\Support\Facades\Log;

    class WorkWeixinAppController
	{
		public static function getCorpToken(WwCorpInfo $corpInfo)
		{
			if ($corpInfo->at_ex_time >= date("Y-m-d H:i:s", time())) {
				return $corpInfo->access_token;
			}
			//查询应用是不是自建应用或者代开发，如果是新建app，如果不是使用第三方app
			$wwApp = WwAppList::query()->where("suite_id", $corpInfo->suite_id)->first();
			if (!$wwApp) {
				return false;
			}
			if (!$wwApp->is_3rd) {
				//如果不是第三方应用，那么就需要创建自建app进行返回token，自建饮用需要单独处理
				$config = [
					'corp_id' => $corpInfo->corp_id,
					'secret'  => $corpInfo->secret,
					'token'   => $wwApp->token,
					'aes_key' => $wwApp->aes_key,
				];
				$app    = new \EasyWeChat\Work\Application($config);
				$app->setCache(Cache::store("redis"));
				$accessTokenObj = $app->getAccessToken();
				$accessToken    = $accessTokenObj->getToken(); // string
			} else {
				//如果是三方应用，去获取三方app，然后获取token
				$app                   = self::getApp($corpInfo->suite_id);
				$suiteAccessToken      = $app->getSuiteAccessToken();
				$authorizerAccessToken = $app->getAuthorizerAccessToken($corpInfo->corp_id, $corpInfo->secret, $suiteAccessToken);
				$accessToken           = $authorizerAccessToken->getToken(); // string
			}

			$corpInfo->at_ex_time   = date("Y-m-d H:i:s", time() + 7200);
			$corpInfo->access_token = $accessToken;
			$corpInfo->save();
			return $accessToken;
		}

		/**
		 * @return Application
		 * @throws InvalidArgumentException
		 */
		public static function getApp($suite_id = '', $sid = ''): Application
		{
			//这里需要根据GET参数获取不同的账户ID
			if (empty($suite_id)) {
				$suite_id = $_GET['suite_id'] ?? "";
			}
			switch ($suite_id) {
				case 'ww8f0203dd6465fd0e': //智投方舟
                    Log::info('智投方舟');
					return WorkWeixinProController::getApp();
				case 'ww8880b14abdbd807f':
                    Log::info('其他');
				default:
                    Log::info('默认');
					return WorkWeixin3rdController::getApp($suite_id, $sid);
			}
		}
	}
