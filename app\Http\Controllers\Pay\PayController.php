<?php

namespace App\Http\Controllers\Pay;

use App\Models\CustomerOrders;
use App\Models\LinkViewRecord;
use App\Services\CacheService;
use App\Services\NotifySendService;
use App\Services\WxPayService;
use EasyWeChat\Pay\Message;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;
use EasyWeChat\Pay\Application;

class PayController
{

    protected $payType = 'jsapi';//jsapi
    /**
     * 发起支付
     * @param Request $request
     * @return mixed
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidArgumentException
     * @throws \ReflectionException
     * @throws \Throwable
     */
    public function pay(Request $request)
    {
        $params = $request->all();
//        if(!$params['vid']){
//            return response()->json(['code' => 500, 'data' => [], 'message' => '缺少vid'],JSON_UNESCAPED_UNICODE);
//        }
        if(!$params['pay_type'] || !in_array($params['pay_type'],['jsapi','h5'])){
            return response()->json(['code' => 500, 'data' => [], 'message' => '非法支付环境'],JSON_UNESCAPED_UNICODE);
        }
//        $lockKey = 'customer_pay_' . $params['vid'] .  '_' . getIp();
//        $lock = CacheService::requestLock($lockKey);
//        if(!$lock){
//            return response()->json(['code' => 500, 'data' => [], 'message' => '您手速太快了'],JSON_UNESCAPED_UNICODE);
//        }
//        $linkViewRecord = LinkViewRecord::query()
//            ->select('id','admin_uid','user_id')
//            ->where('id',$params['vid'])
//            ->first();
//        if(!$linkViewRecord){
//            return response()->json(['code' => 500, 'data' => [], 'message' => '访问记录不存在'],JSON_UNESCAPED_UNICODE);
//        }

        try {
            //组装订单数据
            $orderNo = CustomerOrders::makeOrderNo();
            $orderPrice = 1;//TODO 上线的时候支付金额要修改
            $orderData = [
                'link_view_record_id' => $data['vid'] ?? 0,
                'admin_uid' => $linkViewRecord['admin_uid'] ?? 0,
                'user_id' => $linkViewRecord['user_id'] ?? 0,
                'order_no' => $orderNo,
                'order_price' => $orderPrice, //单位 分
                'date' => date('Y-m-d'),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ];

            //获取支付配置
            $payConfig = WxPayService::getConfig();
            $app = WxPayService::getApp($payConfig);
            $wxPayData = [
                'appid' => $payConfig['app_id'],
                'mchid' =>  (string) $payConfig['mch_id'],
                'description' => '购买课程',
                'out_trade_no' => $orderNo, // 商户订单号
                'notify_url' => $payConfig['notify_url'],
                'amount' => [
                    'total' => $orderPrice, // 金额，单位分
                    'currency' => 'CNY',
                ],
            ];

            //如果是jsapi 则要获取用户的openid
            if($params['pay_type'] == 'jsapi'){
//                $openId = WechatUser::query()->where('id',$linkViewRecord->user_id)->value('openid');
//                if(!$openId){
//                    return response()->json(['code' => 500, 'data' => [], 'message' => '未获取到用户的openid'],JSON_UNESCAPED_UNICODE);
//                }
//                $redisKey = 'pay_info:' . $linkViewRecord['admin_uid'] . '_' . $linkViewRecord['vid'];
//                $openId = Cache::store('redis')->get($redisKey);
//                if(!$openId){
//                    return response()->json(['code' => 500, 'data' => [], 'message' => '未获取到用户的信息，或订单已过期，请刷新后重试。'],JSON_UNESCAPED_UNICODE);
//                }
                $openId = 'o6-08vlqCSyNcCxj6_56PzdjX2Sk';
                $orderData['open_id'] = $openId;//将openid 存入订单数据
                $wxPayData['payer'] = [
                    "openid" => $openId, //oIdrA7VbCnRnwGsy9NA2qWjLPzfc, // <---- 请修改为服务号下单用户的 openid
//                    "openid" => 'oIdrA7VbCnRnwGsy9NA2qWjLPzfc',
                ];
            }
            //如果是h5支付
            if($params['pay_type'] == 'h5'){
                $wxPayData['scene_info'] = [
                    'payer_client_ip' => getIp(), // 用户终端IP
                    'h5_info' => [
                        'type' => 'Wap', // 场景类型
                    ],
                ];
            }
            $orderData['request_data'] = json_encode($wxPayData, JSON_UNESCAPED_UNICODE);//将接口请求参数存入订单数据
            //创建订单
            $orderInfo = CustomerOrders::query()->insertGetId($orderData);
            if(!$orderInfo){
                return response()->json(['code' => 500, 'data' => [], 'message' => '生成订单失败-1'],JSON_UNESCAPED_UNICODE);
            }
            //请求支付接口
            $response  = $app->getClient()->postJson('v3/pay/transactions/' . $params['pay_type'], $wxPayData);
            if (!$response) {
                return response()->json(['code' => 500, 'data' => [], 'message' => '发起支付失败-1'],JSON_UNESCAPED_UNICODE);
            }
            $response = $response->toArray();
            $data = [];
            //如果是h5支付 非微信环境，浏览器支付
            if($params['pay_type'] == 'h5'){
                if(!isset($response['h5_url'])){
                    return response()->json(['code' => 500, 'data' => [], 'message' => $response['message'] ?? '发起支付失败-2']);
                }
                $data = [
                    'h5_url' => $response['h5_url'],
                ];
            }
            //如果是jsapi支付 微信环境内支付
            if($params['pay_type'] == 'jsapi'){
                if(!isset($response['prepay_id'])){
                    return response()->json(['code' => 500, 'data' => [], 'message' => $response['message'] ?? '发起支付失败-3']);
                }
                $config = WxPayService::getJsapiConfig($payConfig['app_id'],$response['prepay_id']);
                $data = $config;
            }
            return response()->json(['code' => 200, 'data' => $data, 'message' => '发起支付成功'],JSON_UNESCAPED_UNICODE);
        } catch (\Exception $exception) {
            Log::error('客户支付-发起支付失败：' . $exception->getMessage());
            NotifySendService::sendWorkWeixinForError('客户支付-发起支付失败：' . $exception->getMessage());
            return response()->json(['code' => 500, 'data' => [], 'message' => '发起支付失败-4'],JSON_UNESCAPED_UNICODE);
        }
    }



}
