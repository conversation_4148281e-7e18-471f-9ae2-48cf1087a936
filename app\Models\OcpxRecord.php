<?php
	
	namespace App\Models;
	
	use Dcat\Admin\Traits\HasDateTimeFormatter;
	use Illuminate\Database\Eloquent\Model;
	
	/**
	 * @property mixed              $media_type
	 * @property bool|mixed|string  $return_json
	 * @property false|mixed|string $push_json
	 * @property mixed|string       $action_time
	 * @property mixed              $action_type
	 * @property mixed|string       $type
	 * @property mixed|string       $traceid
	 * @property int|mixed          $link_id
	 * @property int|mixed          $result
	 */
	class OcpxRecord extends Model
	{
		use HasDateTimeFormatter;
		
		protected $table = 'ocpx_record';
		
	}
