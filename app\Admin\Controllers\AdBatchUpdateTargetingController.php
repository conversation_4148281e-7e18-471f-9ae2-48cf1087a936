<?php

namespace App\Admin\Controllers;

use App\Admin\Renderable\TencentAd\AdAccountSelectTable;
use App\Admin\Repositories\AdBatchUpdateTargeting;
use App\Jobs\Tads\AdGroupBatchUpdateTargetingJob;
use App\Models\TencentAdAccount;
use App\Models\TencentAdRegion;
use App\Services\TenCentAd\AdGroups\GetTargetingTagsService;
use App\Services\Tools\UtilsService;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Show;
use Dcat\Admin\Widgets\Tooltip;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;

class AdBatchUpdateTargetingController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new AdBatchUpdateTargeting(), function (Grid $grid) {
            $grid->disableActions();
            $grid->disableBatchDelete();
            $grid->column('id')->sortable();
            if (!Admin::user()->isAdministrator() && !Admin::user()->isRole('wop')) {
                $grid->model()->where("admin_uid", Admin::user()->id);
            } else {
                $grid->column('admin_uid', '用户ID');
                $grid->column('adminInfo.username', '用户');
            }
            $grid->model()->orderBy('id', 'desc');
            $grid->column('account_ids')->display(function ($value){
                return json_decode($value);
            });
            $grid->column('geo_regions')->display(function ($value) {
                $areaIds = json_decode($value, true);
                $areaData = TencentAdRegion::query()->select("id", "name")->whereIn("t_id",$areaIds)->get()->toArray();
                if (!empty($areaData)) {
                    $areaString = implode(",", array_column($areaData, 'name'));
                } else {
                    $areaString = '无配置';
                }
                Tooltip::make('.geo_regions' . $this->id)->title($areaString);
                return '<div class="geo_regions' . $this->id . '">' . count($areaIds) . "个地域" . '</div>';
            });
            $grid->column('gender')->using([
                'NONE'   => '不限',
                'MALE'   => '男',
                'FEMALE' => '女',
            ]);
            $grid->column('min_age');
            $grid->column('max_age');
            $grid->column('custom_audience');
            $grid->column('excluded_custom_audience');
            $grid->column('status')->using([
                0=>'待执行',
                2=>'执行中',
                3=>'已完成',
            ]);
            $grid->column('resp','执行结果')->display(function($value){
                $data = json_decode($value,true)?? [];
                if(!$data){
                    return "无";
                }
                foreach($data as &$accountData){
                    foreach($accountData as &$datum){
                        if(!is_string($datum)){ continue;}
                        $tempData = json_decode($datum,true);
                        if(isset($tempData['code']) && $tempData['code'] == 0){
                            $datum = '成功';
                        }else{
                            if(isset($tempData['message_cn']) && str_contains($tempData['message_cn'],'当前广告的设置内容和已有广告人群覆盖高度相似，无法创建，请修改定向设置或 前往查看已有广告')){
                                $datum = 'X高似广告'.str_replace('当前广告的设置内容和已有广告人群覆盖高度相似，无法创建，请修改定向设置或 前往查看已有广告','', $tempData['message_cn']);
                                continue;
                            }
                            $datum = 'XXX失败XXX';
                        }
                    }
                }
                return $data;
            });
            $grid->column('created_at');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new AdBatchUpdateTargeting(), function (Show $show) {
            $show->field('id');
            $show->field('admin_uid');
            $show->field('account_ids');
            $show->field('geo_regions');
            $show->field('gender');
            $show->field('min_age');
            $show->field('max_age');
            $show->field('custom_audience');
            $show->field('excluded_custom_audience');
            $show->field('status');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }


    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new AdBatchUpdateTargeting(), function (Form $form) {
            $accountIdsLoadsRoute = admin_route('ad.crowd-list.get');
            $form->display('id');
            $form->multipleSelectTable('account_ids', "账户ID")
                ->loads(['custom_audience','excluded_custom_audience'], [$accountIdsLoadsRoute,$accountIdsLoadsRoute])
                ->saveAsJson()
                ->title('选择账户')
                ->dialogWidth('60%')           // 弹窗宽度，默认 800px
                ->from(AdAccountSelectTable::make()) // 设置渲染类实例，并传递自定义参数
                ->model(TencentAdAccount::class, 'account_id', 'account_id')->required(); // 设置编辑数据显示
            $form->radio('gender')->options([
                'NONE'   => '不限',
                'MALE'   => '男',
                'FEMALE' => '女',
            ])->help("不填写表示不更改");
            $form->number('min_age')->min(0)->max(66)->help("不填写表示不更改");
            $form->number('max_age')->min(0)->max(66)->help("不填写表示不更改");
            $form->multipleSelect('custom_audience')->saveAsJson()->help("不填写表示不更改");
            $form->multipleSelect('excluded_custom_audience')->saveAsJson()->help("不填写表示不更改");
            $form->html('<div id="search_block_area"></div>','搜索选择地域');
            UtilsService::searchJsTree('search_block_area','geo_regions','');
            $areaData = GetTargetingTagsService::getRegion();
            $form->tree('geo_regions')
                ->nodes($areaData) // 设置所有节点
                ->expand(false)
                ->treeState(false) # 允许单独选择父节点
                ->customFormat(function ($v) { // 格式化外部注入的值
                    if (!$v) {
                        return [];
                    }
                    return $v;
                })->saveAsJson()
                ->help("不填写表示不更改");
            $form->hidden('admin_uid');
            $form->saving(function (Form $form) {
                $maxAge = $form->input("max_age");
                $minAge = $form->input("min_age");
                if($maxAge != 0 || $minAge != 0) {
                    if ($maxAge == 0 || $minAge == 0) {
                        return $form->response()->error('最小年龄与最大年龄必须配合使用，且范围应该在 14-66之间');
                    }
                    if ($maxAge - $minAge < 4) {
                        return $form->response()->error('最小年龄不能大于最大年龄，且差距需要最少 4 岁');
                    }
                }

                $form->input("admin_uid", Admin::user()->id);
            })->saving(function (Form $form) {
                $countGeoRegions = count(explode(',',$form->input("geo_regions")));
                if($countGeoRegions > 2000){
                    return $form->response()->alert()->error('提示')->detail('选择的省市区地域总和不能超过2000个');
                }
            });
            $form->saved(function (Form $form,$result) {
                $insertId = $result;//新增的主键ID
                $adBatchUpdateTargeting = \App\Models\AdBatchUpdateTargeting::query()->find($insertId);
                AdGroupBatchUpdateTargetingJob::dispatch($adBatchUpdateTargeting)->onQueue('AdGroupBatchUpdateTargetingJob');
            });
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
