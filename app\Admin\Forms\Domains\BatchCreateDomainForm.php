<?php

namespace App\Admin\Forms\Domains;

use Admin;
use App\Jobs\AdTrackClickConfigJob;
use App\Models\AdAccount;
use App\Models\AdminDomain;
use App\Models\AdminUser;
use App\Services\Tools\LogService;
use Dcat\Admin\Widgets\Form;

class BatchCreateDomainForm extends Form
{
    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        LogService::inputLog('Tools','域名管理-批量新建', $input, Admin::user()->id, Admin::user()->username);
        if(!$input['domain']){
            return $this->response()->alert()->error('提示')->detail('请填写域名')->refresh();
        }
        $input['domain'] = explode(PHP_EOL, $input['domain']);
        $domain = array_filter($input['domain'],function ($value){
            return !empty($value);
        });

        $alreadyExistsDomains = AdminDomain::query()->pluck('domain')->toArray();
        $intersectDomains = array_intersect($input['domain'], $alreadyExistsDomains);
        $insert = [];


        foreach ($domain as $key => $value) {

            $value = trim($value);
            $insert[] = [
                'admin_uid' => $input['admin_uid'],
                'domain' => 'https://' . $value,
                'ssl_ex_time' => $input['ssl_ex_time'],
                'status' => $input['status'],
                'created_at' => date('Y-m-d H:i:s', time()),
                'updated_at' => date('Y-m-d H:i:s', time()),
            ];
        }
        if($insert){
            AdminDomain::query()->insert($insert);
        }
        if(!empty($intersectDomains)){
            $intersectDomains = implode('，', $intersectDomains);
            return $this->response()->alert()->success('提示')->detail('批量新增成功，其中：' . $intersectDomains . '，已存在，请确认。')->refresh();
        }

        return $this->response()->alert()->success('提示')->detail('批量新增成功')->refresh();
    }

    /**
     * Build a form here.
     */
    public function form()
    {

        $adminUsers = AdminUser::query()
            ->pluck('username', 'id')
            ->toArray();
        $this->select('admin_uid', '用户')->options($adminUsers)->required();
        $this->textarea('domain')->help('多个换行输入');
        $this->date('ssl_ex_time')->required();
        $this->radio('status')->options([0 => '不可用',1 => '可用'])->required()->default(1);


    }

    /**
     * The data of the form.
     *
     * @return array
     */
    public function default()
    {
        return [

        ];
    }
}
