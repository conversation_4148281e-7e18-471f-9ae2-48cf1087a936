<?php

namespace App\Models;

use App\Models\AdminActionLog\AdminActionLog1;
use App\Models\AdminActionLog\AdminActionLog10;
use App\Models\AdminActionLog\AdminActionLog2;
use App\Models\AdminActionLog\AdminActionLog3;
use App\Models\AdminActionLog\AdminActionLog4;
use App\Models\AdminActionLog\AdminActionLog5;
use App\Models\AdminActionLog\AdminActionLog6;
use App\Models\AdminActionLog\AdminActionLog7;
use App\Models\AdminActionLog\AdminActionLog8;
use App\Models\AdminActionLog\AdminActionLog9;
use App\Admin\Repositories\AdminActionLog as AdminActionLogRepository;
use Dcat\Admin\Repositories\EloquentRepository;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property false|mixed|string $other_data
 * @property mixed              $action_type
 * @property mixed              $rel_id
 * @property mixed|string       $action_desc
 * @property mixed              $action
 * @property mixed              $admin_uid
 * @property mixed|string       $message
 */
class AdminActionLog extends Model
{
    use HasDateTimeFormatter;

    const ACTION = [
        'add_sub_user'      => '新建子账号',
        'del_sub_user'      => '删除子账号',
        'create_corp'          => '企业微信授权',
        'auth_corp'            => '企业微信分配至子账号',
        'del_corp'             => '企业微信取消授权',
        'create_ww_user'       => '添加销售',
        'create_ww_user_group' => '导入销售群',
        'create_ww_link'       => '创建投放链接',
        'update_ww_link'       => '修改投放链接',
        'delete_ww_link'       => '删除投放链接',
        'batch_auto_online'    => '批量自动上下线',
        'batch_set_group'      => '批量增减分组',
        'batch_set_weight'     => '批量设置权重',
        'edit_ww_user'         => '编辑销售',
        'time_auto_online'     => '销售定时上线',
        'time_auto_offline'    => '销售定时下线',
        'batch_set_wind_label' => '批量设置销售智投标签',
        'batch_set_online_status'    => '销售批量上下线',
        'batch_set_add_method' => '批量设置销售加粉模式',
        'batch_set_corp_label' => '批量设置销售企微标签',
        'batch_set_link_rate'  => '批量设置投放链接回传比例',
        'ocpx_upload'          => '加粉手动上报',
        'batch_ocpx_upload'    => '加粉批量手动上报',
        'batch_deep_ocpx_upload' => '加粉批量深度回传',
        'flush_link_tads_conv_count' => '刷新投放链接回传',
        'link_view_record_batch_ocpx_upload' => '实时访客批量手动上报',
        'link_view_record_ocpx_upload' => '实时访客手动上报',
        'batch_set_ww_link_use_domain' => '批量设置企微投放链接使用域名',
        'batch_set_shield_policy_block' => '批量设置屏蔽地域',
        'batch_set_ww_link_switch' => '批量配置企微链接开关',
        'batch_set_ww_link_tpl' => '批量配置企微链接落地页',
        'batch_set_ww_link_user_group' => '批量配置企微链接销售分组',
        'batch_set_ww_link_shield_policy' => '批量配置企微链接屏蔽规则',
        'batch_set_ww_link_ww_label' => '批量配置企微链接企微标签',
        'ww_user_group_create' => '新增销售分组',
        'ww_user_group_update' => '编辑销售分组',
        'ww_user_group_delete' => '删除销售分组',
        'ww_user_delete' => '删除销售分组',
        'shield_policy_delete' => '删除屏蔽规则',
        'shield_policy_create' => '新增屏蔽规则',
        'shield_policy_update' => '编辑屏蔽规则',
        'batch_set_welcome_message' => '批量配置销售欢迎语',
        'transfer_ww_user' => '转移销售许可证',
        'tencent_ad_account_switch' => '转移腾讯广告账户',
        'sub_user_create'      => '新建子账号',
        'sub_user_update'      => '编辑子账号',
        'inline_edit_corp_auto_label' => '快捷编辑企业微信企微标签',
        'inline_edit_corp_group_name' => '快捷编辑企业微信企微分组',
        'customer_delete_sale' => '客户删除销售',
        'sale_delete_customer' => '销售删除客户'
    ];
    const ACTION_TYPE = [
        '账号'   => 'account',
        '企微'   => 'corp',
        '销售'   => 'ww_user',
        '销售组' => 'ww_user_group',
        '落地页' => 'page_tpl',
        '链接'   => 'ww_link',
        '加粉'   => 'add_record',
        '防水墙' => 'shield_policy',
    ];
    protected $table = 'admin_action_logs';

    public static function create($action, $rel_id, $action_type, $message, $ip,$adminUid = 0): void
    {
        if(!$ip){
            $ip = '';
        }
        $newObj              = new AdminActionLog();
        $newObj->admin_uid   = $adminUid;
        $newObj->action      = $action;
        $newObj->action_desc = self::ACTION[$action];
        $newObj->rel_id      = $rel_id;
        $newObj->action_type = $action_type;
        $newObj->message     = $message ?? "无备注";
        $newObj->ip          = $ip;
        $newObj->save();
    }

    public function adminInfo(): BelongsTo
    {
        return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
    }


    /**
     * 根据AdminUid获取操作记录模型
     * @param $adminUid
     * @return Model
     */
    public static function getAdminUserActionLogTableModel($adminUid) :Model
    {
        $query =  AdminUser::query()
            ->where('id', $adminUid)
            ->value('action_log_table');
        return match ($query) {
            1 => new AdminActionLog1(),
            2 => new AdminActionLog2(),
            3 => new AdminActionLog3(),
            4 => new AdminActionLog4(),
            5 => new AdminActionLog5(),
            6 => new AdminActionLog6(),
            7 => new AdminActionLog7(),
            8 => new AdminActionLog8(),
            9 => new AdminActionLog9(),
            10 => new AdminActionLog10(),
            default => new AdminActionLog(),
        };
    }

    /**
     * 根据AdminUid获取操作记录数据仓库
     * @param $adminUid
     * @return EloquentRepository
     */
    public static function getAdminUserActionLogTableRepositories($adminUid) :EloquentRepository
    {
        $query =  AdminUser::query()
            ->where('id', $adminUid)
            ->value('action_log_table');
        return match ($query) {
            1 => new AdminActionLogRepository\AdminActionLog1(['adminInfo']),
            2 => new AdminActionLogRepository\AdminActionLog2(['adminInfo']),
            3 => new AdminActionLogRepository\AdminActionLog3(['adminInfo']),
            4 => new AdminActionLogRepository\AdminActionLog4(['adminInfo']),
            5 => new AdminActionLogRepository\AdminActionLog5(['adminInfo']),
            6 => new AdminActionLogRepository\AdminActionLog6(['adminInfo']),
            7 => new AdminActionLogRepository\AdminActionLog7(['adminInfo']),
            8 => new AdminActionLogRepository\AdminActionLog8(['adminInfo']),
            9 => new AdminActionLogRepository\AdminActionLog9(['adminInfo']),
            10 => new AdminActionLogRepository\AdminActionLog10(['adminInfo']),
            default => new AdminActionLogRepository(['adminInfo']),
        };
    }

    /**
     * 根据表ID直接获取操作记录数据仓库
     * @param $tableId
     * @return EloquentRepository
     */
    public static function getAdminUserActionLogTableRepositoriesById($tableId) :EloquentRepository
    {
        return match ((int)$tableId) {
            1 => new AdminActionLogRepository\AdminActionLog1(['adminInfo']),
            2 => new AdminActionLogRepository\AdminActionLog2(['adminInfo']),
            3 => new AdminActionLogRepository\AdminActionLog3(['adminInfo']),
            4 => new AdminActionLogRepository\AdminActionLog4(['adminInfo']),
            5 => new AdminActionLogRepository\AdminActionLog5(['adminInfo']),
            6 => new AdminActionLogRepository\AdminActionLog6(['adminInfo']),
            7 => new AdminActionLogRepository\AdminActionLog7(['adminInfo']),
            8 => new AdminActionLogRepository\AdminActionLog8(['adminInfo']),
            9 => new AdminActionLogRepository\AdminActionLog9(['adminInfo']),
            10 => new AdminActionLogRepository\AdminActionLog10(['adminInfo']),
            default => new AdminActionLogRepository(['adminInfo']),
        };
    }
}
