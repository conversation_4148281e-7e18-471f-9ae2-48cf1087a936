<?php

namespace App\Admin\Controllers;

use Admin;
use App\Http\Controllers\Controller;
use App\Services\Tools\UtilsService;
use Dcat\Admin\Layout\Column;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;


class HomeController extends Controller
{
    public function index(Content $content): Content
    {
        /** 如果有未读公告，弹出 */
        UtilsService::indexAnnouncement();

        //隐藏面包屑
        Admin::style('.breadcrumb { display: none !important; }');
        return $content
//            ->header('智投方舟')
//            ->description('智投方舟')
            ->body(function (Row $row) {
                $row->column(12, function (Column $column) {
                    $column->row(view('wra.index'));
                });
            });
    }
}
