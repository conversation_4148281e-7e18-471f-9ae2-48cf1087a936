<?php

    namespace App\Http\Controllers\WorkWeixin3rd;

    use App\Jobs\AddMemberExternalContactJob;
    use Illuminate\Support\Facades\Log;

    /**
     * 好友变动通知
     */
    class ChangeExternalChat
    {
        public function handler($message, $param): bool
        {
            $message = json_decode($message, true);
            // 这里是智投新增的参数，用户把sid和suite_id传到系统内
            $message['wind_param'] = $param;

            // Event - 自建应用的通知方式
            // InfoType - 第三方应用的通知方式
            /**
             * 这里是兼容了第三方应用和代开发应用（代开发算是自建，服务商代替客户创建的自建，权限基本一致）
             * Event是 服务商代开发的回调参数，用来判断回调类型 https://developer.work.weixin.qq.com/document/path/96361
             * InfoType是 第三方应用的回调参数，用来判断回调类型 https://developer.work.weixin.qq.com/document/path/92277
             */
            $infoType = $message['Event'] ?? "";
            if (empty($infoType)) {
                $infoType = $message['InfoType'] ?? "";
            }
            /**
             * change_external_chat，代表是企业微信群组的的回调，详见上方注释文档
             */
            if ($infoType == 'change_external_chat') {
                 //Log::info('ChangeExternalChat 群变更 - Event - MESSAGE：' . json_encode($message));
                if (isset($message['ChangeType']) && $message['ChangeType'] == 'update') { // 客户群变更事件
                    switch ($message['UpdateDetail']) {
                        case 'add_member':// 成员进群
                            $this->add_member_external_contact_by_job($message);
                            break;
                        case 'del_member'://成员退群
                            break;
                    }
                }

            }
            return true;
        }


        /**
         * 添加外部联系人事件处理队列
         *
         * @param $message
         *
         * @return bool
         */
        public function add_member_external_contact_by_job($message): bool
        {
            $job = 'add_member_external_contact';
            AddMemberExternalContactJob::dispatch($message)->onQueue($job);
            return true;
        }
    }
