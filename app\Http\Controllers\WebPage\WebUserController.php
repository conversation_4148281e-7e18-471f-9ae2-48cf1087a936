<?php

	namespace App\Http\Controllers\WebPage;

    use App\Models\GeneratePage;
    use App\Models\WechatUser;
    use App\Models\WwLink;
    use Illuminate\Contracts\Foundation\Application;
	use Illuminate\Http\RedirectResponse;
	use Illuminate\Http\Request;
	use Illuminate\Routing\Redirector;
    use Illuminate\Support\Facades\Cache;
    use Illuminate\Support\Facades\DB;
    use Illuminate\Support\Facades\Http;
    use Illuminate\Support\Facades\Log;
    use Illuminate\Support\Facades\Redirect;

    class WebUserController
	{
		public function webUserLogin(Request $request): Redirector|string|RedirectResponse|Application
		{
			//优先从缓存中获取
			$uuid = session('w-x-uuid');
			if (!$uuid) {
				$ip = getIp();
				/** @var WechatUser $wechatUser */
				$wechatUser = WechatUser::query()->where("ip", $ip)->first();
				if ($wechatUser) {
					$uuid = $wechatUser->md5_id;
				} else {
					$wechatUser                          = new  WechatUser();
					$wechatUser->appid                   = self::getAppInfo()['appid'];
					$wechatUser->openid                  = 'WEB_LOGIN';
					$wechatUser->unionid                 = 'WEB_LOGIN';
					$wechatUser->ip                      = getIp();
					$wechatUser->access_token            = "";
					$wechatUser->refresh_token           = "";
					$wechatUser->access_token_expires_in = date("Y-m-d H:i:s", time());
					$wechatUser->parent_uid              = 0;
					$wechatUser->save();

					if (empty($wechatUser->md5_id)) {
						$wechatUser->md5_id = md5($wechatUser->id . "V587WonAi");
						$wechatUser->save();
					}
					$uuid = $wechatUser->md5_id;
				}
			} else {
				$wechatUser = WechatUser::query()->where("md5_id", $uuid)->first();
			}
			if (!$wechatUser) {
                return view('errors.system-error', [
                    'message' => '系统错误，请退出后重试-1'
                ]);
			}

			session(['w-x-uuid' => $uuid]);
			$_GET['uuid'] = $wechatUser->md5_id;

            $csrfKey = md5(sha1(md5($_GET['uuid'] . time() . microtime() . rand(10000, 99999))) . rand(10000, 99999));
            $_GET['xt'] = $csrfKey;
            Cache::store('redis')->set($csrfKey, 0, 600);

            $linkAdId = $request->get("adt", "");
            /** @var WwLink $wwLink */
            $wwLink = WwLink::query()->where("link_ad_id", $linkAdId)->first();
            if($wwLink && $wwLink->media_type == '巨量广告'){
                $requestParam = Request::create($request->getUri(),$request->getMethod(),$_GET);
                $acsObj = new TencentAdController();
                return $acsObj->shieldCheck($requestParam);
            }

			//登录完成后，校验客户跳转审核还是投放
			$domain = DB::table("page_domain")->where("status", 1)->first();
			if (!$domain) {
				$domain = DB::table("page_domain")->first();
			}
			$ri = $domain->host . route('shieldCheck', [], false);
			return redirect($ri . "?" . http_build_query($_GET));
		}


        public function addWhite(Request $request)
        {
            //优先从缓存中获取
            $uuid = session('w-x-uuid');
            if (!$uuid) {
                $ip = getIp();
                /** @var WechatUser $wechatUser */
                $wechatUser = WechatUser::query()->where("ip", $ip)->first();
                if ($wechatUser) {
                    $uuid = $wechatUser->uuid;
                } else {
                    $wechatUser                          = new  WechatUser();
                    $wechatUser->appid                   = self::getAppInfo()['appid'];
                    $wechatUser->openid                  = 'WEB_LOGIN';
                    $wechatUser->unionid                 = 'WEB_LOGIN';
                    $wechatUser->ip                      = getIp();
                    $wechatUser->access_token            = "";
                    $wechatUser->refresh_token           = "";
                    $wechatUser->access_token_expires_in = date("Y-m-d H:i:s", time());
                    $wechatUser->parent_uid              = 0;
                    $wechatUser->save();

                    if (empty($wechatUser->md5_id)) {
                        $wechatUser->md5_id = md5($wechatUser->id . "V587WonAi");
                        $wechatUser->save();
                    }
                    $uuid = $wechatUser->md5_id;
                }
            } else {
                $wechatUser = WechatUser::query()->where("md5_id", $uuid)->first();
            }
            if (!$wechatUser) {
                return view('errors.system-error', [
                    'message' => '系统错误，请退出后重试-1'
                ]);
            }

            echo $wechatUser->md5_id;
        }

		public static function getAppInfo(): array
		{
			return [
				'name'   => 'WEB_LOGIN',
				'appid'  => 'WEB_LOGIN',
				'secret' => 'WEB_LOGIN'
			];
		}

        public function htmlPreview(Request $request)
        {

            $id = $request->input("id", "");
            if(!$id){
                dd('页面不存在-1');
            }
            $html = GeneratePage::query()->where("id", $id)->first();
            if(!$html){
                dd('页面不存在-2');
            }
            $addMethod = $request->input("add_method");

            try {

                $data = [
                    'id' => '',
                    'ww_user_name' => '',
                    'qrcode_id' => '',
                    'qrcode_url' =>  env('BASE_QRCODE_URL'),
                    'cus_acq_link' =>  env('BASE_QRCODE_URL'),
                    'state' => '',
                    'vid' => 0,
                    'add_method' => $addMethod,
                ];
                return view("customer.{$html->directory}." . $html->name, $data);
//                return view("customer.70-xi-niu-wang-luo.250605_xn_paojiaotong_ad",$data);
//                return view("customer.70-xi-niu-wang-luo.250607_xn_shibu_ad",$data);
            }catch (\Exception $e){
                dd($e->getMessage());
            }

        }


	}

