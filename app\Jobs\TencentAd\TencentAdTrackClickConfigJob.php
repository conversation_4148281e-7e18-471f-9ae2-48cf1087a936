<?php

namespace App\Jobs\TencentAd;

use App\Models\TenCentAdTrackClickConfig as TenCentAdTrackClickConfigModel;
use App\Services\TenCentAd\AdTrackClickService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;

class TencentAdTrackClickConfigJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected  $accountInfo;
    protected  $feedbackName;
    protected  $secondCategoryType;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($accountInfo,$feedbackName,$secondCategoryType)
    {
        $this->accountInfo = $accountInfo;
        $this->feedbackName = $feedbackName;
        $this->secondCategoryType = $secondCategoryType;
    }

    /**
     * 防止任务重叠。 expireAfter 设置锁的最大持有时间（防止任务崩溃导致死锁）
     *
     * @return array
     */
    public function middleware()
    {
        $accountId = $this->accountInfo->account_id;
        if (empty($accountId)) {
            return [];
        }
        return [(new WithoutOverlapping($accountId))->releaseAfter(10)->expireAfter(15)];
    }


    /**
     * Execute the job.
     *
     * @return mixed
     */
    public function handle()
    {
        $date = date('Y-m-d H:i:s');
        $check = TenCentAdTrackClickConfigModel::query()
            ->where('account_id', $this->accountInfo->account_id)
            ->where('second_category_type',$this->secondCategoryType)
            ->exists();
        if (!$check) {
            $apiRes = AdTrackClickService::addConfig($this->accountInfo, $this->feedbackName,$this->secondCategoryType);
            if ($apiRes && isset($apiRes['code']) && $apiRes['code'] == 0) {
                $feedback_id = $apiRes['data']['feedback_info']['feedback_id'];
                $insert = [
                    'admin_uid' => $this->accountInfo->admin_uid,
                    'account_id' =>  $this->accountInfo->account_id,
                    'feedback_id' => $feedback_id,
                    'feedback_name' => $this->feedbackName,
                    'second_category_type' => $this->secondCategoryType,
                    'created_at' => $date,
                    'updated_at' => $date,
                ];
                TenCentAdTrackClickConfigModel::query()->insert($insert);
            }
        }
        return true;
    }
}
