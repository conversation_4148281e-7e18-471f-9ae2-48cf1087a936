<?php

namespace App\Jobs\WwUserAddRecord;

use App\Models\AdminUser;
use App\Models\WwCorpInfo;
use App\Models\WwUserAddRecord;
use App\Services\Corp\WwCorpApiService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class AgainSetTagPreJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $task;


    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($task)
    {
        $this->task = $task;
    }


    /**
     * Execute the job.
     *
     * @return mixed
     */
    public function handle()
    {

        $task = $this->task;
        $task->status = 1;//修改状态为执行中
        $task->err_desc = '';
        $task->save();
//        dd($task);

        $adminUid = $task->admin_uid;
        $addLabel = $task->add_label;   //要补打的标签
        $delLabel = $task->del_label;   //要删除的标签
        $corpId = $task->corp_id;       //企微ID
        $dateRange = [  //进粉记录时间范围
            $task['start_at'],
            $task['end_at'],
        ];
        $addRecordIds = $task->add_record_id;//单独处理的进粉记录ID

        //查询用户ID
        $adminUids = AdminUser::getAdminUidsByParentId(array($adminUid));
        //查询进粉记录
        $query = WwUserAddRecord::query()
            ->with("corpInfo")
            ->whereIn("admin_uid", $adminUids)
            ->where("corp_id", $corpId)
            ->whereBetween("external_contact_created_at", $dateRange);

        //判断是否有单独处理的进粉记录ID，待后续有需求再开发
        if (!empty($addRecordIds)) {
            $query = $query->whereIn('id', $addRecordIds);
        }
        //判断有没有选择分组
        if ($task->ww_group_id) {
            $query = $query->where('ww_user_group_id', $task->ww_group_id);
        }

        if (!empty($addRecordIds)) {
            $addRecordIds = explode(PHP_EOL, $addRecordIds);
            $addRecordIds = array_filter($addRecordIds, fn($v) => $v === 0 || $v === '0' || !empty($v));
            $addRecordIds = array_map(function($item) {
                return str_replace("\r", '', $item);
            }, $addRecordIds);
            $query = $query->whereIn('id', $addRecordIds);
        }

        $addRecords = $query->orderByDesc('id')->get();

        if($addRecords->isEmpty()){
            $task->status = 3;
            $task->err_desc = '未查询到进粉记录';
            $task->save();
            return false;
        }
        //获取企业下标签
        /** @var WwCorpInfo $corpInfo */
        $corpInfo = WwCorpInfo::query()->find($corpId);
        $allLabels = WwCorpApiService::getCorpTagList($corpInfo);
        $addTags = [];
        $removeTags = [];
        if ($addLabel) {
            $addTags = getLabelIdInJob($allLabels, $addLabel);
        }
        if ($delLabel) {
            $removeTags = getLabelIdInJob($allLabels, $delLabel);
        }
        $addRecordTotal = count($addRecords->toArray());
        $task->add_record_total = $addRecordTotal;//更新进粉记录总数

        $i = 0;
        foreach ($addRecords as $addRecord) {
            AgainSetTagJob::dispatch($task, $addRecordTotal, $addRecord, $addTags, $removeTags)->onQueue('again_set_tag');
            $i++;
        }
        if($i >= $addRecordTotal){
            $task->status = 2;
        }
        $task->save();
        return true;
    }

}
