<?php

namespace App\Console\Commands\Admin;


use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use RuntimeException;
use Vtiful\Kernel\Excel;

class MakeDomain extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'MakeDomain';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '生成域名';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $baseDomain = 'smart-ark.com';
//        $count = $this->argument('count');
//        $length = $this->argument('length');
        $taskList = \App\Models\MakeDomain::query()->where('status',0)->get();
        if($taskList->isEmpty()){
            return false;
        }
        foreach ($taskList as $task) {
            $task->status = 1;
            $task->save();
            $domains = $this->generateDomainPrefixes($task->count,$task->length);
            //加入数据库
            $insertData = [];
            foreach ($domains as $domain) {
                $host =  "http://" . $domain . '.'. $baseDomain;
                $insertData[] = [
                    'host' => $host,
                    'status' => 1
                ];
            }
            if (!empty($insertData)) {
                DB::table('page_domain')->insert($insertData);
            }

            //导出文件
            $data = [];
            foreach ($domains as $domain) {
                $data[] = [
                    "CNAME",
                    $domain,
                    "默认",
                    "alb-cicl5gzyvudtqz9ej3.cn-beijing.alb.aliyuncsslb.com",
                    "",
                    "600",
                    "启用",
                    ""
                ];
            }

            $fileName = '生成域名-' . date('YmdHis') . '-' . mt_rand(100000,999999)  . '.xlsx';
            \Dcat\EasyExcel\Excel::export($data)
                ->xlsx()
                ->disk('oss')
                ->store('/domain_task/' . $fileName);
            $filePath = env('CDN_URL') . '/domain_task/' . $fileName;
            $task->oss_path = $filePath;
            $task->status = 2;
            $task->save();
        }


//        $excel = new Excel([
//            'path' => storage_path('app/exports/'),
//        ]);
//        $filePath = $excel->fileName($fileName)
//            ->header(['记录类型', '主机记录', '解析线路', '记录值', 'MX优先级', 'TTL值', '状态(暂停/启用)', '备注'])
//            ->data($data)
//            ->output();

        return Command::SUCCESS;
    }


    /**
     * 生成固定数量的域名前缀
     *
     * @param int $count 需要生成的数量
     * @param int $length 前缀长度（默认8）
     * @return array 生成的域名前缀数组
     */
   public function generateDomainPrefixes(int $count, int $length = 8): array
    {
        // 验证输入参数
        if ($count <= 0) {
            $this->info('数量必须大于0');
            return [];
        }

        if ($length < 3 || $length > 20) {
            $this->info('长度必须在3');
            return [];
        }

        $prefixes = [];
        $characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
        $charCount = strlen($characters);

        while (count($prefixes) < $count) {
            $prefix = '';

            // 生成随机前缀
            for ($i = 0; $i < $length; $i++) {
                $prefix .= $characters[rand(0, $charCount - 1)];
            }

            // 确保唯一性
            if (!in_array($prefix, $prefixes)) {
                $prefixes[] = $prefix;
            }

            // 防止无限循环（安全机制）
            if (count($prefixes) > $count * 100) {
                throw new RuntimeException('生成唯一前缀失败，请尝试减少数量或增加长度');
            }
        }

        return $prefixes;
    }

    /**
     * 验证域名前缀是否符合规则
     */
   public function isValidDomainPrefix(string $prefix): bool
    {
        // 长度检查 (3-63个字符)
        $len = strlen($prefix);
        if ($len < 3 || $len > 63) {
            return false;
        }

        // 只允许字母、数字和连字符
        if (!preg_match('/^[a-z0-9-]+$/', $prefix)) {
            return false;
        }

        // 不能以连字符开头或结尾
        if ($prefix[0] === '-' || $prefix[$len - 1] === '-') {
            return false;
        }

        // 不能包含连续连字符
        if (strpos($prefix, '--') !== false) {
            return false;
        }

        return true;
    }

}
