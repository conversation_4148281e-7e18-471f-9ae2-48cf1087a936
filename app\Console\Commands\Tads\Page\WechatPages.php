<?php

namespace App\Console\Commands\Tads\Page;

use App\Models\TencentAdAccount;
use App\Models\SwitchNativePage;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class WechatPages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'Tads:PageWechatPages';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $switchNativePageList = SwitchNativePage::query()
            ->where('status', 0)
            ->get();
        if($switchNativePageList->isEmpty()){
            return false;
        }

        $resultJson = [];
        foreach ($switchNativePageList as $key => $switchNativePage){
            $switchNativePage->status = 1;
            $switchNativePage->save();
            $accountIds = explode(',', $switchNativePage->account_id);
            foreach ($accountIds as $accountId) {
                $accountId = (int)$accountId;
                $this->alert($accountId);
                /** @var TencentAdAccount $accountInfo */
                $accountInfo = TencentAdAccount::query()->where("account_id", $accountId)->first();
                if (!$accountInfo) {
                    $accountInfo = DB::connection("windFlyMysql")->table("ad_account")->where("account_id", $accountId)->first();
                }

                $pageInfos = $this->wechat_pages_get($accountInfo);
                if (isset($pageInfos['data']['list']) && !empty($pageInfos['data']['list'])) {
                    $pageInfo = $pageInfos['data']['list'][0];
                    $this->info($accountId . "-检测到原生页，正常");
                } else {
                    $this->error($accountId . "-没有检测到原生页");
                    continue;
                }
                $pageId = $pageInfo['page_id'] ?? 0;
                $this->info("原生页ID：".$pageId);
                $dcList = [];
                $this->getDynamicCreatives($accountInfo, time(), '', false, $dcList);
                if(empty($dcList)){
                    $this->error($accountId . "-没有检测到创意");
                    continue;
                }else{
                    $this->info($accountId . "-检测到创意，正常");
                }

                foreach($dcList as $dcData){
                    $interface = 'dynamic_creatives/update';
                    $url       = 'https://api.e.qq.com/v3.0/' . $interface;

                    $common_parameters = array(
                        'access_token' => $accountInfo->access_token,
                        'timestamp'    => time(),
                        'nonce'        => md5(uniqid('', true))
                    );

                    $dcData['creative_components']['jump_info'][0]['value']      = [
                        "page_type" => "PAGE_TYPE_WECHAT_CANVAS",
                        "page_spec" => [
                            "wechat_canvas_spec" => [
                                "page_id" => $pageId
                            ]
                        ]
                    ];
                    $dcData['creative_components']['main_jump_info'][0]['value'] = [
                        "page_type" => "PAGE_TYPE_WECHAT_CANVAS",
                        "page_spec" => [
                            "wechat_canvas_spec" => [
                                "page_id" => $pageId
                            ]
                        ]
                    ];

                    $dcData['account_id'] = $accountId;
                    $parameters           = $dcData;
                    $parameters           = json_encode($parameters);
                    $request_url          = $url . '?' . http_build_query($common_parameters);

                    $curl = curl_init();
                    curl_setopt($curl, CURLOPT_URL, $request_url);
                    curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 60);
                    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
                    curl_setopt($curl, CURLOPT_POST, 1);
                    curl_setopt($curl, CURLOPT_POSTFIELDS, $parameters);
                    curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                        "Content-Type:application/json"
                    ));
                    $response = curl_exec($curl);
                    if (curl_error($curl)) {
                        $error_msg = curl_error($curl);
                        $error_no  = curl_errno($curl);
                        curl_close($curl);
                        throw new \Exception($error_msg, $error_no);
                    }
                    curl_close($curl);
                    $resultJson[] = json_decode($response,true);
                    $this->info("修改结果：".$response);

                }
            }
            $switchNativePage->status = 2;
            $switchNativePage->result_json = json_encode($resultJson, JSON_UNESCAPED_UNICODE);
            $switchNativePage->save();
        }


    }


    public function getDynamicCreatives(TencentAdAccount $accountInfo, $last_modified_time = '', $cursor = '', $isDelete = false, &$dcList = [])
    {
        //$this->info("页码：".$cursor);
        //$this->info("是否删除：".$isDelete);
        $interface = 'dynamic_creatives/get';
        $url       = 'https://api.e.qq.com/v3.0/' . $interface;

        $common_parameters = array(
            'access_token' => $accountInfo->access_token,
            'timestamp'    => time(),
            'nonce'        => md5(uniqid('', true))
        );

        $parameters                = [
            'account_id'      => $accountInfo->account_id,
            'fields'          =>
                [
                    'adgroup_id',
                    'dynamic_creative_id',
                    'dynamic_creative_name',
                    'creative_template_id',
                    'delivery_mode',
                    'dynamic_creative_type',
                    'creative_components',
                    'impression_tracking_url',
                    'click_tracking_url',
                    'program_creative_info',
                    'page_track_url',
                    'configured_status',
                    'is_deleted',
                    'created_time',
                    'last_modified_time',
                    'marketing_asset_verification',
                    'creative_set_approval_status',
                    'source',
                    'asset_inconsistent_status',
                ],
            'page_size'       => 100,//000,
            'cursor'          => $cursor,
            'is_deleted'      => $isDelete,
            'pagination_mode' => 'PAGINATION_MODE_CURSOR',
        ];
        $parameters['filtering'][] = [
            'field'    => 'last_modified_time',
            'operator' => 'LESS_EQUALS',
            'values'   => [
                (string)$last_modified_time
            ]
        ];

        $parameters = array_merge($common_parameters, $parameters);

        foreach ($parameters as $key => $value) {
            if (!is_string($value)) {
                $parameters[$key] = json_encode($value);
            }
        }

        $request_url = $url . '?' . http_build_query($parameters);

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $request_url);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 60);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $response = curl_exec($curl);
        if (curl_error($curl)) {
            $error_msg = curl_error($curl);
            $error_no  = curl_errno($curl);
            curl_close($curl);
            throw new \Exception($error_msg, $error_no);
        }
        curl_close($curl);
        //$this->info($response);
        $data = (json_decode($response, true));
        if (isset($data['data']['list'])) {
            foreach ($data['data']['list'] as $datum) {
                $dcList[] = $datum;
            }
        }
        if (!empty($data['data']['cursor_page_info']['next_cursor'])) {
            $this->info("翻页" . time());
            return $this->getDynamicCreatives($accountInfo, $last_modified_time, $data['data']['cursor_page_info']['next_cursor'], $isDelete, $data);
        }
        return true;
    }

    public function makeCustomerPage($accountInfo)
    {
        $interface = 'wechat_pages_custom/add';
        $url       = 'https://api.e.qq.com/v3.0/' . $interface;
        // dd($accountInfo);
        $common_parameters = array(
            'access_token' => $accountInfo->access_token,
            'timestamp'    => time(),
            'nonce'        => md5(uniqid('', true))
        );

        $parameters = [
            "account_id"         => $accountInfo->account_id,
            "page_name"          => "测试原生页",
            'page_template_id'   => **********,
            "page_specs_list"    => [
                "0" => [
                    "page_elements_spec_list" => [
                        "0" => [
                            "element_type"   => "TOP_IMAGE",
                            "top_image_spec" => [
                                "image_id"    => "***********",
                                "width"       => 800,
                                "height"      => 800,
                                "ad_location" => "gzh"
                            ]
                        ],
                    ]
                ]
            ],
            "share_content_spec" => [
                "share_title"       => "标题",
                "share_description" => "分享内容"
            ]
        ];

        $parameters  = json_encode($parameters);
        $request_url = $url . '?' . http_build_query($common_parameters);

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $request_url);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 60);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $parameters);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array(
            "Content-Type:application/json"
        ));
        $response = curl_exec($curl);
        if (curl_error($curl)) {
            $error_msg = curl_error($curl);
            $error_no  = curl_errno($curl);
            curl_close($curl);
            throw new \Exception($error_msg, $error_no);
        }
        curl_close($curl);
        return json_decode($response, true);
    }

    public function imagesAdd(TencentAdAccount $accountInfo)
    {
        $interface = 'images/add';
        $url       = 'https://api.e.qq.com/v1.1/' . $interface;

        $common_parameters = array(
            'access_token' => $accountInfo->access_token,
            'timestamp'    => time(),
            'nonce'        => md5(uniqid('', true))
        );

        $request_url = $url . '?' . http_build_query($common_parameters);

        $fileName
            = 'https://wra.oss-cn-hangzhou.aliyuncs.com/demo%2B%2B.jpg?x-oss-credential=TMP.3KqPyKjnZYyK3puepR6ALKM3KhFFTum8pQVo7ofKLfm5sfwkNYbeX9ZM4faP7EfeuS6ew1CnLd5hTT61mcGwJ6nTyYunsz%2F20250413%2Fcn-hangzhou%2Foss%2Faliyun_v4_request&x-oss-date=20250413T090941Z&x-oss-expires=3600&x-oss-signature-version=OSS4-HMAC-SHA256&x-oss-signature=3bf5a26d07bfdb39207eec08a6b30de336682ae89cc129ce50bd80692d40bd22';
        $parameters = array(
            'account_id'  => $accountInfo->account_id,
            'upload_type' => 'UPLOAD_TYPE_FILE',
            'signature'   => md5_file($fileName),
        );

        $files = array(
            'file' => curl_file_create($fileName),
        );

        foreach ($parameters as $key => $value) {
            if (!is_string($value)) {
                $parameters[$key] = json_encode($value);
            }
        }

        $parameters = array_merge($parameters, $files);

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $request_url);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 60);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $parameters);
        $response = curl_exec($curl);
        if (curl_error($curl)) {
            $error_msg = curl_error($curl);
            $error_no  = curl_errno($curl);
            curl_close($curl);
            throw new \Exception($error_msg, $error_no);
        }
        curl_close($curl);
        return $response;
    }

    function wechat_pages_get($accountInfo)
    {
        $interface = 'wechat_pages/get';
        $url       = 'https://api.e.qq.com/v1.1/' . $interface;

        $common_parameters = array(
            'access_token' => $accountInfo->access_token,
            'timestamp'    => time(),
            'nonce'        => md5(uniqid('', true)),
            'fields'       => [
                'page_name',
                'created_time',
                'page_template_id',
                'page_elements_spec_list',
                'share_content_spec',
                'canvas_type',
                'page_status'
            ]
        );

        $parameters = array(
            'account_id' => $accountInfo->account_id,
            'filtering'  => [],
            'page'       => 1,
            'page_size'  => 10,
        );

        $parameters = array_merge($common_parameters, $parameters);

        foreach ($parameters as $key => $value) {
            if (!is_string($value)) {
                $parameters[$key] = json_encode($value);
            }
        }

        $request_url = $url . '?' . http_build_query($parameters);

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $request_url);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 60);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $response = curl_exec($curl);
        if (curl_error($curl)) {
            $error_msg = curl_error($curl);
            $error_no  = curl_errno($curl);
            curl_close($curl);
            throw new \Exception($error_msg, $error_no);
        }
        curl_close($curl);
        return json_decode($response, true);
    }

    function wechat_pages_add($access_token)
    {
        $interface = 'wechat_pages/add';
        $url       = 'https://api.e.qq.com/v3.0/' . $interface;

        $common_parameters = array(
            'access_token' => $access_token,
            'timestamp'    => time(),
            'nonce'        => md5(uniqid('', true))
        );

        $parameters = [
            'account_id'              => *********,
            'page_name'               => 'test+name',
            'page_template_id'        => '<PAGE_TEMPLATE_ID>',
            'page_elements_spec_list' => [
                0 =>
                    [
                        'element_type' => 'IMAGE',
                        'image_spec'   =>
                            [
                                'image_id_list' =>
                                    [
                                        0 => '4152626:583dfc8e7f53a58db8293622b78a3c7f',
                                    ],
                            ],
                    ],
                1 =>
                    [
                        'element_type' => 'BUTTON',
                        'button_spec'  =>
                            [
                                'title' => '中老年食补养生知识',
                                'url'   => 'http://jd.com',
                            ],
                    ],
            ],
            'share_content_spec'      =>
                [
                    'share_title'       => '分享标题',
                    'share_description' => '分享描述信息',
                ],
        ];

        $parameters  = json_encode($parameters);
        $request_url = $url . '?' . http_build_query($common_parameters);

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $request_url);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 60);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $parameters);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array(
            "Content-Type:application/json"
        ));
        $response = curl_exec($curl);
        if (curl_error($curl)) {
            $error_msg = curl_error($curl);
            $error_no  = curl_errno($curl);
            curl_close($curl);
            throw new \Exception($error_msg, $error_no);
        }
        curl_close($curl);
        return $response;
    }

    function dynamic_creatives_get(TencentAdAccount $accountInfo, $did)
    {
        $interface = 'dynamic_creatives/get';
        $url       = 'https://api.e.qq.com/v3.0/' . $interface;

        $common_parameters = array(
            'access_token' => $accountInfo->access_token,
            'timestamp'    => time(),
            'nonce'        => md5(uniqid('', true))
        );

        $parameters = array(
            'filtering'       =>
                array(
                    0 =>
                        array(
                            'operator' => 'EQUALS',
                            'field'    => 'dynamic_creative_id',
                            'values'   => [
                                $did
                            ]
                        ),
                ),
            'account_id'      => $accountInfo->account_id,
            'fields'          => [
                'adgroup_id', 'dynamic_creative_id', 'dynamic_creative_name', 'creative_template_id', 'delivery_mode',
                'auto_derived_program_creative_switch', 'creative_components',
                'program_creative_info',
                'dynamic_creative_type', 'dynamic_creative_elements'
            ],
            'page_size'       => 10,
            'pagination_mode' => 'PAGINATION_MODE_CURSOR',
        );

        $parameters = array_merge($common_parameters, $parameters);

        foreach ($parameters as $key => $value) {
            if (!is_string($value)) {
                $parameters[$key] = json_encode($value);
            }
        }

        $request_url = $url . '?' . http_build_query($parameters);

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $request_url);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 60);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $response = curl_exec($curl);
        if (curl_error($curl)) {
            $error_msg = curl_error($curl);
            $error_no  = curl_errno($curl);
            curl_close($curl);
            throw new \Exception($error_msg, $error_no);
        }
        curl_close($curl);
        return $response;
    }

    function dynamic_creatives_add(TencentAdAccount $accountInfo)
    {
        $interface = 'dynamic_creatives/add';
        $url       = 'https://api.e.qq.com/v3.0/' . $interface;

        $common_parameters = [
            'access_token' => $accountInfo->access_token,
            'timestamp'    => time(),
            'nonce'        => md5(uniqid('', true))
        ];

        $parameters = [
            "account_id"            => ********,
            "creative_components"   => [
                "image"          => [
                    [
                        "component_id" => ***********
                    ]
                ],
                "action_button"  => [
                    [
                        "value" => [
                            "button_text" => "查看详情"
                        ]
                    ]
                ],
                "main_jump_info" => [
                    [
                        "value" => [
                            "page_type" => "PAGE_TYPE_WECHAT_CANVAS",
                            "page_spec" => [
                                "wechat_canvas_spec" => [
                                    "page_id" => **********
                                ]
                            ]
                        ]
                    ]
                ],
                "description"    => [
                    [
                        "component_id" => 26154192256
                    ]
                ],
                "brand"          => [
                    [
                        "value" => [
                            "brand_name"     => "聪茜-四季养生书",
                            "brand_image_id" => "17322141142"
                        ]
                    ]
                ]
            ],
            "adgroup_id"            => 36254412691,
            "dynamic_creative_name" => "组件化创意_创意1_04_17_18:19:40",
            "dynamic_creative_type" => "DYNAMIC_CREATIVE_TYPE_PROGRAM",
            "delivery_mode"         => "DELIVERY_MODE_COMPONENT",
            "creative_template_id"  => 711,
            "program_creative_info" => [
                "material_derive_info" => [
                    [
                        "original_material_id_list"            => [
                            "17918795133"
                        ],
                        "original_adcreative_template_id_list" => [
                            711
                        ]
                    ]
                ]
            ]
        ];

        $parameters  = json_encode($parameters);
        $request_url = $url . '?' . http_build_query($common_parameters);

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $request_url);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 60);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $parameters);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array(
            "Content-Type:application/json"
        ));
        $response = curl_exec($curl);
        if (curl_error($curl)) {
            $error_msg = curl_error($curl);
            $error_no  = curl_errno($curl);
            curl_close($curl);
            throw new \Exception($error_msg, $error_no);
        }
        curl_close($curl);
        return $response;
    }
}
