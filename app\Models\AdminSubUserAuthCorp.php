<?php

namespace App\Models;

use App\Jobs\AdminActionLogJob;
use Dcat\Admin\Admin;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property mixed $corp_id
 * @property mixed $id
 * @property mixed $admin_uid
 * @property mixed|null $deleted_at
 * @property WwCorpInfo $corpInfo
 */
class AdminSubUserAuthCorp extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'admin_sub_user_auth_corps';

    public function adminInfo(): BelongsTo
    {
        return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
    }

    public function corpInfo(): BelongsTo
    {
        return $this->BelongsTo(WwCorpInfo::class, 'corp_id', 'id');
    }


    /**
     * 添加授权记录
     * @param $adminUid
     * @param $corpInfo
     * @param $autoLabel
     * @return void
     */
    public static function addCorpRecord($adminUid, $corpInfo, $autoLabel = ''): void
    {
        //主管理员，授权给自己
        $corpUser = AdminSubUserAuthCorp::withTrashed()->where("admin_uid", $adminUid)->where("corp_id", $corpInfo->id)->first();
        if (!$corpUser) {
            $corpUser = new AdminSubUserAuthCorp();
        }
        $corpUser->deleted_at = null;
        $corpUser->admin_uid = $adminUid;
        $corpUser->corp_id = $corpInfo->id;
        $corpUser->save();

        //添加操作日志队列
        AdminActionLogJob::dispatch(
            'create_corp',
            $corpUser->id,
            AdminActionLog::ACTION_TYPE['企微'],
            '企业微信「' . $corpInfo->corp_name . '」授权。',
            getIp(),
            $adminUid,
        )->onQueue('admin_action_log_job');
    }

    /**
     * 验证共享企业访问权限
     * @param $subUserAuthCorp
     * @return array{success: bool, message: string,data?: WwCorpInfo}
     */
    public static function validateSubUserAuthCorpAccess($subUserAuthCorp): array
    {
        if (!$subUserAuthCorp) {
            return [
                'success' => false,
                'message' => '共享的企业不存在，请刷新后重试-1',
            ];
        }
        if (!AdminUser::isAdmin($subUserAuthCorp) && !Admin::user()->isRole("wop")) {
            return [
                'success' => false,
                'message' => '无操作权限',
            ];
        }
        $corpInfo = WwCorpInfo::query()->find($subUserAuthCorp->corp_id);
        if (!$corpInfo) {
            return [
                'success' => false,
                'message' => '共享的企业不存在，请刷新后重试-2',
            ];
        }
        return [
            'success' => true,
            'message' => '',
            'data' => $corpInfo
        ];
    }


    /**
     * 获取企微列表 根据admin_uid
     * @param $adminUid
     * @return array
     */
    public static function getCorpAuthListBuyAdminUid($adminUid)
    {
        $query = self::query()
            ->with(['corpInfo:id,corp_name']) // 只加载必要的关联字段
            ->orderByDesc('id');

        //如果不是超管跟运营 则获取各自对应的企微
        if (!AdminUser::isSystemOp()) {
            $query = $query->whereIn('admin_uid', AdminSubUser::getAdminUids($adminUid));
        }
        $getCorpAuthList = $query
            ->get()
            ->mapWithKeys(function ($item) {
                if (AdminUser::isSystemOp()) {
                    return [
                        $item->id => "【{$item->adminInfo->name}】「{$item->id}」{$item->corpInfo->corp_name}"
                    ];
                } else {
                    return [
                        $item->id => "「{$item->id}」{$item->corpInfo->corp_name}"
                    ];
                }
            })
            ->all();
        return $getCorpAuthList;
    }

    /**
     * 获取企微列表 根据copr_id
     * @param $adminUid
     * @return array
     */
    public static function getCorpAuthListBuyCorpIds($corpIds)
    {
        if(empty($corpIds)) {
            return [];
        }
        $query = self::query()
            ->with(['corpInfo:id,corp_name']) // 只加载必要的关联字段
            ->orderByDesc('id');

        $query = $query->whereIn('id', $corpIds);
        $getCorpAuthList = $query
            ->get()
            ->mapWithKeys(function ($item) {
                if (AdminUser::isSystemOp()) {
                    return [
                        $item->id => "【{$item->adminInfo->username}】「{$item->id}」{$item->corpInfo->corp_name}"
                    ];
                } else {
                    return [
                        $item->id => "「{$item->id}」{$item->corpInfo->corp_name}"
                    ];
                }
            })
            ->all();
        return $getCorpAuthList;
    }
}
