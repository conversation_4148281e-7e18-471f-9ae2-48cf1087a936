<?php

namespace App\Admin\Actions\Grid\wwUser;

use App\Admin\Extensions\Grid\BatchActionPlus;
use App\Admin\Forms\WwUser\BatchSetWeightForm;

class BatchSetWeight extends BatchActionPlus
{
    /**
     * @return string
     */
    public $title = '接粉权重';

    public $buttonText = '<button class="btn btn-primary ww_user_batch_btn"><i class="feather icon-award"></i>&nbsp;接粉权重</button>';


    public function form(): BatchSetWeightForm
    {
        // 实例化表单类
        return BatchSetWeightForm::make();
    }
}
