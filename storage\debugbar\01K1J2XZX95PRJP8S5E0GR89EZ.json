{"__meta": {"id": "01K1J2XZX95PRJP8S5E0GR89EZ", "datetime": "2025-08-01 13:50:58", "utime": **********.474472, "method": "GET", "uri": "/ztfz/sale", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754027454.874315, "end": **********.474489, "duration": 3.6001739501953125, "duration_str": "3.6s", "measures": [{"label": "Booting", "start": 1754027454.874315, "relative_start": 0, "end": **********.177531, "relative_end": **********.177531, "duration": 0.*****************, "duration_str": "303ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.177547, "relative_start": 0.*****************, "end": **********.47449, "relative_end": 9.5367431640625e-07, "duration": 3.***************, "duration_str": "3.3s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.207403, "relative_start": 0.****************, "end": **********.212107, "relative_end": **********.212107, "duration": 0.004703998565673828, "duration_str": "4.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: admin::grid.table", "start": **********.199383, "relative_start": 1.****************, "end": **********.199383, "relative_end": **********.199383, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.table-toolbar", "start": **********.200585, "relative_start": 1.****************, "end": **********.200585, "relative_end": **********.200585, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.batch-actions", "start": **********.201507, "relative_start": 1.****************, "end": **********.201507, "relative_end": **********.201507, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.dropdown-actions", "start": **********.203404, "relative_start": 1.3290889263153076, "end": **********.203404, "relative_end": **********.203404, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.dropdown-actions", "start": **********.204491, "relative_start": 1.3301758766174316, "end": **********.204491, "relative_end": **********.204491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.dropdown-actions", "start": **********.204849, "relative_start": 1.3305339813232422, "end": **********.204849, "relative_end": **********.204849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.table-pagination", "start": **********.205508, "relative_start": 1.331192970275879, "end": **********.205508, "relative_end": **********.205508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.pagination", "start": **********.207637, "relative_start": 1.3333220481872559, "end": **********.207637, "relative_end": **********.207637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.dropdown", "start": **********.209798, "relative_start": 1.3354830741882324, "end": **********.209798, "relative_end": **********.209798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.switch", "start": **********.077312, "relative_start": 2.2029969692230225, "end": **********.077312, "relative_end": **********.077312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.switch", "start": **********.078307, "relative_start": 2.2039918899536133, "end": **********.078307, "relative_end": **********.078307, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.switch", "start": **********.07886, "relative_start": 2.204545021057129, "end": **********.07886, "relative_end": **********.07886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.radio", "start": **********.080124, "relative_start": 2.2058088779449463, "end": **********.080124, "relative_end": **********.080124, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.radio", "start": **********.08062, "relative_start": 2.2063050270080566, "end": **********.08062, "relative_end": **********.08062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.082393, "relative_start": 2.208077907562256, "end": **********.082393, "relative_end": **********.082393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.radio", "start": **********.083818, "relative_start": 2.209502935409546, "end": **********.083818, "relative_end": **********.083818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.radio", "start": **********.084033, "relative_start": 2.2097179889678955, "end": **********.084033, "relative_end": **********.084033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.084799, "relative_start": 2.210484027862549, "end": **********.084799, "relative_end": **********.084799, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.radio", "start": **********.085643, "relative_start": 2.2113280296325684, "end": **********.085643, "relative_end": **********.085643, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.radio", "start": **********.085848, "relative_start": 2.2115330696105957, "end": **********.085848, "relative_end": **********.085848, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.086566, "relative_start": 2.2122509479522705, "end": **********.086566, "relative_end": **********.086566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.fixed-table", "start": **********.091587, "relative_start": 2.2172720432281494, "end": **********.091587, "relative_end": **********.091587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.table-toolbar", "start": **********.092467, "relative_start": 2.2181520462036133, "end": **********.092467, "relative_end": **********.092467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.batch-actions", "start": **********.092976, "relative_start": 2.218661069869995, "end": **********.092976, "relative_end": **********.092976, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.button", "start": **********.094904, "relative_start": 2.2205889225006104, "end": **********.094904, "relative_end": **********.094904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.form", "start": **********.511562, "relative_start": 2.637247085571289, "end": **********.511562, "relative_end": **********.511562, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.fields", "start": **********.512577, "relative_start": 2.6382620334625244, "end": **********.512577, "relative_end": **********.512577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select", "start": **********.51356, "relative_start": 2.63924503326416, "end": **********.51356, "relative_end": **********.51356, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.514678, "relative_start": 2.6403629779815674, "end": **********.514678, "relative_end": **********.514678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.515909, "relative_start": 2.6415939331054688, "end": **********.515909, "relative_end": **********.515909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select-script", "start": **********.516794, "relative_start": 2.6424789428710938, "end": **********.516794, "relative_end": **********.516794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.517719, "relative_start": 2.643404006958008, "end": **********.517719, "relative_end": **********.517719, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.hidden", "start": **********.518915, "relative_start": 2.6445999145507812, "end": **********.518915, "relative_end": **********.518915, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.hidden", "start": **********.519577, "relative_start": 2.6452620029449463, "end": **********.519577, "relative_end": **********.519577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.form", "start": **********.100889, "relative_start": 3.226573944091797, "end": **********.100889, "relative_end": **********.100889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.fields", "start": **********.101374, "relative_start": 3.2270588874816895, "end": **********.101374, "relative_end": **********.101374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select", "start": **********.101864, "relative_start": 3.2275490760803223, "end": **********.101864, "relative_end": **********.101864, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.102308, "relative_start": 3.2279930114746094, "end": **********.102308, "relative_end": **********.102308, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.102682, "relative_start": 3.2283670902252197, "end": **********.102682, "relative_end": **********.102682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select-script", "start": **********.103026, "relative_start": 3.228710889816284, "end": **********.103026, "relative_end": **********.103026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.103397, "relative_start": 3.229081869125366, "end": **********.103397, "relative_end": **********.103397, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.multipleselect", "start": **********.104405, "relative_start": 3.2300899028778076, "end": **********.104405, "relative_end": **********.104405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.105014, "relative_start": 3.230699062347412, "end": **********.105014, "relative_end": **********.105014, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.10536, "relative_start": 3.2310450077056885, "end": **********.10536, "relative_end": **********.10536, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select-script", "start": **********.105721, "relative_start": 3.2314059734344482, "end": **********.105721, "relative_end": **********.105721, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.10608, "relative_start": 3.231765031814575, "end": **********.10608, "relative_end": **********.10608, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select", "start": **********.106744, "relative_start": 3.232429027557373, "end": **********.106744, "relative_end": **********.106744, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.107179, "relative_start": 3.2328639030456543, "end": **********.107179, "relative_end": **********.107179, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.10753, "relative_start": 3.233215093612671, "end": **********.10753, "relative_end": **********.10753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select-script", "start": **********.107873, "relative_start": 3.233557939529419, "end": **********.107873, "relative_end": **********.107873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.108215, "relative_start": 3.2339000701904297, "end": **********.108215, "relative_end": **********.108215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.file", "start": **********.10922, "relative_start": 3.2349050045013428, "end": **********.10922, "relative_end": **********.10922, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.109886, "relative_start": 3.2355709075927734, "end": **********.109886, "relative_end": **********.109886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.11025, "relative_start": 3.2359349727630615, "end": **********.11025, "relative_end": **********.11025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.keyvalue", "start": **********.11133, "relative_start": 3.2370150089263916, "end": **********.11133, "relative_end": **********.11133, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.textarea", "start": **********.112525, "relative_start": 3.2382099628448486, "end": **********.112525, "relative_end": **********.112525, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.113099, "relative_start": 3.238784074783325, "end": **********.113099, "relative_end": **********.113099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.113431, "relative_start": 3.2391159534454346, "end": **********.113431, "relative_end": **********.113431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.textarea", "start": **********.113969, "relative_start": 3.239654064178467, "end": **********.113969, "relative_end": **********.113969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.114369, "relative_start": 3.24005389213562, "end": **********.114369, "relative_end": **********.114369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.114839, "relative_start": 3.2405240535736084, "end": **********.114839, "relative_end": **********.114839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.listbox", "start": **********.11574, "relative_start": 3.241425037384033, "end": **********.11574, "relative_end": **********.11574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.116351, "relative_start": 3.2420358657836914, "end": **********.116351, "relative_end": **********.116351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.116724, "relative_start": 3.2424089908599854, "end": **********.116724, "relative_end": **********.116724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.radio", "start": **********.117645, "relative_start": 3.2433300018310547, "end": **********.117645, "relative_end": **********.117645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.radio", "start": **********.117833, "relative_start": 3.2435178756713867, "end": **********.117833, "relative_end": **********.117833, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.118862, "relative_start": 3.244546890258789, "end": **********.118862, "relative_end": **********.118862, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.119203, "relative_start": 3.2448880672454834, "end": **********.119203, "relative_end": **********.119203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.radio", "start": **********.119851, "relative_start": 3.2455360889434814, "end": **********.119851, "relative_end": **********.119851, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.radio", "start": **********.120032, "relative_start": 3.2457170486450195, "end": **********.120032, "relative_end": **********.120032, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.12081, "relative_start": 3.246495008468628, "end": **********.12081, "relative_end": **********.12081, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.121137, "relative_start": 3.246821880340576, "end": **********.121137, "relative_end": **********.121137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.122036, "relative_start": 3.247720956802368, "end": **********.122036, "relative_end": **********.122036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.122601, "relative_start": 3.248286008834839, "end": **********.122601, "relative_end": **********.122601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.122923, "relative_start": 3.248607873916626, "end": **********.122923, "relative_end": **********.122923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.12343, "relative_start": 3.249114990234375, "end": **********.12343, "relative_end": **********.12343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.123792, "relative_start": 3.249476909637451, "end": **********.123792, "relative_end": **********.123792, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.124111, "relative_start": 3.249795913696289, "end": **********.124111, "relative_end": **********.124111, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.number", "start": **********.124929, "relative_start": 3.2506139278411865, "end": **********.124929, "relative_end": **********.124929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.125506, "relative_start": 3.2511909008026123, "end": **********.125506, "relative_end": **********.125506, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.12585, "relative_start": 3.251534938812256, "end": **********.12585, "relative_end": **********.12585, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.number", "start": **********.1264, "relative_start": 3.252084970474243, "end": **********.1264, "relative_end": **********.1264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.126808, "relative_start": 3.252492904663086, "end": **********.126808, "relative_end": **********.126808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.1272, "relative_start": 3.252884864807129, "end": **********.1272, "relative_end": **********.1272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.128104, "relative_start": 3.253788948059082, "end": **********.128104, "relative_end": **********.128104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.128605, "relative_start": 3.2542898654937744, "end": **********.128605, "relative_end": **********.128605, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.129023, "relative_start": 3.2547080516815186, "end": **********.129023, "relative_end": **********.129023, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.129598, "relative_start": 3.2552828788757324, "end": **********.129598, "relative_end": **********.129598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.130189, "relative_start": 3.255873918533325, "end": **********.130189, "relative_end": **********.130189, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.130568, "relative_start": 3.2562530040740967, "end": **********.130568, "relative_end": **********.130568, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.checkbox", "start": **********.132922, "relative_start": 3.2586069107055664, "end": **********.132922, "relative_end": **********.132922, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.checkbox", "start": **********.134127, "relative_start": 3.2598118782043457, "end": **********.134127, "relative_end": **********.134127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.135812, "relative_start": 3.2614970207214355, "end": **********.135812, "relative_end": **********.135812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.136227, "relative_start": 3.2619118690490723, "end": **********.136227, "relative_end": **********.136227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.137042, "relative_start": 3.2627270221710205, "end": **********.137042, "relative_end": **********.137042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.137507, "relative_start": 3.2631919384002686, "end": **********.137507, "relative_end": **********.137507, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.137931, "relative_start": 3.2636160850524902, "end": **********.137931, "relative_end": **********.137931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.138595, "relative_start": 3.264280080795288, "end": **********.138595, "relative_end": **********.138595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.139194, "relative_start": 3.264878988265991, "end": **********.139194, "relative_end": **********.139194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.139821, "relative_start": 3.2655060291290283, "end": **********.139821, "relative_end": **********.139821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.140929, "relative_start": 3.2666139602661133, "end": **********.140929, "relative_end": **********.140929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.141436, "relative_start": 3.2671210765838623, "end": **********.141436, "relative_end": **********.141436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.141792, "relative_start": 3.267477035522461, "end": **********.141792, "relative_end": **********.141792, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.hidden", "start": **********.142329, "relative_start": 3.2680139541625977, "end": **********.142329, "relative_end": **********.142329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.hidden", "start": **********.142826, "relative_start": 3.2685110569000244, "end": **********.142826, "relative_end": **********.142826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.column-selector", "start": **********.143706, "relative_start": 3.2693910598754883, "end": **********.143706, "relative_end": **********.143706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.checkbox", "start": **********.143893, "relative_start": 3.269577980041504, "end": **********.143893, "relative_end": **********.143893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.checkbox", "start": **********.145206, "relative_start": 3.270890951156616, "end": **********.145206, "relative_end": **********.145206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.container", "start": **********.147981, "relative_start": 3.2736659049987793, "end": **********.147981, "relative_end": **********.147981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.149476, "relative_start": 3.275161027908325, "end": **********.149476, "relative_end": **********.149476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.150636, "relative_start": 3.2763209342956543, "end": **********.150636, "relative_end": **********.150636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.151798, "relative_start": 3.2774829864501953, "end": **********.151798, "relative_end": **********.151798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.152473, "relative_start": 3.278157949447632, "end": **********.152473, "relative_end": **********.152473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.153141, "relative_start": 3.2788259983062744, "end": **********.153141, "relative_end": **********.153141, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.153884, "relative_start": 3.279568910598755, "end": **********.153884, "relative_end": **********.153884, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.154268, "relative_start": 3.2799530029296875, "end": **********.154268, "relative_end": **********.154268, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.154853, "relative_start": 3.2805380821228027, "end": **********.154853, "relative_end": **********.154853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.155202, "relative_start": 3.2808868885040283, "end": **********.155202, "relative_end": **********.155202, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.155738, "relative_start": 3.2814230918884277, "end": **********.155738, "relative_end": **********.155738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.156107, "relative_start": 3.281791925430298, "end": **********.156107, "relative_end": **********.156107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.156651, "relative_start": 3.2823359966278076, "end": **********.156651, "relative_end": **********.156651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.157012, "relative_start": 3.2826969623565674, "end": **********.157012, "relative_end": **********.157012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.157449, "relative_start": 3.2831339836120605, "end": **********.157449, "relative_end": **********.157449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.15818, "relative_start": 3.283864974975586, "end": **********.15818, "relative_end": **********.15818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.158565, "relative_start": 3.284250020980835, "end": **********.158565, "relative_end": **********.158565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.158984, "relative_start": 3.2846689224243164, "end": **********.158984, "relative_end": **********.158984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.159635, "relative_start": 3.2853200435638428, "end": **********.159635, "relative_end": **********.159635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.160051, "relative_start": 3.285736083984375, "end": **********.160051, "relative_end": **********.160051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.160916, "relative_start": 3.2866010665893555, "end": **********.160916, "relative_end": **********.160916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.161466, "relative_start": 3.2871508598327637, "end": **********.161466, "relative_end": **********.161466, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.162455, "relative_start": 3.288140058517456, "end": **********.162455, "relative_end": **********.162455, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.163315, "relative_start": 3.2890000343322754, "end": **********.163315, "relative_end": **********.163315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.163734, "relative_start": 3.289418935775757, "end": **********.163734, "relative_end": **********.163734, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.164166, "relative_start": 3.289850950241089, "end": **********.164166, "relative_end": **********.164166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.165337, "relative_start": 3.2910220623016357, "end": **********.165337, "relative_end": **********.165337, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.166266, "relative_start": 3.2919509410858154, "end": **********.166266, "relative_end": **********.166266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.166876, "relative_start": 3.2925610542297363, "end": **********.166876, "relative_end": **********.166876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.167673, "relative_start": 3.293358087539673, "end": **********.167673, "relative_end": **********.167673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.168068, "relative_start": 3.293752908706665, "end": **********.168068, "relative_end": **********.168068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.168515, "relative_start": 3.2941999435424805, "end": **********.168515, "relative_end": **********.168515, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.169154, "relative_start": 3.2948389053344727, "end": **********.169154, "relative_end": **********.169154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.169513, "relative_start": 3.2951979637145996, "end": **********.169513, "relative_end": **********.169513, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.170094, "relative_start": 3.29577898979187, "end": **********.170094, "relative_end": **********.170094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.170456, "relative_start": 3.2961409091949463, "end": **********.170456, "relative_end": **********.170456, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.170871, "relative_start": 3.296555995941162, "end": **********.170871, "relative_end": **********.170871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.table-pagination", "start": **********.172324, "relative_start": 3.298008918762207, "end": **********.172324, "relative_end": **********.172324, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.pagination", "start": **********.172826, "relative_start": 3.298511028289795, "end": **********.172826, "relative_end": **********.172826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.dropdown", "start": **********.173384, "relative_start": 3.2990689277648926, "end": **********.173384, "relative_end": **********.173384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.tab", "start": **********.177479, "relative_start": 3.303164005279541, "end": **********.177479, "relative_end": **********.177479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::layouts.content", "start": **********.179195, "relative_start": 3.304879903793335, "end": **********.179195, "relative_end": **********.179195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.breadcrumb", "start": **********.180263, "relative_start": 3.30594801902771, "end": **********.180263, "relative_end": **********.180263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.alerts", "start": **********.181688, "relative_start": 3.307373046875, "end": **********.181688, "relative_end": **********.181688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.exception", "start": **********.182931, "relative_start": 3.3086159229278564, "end": **********.182931, "relative_end": **********.182931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.toastr", "start": **********.184164, "relative_start": 3.3098490238189697, "end": **********.184164, "relative_end": **********.184164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::layouts.page", "start": **********.185712, "relative_start": 3.311397075653076, "end": **********.185712, "relative_end": **********.185712, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::layouts.container", "start": **********.187104, "relative_start": 3.312788963317871, "end": **********.187104, "relative_end": **********.187104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.sidebar", "start": **********.188134, "relative_start": 3.31381893157959, "end": **********.188134, "relative_end": **********.188134, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.421248, "relative_start": 3.5469329357147217, "end": **********.421248, "relative_end": **********.421248, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.422958, "relative_start": 3.548642873764038, "end": **********.422958, "relative_end": **********.422958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.423646, "relative_start": 3.549330949783325, "end": **********.423646, "relative_end": **********.423646, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.424136, "relative_start": 3.549820899963379, "end": **********.424136, "relative_end": **********.424136, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.424648, "relative_start": 3.550333023071289, "end": **********.424648, "relative_end": **********.424648, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.425105, "relative_start": 3.5507900714874268, "end": **********.425105, "relative_end": **********.425105, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.425561, "relative_start": 3.551245927810669, "end": **********.425561, "relative_end": **********.425561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.42597, "relative_start": 3.5516550540924072, "end": **********.42597, "relative_end": **********.42597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.426759, "relative_start": 3.5524439811706543, "end": **********.426759, "relative_end": **********.426759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.427869, "relative_start": 3.553554058074951, "end": **********.427869, "relative_end": **********.427869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.428825, "relative_start": 3.5545098781585693, "end": **********.428825, "relative_end": **********.428825, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.429615, "relative_start": 3.555299997329712, "end": **********.429615, "relative_end": **********.429615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.430346, "relative_start": 3.5560309886932373, "end": **********.430346, "relative_end": **********.430346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.431077, "relative_start": 3.5567619800567627, "end": **********.431077, "relative_end": **********.431077, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.432032, "relative_start": 3.5577170848846436, "end": **********.432032, "relative_end": **********.432032, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.433374, "relative_start": 3.559058904647827, "end": **********.433374, "relative_end": **********.433374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.434388, "relative_start": 3.560072898864746, "end": **********.434388, "relative_end": **********.434388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.4352, "relative_start": 3.560884952545166, "end": **********.4352, "relative_end": **********.4352, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.436223, "relative_start": 3.561908006668091, "end": **********.436223, "relative_end": **********.436223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.437049, "relative_start": 3.5627338886260986, "end": **********.437049, "relative_end": **********.437049, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.437675, "relative_start": 3.5633599758148193, "end": **********.437675, "relative_end": **********.437675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.438223, "relative_start": 3.5639078617095947, "end": **********.438223, "relative_end": **********.438223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.438783, "relative_start": 3.5644679069519043, "end": **********.438783, "relative_end": **********.438783, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.439326, "relative_start": 3.5650110244750977, "end": **********.439326, "relative_end": **********.439326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.4399, "relative_start": 3.565584897994995, "end": **********.4399, "relative_end": **********.4399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.440432, "relative_start": 3.56611704826355, "end": **********.440432, "relative_end": **********.440432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.440937, "relative_start": 3.566622018814087, "end": **********.440937, "relative_end": **********.440937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.441444, "relative_start": 3.567128896713257, "end": **********.441444, "relative_end": **********.441444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.441978, "relative_start": 3.5676629543304443, "end": **********.441978, "relative_end": **********.441978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.442511, "relative_start": 3.5681960582733154, "end": **********.442511, "relative_end": **********.442511, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.443029, "relative_start": 3.568713903427124, "end": **********.443029, "relative_end": **********.443029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.443535, "relative_start": 3.5692200660705566, "end": **********.443535, "relative_end": **********.443535, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.44403, "relative_start": 3.5697150230407715, "end": **********.44403, "relative_end": **********.44403, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.444563, "relative_start": 3.5702478885650635, "end": **********.444563, "relative_end": **********.444563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.44512, "relative_start": 3.570805072784424, "end": **********.44512, "relative_end": **********.44512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.445626, "relative_start": 3.5713109970092773, "end": **********.445626, "relative_end": **********.445626, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.446119, "relative_start": 3.5718040466308594, "end": **********.446119, "relative_end": **********.446119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.446634, "relative_start": 3.5723190307617188, "end": **********.446634, "relative_end": **********.446634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.447144, "relative_start": 3.572829008102417, "end": **********.447144, "relative_end": **********.447144, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.447928, "relative_start": 3.573612928390503, "end": **********.447928, "relative_end": **********.447928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.449082, "relative_start": 3.5747668743133545, "end": **********.449082, "relative_end": **********.449082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.44986, "relative_start": 3.575545072555542, "end": **********.44986, "relative_end": **********.44986, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.450441, "relative_start": 3.5761258602142334, "end": **********.450441, "relative_end": **********.450441, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.450999, "relative_start": 3.57668399810791, "end": **********.450999, "relative_end": **********.450999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.451668, "relative_start": 3.577353000640869, "end": **********.451668, "relative_end": **********.451668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.452293, "relative_start": 3.5779778957366943, "end": **********.452293, "relative_end": **********.452293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.452884, "relative_start": 3.578568935394287, "end": **********.452884, "relative_end": **********.452884, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.45335, "relative_start": 3.5790350437164307, "end": **********.45335, "relative_end": **********.45335, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.453876, "relative_start": 3.5795609951019287, "end": **********.453876, "relative_end": **********.453876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.454397, "relative_start": 3.5800819396972656, "end": **********.454397, "relative_end": **********.454397, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.454973, "relative_start": 3.580657958984375, "end": **********.454973, "relative_end": **********.454973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.455496, "relative_start": 3.581181049346924, "end": **********.455496, "relative_end": **********.455496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.456022, "relative_start": 3.581707000732422, "end": **********.456022, "relative_end": **********.456022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.456568, "relative_start": 3.5822529792785645, "end": **********.456568, "relative_end": **********.456568, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.457082, "relative_start": 3.5827670097351074, "end": **********.457082, "relative_end": **********.457082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.457601, "relative_start": 3.5832860469818115, "end": **********.457601, "relative_end": **********.457601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.458095, "relative_start": 3.58378005027771, "end": **********.458095, "relative_end": **********.458095, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.458628, "relative_start": 3.584312915802002, "end": **********.458628, "relative_end": **********.458628, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.459177, "relative_start": 3.584861993789673, "end": **********.459177, "relative_end": **********.459177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.46, "relative_start": 3.5856850147247314, "end": **********.46, "relative_end": **********.46, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.461685, "relative_start": 3.587369918823242, "end": **********.461685, "relative_end": **********.461685, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.462943, "relative_start": 3.588628053665161, "end": **********.462943, "relative_end": **********.462943, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.463931, "relative_start": 3.589616060256958, "end": **********.463931, "relative_end": **********.463931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.465725, "relative_start": 3.591409921646118, "end": **********.465725, "relative_end": **********.465725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.467121, "relative_start": 3.592805862426758, "end": **********.467121, "relative_end": **********.467121, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.navbar", "start": **********.468614, "relative_start": 3.594299077987671, "end": **********.468614, "relative_end": **********.468614, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: announcements.index", "start": **********.469558, "relative_start": 3.595242977142334, "end": **********.469558, "relative_end": **********.469558, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.navbar-user-panel", "start": **********.470655, "relative_start": 3.5963399410247803, "end": **********.470655, "relative_end": **********.470655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 36356840, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.20", "PHP Version": "8.0.2", "Environment": "local", "Debug Mode": "Enabled", "URL": "sen.test", "Timezone": "PRC", "Locale": "zh_CN"}}, "views": {"count": 225, "nb_templates": 225, "templates": [{"name": "1x admin::grid.table", "param_count": null, "params": [], "start": **********.199267, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/table.blade.phpadmin::grid.table", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Ftable.blade.php&line=1", "ajax": false, "filename": "table.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.table"}, {"name": "2x admin::grid.table-toolbar", "param_count": null, "params": [], "start": **********.200513, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/table-toolbar.blade.phpadmin::grid.table-toolbar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Ftable-toolbar.blade.php&line=1", "ajax": false, "filename": "table-toolbar.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::grid.table-toolbar"}, {"name": "2x admin::grid.batch-actions", "param_count": null, "params": [], "start": **********.201435, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/batch-actions.blade.phpadmin::grid.batch-actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fbatch-actions.blade.php&line=1", "ajax": false, "filename": "batch-actions.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::grid.batch-actions"}, {"name": "3x admin::grid.dropdown-actions", "param_count": null, "params": [], "start": **********.203324, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/dropdown-actions.blade.phpadmin::grid.dropdown-actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdropdown-actions.blade.php&line=1", "ajax": false, "filename": "dropdown-actions.blade.php", "line": "?"}, "render_count": 3, "name_original": "admin::grid.dropdown-actions"}, {"name": "2x admin::grid.table-pagination", "param_count": null, "params": [], "start": **********.205436, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/table-pagination.blade.phpadmin::grid.table-pagination", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Ftable-pagination.blade.php&line=1", "ajax": false, "filename": "table-pagination.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::grid.table-pagination"}, {"name": "2x admin::grid.pagination", "param_count": null, "params": [], "start": **********.207527, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/pagination.blade.phpadmin::grid.pagination", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fpagination.blade.php&line=1", "ajax": false, "filename": "pagination.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::grid.pagination"}, {"name": "2x admin::widgets.dropdown", "param_count": null, "params": [], "start": **********.209726, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/dropdown.blade.phpadmin::widgets.dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fdropdown.blade.php&line=1", "ajax": false, "filename": "dropdown.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::widgets.dropdown"}, {"name": "3x admin::grid.displayer.switch", "param_count": null, "params": [], "start": **********.07724, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/switch.blade.phpadmin::grid.displayer.switch", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Fswitch.blade.php&line=1", "ajax": false, "filename": "switch.blade.php", "line": "?"}, "render_count": 3, "name_original": "admin::grid.displayer.switch"}, {"name": "3x admin::grid.displayer.editinline.radio", "param_count": null, "params": [], "start": **********.080054, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/editinline/radio.blade.phpadmin::grid.displayer.editinline.radio", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Feditinline%2Fradio.blade.php&line=1", "ajax": false, "filename": "radio.blade.php", "line": "?"}, "render_count": 3, "name_original": "admin::grid.displayer.editinline.radio"}, {"name": "5x admin::widgets.radio", "param_count": null, "params": [], "start": **********.080538, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/radio.blade.phpadmin::widgets.radio", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fradio.blade.php&line=1", "ajax": false, "filename": "radio.blade.php", "line": "?"}, "render_count": 5, "name_original": "admin::widgets.radio"}, {"name": "3x admin::grid.displayer.editinline.template", "param_count": null, "params": [], "start": **********.082316, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/editinline/template.blade.phpadmin::grid.displayer.editinline.template", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Feditinline%2Ftemplate.blade.php&line=1", "ajax": false, "filename": "template.blade.php", "line": "?"}, "render_count": 3, "name_original": "admin::grid.displayer.editinline.template"}, {"name": "1x admin::grid.fixed-table", "param_count": null, "params": [], "start": **********.091517, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/fixed-table.blade.phpadmin::grid.fixed-table", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Ffixed-table.blade.php&line=1", "ajax": false, "filename": "fixed-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.fixed-table"}, {"name": "1x admin::filter.button", "param_count": null, "params": [], "start": **********.09481, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/button.blade.phpadmin::filter.button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::filter.button"}, {"name": "2x admin::widgets.form", "param_count": null, "params": [], "start": **********.511488, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/form.blade.phpadmin::widgets.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::widgets.form"}, {"name": "2x admin::form.fields", "param_count": null, "params": [], "start": **********.512507, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/fields.blade.phpadmin::form.fields", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ffields.blade.php&line=1", "ajax": false, "filename": "fields.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::form.fields"}, {"name": "3x admin::form.select", "param_count": null, "params": [], "start": **********.513492, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/select.blade.phpadmin::form.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 3, "name_original": "admin::form.select"}, {"name": "20x admin::form.error", "param_count": null, "params": [], "start": **********.514516, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/error.blade.phpadmin::form.error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 20, "name_original": "admin::form.error"}, {"name": "20x admin::form.help-block", "param_count": null, "params": [], "start": **********.515835, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/help-block.blade.phpadmin::form.help-block", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fhelp-block.blade.php&line=1", "ajax": false, "filename": "help-block.blade.php", "line": "?"}, "render_count": 20, "name_original": "admin::form.help-block"}, {"name": "4x admin::form.select-script", "param_count": null, "params": [], "start": **********.516722, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/select-script.blade.phpadmin::form.select-script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fselect-script.blade.php&line=1", "ajax": false, "filename": "select-script.blade.php", "line": "?"}, "render_count": 4, "name_original": "admin::form.select-script"}, {"name": "12x admin::scripts.select", "param_count": null, "params": [], "start": **********.517646, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/scripts/select.blade.phpadmin::scripts.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fscripts%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 12, "name_original": "admin::scripts.select"}, {"name": "4x admin::form.hidden", "param_count": null, "params": [], "start": **********.518846, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/hidden.blade.phpadmin::form.hidden", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fhidden.blade.php&line=1", "ajax": false, "filename": "hidden.blade.php", "line": "?"}, "render_count": 4, "name_original": "admin::form.hidden"}, {"name": "1x admin::form.multipleselect", "param_count": null, "params": [], "start": **********.104334, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/multipleselect.blade.phpadmin::form.multipleselect", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fmultipleselect.blade.php&line=1", "ajax": false, "filename": "multipleselect.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.multipleselect"}, {"name": "1x admin::form.file", "param_count": null, "params": [], "start": **********.109153, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/file.blade.phpadmin::form.file", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ffile.blade.php&line=1", "ajax": false, "filename": "file.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.file"}, {"name": "1x admin::form.keyvalue", "param_count": null, "params": [], "start": **********.111264, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/keyvalue.blade.phpadmin::form.keyvalue", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fkeyvalue.blade.php&line=1", "ajax": false, "filename": "keyvalue.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.keyvalue"}, {"name": "2x admin::form.textarea", "param_count": null, "params": [], "start": **********.112457, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/textarea.blade.phpadmin::form.textarea", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ftextarea.blade.php&line=1", "ajax": false, "filename": "textarea.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::form.textarea"}, {"name": "1x admin::form.listbox", "param_count": null, "params": [], "start": **********.115676, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/listbox.blade.phpadmin::form.listbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Flistbox.blade.php&line=1", "ajax": false, "filename": "listbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.listbox"}, {"name": "2x admin::form.radio", "param_count": null, "params": [], "start": **********.117574, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/radio.blade.phpadmin::form.radio", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fradio.blade.php&line=1", "ajax": false, "filename": "radio.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::form.radio"}, {"name": "7x admin::form.input", "param_count": null, "params": [], "start": **********.121968, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/input.blade.phpadmin::form.input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Finput.blade.php&line=1", "ajax": false, "filename": "input.blade.php", "line": "?"}, "render_count": 7, "name_original": "admin::form.input"}, {"name": "2x admin::form.number", "param_count": null, "params": [], "start": **********.124866, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/number.blade.phpadmin::form.number", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fnumber.blade.php&line=1", "ajax": false, "filename": "number.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::form.number"}, {"name": "1x admin::form.checkbox", "param_count": null, "params": [], "start": **********.132817, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/checkbox.blade.phpadmin::form.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.checkbox"}, {"name": "3x admin::widgets.checkbox", "param_count": null, "params": [], "start": **********.133997, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/checkbox.blade.phpadmin::widgets.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 3, "name_original": "admin::widgets.checkbox"}, {"name": "1x admin::grid.column-selector", "param_count": null, "params": [], "start": **********.143638, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/column-selector.blade.phpadmin::grid.column-selector", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fcolumn-selector.blade.php&line=1", "ajax": false, "filename": "column-selector.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.column-selector"}, {"name": "1x admin::filter.container", "param_count": null, "params": [], "start": **********.147891, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/container.blade.phpadmin::filter.container", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fcontainer.blade.php&line=1", "ajax": false, "filename": "container.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::filter.container"}, {"name": "14x admin::filter.where", "param_count": null, "params": [], "start": **********.149401, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/where.blade.phpadmin::filter.where", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fwhere.blade.php&line=1", "ajax": false, "filename": "where.blade.php", "line": "?"}, "render_count": 14, "name_original": "admin::filter.where"}, {"name": "6x admin::filter.text", "param_count": null, "params": [], "start": **********.150529, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/text.blade.phpadmin::filter.text", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Ftext.blade.php&line=1", "ajax": false, "filename": "text.blade.php", "line": "?"}, "render_count": 6, "name_original": "admin::filter.text"}, {"name": "8x admin::filter.select", "param_count": null, "params": [], "start": **********.152407, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/select.blade.phpadmin::filter.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 8, "name_original": "admin::filter.select"}, {"name": "1x admin::widgets.tab", "param_count": null, "params": [], "start": **********.17741, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/tab.blade.phpadmin::widgets.tab", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Ftab.blade.php&line=1", "ajax": false, "filename": "tab.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::widgets.tab"}, {"name": "1x admin::layouts.content", "param_count": null, "params": [], "start": **********.179121, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/layouts/content.blade.phpadmin::layouts.content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Flayouts%2Fcontent.blade.php&line=1", "ajax": false, "filename": "content.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::layouts.content"}, {"name": "1x admin::partials.breadcrumb", "param_count": null, "params": [], "start": **********.180194, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/breadcrumb.blade.phpadmin::partials.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.breadcrumb"}, {"name": "1x admin::partials.alerts", "param_count": null, "params": [], "start": **********.18161, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/alerts.blade.phpadmin::partials.alerts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Falerts.blade.php&line=1", "ajax": false, "filename": "alerts.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.alerts"}, {"name": "1x admin::partials.exception", "param_count": null, "params": [], "start": **********.182847, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/exception.blade.phpadmin::partials.exception", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fexception.blade.php&line=1", "ajax": false, "filename": "exception.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.exception"}, {"name": "1x admin::partials.toastr", "param_count": null, "params": [], "start": **********.184067, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/toastr.blade.phpadmin::partials.toastr", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Ftoastr.blade.php&line=1", "ajax": false, "filename": "toastr.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.toastr"}, {"name": "1x admin::layouts.page", "param_count": null, "params": [], "start": **********.18564, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/layouts/page.blade.phpadmin::layouts.page", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Flayouts%2Fpage.blade.php&line=1", "ajax": false, "filename": "page.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::layouts.page"}, {"name": "1x admin::layouts.container", "param_count": null, "params": [], "start": **********.187035, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\resources\\views/vendor/admin/layouts/container.blade.phpadmin::layouts.container", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fresources%2Fviews%2Fvendor%2Fadmin%2Flayouts%2Fcontainer.blade.php&line=1", "ajax": false, "filename": "container.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::layouts.container"}, {"name": "1x admin::partials.sidebar", "param_count": null, "params": [], "start": **********.188064, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/sidebar.blade.phpadmin::partials.sidebar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.sidebar"}, {"name": "65x admin::partials.menu", "param_count": null, "params": [], "start": **********.421169, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/menu.blade.phpadmin::partials.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 65, "name_original": "admin::partials.menu"}, {"name": "1x admin::partials.navbar", "param_count": null, "params": [], "start": **********.468515, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/navbar.blade.phpadmin::partials.navbar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fnavbar.blade.php&line=1", "ajax": false, "filename": "navbar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.navbar"}, {"name": "1x announcements.index", "param_count": null, "params": [], "start": **********.469487, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\resources\\views/announcements/index.blade.phpannouncements.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fresources%2Fviews%2Fannouncements%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "announcements.index"}, {"name": "1x admin::partials.navbar-user-panel", "param_count": null, "params": [], "start": **********.470575, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/navbar-user-panel.blade.phpadmin::partials.navbar-user-panel", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fnavbar-user-panel.blade.php&line=1", "ajax": false, "filename": "navbar-user-panel.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.navbar-user-panel"}]}, "queries": {"count": 50, "nb_statements": 49, "nb_visible_statements": 50, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 2.635430000000001, "accumulated_duration_str": "2.64s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Admin.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", "line": 145}, {"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 36}], "start": **********.24612, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "wind_rich", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `wr_admin_users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Admin.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", "line": 145}, {"index": 18, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.255317, "duration": 0.24433000000000002, "duration_str": "244ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wind_rich", "explain": null, "start_percent": 0, "width_percent": 9.271}, {"sql": "insert into `wr_operation_logs` (`admin_uid`, `user_name`, `path`, `method`, `ip`, `input`, `updated_at`, `created_at`) values (1, 'ZtFzSuperAdminPro', 'ztfz/sale', 'GET', '127.0.0.1', '[]', '2025-08-01 13:50:55', '2025-08-01 13:50:55')", "type": "query", "params": [], "bindings": [1, "ZtFzSuperAdminPro", "ztfz/sale", "GET", "127.0.0.1", "[]", "2025-08-01 13:50:55", "2025-08-01 13:50:55"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 51}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.505647, "duration": 0.056850000000000005, "duration_str": "56.85ms", "memory": 0, "memory_str": null, "filename": "OpRecord.php:51", "source": {"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FHttp%2FMiddleware%2FOpRecord.php&line=51", "ajax": false, "filename": "OpRecord.php", "line": "51"}, "connection": "wind_rich", "explain": null, "start_percent": 9.271, "width_percent": 2.157}, {"sql": "select * from `wr_announcements` where `wr_announcements`.`deleted_at` is null order by `id` desc limit 7", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Announcement.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\Announcement.php", "line": 24}, {"index": 15, "namespace": null, "name": "app/Admin/bootstrap.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\bootstrap.php", "line": 62}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Admin.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", "line": 164}, {"index": 17, "namespace": null, "name": "app/Admin/bootstrap.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\bootstrap.php", "line": 80}, {"index": 19, "namespace": "middleware", "name": "admin.bootstrap", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\Bootstrap.php", "line": 14}], "start": **********.5713048, "duration": 0.047990000000000005, "duration_str": "47.99ms", "memory": 0, "memory_str": null, "filename": "Announcement.php:24", "source": {"index": 14, "namespace": null, "name": "app/Models/Announcement.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\Announcement.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAnnouncement.php&line=24", "ajax": false, "filename": "Announcement.php", "line": "24"}, "connection": "wind_rich", "explain": null, "start_percent": 11.428, "width_percent": 1.821}, {"sql": "select `wr_admin_roles`.*, `wr_admin_role_users`.`user_id` as `pivot_user_id`, `wr_admin_role_users`.`role_id` as `pivot_role_id`, `wr_admin_role_users`.`created_at` as `pivot_created_at`, `wr_admin_role_users`.`updated_at` as `pivot_updated_at` from `wr_admin_roles` inner join `wr_admin_role_users` on `wr_admin_roles`.`id` = `wr_admin_role_users`.`role_id` where `wr_admin_role_users`.`user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/HasPermissions.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\HasPermissions.php", "line": 88}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/HasPermissions.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\HasPermissions.php", "line": 76}, {"index": 21, "namespace": "middleware", "name": "admin.permission", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\Permission.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 23, "namespace": "middleware", "name": "admin.bootstrap", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\Bootstrap.php", "line": 19}], "start": **********.7290351, "duration": 0.048240000000000005, "duration_str": "48.24ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:88", "source": {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/HasPermissions.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\HasPermissions.php", "line": 88}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FHasPermissions.php&line=88", "ajax": false, "filename": "HasPermissions.php", "line": "88"}, "connection": "wind_rich", "explain": null, "start_percent": 13.249, "width_percent": 1.83}, {"sql": "select * from `wr_ww_users` where `wr_ww_users`.`deleted_at` is not null limit 10", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Admin/Traits/RecycleBinTrait.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Traits\\RecycleBinTrait.php", "line": 38}, {"index": 15, "namespace": null, "name": "app/Admin/Traits/RecycleBinTrait.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Traits\\RecycleBinTrait.php", "line": 25}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 232}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasTools.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasTools.php", "line": 53}, {"index": 18, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 235}], "start": **********.819776, "duration": 0.048100000000000004, "duration_str": "48.1ms", "memory": 0, "memory_str": null, "filename": "RecycleBinTrait.php:38", "source": {"index": 14, "namespace": null, "name": "app/Admin/Traits/RecycleBinTrait.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Traits\\RecycleBinTrait.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FTraits%2FRecycleBinTrait.php&line=38", "ajax": false, "filename": "RecycleBinTrait.php", "line": "38"}, "connection": "wind_rich", "explain": null, "start_percent": 15.08, "width_percent": 1.825}, {"sql": "select count(*) as aggregate from `wr_ww_users` where `wr_ww_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.8723001, "duration": 0.04825, "duration_str": "48.25ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 16.905, "width_percent": 1.831}, {"sql": "select * from `wr_ww_users` where `wr_ww_users`.`deleted_at` is null limit 20 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.923286, "duration": 0.04883, "duration_str": "48.83ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 18.735, "width_percent": 1.853}, {"sql": "select * from `wr_ww_corp_info` where `wr_ww_corp_info`.`id` in (1, 3) and `wr_ww_corp_info`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.976534, "duration": 0.05323, "duration_str": "53.23ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 20.588, "width_percent": 2.02}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` in (1, 4) and `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.033032, "duration": 0.04974, "duration_str": "49.74ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 22.608, "width_percent": 1.887}, {"sql": "select `wr_ww_users_groups`.*, `wr_ww_users_groups_rel`.`ww_user_id` as `pivot_ww_user_id`, `wr_ww_users_groups_rel`.`ww_group_id` as `pivot_ww_group_id`, `wr_ww_users_groups_rel`.`created_at` as `pivot_created_at`, `wr_ww_users_groups_rel`.`updated_at` as `pivot_updated_at` from `wr_ww_users_groups` inner join `wr_ww_users_groups_rel` on `wr_ww_users_groups`.`id` = `wr_ww_users_groups_rel`.`ww_group_id` where `wr_ww_users_groups_rel`.`ww_user_id` in (5, 6, 13) and `wr_ww_users_groups`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.0863369, "duration": 0.04852, "duration_str": "48.52ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 24.495, "width_percent": 1.841}, {"sql": "select * from `wr_ww_user_qrcodes` where `is_used` = 0 and `wr_ww_user_qrcodes`.`ww_user_id` in (5, 6, 13)", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.138531, "duration": 0.047310000000000005, "duration_str": "47.31ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 26.336, "width_percent": 1.795}, {"sql": "select * from `wr_admin_sub_user_auth_corps` where `wr_admin_sub_user_auth_corps`.`deleted_at` is null order by `id` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 461}, {"index": 18, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.2147622, "duration": 0.04996, "duration_str": "49.96ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:117", "source": {"index": 14, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=117", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "117"}, "connection": "wind_rich", "explain": null, "start_percent": 28.132, "width_percent": 1.896}, {"sql": "select `id`, `corp_name` from `wr_ww_corp_info` where `wr_ww_corp_info`.`id` in (1, 3, 5, 6) and `wr_ww_corp_info`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, {"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 461}, {"index": 23, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.267777, "duration": 0.0471, "duration_str": "47.1ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:117", "source": {"index": 19, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=117", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "117"}, "connection": "wind_rich", "explain": null, "start_percent": 30.027, "width_percent": 1.787}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 461}, {"index": 27, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}], "start": **********.3179522, "duration": 0.05699, "duration_str": "56.99ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 31.815, "width_percent": 2.162}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 461}, {"index": 27, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}], "start": **********.377058, "duration": 0.048310000000000006, "duration_str": "48.31ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 33.977, "width_percent": 1.833}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 9 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 461}, {"index": 27, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}], "start": **********.4278338, "duration": 0.048, "duration_str": "48ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 35.81, "width_percent": 1.821}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 461}, {"index": 27, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}], "start": **********.478044, "duration": 0.048420000000000005, "duration_str": "48.42ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 37.631, "width_percent": 1.837}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 461}, {"index": 27, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}], "start": **********.529133, "duration": 0.0526, "duration_str": "52.6ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 39.469, "width_percent": 1.996}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 461}, {"index": 27, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}], "start": **********.584279, "duration": 0.06166, "duration_str": "61.66ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 41.465, "width_percent": 2.34}, {"sql": "select `title`, `id` from `wr_ww_users_groups` where `wr_ww_users_groups`.`deleted_at` is null order by `id` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 510}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 1008}], "start": **********.6504202, "duration": 0.05733, "duration_str": "57.33ms", "memory": 0, "memory_str": null, "filename": "WwUserController.php:510", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 510}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwUserController.php&line=510", "ajax": false, "filename": "WwUserController.php", "line": "510"}, "connection": "wind_rich", "explain": null, "start_percent": 43.804, "width_percent": 2.175}, {"sql": "select `username`, `id` from `wr_admin_users` where `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 527}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 1008}], "start": **********.710177, "duration": 0.0557, "duration_str": "55.7ms", "memory": 0, "memory_str": null, "filename": "WwUserController.php:527", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 527}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwUserController.php&line=527", "ajax": false, "filename": "WwUserController.php", "line": "527"}, "connection": "wind_rich", "explain": null, "start_percent": 45.98, "width_percent": 2.114}, {"sql": "select count(*) as aggregate from `wr_ww_users` where `type` = 1 and `wr_ww_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.769908, "duration": 0.04934, "duration_str": "49.34ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 48.093, "width_percent": 1.872}, {"sql": "select * from `wr_ww_users` where `type` = 1 and `wr_ww_users`.`deleted_at` is null order by `id` desc limit 20 offset 0", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.8214178, "duration": 0.04851, "duration_str": "48.51ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 49.965, "width_percent": 1.841}, {"sql": "select * from `wr_ww_corp_info` where `wr_ww_corp_info`.`id` in (1, 3) and `wr_ww_corp_info`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.872165, "duration": 0.048420000000000005, "duration_str": "48.42ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 51.806, "width_percent": 1.837}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` in (1, 4) and `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.9227319, "duration": 0.047659999999999994, "duration_str": "47.66ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 53.643, "width_percent": 1.808}, {"sql": "select `wr_ww_users_groups`.*, `wr_ww_users_groups_rel`.`ww_user_id` as `pivot_ww_user_id`, `wr_ww_users_groups_rel`.`ww_group_id` as `pivot_ww_group_id`, `wr_ww_users_groups_rel`.`created_at` as `pivot_created_at`, `wr_ww_users_groups_rel`.`updated_at` as `pivot_updated_at` from `wr_ww_users_groups` inner join `wr_ww_users_groups_rel` on `wr_ww_users_groups`.`id` = `wr_ww_users_groups_rel`.`ww_group_id` where `wr_ww_users_groups_rel`.`ww_user_id` in (5, 6, 13) and `wr_ww_users_groups`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.9731119, "duration": 0.04829, "duration_str": "48.29ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 55.452, "width_percent": 1.832}, {"sql": "select * from `wr_ww_user_qrcodes` where `is_used` = 0 and `wr_ww_user_qrcodes`.`ww_user_id` in (5, 6, 13)", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.0241652, "duration": 0.04874, "duration_str": "48.74ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 57.284, "width_percent": 1.849}, {"sql": "select * from `wr_admin_sub_user_auth_corps` where `wr_admin_sub_user_auth_corps`.`deleted_at` is null order by `id` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.101411, "duration": 0.048409999999999995, "duration_str": "48.41ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:117", "source": {"index": 14, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=117", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "117"}, "connection": "wind_rich", "explain": null, "start_percent": 59.133, "width_percent": 1.837}, {"sql": "select `id`, `corp_name` from `wr_ww_corp_info` where `wr_ww_corp_info`.`id` in (1, 3, 5, 6) and `wr_ww_corp_info`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, {"index": 20, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.153414, "duration": 0.04722, "duration_str": "47.22ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:117", "source": {"index": 19, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=117", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "117"}, "connection": "wind_rich", "explain": null, "start_percent": 60.97, "width_percent": 1.792}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.203159, "duration": 0.04809, "duration_str": "48.09ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 62.762, "width_percent": 1.825}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.253391, "duration": 0.04796, "duration_str": "47.96ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 64.587, "width_percent": 1.82}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 9 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.3035572, "duration": 0.048369999999999996, "duration_str": "48.37ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 66.407, "width_percent": 1.835}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.354206, "duration": 0.047740000000000005, "duration_str": "47.74ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 68.242, "width_percent": 1.811}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.4040399, "duration": 0.047799999999999995, "duration_str": "47.8ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 70.053, "width_percent": 1.814}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.453957, "duration": 0.04793, "duration_str": "47.93ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 71.867, "width_percent": 1.819}, {"sql": "select * from `wr_admin_sub_user_auth_corps` where `wr_admin_sub_user_auth_corps`.`deleted_at` is null order by `id` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.520133, "duration": 0.048350000000000004, "duration_str": "48.35ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:117", "source": {"index": 14, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=117", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "117"}, "connection": "wind_rich", "explain": null, "start_percent": 73.686, "width_percent": 1.835}, {"sql": "select `id`, `corp_name` from `wr_ww_corp_info` where `wr_ww_corp_info`.`id` in (1, 3, 5, 6) and `wr_ww_corp_info`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, {"index": 20, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.5713031, "duration": 0.04802, "duration_str": "48.02ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:117", "source": {"index": 19, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=117", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "117"}, "connection": "wind_rich", "explain": null, "start_percent": 75.521, "width_percent": 1.822}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.6216981, "duration": 0.047700000000000006, "duration_str": "47.7ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 77.343, "width_percent": 1.81}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.671465, "duration": 0.05117, "duration_str": "51.17ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 79.153, "width_percent": 1.942}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 9 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.724726, "duration": 0.04772, "duration_str": "47.72ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 81.094, "width_percent": 1.811}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.774487, "duration": 0.049479999999999996, "duration_str": "49.48ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 82.905, "width_percent": 1.877}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.826187, "duration": 0.04883, "duration_str": "48.83ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 84.782, "width_percent": 1.853}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.877056, "duration": 0.04851, "duration_str": "48.51ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 86.635, "width_percent": 1.841}, {"sql": "select `id` from `wr_ww_users_groups` where `admin_uid` = 1 and `wr_ww_users_groups`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/WwUsersGroup.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\WwUsersGroup.php", "line": 155}, {"index": 14, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 234}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.9430802, "duration": 0.04768, "duration_str": "47.68ms", "memory": 0, "memory_str": null, "filename": "WwUsersGroup.php:155", "source": {"index": 13, "namespace": null, "name": "app/Models/WwUsersGroup.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\WwUsersGroup.php", "line": 155}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwUsersGroup.php&line=155", "ajax": false, "filename": "WwUsersGroup.php", "line": "155"}, "connection": "wind_rich", "explain": null, "start_percent": 88.476, "width_percent": 1.809}, {"sql": "select `ww_user_group_ids` from `wr_admin_sub_users` where `admin_uid` = 1 and `wr_admin_sub_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/WwUsersGroup.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\WwUsersGroup.php", "line": 156}, {"index": 17, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 234}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.9942439, "duration": 0.04815, "duration_str": "48.15ms", "memory": 0, "memory_str": null, "filename": "WwUsersGroup.php:156", "source": {"index": 16, "namespace": null, "name": "app/Models/WwUsersGroup.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\WwUsersGroup.php", "line": 156}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwUsersGroup.php&line=156", "ajax": false, "filename": "WwUsersGroup.php", "line": "156"}, "connection": "wind_rich", "explain": null, "start_percent": 90.285, "width_percent": 1.827}, {"sql": "select `title`, `id` from `wr_ww_users_groups` where `id` in (1, 2) and `wr_ww_users_groups`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/WwUsersGroup.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\WwUsersGroup.php", "line": 161}, {"index": 14, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 234}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.044835, "duration": 0.04809, "duration_str": "48.09ms", "memory": 0, "memory_str": null, "filename": "WwUsersGroup.php:161", "source": {"index": 13, "namespace": null, "name": "app/Models/WwUsersGroup.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\WwUsersGroup.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwUsersGroup.php&line=161", "ajax": false, "filename": "WwUsersGroup.php", "line": "161"}, "connection": "wind_rich", "explain": null, "start_percent": 92.112, "width_percent": 1.825}, {"sql": "select * from `wr_admin_menu` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 115}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/MenuCache.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\MenuCache.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 99}], "start": **********.189729, "duration": 0.0519, "duration_str": "51.9ms", "memory": 0, "memory_str": null, "filename": "ModelTree.php:123", "source": {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FModelTree.php&line=123", "ajax": false, "filename": "ModelTree.php", "line": "123"}, "connection": "wind_rich", "explain": null, "start_percent": 93.937, "width_percent": 1.969}, {"sql": "select `wr_admin_permissions`.*, `wr_admin_permission_menu`.`menu_id` as `pivot_menu_id`, `wr_admin_permission_menu`.`permission_id` as `pivot_permission_id`, `wr_admin_permission_menu`.`created_at` as `pivot_created_at`, `wr_admin_permission_menu`.`updated_at` as `pivot_updated_at` from `wr_admin_permissions` inner join `wr_admin_permission_menu` on `wr_admin_permissions`.`id` = `wr_admin_permission_menu`.`permission_id` where `wr_admin_permission_menu`.`menu_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 115}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 98}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/MenuCache.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\MenuCache.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 99}], "start": **********.248465, "duration": 0.05811, "duration_str": "58.11ms", "memory": 0, "memory_str": null, "filename": "ModelTree.php:123", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FModelTree.php&line=123", "ajax": false, "filename": "ModelTree.php", "line": "123"}, "connection": "wind_rich", "explain": null, "start_percent": 95.906, "width_percent": 2.205}, {"sql": "select `wr_admin_roles`.*, `wr_admin_role_menu`.`menu_id` as `pivot_menu_id`, `wr_admin_role_menu`.`role_id` as `pivot_role_id`, `wr_admin_role_menu`.`created_at` as `pivot_created_at`, `wr_admin_role_menu`.`updated_at` as `pivot_updated_at` from `wr_admin_roles` inner join `wr_admin_role_menu` on `wr_admin_roles`.`id` = `wr_admin_role_menu`.`role_id` where `wr_admin_role_menu`.`menu_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 115}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 98}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/MenuCache.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\MenuCache.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 99}], "start": **********.3133352, "duration": 0.04978, "duration_str": "49.78ms", "memory": 0, "memory_str": null, "filename": "ModelTree.php:123", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FModelTree.php&line=123", "ajax": false, "filename": "ModelTree.php", "line": "123"}, "connection": "wind_rich", "explain": null, "start_percent": 98.111, "width_percent": 1.889}]}, "models": {"data": {"Dcat\\Admin\\Models\\Role": {"value": 156, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Dcat\\Admin\\Models\\Menu": {"value": 61, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FMenu.php&line=1", "ajax": false, "filename": "Menu.php", "line": "?"}}, "Dcat\\Admin\\Models\\Permission": {"value": 56, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\AdminUser": {"value": 22, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminUser.php&line=1", "ajax": false, "filename": "AdminUser.php", "line": "?"}}, "App\\Models\\AdminSubUserAuthCorp": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=1", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "?"}}, "App\\Models\\WwUser": {"value": 16, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwUser.php&line=1", "ajax": false, "filename": "WwUser.php", "line": "?"}}, "App\\Models\\WwCorpInfo": {"value": 16, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwCorpInfo.php&line=1", "ajax": false, "filename": "WwCorpInfo.php", "line": "?"}}, "App\\Models\\WwUsersGroup": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwUsersGroup.php&line=1", "ajax": false, "filename": "WwUsersGroup.php", "line": "?"}}, "Dcat\\Admin\\Models\\Administrator": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FAdministrator.php&line=1", "ajax": false, "filename": "Administrator.php", "line": "?"}}, "App\\Models\\Announcement": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAnnouncement.php&line=1", "ajax": false, "filename": "Announcement.php", "line": "?"}}}, "count": 353, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://sen.test/ztfz/sale", "action_name": "dcat.admin.sale.index", "controller_action": "App\\Admin\\Controllers\\WwUserController@index", "uri": "GET ztfz/sale", "controller": "App\\Admin\\Controllers\\WwUserController@index<a href=\"phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwUserController.php&line=72\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Admin\\Controllers", "prefix": "/ztfz", "file": "<a href=\"phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwUserController.php&line=72\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Admin/Controllers/WwUserController.php:72-108</a>", "middleware": "admin.app:admin, web, admin", "duration": "3.61s", "peak_memory": "8MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-301915899 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-301915899\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-352864116 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-352864116\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-745424815 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InF3MnhKNzg1UTE3bVJicndYdkMrc0E9PSIsInZhbHVlIjoiaENsV3lKS3pxaEZZS2dXZnRxZGwyRGErZlFpaGEveEFiMGVWcHc5QytQMkEyRHpXMHNOWEVsbC9pbjJvdEZCK2ZteE4yTXRQTm04bFNQbWp6WnJia1doclNzZklCNjhwL2J1aGdIY1owRmVTRmFVMjI3azVucjNQSHRzZzdFKysiLCJtYWMiOiI4YWI1M2MwMWU3MWYwZTExMTBiOTI0MmIzNDk5NWE2N2QwY2FmY2YyMmFmZDliZDkzNTIzYTgwYjAwMGE5YmEzIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Im1qQ2ZsTHpBb2h0MjhrUzVIei9zVVE9PSIsInZhbHVlIjoiWHV2aVlSczk4Qkw4K3ZBWkJlSVBNU1ZXaHRMbG5NMEFQU1cyMExNYzN6dUpNaHZuV2hkczY3eFVQbEhCZ1ZQRjE3QWlaZU9KOTk5Wi9nbjc5QXNDNU9BQzJ4U2VjTklKRnoyZTQ0TG9jMTNjMlNYS1lOU1pTQ2hYSHhPQ3FEUjQiLCJtYWMiOiJiYTQ4M2VlNDEwMzkyOWQ0OTUzY2RhMGRjZDhmYzY0ZmUxM2FiMDJiOGViNDgzNGEwNmI2NTliYzg4ZGFmYmMzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">zh-CN,zh;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">sen.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-745424815\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1655816293 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0esJyfQEGYXbNzItiQn8t75dzSvXZmiKqhcwIHBs</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Sc1mCMq4OSwvqqSk8mbaIttmOBzjSrhvaXfSjoYm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1655816293\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-472985047 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Aug 2025 05:50:58 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-472985047\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1429176938 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>prev</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://sen.test/ztfz/sale</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">admin.prev.url</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0esJyfQEGYXbNzItiQn8t75dzSvXZmiKqhcwIHBs</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://sen.test/ztfz/sale</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1429176938\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://sen.test/ztfz/sale", "action_name": "dcat.admin.sale.index", "controller_action": "App\\Admin\\Controllers\\WwUserController@index"}, "badge": null}}