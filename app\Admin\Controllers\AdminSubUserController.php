<?php

namespace App\Admin\Controllers;

use Admin;
use App\Admin\Forms\Admin\MakeSubUser;
use App\Admin\Repositories\AdminSubUser;
use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Models\AdminSubUser as AdminSubUserModel;
use App\Models\AdminUser;
use App\Models\WwTpl;
use App\Models\WwUsersGroup;
use App\Services\NotifySendService;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Models\Role;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Modal;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;

class AdminSubUserController extends AdminController
{
    const AddRecordColumn = [
        'corp_name'=>'企微',
        'ww_user_name'=>'销售名称',
        'ww_user_id'=>'销售ID'
    ];

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid(): Grid
    {
        return Grid::make(new AdminSubUser(with(['adminInfo','roles'])), function (Grid $grid) {
            $grid->paginate(AdminUser::getPaginate());
            $grid->disableCreateButton();
            $grid->disableViewButton();
            $grid->model()->whereIn("admin_uid", AdminSubUserModel::getAdminUids(Admin::user()->id))->orderByDesc("id");
            $grid->column('id')->sortable();
            $grid->column('adminInfo.username','账号');
            $grid->column('roles','权限')->display(function($value){
                $data = $value->pluck("name");
                return implode("<br/>", json_decode($data));
            });
            $grid->column('tpl_ids','共享落地页')->display(function($value){
                $ids = json_decode($value,true);
                if(!$ids){
                    return '无';
                }
                if(in_array(-1,$ids)){
                    return '全部';
                }
                $tplList = WwTpl::query()->whereIn("id",$ids)->pluck("name");
                return implode("<br/>", $tplList->toArray());
            });
            $grid->column('ww_user_group_ids','共享销售组')->display(function($value){
                $ids = json_decode($value,true);
                if(!$ids){
                    return '无';
                }
                if(in_array(-1,$ids)){
                    return '全部';
                }
                $wwUserGroup = WwUsersGroup::query()->whereIn("id",$ids)->pluck("title");
                return implode("<br/>", $wwUserGroup->toArray());
            });
            $grid->column('hide_add_record_column','进粉记录隐藏列')->display(function($value){
                $ids = json_decode($value,true);
                if(!$ids){
                    return '无';
                }
                $return = [];
                foreach($ids as $id){
                    $return[] = self::AddRecordColumn[$id];
                }
                return implode("<br/>", $return);
            });
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->tools(function (Grid\Tools $tools) {
                $subUserModel = Modal::make()
                    ->lg()
                    ->title('新增子账号')
                    ->body(MakeSubUser::make())
                    ->button('<button class="btn btn-primary float-right" style="margin-left: 5px"><i class="feather icon-plus-square"></i>&nbsp;&nbsp;新增子账号</button>');
                $tools->append($subUserModel);
            });

            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                $filter->expand();
                $filter->equal('id')->width(2);
                $filter->like('adminInfo.username',"账号")->width(2);
            });
        });
    }

    public static function getRoleDesc($roleId): string
    {
        $data = [
//            3 => '完整权限「包含以下全部（除数据统计-整体，数据统计-整体需单独配置）」',
//            4 => '基础权限「包含腾讯广告账户管理、落地页模板、企微投放链接、进粉记录、账号管理功能」',
//            6 => '企微与销售管理「包含企微管理、销售客服管理、销售客服分组」',
//            7 => '数据统计「自身账号的数据统计，包含按销售统计、按组统计、按企业微信统计」',
//            8 => '数据统计-整体「主账户下所有账户数据合计，按照企业微信主体统计，如某企业微信今日合计进粉量」',

            3 => '完整权限：所有权限。',
            5 => '基础权限：落地页模板、投放链接、腾讯广告账户管理、加粉明细、账号管理',
            6 => '企微管理&销售管理：企微管理、销售管理、销售分组、许可证订单',
            7 => '数据统计-自身：销售统计、销售分组统计、企业微信统计',
            8 => '数据统计-全部：主账号所有账号数据合计，企业微信主体统计',
        ];
        return $data[$roleId];
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail(mixed $id): Show
    {
        return Show::make($id, new AdminSubUser(), function (Show $show) {
            if (!AdminUser::isAdmin($show->model())) {
                $show->field('N')->value("无数据");
            } else {
                $show->field('id');
            }
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new AdminSubUser(['roles']), function (Form $form) {
            $form->hidden("admin_uid")->default(Admin::user()->id);
            if (!$form->isCreating() && !AdminUser::isAdmin($form->model())) {
                $form->display('N')->value("无数据");
            }else{

                $tpl_list = WwTpl::query()->where('admin_uid', Admin::user()->id)->get()->toArray();
                $data = [
                    [
                        'id' => -1,
                        'parent_id' => 0,
                        'name' => '全部',
                        'order' => 0
                    ]
                ];
                foreach ($tpl_list as $k => $v) {
                    if($v['type'] == 1){
                        $dot = '<i class="fa fa-circle" style="font-size: 13px;color: #21b978">投放</i>';
                    }else{
                        $dot = '<i class="fa fa-circle" style="font-size: 13px;color: #e03e2d">屏蔽</i>';
                    }
                    $data[] = [
                        'id' => $v['id'],
                        'parent_id' => -1,
                        'name' => $v['name'] . '（' . $dot . '）',
                        'order' => $v['id']
                    ];
                }
                $form->tree('tpl_ids', '共享落地页模板')
                    ->nodes($data) // 设置所有节点
                    ->expand(true)
                    ->treeState(false)
                    ->customFormat(function ($v) { // 格式化外部注入的值
                        return $v;
                    })->help("将所选的落地页模板共享给子账号，勾选全部，则无需再单独选择");
                $wwUserGroupData = WwUsersGroup::query()->where('admin_uid', Admin::user()->id)->get()->toArray();
                $wwUserGroupOption = [
                    [
                        'id' => -1,
                        'parent_id' => 0,
                        'name' => '全部',
                        'order' => 0
                    ]
                ];
                foreach ($wwUserGroupData as $k => $v) {
                    if($v['type'] == 1){
                        $dot = '<i class="fa fa-circle" style="font-size: 13px;color: #21b978">投放</i>';
                    }else{
                        $dot = '<i class="fa fa-circle" style="font-size: 13px;color: #e03e2d">屏蔽</i>';
                    }
                    $wwUserGroupOption[] = [
                        'id' => $v['id'],
                        'parent_id' => -1,
                        'name' => $v['title'] . '（' . $dot . '）',
                        'order' => $v['id']
                    ];
                }
                $form->tree('ww_user_group_ids', '共享销售分组')
                    ->nodes($wwUserGroupOption) // 设置所有节点
                    ->expand(true)
                    ->treeState(false)
                    ->customFormat(function ($v) { // 格式化外部注入的值
                        return $v;
                    })->help("将所选的销售分组共享给子账号，勾选全部，则无需再单独选择");
            }
//            $form->multipleSelect("hide_add_record_column","加粉明细隐藏字段")->options(self::AddRecordColumn);
            $form->divider();
//            $form->display("权限配置说明")->value("以下是配置子账户的权限，即用户可以看到的菜单")->help("不涉及数据共享，数据请通过上面的数据共享配置，如未配置共享，子账户仅能查看自身账户的数据，如销售客服分组，子账号仅能看到自己创建的」");
            $rolesOption = [];
            $rolesData = config('admin.database.roles_model')::query()->whereIn("id",AdminSubUserModel::MANAGEABLE_ROLE_IDS)
                ->pluck('name', 'id');
            foreach ($rolesData as $k => $v) {
                $rolesOption[] = [
                    'id' => $k,
                    'parent_id' => 0,
                    'name' => self::getRoleDesc($k),
                    'order' => $k
                ];
            }
            $form->tree('roles', '权限')
                ->nodes($rolesOption)
                ->customFormat(function ($v) {
                    return array_column($v, 'id');
                });
            $form->saving(function (Form $form)  {
                /** 选择的角色ID数据 */
                $selectedRoleIds  = array_map('intval', explode(',', $form->roles));
                /** 定义不允许分配的角色ID */
                $forbiddenRoleIds = array_diff($selectedRoleIds , AdminSubUserModel::MANAGEABLE_ROLE_IDS);

                // 如果用户尝试分配未授权的角色，则阻止并记录安全事件
                if (!empty($forbiddenRoleIds)) {
                    /** @var AdminUser $adminUser */
                    $adminUser = Admin::user();

                    // 获取未授权角色的名称，用于日志和提示
                    $unauthorizedName = Role::query()
                        ->whereIn('id', $forbiddenRoleIds)
                        ->pluck('name')
                        ->toArray();

                    // 发送企业微信安全告警
                    NotifySendService::sendWorkWeixinForError(
                        "⚠【安全警告】【紧急】【子账户管理】【恶意行为】⚠" . PHP_EOL . PHP_EOL .
                        "用户「" . $adminUser->username . "」 用户ID「" . $adminUser->id . "」" . PHP_EOL .
                        "IP「" . getIp() . "」" . PHP_EOL .
                        "非法分配角色，已被系统阻止". PHP_EOL .
                        "请求角色ID：" .$form->roles. PHP_EOL .
                        "非法角色：" .implode('、', $unauthorizedName)
                    );

                    return $form->response()->alert()->error('保存失败')->detail('权限错误')->refresh();
                }

                // 保留超管分配的角色，合并当前选择的可管理角色
                AdminSubUserModel::syncUserRoles($form);
                return true;
            });
            $form->saved(function (Form $form) {
                // 记录日志
                AdminSubUserModel::logActivity($form);
            });
            $form->disableViewButton();
            $form->disableViewCheck();
            $form->disableEditingCheck();
            $form->disableViewCheck();
            $form->disableDeleteButton();
        });
    }

    /**
     * 删除子账号
     * @param $id
     * @return JsonResponse
     * @throws Throwable
     */
    public function destroy($id): JsonResponse
    {
        // 转数组
        $ids = explode(',', $id);
        try {
            //开启事务
            DB::beginTransaction();
            //1. 先查询要删除的 admin_uid
            $queryAdminUIds = AdminSubUserModel::query()
                ->select(['admin_uid'])
                ->whereIn("id",$ids)
                ->pluck('admin_uid')
                ->toArray();

            //2. 先删除子账户
            $delete = AdminSubUserModel::query()->whereIn("id",$ids)->delete();
            if (!$delete) {
                DB::rollBack();
                return $this->form()->response()->alert()->error('错误')->detail('删除子账户失败');
            }
            // 3. 再删除管理员
            $deleteAdminUser = AdminUser::query()
                ->whereIn("id",$queryAdminUIds)
                ->update(
                    [
                        'status' => 0,
                        'deleted_at' => date("Y-m-d H:i:s")
                    ]
                );

            if (!$deleteAdminUser) {
                DB::rollBack();
                return $this->form()->response()->alert()->error('错误')->detail('删除账户失败');
            }
            DB::commit();

            foreach ($ids as $id) {
                AdminActionLogJob::dispatch(
                    'del_sub_user',
                    $id,
                    AdminActionLog::ACTION_TYPE['账号'],
                    "删除子账号ID：".$id.'，用户ID: ' . implode(',', $queryAdminUIds),
                    getIp(),
                    \Dcat\Admin\Admin::user()->id
                )->onQueue('admin_action_log_job');
            }


            return $this->form()->response()->alert()->info('成功')->detail('删除成功');
        } catch (Throwable $e) {
            DB::rollBack();
            // 记录错误日志
            Log::error('删除子账户操作失败', [
                'id' => Admin::user()->id,
                'ids' => $ids,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->form()->response()->alert()->error('错误')->detail('删除操作失败 错误代码-1');
        }

    }

}
