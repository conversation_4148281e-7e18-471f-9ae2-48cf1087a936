<?php

    namespace App\Models;

    use Dcat\Admin\Traits\HasDateTimeFormatter;
    use Illuminate\Database\Eloquent\Model;
    use Illuminate\Database\Eloquent\Relations\BelongsTo;
    use Illuminate\Database\Eloquent\SoftDeletes;

    /**
     * @property WwTpl $wwTpl
     * @property mixed $ad_env
     * @property mixed $block_area
     * @property mixed $view_count
     * @property mixed $default_block_ip
     * @property mixed $share_block_ip
     * @property mixed $default_block_user
     * @property mixed $share_block_user
     * @property mixed $is_mobile
     * @property mixed $vc_count
     * @property mixed $add_count
     * @property mixed $ww_user_group_id
     * @property mixed $name
     * @property WwUsersGroup $wwUserGroup
     * @property mixed $product
     * @property mixed $mdm
     * @property mixed $admin_uid
     * @property mixed $tpl_audit_id
     */
    class ShieldPolicy extends Model
    {
        use HasDateTimeFormatter;
        use SoftDeletes;

        protected $table = 'shield_policies';

        public function adminInfo(): BelongsTo
        {
            return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
        }

        public function wwUserGroup(): BelongsTo
        {
            return $this->BelongsTo(WwUsersGroup::class, 'ww_user_group_id', 'id');
        }

        public function wwTpl(): BelongsTo
        {
            return $this->BelongsTo(WwTpl::class, 'tpl_audit_id', 'id');
        }
    }
