{"__meta": {"id": "01K1J4A6F803KFQS9RMD0ZHACR", "datetime": "2025-08-01 14:15:06", "utime": **********.985466, "method": "GET", "uri": "/ztfz/posting_link", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754028901.575925, "end": **********.985486, "duration": 5.409560918807983, "duration_str": "5.41s", "measures": [{"label": "Booting", "start": 1754028901.575925, "relative_start": 0, "end": **********.781083, "relative_end": **********.781083, "duration": 2.***************, "duration_str": "2.21s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.781263, "relative_start": 2.****************, "end": **********.985488, "relative_end": 1.9073486328125e-06, "duration": 3.****************, "duration_str": "3.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.835391, "relative_start": 2.****************, "end": **********.845936, "relative_end": **********.845936, "duration": 0.010545015335083008, "duration_str": "10.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: admin::grid.displayer.switch", "start": **********.807672, "relative_start": 4.****************, "end": **********.807672, "relative_end": **********.807672, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.switch", "start": **********.809966, "relative_start": 4.***************, "end": **********.809966, "relative_end": **********.809966, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.switch", "start": **********.811044, "relative_start": 4.***************, "end": **********.811044, "relative_end": **********.811044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.switch", "start": **********.812202, "relative_start": 4.236276865005493, "end": **********.812202, "relative_end": **********.812202, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.switch", "start": **********.813679, "relative_start": 4.237753868103027, "end": **********.813679, "relative_end": **********.813679, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.input", "start": **********.095926, "relative_start": 4.52000093460083, "end": **********.095926, "relative_end": **********.095926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.097685, "relative_start": 4.521759986877441, "end": **********.097685, "relative_end": **********.097685, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.input", "start": **********.099723, "relative_start": 4.523797988891602, "end": **********.099723, "relative_end": **********.099723, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.100491, "relative_start": 4.524565935134888, "end": **********.100491, "relative_end": **********.100491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.input", "start": **********.10204, "relative_start": 4.5261149406433105, "end": **********.10204, "relative_end": **********.10204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.102843, "relative_start": 4.526917934417725, "end": **********.102843, "relative_end": **********.102843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.input", "start": **********.104435, "relative_start": 4.528509855270386, "end": **********.104435, "relative_end": **********.104435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.105174, "relative_start": 4.529248952865601, "end": **********.105174, "relative_end": **********.105174, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.input", "start": **********.106632, "relative_start": 4.530706882476807, "end": **********.106632, "relative_end": **********.106632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.107498, "relative_start": 4.5315728187561035, "end": **********.107498, "relative_end": **********.107498, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.textarea", "start": **********.507869, "relative_start": 4.931943893432617, "end": **********.507869, "relative_end": **********.507869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.510045, "relative_start": 4.934119939804077, "end": **********.510045, "relative_end": **********.510045, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.textarea", "start": **********.512154, "relative_start": 4.93622899055481, "end": **********.512154, "relative_end": **********.512154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.513294, "relative_start": 4.937368869781494, "end": **********.513294, "relative_end": **********.513294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.textarea", "start": **********.51515, "relative_start": 4.9392249584198, "end": **********.51515, "relative_end": **********.51515, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.516015, "relative_start": 4.94008994102478, "end": **********.516015, "relative_end": **********.516015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.textarea", "start": **********.517712, "relative_start": 4.941787004470825, "end": **********.517712, "relative_end": **********.517712, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.518597, "relative_start": 4.942671775817871, "end": **********.518597, "relative_end": **********.518597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.textarea", "start": **********.520318, "relative_start": 4.944392919540405, "end": **********.520318, "relative_end": **********.520318, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.521159, "relative_start": 4.9452338218688965, "end": **********.521159, "relative_end": **********.521159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.fixed-table", "start": **********.539571, "relative_start": 4.963645935058594, "end": **********.539571, "relative_end": **********.539571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.table-toolbar", "start": **********.543108, "relative_start": 4.967182874679565, "end": **********.543108, "relative_end": **********.543108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.batch-actions", "start": **********.545765, "relative_start": 4.969839811325073, "end": **********.545765, "relative_end": **********.545765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.button", "start": **********.54812, "relative_start": 4.9721949100494385, "end": **********.54812, "relative_end": **********.54812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.form", "start": **********.572917, "relative_start": 4.996991872787476, "end": **********.572917, "relative_end": **********.572917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.fields", "start": **********.57517, "relative_start": 4.999244928359985, "end": **********.57517, "relative_end": **********.57517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.display", "start": **********.577623, "relative_start": 5.001697778701782, "end": **********.577623, "relative_end": **********.577623, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.579938, "relative_start": 5.004012823104858, "end": **********.579938, "relative_end": **********.579938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.file", "start": **********.581587, "relative_start": 5.005661964416504, "end": **********.581587, "relative_end": **********.581587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.58289, "relative_start": 5.006964921951294, "end": **********.58289, "relative_end": **********.58289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.583697, "relative_start": 5.007771968841553, "end": **********.583697, "relative_end": **********.583697, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.hidden", "start": **********.585009, "relative_start": 5.009083986282349, "end": **********.585009, "relative_end": **********.585009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.hidden", "start": **********.585932, "relative_start": 5.010006904602051, "end": **********.585932, "relative_end": **********.585932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.column-selector", "start": **********.587783, "relative_start": 5.011857986450195, "end": **********.587783, "relative_end": **********.587783, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.checkbox", "start": **********.588401, "relative_start": 5.012475967407227, "end": **********.588401, "relative_end": **********.588401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.checkbox", "start": **********.590178, "relative_start": 5.0142529010772705, "end": **********.590178, "relative_end": **********.590178, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.container", "start": **********.595852, "relative_start": 5.0199267864227295, "end": **********.595852, "relative_end": **********.595852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.598369, "relative_start": 5.022443771362305, "end": **********.598369, "relative_end": **********.598369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.59999, "relative_start": 5.024064779281616, "end": **********.59999, "relative_end": **********.59999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.601205, "relative_start": 5.025279998779297, "end": **********.601205, "relative_end": **********.601205, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.601746, "relative_start": 5.025820970535278, "end": **********.601746, "relative_end": **********.601746, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.602659, "relative_start": 5.026733875274658, "end": **********.602659, "relative_end": **********.602659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.603577, "relative_start": 5.027651786804199, "end": **********.603577, "relative_end": **********.603577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.604984, "relative_start": 5.029058933258057, "end": **********.604984, "relative_end": **********.604984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.606276, "relative_start": 5.030350923538208, "end": **********.606276, "relative_end": **********.606276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.606826, "relative_start": 5.030900955200195, "end": **********.606826, "relative_end": **********.606826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.608099, "relative_start": 5.0321738719940186, "end": **********.608099, "relative_end": **********.608099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.609531, "relative_start": 5.0336058139801025, "end": **********.609531, "relative_end": **********.609531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.610577, "relative_start": 5.0346519947052, "end": **********.610577, "relative_end": **********.610577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.611126, "relative_start": 5.035200834274292, "end": **********.611126, "relative_end": **********.611126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.611787, "relative_start": 5.035861968994141, "end": **********.611787, "relative_end": **********.611787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.61364, "relative_start": 5.037714958190918, "end": **********.61364, "relative_end": **********.61364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.614599, "relative_start": 5.0386738777160645, "end": **********.614599, "relative_end": **********.614599, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.615448, "relative_start": 5.039522886276245, "end": **********.615448, "relative_end": **********.615448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.61673, "relative_start": 5.040804862976074, "end": **********.61673, "relative_end": **********.61673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.617434, "relative_start": 5.041508913040161, "end": **********.617434, "relative_end": **********.617434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.618533, "relative_start": 5.04260778427124, "end": **********.618533, "relative_end": **********.618533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.619244, "relative_start": 5.0433189868927, "end": **********.619244, "relative_end": **********.619244, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.620061, "relative_start": 5.044135808944702, "end": **********.620061, "relative_end": **********.620061, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.table-pagination", "start": **********.62577, "relative_start": 5.049844980239868, "end": **********.62577, "relative_end": **********.62577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.pagination", "start": **********.628591, "relative_start": 5.052665948867798, "end": **********.628591, "relative_end": **********.628591, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.dropdown", "start": **********.630948, "relative_start": 5.055022954940796, "end": **********.630948, "relative_end": **********.630948, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::layouts.content", "start": **********.632742, "relative_start": 5.056816816329956, "end": **********.632742, "relative_end": **********.632742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.breadcrumb", "start": **********.63408, "relative_start": 5.058154821395874, "end": **********.63408, "relative_end": **********.63408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.alerts", "start": **********.635511, "relative_start": 5.059585809707642, "end": **********.635511, "relative_end": **********.635511, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.exception", "start": **********.636906, "relative_start": 5.060980796813965, "end": **********.636906, "relative_end": **********.636906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.toastr", "start": **********.638147, "relative_start": 5.062222003936768, "end": **********.638147, "relative_end": **********.638147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::layouts.page", "start": **********.64053, "relative_start": 5.064604997634888, "end": **********.64053, "relative_end": **********.64053, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::layouts.container", "start": **********.644064, "relative_start": 5.068138837814331, "end": **********.644064, "relative_end": **********.644064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.sidebar", "start": **********.64619, "relative_start": 5.07026481628418, "end": **********.64619, "relative_end": **********.64619, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.892314, "relative_start": 5.316388845443726, "end": **********.892314, "relative_end": **********.892314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.894424, "relative_start": 5.318498849868774, "end": **********.894424, "relative_end": **********.894424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.895274, "relative_start": 5.3193488121032715, "end": **********.895274, "relative_end": **********.895274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.895872, "relative_start": 5.319947004318237, "end": **********.895872, "relative_end": **********.895872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.896413, "relative_start": 5.320487976074219, "end": **********.896413, "relative_end": **********.896413, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.896965, "relative_start": 5.321039915084839, "end": **********.896965, "relative_end": **********.896965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.897496, "relative_start": 5.321570873260498, "end": **********.897496, "relative_end": **********.897496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.898019, "relative_start": 5.322093963623047, "end": **********.898019, "relative_end": **********.898019, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.898713, "relative_start": 5.3227880001068115, "end": **********.898713, "relative_end": **********.898713, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.899495, "relative_start": 5.3235697746276855, "end": **********.899495, "relative_end": **********.899495, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.900094, "relative_start": 5.324168920516968, "end": **********.900094, "relative_end": **********.900094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.900666, "relative_start": 5.324740886688232, "end": **********.900666, "relative_end": **********.900666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.901281, "relative_start": 5.3253560066223145, "end": **********.901281, "relative_end": **********.901281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.901887, "relative_start": 5.3259618282318115, "end": **********.901887, "relative_end": **********.901887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.902469, "relative_start": 5.326543807983398, "end": **********.902469, "relative_end": **********.902469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.90313, "relative_start": 5.327204942703247, "end": **********.90313, "relative_end": **********.90313, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.903758, "relative_start": 5.327832937240601, "end": **********.903758, "relative_end": **********.903758, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.904347, "relative_start": 5.3284218311309814, "end": **********.904347, "relative_end": **********.904347, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.904939, "relative_start": 5.329013824462891, "end": **********.904939, "relative_end": **********.904939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.905513, "relative_start": 5.329587936401367, "end": **********.905513, "relative_end": **********.905513, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.906115, "relative_start": 5.330189943313599, "end": **********.906115, "relative_end": **********.906115, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.90669, "relative_start": 5.3307647705078125, "end": **********.90669, "relative_end": **********.90669, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.908037, "relative_start": 5.332111835479736, "end": **********.908037, "relative_end": **********.908037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.909544, "relative_start": 5.333618879318237, "end": **********.909544, "relative_end": **********.909544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.910623, "relative_start": 5.334697961807251, "end": **********.910623, "relative_end": **********.910623, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.911627, "relative_start": 5.335701942443848, "end": **********.911627, "relative_end": **********.911627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.91261, "relative_start": 5.336684942245483, "end": **********.91261, "relative_end": **********.91261, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.913493, "relative_start": 5.3375678062438965, "end": **********.913493, "relative_end": **********.913493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.91421, "relative_start": 5.338284969329834, "end": **********.91421, "relative_end": **********.91421, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.914856, "relative_start": 5.33893084526062, "end": **********.914856, "relative_end": **********.914856, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.91548, "relative_start": 5.339554786682129, "end": **********.91548, "relative_end": **********.91548, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.916086, "relative_start": 5.340160846710205, "end": **********.916086, "relative_end": **********.916086, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.916599, "relative_start": 5.340673923492432, "end": **********.916599, "relative_end": **********.916599, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.917189, "relative_start": 5.341263771057129, "end": **********.917189, "relative_end": **********.917189, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.917747, "relative_start": 5.341821908950806, "end": **********.917747, "relative_end": **********.917747, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.918351, "relative_start": 5.34242582321167, "end": **********.918351, "relative_end": **********.918351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.918975, "relative_start": 5.343050003051758, "end": **********.918975, "relative_end": **********.918975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.919573, "relative_start": 5.3436479568481445, "end": **********.919573, "relative_end": **********.919573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.945665, "relative_start": 5.369739770889282, "end": **********.945665, "relative_end": **********.945665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.94843, "relative_start": 5.372504949569702, "end": **********.94843, "relative_end": **********.94843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.949928, "relative_start": 5.374002933502197, "end": **********.949928, "relative_end": **********.949928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.951254, "relative_start": 5.375328779220581, "end": **********.951254, "relative_end": **********.951254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.952738, "relative_start": 5.376812934875488, "end": **********.952738, "relative_end": **********.952738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.954373, "relative_start": 5.378447771072388, "end": **********.954373, "relative_end": **********.954373, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.95581, "relative_start": 5.379884958267212, "end": **********.95581, "relative_end": **********.95581, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.958192, "relative_start": 5.382266998291016, "end": **********.958192, "relative_end": **********.958192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.960055, "relative_start": 5.384130001068115, "end": **********.960055, "relative_end": **********.960055, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.961138, "relative_start": 5.3852128982543945, "end": **********.961138, "relative_end": **********.961138, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.962286, "relative_start": 5.3863608837127686, "end": **********.962286, "relative_end": **********.962286, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.96333, "relative_start": 5.387404918670654, "end": **********.96333, "relative_end": **********.96333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.964389, "relative_start": 5.388463973999023, "end": **********.964389, "relative_end": **********.964389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.965522, "relative_start": 5.389596939086914, "end": **********.965522, "relative_end": **********.965522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.9665, "relative_start": 5.390574932098389, "end": **********.9665, "relative_end": **********.9665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.96752, "relative_start": 5.391594886779785, "end": **********.96752, "relative_end": **********.96752, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.968443, "relative_start": 5.392517805099487, "end": **********.968443, "relative_end": **********.968443, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.969195, "relative_start": 5.393269777297974, "end": **********.969195, "relative_end": **********.969195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.969871, "relative_start": 5.393945932388306, "end": **********.969871, "relative_end": **********.969871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.970529, "relative_start": 5.394603967666626, "end": **********.970529, "relative_end": **********.970529, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.971156, "relative_start": 5.395230770111084, "end": **********.971156, "relative_end": **********.971156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.971793, "relative_start": 5.395867824554443, "end": **********.971793, "relative_end": **********.971793, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.972463, "relative_start": 5.396537780761719, "end": **********.972463, "relative_end": **********.972463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.973232, "relative_start": 5.3973069190979, "end": **********.973232, "relative_end": **********.973232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.973861, "relative_start": 5.39793586730957, "end": **********.973861, "relative_end": **********.973861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.974831, "relative_start": 5.398905992507935, "end": **********.974831, "relative_end": **********.974831, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.976003, "relative_start": 5.400077819824219, "end": **********.976003, "relative_end": **********.976003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.navbar", "start": **********.977153, "relative_start": 5.401227951049805, "end": **********.977153, "relative_end": **********.977153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: announcements.index", "start": **********.978436, "relative_start": 5.40251088142395, "end": **********.978436, "relative_end": **********.978436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.navbar-user-panel", "start": **********.980435, "relative_start": 5.404509782791138, "end": **********.980435, "relative_end": **********.980435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 35123752, "peak_usage_str": "33MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.20", "PHP Version": "8.0.2", "Environment": "local", "Debug Mode": "Enabled", "URL": "sen.test", "Timezone": "PRC", "Locale": "zh_CN"}}, "views": {"count": 143, "nb_templates": 143, "templates": [{"name": "5x admin::grid.displayer.switch", "param_count": null, "params": [], "start": **********.807443, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/switch.blade.phpadmin::grid.displayer.switch", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Fswitch.blade.php&line=1", "ajax": false, "filename": "switch.blade.php", "line": "?"}, "render_count": 5, "name_original": "admin::grid.displayer.switch"}, {"name": "5x admin::grid.displayer.editinline.input", "param_count": null, "params": [], "start": **********.095783, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/editinline/input.blade.phpadmin::grid.displayer.editinline.input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Feditinline%2Finput.blade.php&line=1", "ajax": false, "filename": "input.blade.php", "line": "?"}, "render_count": 5, "name_original": "admin::grid.displayer.editinline.input"}, {"name": "10x admin::grid.displayer.editinline.template", "param_count": null, "params": [], "start": **********.097558, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/editinline/template.blade.phpadmin::grid.displayer.editinline.template", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Feditinline%2Ftemplate.blade.php&line=1", "ajax": false, "filename": "template.blade.php", "line": "?"}, "render_count": 10, "name_original": "admin::grid.displayer.editinline.template"}, {"name": "5x admin::grid.displayer.editinline.textarea", "param_count": null, "params": [], "start": **********.5076, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/editinline/textarea.blade.phpadmin::grid.displayer.editinline.textarea", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Feditinline%2Ftextarea.blade.php&line=1", "ajax": false, "filename": "textarea.blade.php", "line": "?"}, "render_count": 5, "name_original": "admin::grid.displayer.editinline.textarea"}, {"name": "1x admin::grid.fixed-table", "param_count": null, "params": [], "start": **********.539405, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/fixed-table.blade.phpadmin::grid.fixed-table", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Ffixed-table.blade.php&line=1", "ajax": false, "filename": "fixed-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.fixed-table"}, {"name": "1x admin::grid.table-toolbar", "param_count": null, "params": [], "start": **********.542937, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/table-toolbar.blade.phpadmin::grid.table-toolbar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Ftable-toolbar.blade.php&line=1", "ajax": false, "filename": "table-toolbar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.table-toolbar"}, {"name": "1x admin::grid.batch-actions", "param_count": null, "params": [], "start": **********.545631, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/batch-actions.blade.phpadmin::grid.batch-actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fbatch-actions.blade.php&line=1", "ajax": false, "filename": "batch-actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.batch-actions"}, {"name": "1x admin::filter.button", "param_count": null, "params": [], "start": **********.54802, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/button.blade.phpadmin::filter.button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::filter.button"}, {"name": "1x admin::widgets.form", "param_count": null, "params": [], "start": **********.572803, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/form.blade.phpadmin::widgets.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::widgets.form"}, {"name": "1x admin::form.fields", "param_count": null, "params": [], "start": **********.574938, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/fields.blade.phpadmin::form.fields", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ffields.blade.php&line=1", "ajax": false, "filename": "fields.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.fields"}, {"name": "1x admin::form.display", "param_count": null, "params": [], "start": **********.577529, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/display.blade.phpadmin::form.display", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fdisplay.blade.php&line=1", "ajax": false, "filename": "display.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.display"}, {"name": "2x admin::form.help-block", "param_count": null, "params": [], "start": **********.579827, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/help-block.blade.phpadmin::form.help-block", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fhelp-block.blade.php&line=1", "ajax": false, "filename": "help-block.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::form.help-block"}, {"name": "1x admin::form.file", "param_count": null, "params": [], "start": **********.581498, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/file.blade.phpadmin::form.file", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ffile.blade.php&line=1", "ajax": false, "filename": "file.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.file"}, {"name": "1x admin::form.error", "param_count": null, "params": [], "start": **********.582797, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/error.blade.phpadmin::form.error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.error"}, {"name": "2x admin::form.hidden", "param_count": null, "params": [], "start": **********.584919, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/hidden.blade.phpadmin::form.hidden", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fhidden.blade.php&line=1", "ajax": false, "filename": "hidden.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::form.hidden"}, {"name": "1x admin::grid.column-selector", "param_count": null, "params": [], "start": **********.587675, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/column-selector.blade.phpadmin::grid.column-selector", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fcolumn-selector.blade.php&line=1", "ajax": false, "filename": "column-selector.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.column-selector"}, {"name": "2x admin::widgets.checkbox", "param_count": null, "params": [], "start": **********.588302, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/checkbox.blade.phpadmin::widgets.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::widgets.checkbox"}, {"name": "1x admin::filter.container", "param_count": null, "params": [], "start": **********.595707, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/container.blade.phpadmin::filter.container", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fcontainer.blade.php&line=1", "ajax": false, "filename": "container.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::filter.container"}, {"name": "9x admin::filter.where", "param_count": null, "params": [], "start": **********.598228, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/where.blade.phpadmin::filter.where", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fwhere.blade.php&line=1", "ajax": false, "filename": "where.blade.php", "line": "?"}, "render_count": 9, "name_original": "admin::filter.where"}, {"name": "5x admin::filter.text", "param_count": null, "params": [], "start": **********.599882, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/text.blade.phpadmin::filter.text", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Ftext.blade.php&line=1", "ajax": false, "filename": "text.blade.php", "line": "?"}, "render_count": 5, "name_original": "admin::filter.text"}, {"name": "4x admin::filter.select", "param_count": null, "params": [], "start": **********.603488, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/select.blade.phpadmin::filter.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 4, "name_original": "admin::filter.select"}, {"name": "4x admin::scripts.select", "param_count": null, "params": [], "start": **********.60489, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/scripts/select.blade.phpadmin::scripts.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fscripts%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 4, "name_original": "admin::scripts.select"}, {"name": "1x admin::grid.table-pagination", "param_count": null, "params": [], "start": **********.625558, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/table-pagination.blade.phpadmin::grid.table-pagination", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Ftable-pagination.blade.php&line=1", "ajax": false, "filename": "table-pagination.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.table-pagination"}, {"name": "1x admin::grid.pagination", "param_count": null, "params": [], "start": **********.62849, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/pagination.blade.phpadmin::grid.pagination", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fpagination.blade.php&line=1", "ajax": false, "filename": "pagination.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.pagination"}, {"name": "1x admin::widgets.dropdown", "param_count": null, "params": [], "start": **********.630847, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/dropdown.blade.phpadmin::widgets.dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fdropdown.blade.php&line=1", "ajax": false, "filename": "dropdown.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::widgets.dropdown"}, {"name": "1x admin::layouts.content", "param_count": null, "params": [], "start": **********.632651, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/layouts/content.blade.phpadmin::layouts.content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Flayouts%2Fcontent.blade.php&line=1", "ajax": false, "filename": "content.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::layouts.content"}, {"name": "1x admin::partials.breadcrumb", "param_count": null, "params": [], "start": **********.633992, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/breadcrumb.blade.phpadmin::partials.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.breadcrumb"}, {"name": "1x admin::partials.alerts", "param_count": null, "params": [], "start": **********.635388, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/alerts.blade.phpadmin::partials.alerts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Falerts.blade.php&line=1", "ajax": false, "filename": "alerts.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.alerts"}, {"name": "1x admin::partials.exception", "param_count": null, "params": [], "start": **********.636811, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/exception.blade.phpadmin::partials.exception", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fexception.blade.php&line=1", "ajax": false, "filename": "exception.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.exception"}, {"name": "1x admin::partials.toastr", "param_count": null, "params": [], "start": **********.638058, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/toastr.blade.phpadmin::partials.toastr", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Ftoastr.blade.php&line=1", "ajax": false, "filename": "toastr.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.toastr"}, {"name": "1x admin::layouts.page", "param_count": null, "params": [], "start": **********.640386, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/layouts/page.blade.phpadmin::layouts.page", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Flayouts%2Fpage.blade.php&line=1", "ajax": false, "filename": "page.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::layouts.page"}, {"name": "1x admin::layouts.container", "param_count": null, "params": [], "start": **********.64393, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\resources\\views/vendor/admin/layouts/container.blade.phpadmin::layouts.container", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fresources%2Fviews%2Fvendor%2Fadmin%2Flayouts%2Fcontainer.blade.php&line=1", "ajax": false, "filename": "container.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::layouts.container"}, {"name": "1x admin::partials.sidebar", "param_count": null, "params": [], "start": **********.646026, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/sidebar.blade.phpadmin::partials.sidebar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.sidebar"}, {"name": "65x admin::partials.menu", "param_count": null, "params": [], "start": **********.892198, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/menu.blade.phpadmin::partials.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 65, "name_original": "admin::partials.menu"}, {"name": "1x admin::partials.navbar", "param_count": null, "params": [], "start": **********.977061, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/navbar.blade.phpadmin::partials.navbar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fnavbar.blade.php&line=1", "ajax": false, "filename": "navbar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.navbar"}, {"name": "1x announcements.index", "param_count": null, "params": [], "start": **********.978289, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\resources\\views/announcements/index.blade.phpannouncements.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fresources%2Fviews%2Fannouncements%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "announcements.index"}, {"name": "1x admin::partials.navbar-user-panel", "param_count": null, "params": [], "start": **********.980332, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/navbar-user-panel.blade.phpadmin::partials.navbar-user-panel", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fnavbar-user-panel.blade.php&line=1", "ajax": false, "filename": "navbar-user-panel.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.navbar-user-panel"}]}, "queries": {"count": 41, "nb_statements": 40, "nb_visible_statements": 41, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 2.2962000000000002, "accumulated_duration_str": "2.3s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Admin.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", "line": 145}, {"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 36}], "start": **********.926105, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "wind_rich", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `wr_admin_users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Admin.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", "line": 145}, {"index": 18, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.9402049, "duration": 0.27954, "duration_str": "280ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wind_rich", "explain": null, "start_percent": 0, "width_percent": 12.174}, {"sql": "insert into `wr_operation_logs` (`admin_uid`, `user_name`, `path`, `method`, `ip`, `input`, `updated_at`, `created_at`) values (1, 'ZtFzSuperAdminPro', 'ztfz/posting_link', 'GET', '127.0.0.1', '[]', '2025-08-01 14:15:04', '2025-08-01 14:15:04')", "type": "query", "params": [], "bindings": [1, "ZtFzSuperAdminPro", "ztfz/posting_link", "GET", "127.0.0.1", "[]", "2025-08-01 14:15:04", "2025-08-01 14:15:04"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 51}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.228374, "duration": 0.05797, "duration_str": "57.97ms", "memory": 0, "memory_str": null, "filename": "OpRecord.php:51", "source": {"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FHttp%2FMiddleware%2FOpRecord.php&line=51", "ajax": false, "filename": "OpRecord.php", "line": "51"}, "connection": "wind_rich", "explain": null, "start_percent": 12.174, "width_percent": 2.525}, {"sql": "select * from `wr_announcements` where `wr_announcements`.`deleted_at` is null order by `id` desc limit 7", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Announcement.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\Announcement.php", "line": 24}, {"index": 15, "namespace": null, "name": "app/Admin/bootstrap.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\bootstrap.php", "line": 62}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Admin.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", "line": 164}, {"index": 17, "namespace": null, "name": "app/Admin/bootstrap.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\bootstrap.php", "line": 80}, {"index": 19, "namespace": "middleware", "name": "admin.bootstrap", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\Bootstrap.php", "line": 14}], "start": **********.29868, "duration": 0.05163, "duration_str": "51.63ms", "memory": 0, "memory_str": null, "filename": "Announcement.php:24", "source": {"index": 14, "namespace": null, "name": "app/Models/Announcement.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\Announcement.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAnnouncement.php&line=24", "ajax": false, "filename": "Announcement.php", "line": "24"}, "connection": "wind_rich", "explain": null, "start_percent": 14.699, "width_percent": 2.248}, {"sql": "select `wr_admin_roles`.*, `wr_admin_role_users`.`user_id` as `pivot_user_id`, `wr_admin_role_users`.`role_id` as `pivot_role_id`, `wr_admin_role_users`.`created_at` as `pivot_created_at`, `wr_admin_role_users`.`updated_at` as `pivot_updated_at` from `wr_admin_roles` inner join `wr_admin_role_users` on `wr_admin_roles`.`id` = `wr_admin_role_users`.`role_id` where `wr_admin_role_users`.`user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/HasPermissions.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\HasPermissions.php", "line": 88}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/HasPermissions.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\HasPermissions.php", "line": 76}, {"index": 21, "namespace": "middleware", "name": "admin.permission", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\Permission.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 23, "namespace": "middleware", "name": "admin.bootstrap", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\Bootstrap.php", "line": 19}], "start": **********.478407, "duration": 0.05111, "duration_str": "51.11ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:88", "source": {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/HasPermissions.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\HasPermissions.php", "line": 88}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FHasPermissions.php&line=88", "ajax": false, "filename": "HasPermissions.php", "line": "88"}, "connection": "wind_rich", "explain": null, "start_percent": 16.947, "width_percent": 2.226}, {"sql": "select * from `wr_ww_link` where `wr_ww_link`.`deleted_at` is not null order by `deleted_at` desc limit 20", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Admin/Traits/RecycleBinTrait.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Traits\\RecycleBinTrait.php", "line": 63}, {"index": 15, "namespace": null, "name": "app/Admin/Traits/RecycleBinTrait.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Traits\\RecycleBinTrait.php", "line": 40}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 118}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasTools.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasTools.php", "line": 53}, {"index": 18, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 119}], "start": **********.589199, "duration": 0.05066, "duration_str": "50.66ms", "memory": 0, "memory_str": null, "filename": "RecycleBinTrait.php:63", "source": {"index": 14, "namespace": null, "name": "app/Admin/Traits/RecycleBinTrait.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Traits\\RecycleBinTrait.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FTraits%2FRecycleBinTrait.php&line=63", "ajax": false, "filename": "RecycleBinTrait.php", "line": "63"}, "connection": "wind_rich", "explain": null, "start_percent": 19.173, "width_percent": 2.206}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Admin/Traits/RecycleBinTrait.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Traits\\RecycleBinTrait.php", "line": 167}, {"index": 22, "namespace": null, "name": "app/Admin/Traits/RecycleBinTrait.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Traits\\RecycleBinTrait.php", "line": 111}, {"index": 23, "namespace": null, "name": "app/Admin/Traits/RecycleBinTrait.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Traits\\RecycleBinTrait.php", "line": 40}, {"index": 24, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 118}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasTools.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasTools.php", "line": 53}], "start": **********.650173, "duration": 0.05096, "duration_str": "50.96ms", "memory": 0, "memory_str": null, "filename": "RecycleBinTrait.php:167", "source": {"index": 21, "namespace": null, "name": "app/Admin/Traits/RecycleBinTrait.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Traits\\RecycleBinTrait.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FTraits%2FRecycleBinTrait.php&line=167", "ajax": false, "filename": "RecycleBinTrait.php", "line": "167"}, "connection": "wind_rich", "explain": null, "start_percent": 21.379, "width_percent": 2.219}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Admin/Traits/RecycleBinTrait.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Traits\\RecycleBinTrait.php", "line": 167}, {"index": 22, "namespace": null, "name": "app/Admin/Traits/RecycleBinTrait.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Traits\\RecycleBinTrait.php", "line": 111}, {"index": 23, "namespace": null, "name": "app/Admin/Traits/RecycleBinTrait.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Traits\\RecycleBinTrait.php", "line": 40}, {"index": 24, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 118}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasTools.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasTools.php", "line": 53}], "start": **********.7043362, "duration": 0.0512, "duration_str": "51.2ms", "memory": 0, "memory_str": null, "filename": "RecycleBinTrait.php:167", "source": {"index": 21, "namespace": null, "name": "app/Admin/Traits/RecycleBinTrait.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Traits\\RecycleBinTrait.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FTraits%2FRecycleBinTrait.php&line=167", "ajax": false, "filename": "RecycleBinTrait.php", "line": "167"}, "connection": "wind_rich", "explain": null, "start_percent": 23.599, "width_percent": 2.23}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Admin/Traits/RecycleBinTrait.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Traits\\RecycleBinTrait.php", "line": 167}, {"index": 22, "namespace": null, "name": "app/Admin/Traits/RecycleBinTrait.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Traits\\RecycleBinTrait.php", "line": 111}, {"index": 23, "namespace": null, "name": "app/Admin/Traits/RecycleBinTrait.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Traits\\RecycleBinTrait.php", "line": 40}, {"index": 24, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 118}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasTools.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasTools.php", "line": 53}], "start": **********.759927, "duration": 0.05061, "duration_str": "50.61ms", "memory": 0, "memory_str": null, "filename": "RecycleBinTrait.php:167", "source": {"index": 21, "namespace": null, "name": "app/Admin/Traits/RecycleBinTrait.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Traits\\RecycleBinTrait.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FTraits%2FRecycleBinTrait.php&line=167", "ajax": false, "filename": "RecycleBinTrait.php", "line": "167"}, "connection": "wind_rich", "explain": null, "start_percent": 25.828, "width_percent": 2.204}, {"sql": "select `id`, `parent_id` from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 52}, {"index": 17, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 285}, {"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 329}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.841619, "duration": 0.05073, "duration_str": "50.73ms", "memory": 0, "memory_str": null, "filename": "AdminSubUser.php:52", "source": {"index": 16, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUser.php&line=52", "ajax": false, "filename": "AdminSubUser.php", "line": "52"}, "connection": "wind_rich", "explain": null, "start_percent": 28.032, "width_percent": 2.209}, {"sql": "select `id` from `wr_admin_users` where `parent_id` = 1 and `status` = 1 and `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 57}, {"index": 14, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 285}, {"index": 17, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 329}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.896766, "duration": 0.05035, "duration_str": "50.35ms", "memory": 0, "memory_str": null, "filename": "AdminSubUser.php:57", "source": {"index": 13, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUser.php&line=57", "ajax": false, "filename": "AdminSubUser.php", "line": "57"}, "connection": "wind_rich", "explain": null, "start_percent": 30.242, "width_percent": 2.193}, {"sql": "select `username`, `id` from `wr_admin_users` where `id` in (1) and `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 286}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 329}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 1008}], "start": **********.950272, "duration": 0.05033, "duration_str": "50.33ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:286", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 286}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=286", "ajax": false, "filename": "WwLinkController.php", "line": "286"}, "connection": "wind_rich", "explain": null, "start_percent": 32.434, "width_percent": 2.192}, {"sql": "select `id`, `parent_id` from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 300}, {"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 329}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.010138, "duration": 0.05056, "duration_str": "50.56ms", "memory": 0, "memory_str": null, "filename": "AdminSubUser.php:35", "source": {"index": 16, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUser.php&line=35", "ajax": false, "filename": "AdminSubUser.php", "line": "35"}, "connection": "wind_rich", "explain": null, "start_percent": 34.626, "width_percent": 2.202}, {"sql": "select `id` from `wr_admin_users` where `parent_id` = 1 and `status` = 1 and `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 39}, {"index": 14, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 300}, {"index": 17, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 329}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.064543, "duration": 0.05027, "duration_str": "50.27ms", "memory": 0, "memory_str": null, "filename": "AdminSubUser.php:39", "source": {"index": 13, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUser.php&line=39", "ajax": false, "filename": "AdminSubUser.php", "line": "39"}, "connection": "wind_rich", "explain": null, "start_percent": 36.828, "width_percent": 2.189}, {"sql": "select `ww_user_group_ids` from `wr_admin_sub_users` where `admin_uid` = 1 and `wr_admin_sub_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 302}, {"index": 19, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 329}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 1008}], "start": **********.118749, "duration": 0.05095, "duration_str": "50.95ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:302", "source": {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 302}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=302", "ajax": false, "filename": "WwLinkController.php", "line": "302"}, "connection": "wind_rich", "explain": null, "start_percent": 39.018, "width_percent": 2.219}, {"sql": "select `id`, `parent_id` from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 321}, {"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 329}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.172381, "duration": 0.05057, "duration_str": "50.57ms", "memory": 0, "memory_str": null, "filename": "AdminSubUser.php:35", "source": {"index": 16, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUser.php&line=35", "ajax": false, "filename": "AdminSubUser.php", "line": "35"}, "connection": "wind_rich", "explain": null, "start_percent": 41.236, "width_percent": 2.202}, {"sql": "select `id` from `wr_admin_users` where `parent_id` = 1 and `status` = 1 and `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 39}, {"index": 14, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 321}, {"index": 17, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 329}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.227186, "duration": 0.0506, "duration_str": "50.6ms", "memory": 0, "memory_str": null, "filename": "AdminSubUser.php:39", "source": {"index": 13, "namespace": null, "name": "app/Models/AdminSubUser.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUser.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUser.php&line=39", "ajax": false, "filename": "AdminSubUser.php", "line": "39"}, "connection": "wind_rich", "explain": null, "start_percent": 43.439, "width_percent": 2.204}, {"sql": "select `title`, `id` from `wr_ww_users_groups` where `admin_uid` in (1) and `type` = 1 and `admin_uid` in (1) and `wr_ww_users_groups`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 323}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 329}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 1008}], "start": **********.2818081, "duration": 0.05593, "duration_str": "55.93ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:323", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 323}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=323", "ajax": false, "filename": "WwLinkController.php", "line": "323"}, "connection": "wind_rich", "explain": null, "start_percent": 45.642, "width_percent": 2.436}, {"sql": "select count(*) as aggregate from `wr_ww_link` where `wr_ww_link`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.342169, "duration": 0.05094, "duration_str": "50.94ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 48.078, "width_percent": 2.218}, {"sql": "select * from `wr_ww_link` where `wr_ww_link`.`deleted_at` is null order by `id` desc limit 20 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.397497, "duration": 0.05165, "duration_str": "51.65ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 50.297, "width_percent": 2.249}, {"sql": "select * from `wr_ad_account` where `wr_ad_account`.`account_id` in ('********', '********', '********', '********', '********') and `wr_ad_account`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["********", "********", "********", "********", "********"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.455976, "duration": 0.05121, "duration_str": "51.21ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 52.546, "width_percent": 2.23}, {"sql": "select * from `wr_ww_tpl` where `wr_ww_tpl`.`id` in (1, 3) and `wr_ww_tpl`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.514953, "duration": 0.05082, "duration_str": "50.82ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 54.776, "width_percent": 2.213}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` in (1, 4) and `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.569892, "duration": 0.05029, "duration_str": "50.29ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 56.989, "width_percent": 2.19}, {"sql": "select * from `wr_ww_users_groups` where `wr_ww_users_groups`.`id` in (1, 6) and `wr_ww_users_groups`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.625411, "duration": 0.052090000000000004, "duration_str": "52.09ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 59.18, "width_percent": 2.269}, {"sql": "select * from `wr_shield_policies` where `wr_shield_policies`.`id` in (1, 2) and `wr_shield_policies`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.684517, "duration": 0.05107, "duration_str": "51.07ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 61.448, "width_percent": 2.224}, {"sql": "select * from `wr_ww_tpl` where `wr_ww_tpl`.`id` in (2, 4) and `wr_ww_tpl`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 28, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 29, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 31, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 32, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.739849, "duration": 0.05055, "duration_str": "50.55ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 28, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 63.672, "width_percent": 2.201}, {"sql": "select * from `wr_admin_domains` where `status` = 1 and `admin_uid` = 1 and `wr_admin_domains`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 155}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}], "start": **********.816606, "duration": 0.05113, "duration_str": "51.13ms", "memory": 0, "memory_str": null, "filename": "AdminDomain.php:56", "source": {"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminDomain.php&line=56", "ajax": false, "filename": "AdminDomain.php", "line": "56"}, "connection": "wind_rich", "explain": null, "start_percent": 65.874, "width_percent": 2.227}, {"sql": "select * from `wr_admin_domains` where `status` = 1 and `admin_uid` = 1 and `wr_admin_domains`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 155}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}], "start": **********.8725512, "duration": 0.05066, "duration_str": "50.66ms", "memory": 0, "memory_str": null, "filename": "AdminDomain.php:56", "source": {"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminDomain.php&line=56", "ajax": false, "filename": "AdminDomain.php", "line": "56"}, "connection": "wind_rich", "explain": null, "start_percent": 68.1, "width_percent": 2.206}, {"sql": "select * from `wr_admin_domains` where `status` = 1 and `admin_uid` = 1 and `wr_admin_domains`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 155}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}], "start": **********.928785, "duration": 0.05145, "duration_str": "51.45ms", "memory": 0, "memory_str": null, "filename": "AdminDomain.php:56", "source": {"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminDomain.php&line=56", "ajax": false, "filename": "AdminDomain.php", "line": "56"}, "connection": "wind_rich", "explain": null, "start_percent": 70.307, "width_percent": 2.241}, {"sql": "select * from `wr_admin_domains` where `status` = 1 and `admin_uid` = 1 and `wr_admin_domains`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 155}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}], "start": **********.9845932, "duration": 0.05278, "duration_str": "52.78ms", "memory": 0, "memory_str": null, "filename": "AdminDomain.php:56", "source": {"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminDomain.php&line=56", "ajax": false, "filename": "AdminDomain.php", "line": "56"}, "connection": "wind_rich", "explain": null, "start_percent": 72.547, "width_percent": 2.299}, {"sql": "select * from `wr_admin_domains` where `status` = 1 and `admin_uid` = 1 and `wr_admin_domains`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 155}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}], "start": **********.040449, "duration": 0.04992, "duration_str": "49.92ms", "memory": 0, "memory_str": null, "filename": "AdminDomain.php:56", "source": {"index": 15, "namespace": null, "name": "app/Models/AdminDomain.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminDomain.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminDomain.php&line=56", "ajax": false, "filename": "AdminDomain.php", "line": "56"}, "connection": "wind_rich", "explain": null, "start_percent": 74.846, "width_percent": 2.174}, {"sql": "select * from `wr_ww_users_groups` where `wr_ww_users_groups`.`id` = 7 and `wr_ww_users_groups`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 182}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 28, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 29, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": **********.110837, "duration": 0.05102, "duration_str": "51.02ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:182", "source": {"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 182}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=182", "ajax": false, "filename": "WwLinkController.php", "line": "182"}, "connection": "wind_rich", "explain": null, "start_percent": 77.02, "width_percent": 2.222}, {"sql": "select `merge_name`, `id` from `wr_area` where 0 = 1 and `wr_area`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 190}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": **********.166441, "duration": 0.05801, "duration_str": "58.01ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:190", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=190", "ajax": false, "filename": "WwLinkController.php", "line": "190"}, "connection": "wind_rich", "explain": null, "start_percent": 79.242, "width_percent": 2.526}, {"sql": "select * from `wr_ww_users_groups` where `wr_ww_users_groups`.`id` = 2 and `wr_ww_users_groups`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 182}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 28, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 29, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": **********.230423, "duration": 0.050320000000000004, "duration_str": "50.32ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:182", "source": {"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 182}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=182", "ajax": false, "filename": "WwLinkController.php", "line": "182"}, "connection": "wind_rich", "explain": null, "start_percent": 81.768, "width_percent": 2.191}, {"sql": "select `merge_name`, `id` from `wr_area` where `id` in ('1', '192', '2648', '3642') and `wr_area`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "192", "2648", "3642"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 190}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": **********.283483, "duration": 0.05088, "duration_str": "50.88ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:190", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=190", "ajax": false, "filename": "WwLinkController.php", "line": "190"}, "connection": "wind_rich", "explain": null, "start_percent": 83.96, "width_percent": 2.216}, {"sql": "select `merge_name`, `id` from `wr_area` where `id` in ('1', '192', '2648', '3642') and `wr_area`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "192", "2648", "3642"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 190}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": **********.339049, "duration": 0.05442, "duration_str": "54.42ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:190", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=190", "ajax": false, "filename": "WwLinkController.php", "line": "190"}, "connection": "wind_rich", "explain": null, "start_percent": 86.175, "width_percent": 2.37}, {"sql": "select `merge_name`, `id` from `wr_area` where `id` in ('1', '192', '2648', '3642') and `wr_area`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "192", "2648", "3642"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 190}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": **********.397441, "duration": 0.05023, "duration_str": "50.23ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:190", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=190", "ajax": false, "filename": "WwLinkController.php", "line": "190"}, "connection": "wind_rich", "explain": null, "start_percent": 88.545, "width_percent": 2.188}, {"sql": "select `merge_name`, `id` from `wr_area` where `id` in ('1', '192', '2648', '3642') and `wr_area`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "192", "2648", "3642"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 190}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 513}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 571}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Column.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Column.php", "line": 581}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 495}], "start": **********.452121, "duration": 0.05052, "duration_str": "50.52ms", "memory": 0, "memory_str": null, "filename": "WwLinkController.php:190", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwLinkController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwLinkController.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=190", "ajax": false, "filename": "WwLinkController.php", "line": "190"}, "connection": "wind_rich", "explain": null, "start_percent": 90.733, "width_percent": 2.2}, {"sql": "select * from `wr_admin_menu` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 115}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/MenuCache.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\MenuCache.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 99}], "start": **********.64908, "duration": 0.052289999999999996, "duration_str": "52.29ms", "memory": 0, "memory_str": null, "filename": "ModelTree.php:123", "source": {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FModelTree.php&line=123", "ajax": false, "filename": "ModelTree.php", "line": "123"}, "connection": "wind_rich", "explain": null, "start_percent": 92.933, "width_percent": 2.277}, {"sql": "select `wr_admin_permissions`.*, `wr_admin_permission_menu`.`menu_id` as `pivot_menu_id`, `wr_admin_permission_menu`.`permission_id` as `pivot_permission_id`, `wr_admin_permission_menu`.`created_at` as `pivot_created_at`, `wr_admin_permission_menu`.`updated_at` as `pivot_updated_at` from `wr_admin_permissions` inner join `wr_admin_permission_menu` on `wr_admin_permissions`.`id` = `wr_admin_permission_menu`.`permission_id` where `wr_admin_permission_menu`.`menu_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 115}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 98}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/MenuCache.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\MenuCache.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 99}], "start": **********.709232, "duration": 0.0518, "duration_str": "51.8ms", "memory": 0, "memory_str": null, "filename": "ModelTree.php:123", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FModelTree.php&line=123", "ajax": false, "filename": "ModelTree.php", "line": "123"}, "connection": "wind_rich", "explain": null, "start_percent": 95.21, "width_percent": 2.256}, {"sql": "select `wr_admin_roles`.*, `wr_admin_role_menu`.`menu_id` as `pivot_menu_id`, `wr_admin_role_menu`.`role_id` as `pivot_role_id`, `wr_admin_role_menu`.`created_at` as `pivot_created_at`, `wr_admin_role_menu`.`updated_at` as `pivot_updated_at` from `wr_admin_roles` inner join `wr_admin_role_menu` on `wr_admin_roles`.`id` = `wr_admin_role_menu`.`role_id` where `wr_admin_role_menu`.`menu_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 115}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 98}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/MenuCache.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\MenuCache.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 99}], "start": **********.769909, "duration": 0.05818, "duration_str": "58.18ms", "memory": 0, "memory_str": null, "filename": "ModelTree.php:123", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FModelTree.php&line=123", "ajax": false, "filename": "ModelTree.php", "line": "123"}, "connection": "wind_rich", "explain": null, "start_percent": 97.466, "width_percent": 2.534}]}, "models": {"data": {"Dcat\\Admin\\Models\\Role": {"value": 156, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Dcat\\Admin\\Models\\Menu": {"value": 61, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FMenu.php&line=1", "ajax": false, "filename": "Menu.php", "line": "?"}}, "Dcat\\Admin\\Models\\Permission": {"value": 56, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\WwLink": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwLink.php&line=1", "ajax": false, "filename": "WwLink.php", "line": "?"}}, "App\\Models\\AdminUser": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminUser.php&line=1", "ajax": false, "filename": "AdminUser.php", "line": "?"}}, "App\\Models\\TencentAdAccount": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FTencentAdAccount.php&line=1", "ajax": false, "filename": "TencentAdAccount.php", "line": "?"}}, "App\\Models\\AdminDomain": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminDomain.php&line=1", "ajax": false, "filename": "AdminDomain.php", "line": "?"}}, "App\\Models\\WwTpl": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwTpl.php&line=1", "ajax": false, "filename": "WwTpl.php", "line": "?"}}, "App\\Models\\WwUsersGroup": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwUsersGroup.php&line=1", "ajax": false, "filename": "WwUsersGroup.php", "line": "?"}}, "App\\Models\\ShieldPolicy": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FShieldPolicy.php&line=1", "ajax": false, "filename": "ShieldPolicy.php", "line": "?"}}, "Dcat\\Admin\\Models\\Administrator": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FAdministrator.php&line=1", "ajax": false, "filename": "Administrator.php", "line": "?"}}, "App\\Models\\Announcement": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAnnouncement.php&line=1", "ajax": false, "filename": "Announcement.php", "line": "?"}}}, "count": 311, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://sen.test/ztfz/posting_link", "action_name": "dcat.admin.posting_link.index", "controller_action": "App\\Admin\\Controllers\\WwLinkController@index", "uri": "GET ztfz/posting_link", "controller": "App\\Admin\\Controllers\\WwLinkController@index<a href=\"phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=85\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Admin\\Controllers", "prefix": "/ztfz", "file": "<a href=\"phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwLinkController.php&line=85\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Admin/Controllers/WwLinkController.php:85-101</a>", "middleware": "admin.app:admin, web, admin", "duration": "5.42s", "peak_memory": "8MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1556777141 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1556777141\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-890778412 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-890778412\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-323707491 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InpRNUpnQjhDaGFLSGZiNjVTekFKa2c9PSIsInZhbHVlIjoiNWxhYWxORkV3NzRCQU5PR2YwTnQ3eStXSCtGbnpXeGE4aSs4eE1MT05ZZzZvNnRTT1hYUnpiTnlyRHFDcGtUTGdJeVV0WDc2NGtvaVQ1QVZpUWdtSmNqWmxrb2VYT0NVeFZaa2l3K2RudmhoL0wveWluUmZYdEhpSUV4cGMzdm8iLCJtYWMiOiJjZGRmNmFlMWVlNjNlZDUzZjZiNmEyNmUwNjI1YmU3ZDZhN2MyZmMzYjIzZTM4ZmQyYjJmMDlhNTI5YTFhN2E3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InV0anNOM1ZGcE5udXJOVEZNWGtpYlE9PSIsInZhbHVlIjoiaDNNV1R2cXF2T3JOblJVYmtpSFhma3FpcDdXVk5pUU04ZEJCTktnd2hJaDR1NjVyM0ZqbUttOVFqdksxVEdhaVRNQURMS3piNURybHZvL3VFRlpSYlE5Sit5a0tqbEI0UUVlWUZDa2hOSm1WRXJOaTNxelRaWmwrbi94c3huRW4iLCJtYWMiOiJjZGE1ODhmZTFkNGE4MzM4MmJjNmQ2OTg1ZTBhZTlkZDA0MDg2NGU3NTU2YzAxMjM4ZGE4Zjc0Mzc1ODJlNjZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">zh-CN,zh;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">sen.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-323707491\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1041995319 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0esJyfQEGYXbNzItiQn8t75dzSvXZmiKqhcwIHBs</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Sc1mCMq4OSwvqqSk8mbaIttmOBzjSrhvaXfSjoYm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1041995319\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1995134167 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Aug 2025 06:15:04 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1995134167\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1873600643 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>prev</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://sen.test/ztfz/posting_link</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">admin.prev.url</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0esJyfQEGYXbNzItiQn8t75dzSvXZmiKqhcwIHBs</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://sen.test/ztfz/posting_link</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1873600643\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://sen.test/ztfz/posting_link", "action_name": "dcat.admin.posting_link.index", "controller_action": "App\\Admin\\Controllers\\WwLinkController@index"}, "badge": null}}