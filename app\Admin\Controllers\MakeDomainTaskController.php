<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\MakeDomain;
use App\Models\AdminUser;
use App\Models\MakeDomain as MakeDomainModel;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class MakeDomainTaskController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new MakeDomain(), function (Grid $grid) {
            $grid->model()->orderBy('id', 'desc');
            $grid->column('id')->sortable();
            $grid->column('status')->using(MakeDomainModel::STATUS)->label([
                0 => 'default',
                1 => 'warning',
                2 => 'success',
                3 => 'warning',
                4 => 'danger',
            ]);
            $grid->column('count');
            $grid->column('length');
            $grid->column('oss_path')->downloadable();
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) use ($grid) {
                $filter->panel();
                $filter->expand();
                $filter->equal("status", '状态')->select(MakeDomainModel::STATUS)->width(2);
            });
            $grid->disableViewButton();
            $grid->disableBatchActions();
        });
    }


    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new MakeDomain(), function (Form $form) {
            $form->display('id');
            $form->hidden('id');
            $form->number('count')->min(1)->max(1000)->help('每次最多生成1000个')->required();
            $form->number('length')->min(1)->max(32)->required();
            $form->radio('status')->options(MakeDomainModel::STATUS)->default(0)->required();
            $form->disableViewButton();
            $form->disableViewCheck();
            $form->disableDeleteButton();
        })->confirm('确认提交吗？');
    }
}
