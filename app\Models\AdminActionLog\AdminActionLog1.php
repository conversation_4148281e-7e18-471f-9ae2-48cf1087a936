<?php

namespace App\Models\AdminActionLog;

use App\Models\AdminUser;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;


class AdminActionLog1 extends Model
{
    use HasDateTimeFormatter;

    protected $table = 'admin_action_logs_1';

    public function adminInfo(): BelongsTo
    {
        return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
    }
}
