<?php

namespace App\Console\Commands\Tads\Account;

use App\Models\TencentAdAccount;
use App\Models\AdAccountDynamicCreative;
use Illuminate\Console\Command;

class GetDynamicCreativesPreviews extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'GetDynamicCreativesPreviews';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        AdAccountDynamicCreative::query()
            ->select("creative_components", "id", "account_id", "system_industry_id","main_video","main_image_list","main_image")
            ->with("adAccountInfo:id,account_id,access_token")
            //->where('system_industry_id',"***********")
            //->where("id",102070)
            ->whereNull("main_image_list")
            ->whereNull("main_video")
            ->whereNull("main_image")
            ->whereNull("main_desc")
            ->chunkById(1000, function ($list) {
                foreach ($list as $item) {
                    $this->info($item->id);
                    $adAccounts[$item->account_id] = $item->adAccountInfo;
                    $data = json_decode($item->creative_components,true);
                    /**
                     * 视频
                     */
                    if(isset($data['video']) && is_null($item->main_video)){
                        $videoIds = [];
                        foreach($data['video'] as $videoItem){
                            $videoIds[] = $videoItem['value']['video_id'];
                        }
                        $resp = $this->videos_get($item->adAccountInfo,$videoIds);
                        $resp = json_decode($resp,true);
                        if(isset($resp['data']['list'])){
                            $item->main_video = json_encode($resp['data']['list'],JSON_UNESCAPED_UNICODE);
                            $item->save();
                        }
                    }
                    /**
                     * 图片
                     */
                    if(isset($data['image']) && is_null($item->main_image)){
                        $imageIds = [];
                        foreach($data['image'] as $imageItem){
                            $imageIds[] = $imageItem['value']['image_id'];
                        }
                        $resp = $this->images_get($item->adAccountInfo,$imageIds);
                        $resp = json_decode($resp,true);
                        if(isset($resp['data']['list'])){
                            $item->main_image = json_encode($resp['data']['list'],JSON_UNESCAPED_UNICODE);
                            $item->save();
                        }
                    }
                    if(isset($data['image_list']) && is_null($item->main_image_list)){
                        $imageIds = [];
                        foreach($data['image_list'] as $imageListItem){
                            foreach($imageListItem['value']['list'] as $listItem){
                                $imageIds[] = $listItem['image_id'];
                            }
                        }
                        $resp = $this->images_get($item->adAccountInfo,$imageIds);
                        $resp = json_decode($resp,true);
                        if(isset($resp['data']['list'])){
                            $item->main_image_list = json_encode($resp['data']['list'],JSON_UNESCAPED_UNICODE);
                            $item->save();
                        }
                    }
                    /**
                     * 文案
                     */
                    if(isset($data['description']) && is_null($item->main_desc)){
                        $content = [];
                        foreach($data['description'] as $descItem){
                            $content[] = $descItem['value']['content'];
                        }
                        $item->main_desc = json_encode($content,JSON_UNESCAPED_UNICODE);
                        $item->save();
                    }
                    //获取创意的预览链接
                    //$data = $this->creative_template_previews_get($item->adAccountInfo,$item->dynamic_creative_id);
                    //dd(json_decode($data,true));
                }
            });
        return Command::SUCCESS;
    }

    function videos_get(TencentAdAccount $adAccount, $videoIds)
    {
        $interface = 'videos/get';
        $url = 'https://api.e.qq.com/v3.0/' . $interface;

        $common_parameters = array (
            'access_token' => $adAccount->access_token,
            'timestamp' => time(),
            'nonce' => md5(uniqid('', true))
        );

        $parameters = array (
            'account_id' => $adAccount->account_id,
            'filtering' =>
                array (
                    0 =>
                        array (
                            'field' => 'media_id',
                            'operator' => 'IN',
                            'values' =>$videoIds
                        ),
                ),
            'page' => 1,
            'page_size' => 100,
        );

        $parameters = array_merge($common_parameters, $parameters);

        foreach ($parameters as $key => $value) {
            if (!is_string($value)) {
                $parameters[$key] = json_encode($value);
            }
        }

        $request_url = $url . '?' . http_build_query($parameters);

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $request_url);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 60);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $response = curl_exec($curl);
        if (curl_error($curl)) {
            $error_msg = curl_error($curl);
            $error_no = curl_errno($curl);
            curl_close($curl);
            throw new \Exception($error_msg, $error_no);
        }
        curl_close($curl);
        return $response;
    }

    function images_get(TencentAdAccount $adAccount, $imageIds)
    {
        $interface = 'images/get';
        $url = 'https://api.e.qq.com/v3.0/' . $interface;

        $common_parameters = array (
            'access_token' => $adAccount->access_token,
            'timestamp' => time(),
            'nonce' => md5(uniqid('', true))
        );

        $parameters = array (
            'account_id' => $adAccount->account_id,
            'filtering' =>
                array (
                    0 =>
                        array (
                            'field' => 'image_id',
                            'operator' => 'IN',
                            'values' =>$imageIds
                        ),
                ),
            'page' => 1,
            'page_size' => 10,
        );

        $parameters = array_merge($common_parameters, $parameters);

        foreach ($parameters as $key => $value) {
            if (!is_string($value)) {
                $parameters[$key] = json_encode($value);
            }
        }

        $request_url = $url . '?' . http_build_query($parameters);

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $request_url);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 60);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $response = curl_exec($curl);
        if (curl_error($curl)) {
            $error_msg = curl_error($curl);
            $error_no = curl_errno($curl);
            curl_close($curl);
            throw new \Exception($error_msg, $error_no);
        }
        curl_close($curl);
        return $response;
    }
}
