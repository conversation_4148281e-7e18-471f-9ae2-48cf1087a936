<?php

    namespace App\Models;

    use App\Http\Controllers\WorkWeixin3rdController;
    use App\Services\Tools\QrcodeService;
    use App\Services\WwSelfCorpApi;
    use App\Services\NotifySendService;
    use Dcat\Admin\Admin;
    use Dcat\Admin\Traits\HasDateTimeFormatter;
    use EasyWeChat\Kernel\Exceptions\BadResponseException;
    use EasyWeChat\Kernel\Exceptions\InvalidArgumentException;
    use Illuminate\Database\Eloquent\HigherOrderBuilderProxy;
    use Illuminate\Database\Eloquent\Model;
    use Illuminate\Database\Eloquent\SoftDeletes;
    use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
    use Symfony\Contracts\HttpClient\Exception\DecodingExceptionInterface;
    use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
    use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
    use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

    /**
     * @property mixed $id
     * @property mixed $aes_key
     * @property mixed $token
     * @property mixed $suite_secret
     * @property mixed $suite_id
     * @property mixed $provider_secret
     * @property mixed $corp_id
     * @property mixed $suite_name
     * @property mixed $md5_id
     */
    class WwAppList extends Model
    {
        use HasDateTimeFormatter;
        use SoftDeletes;

        protected $table = 'ww_app_list';

        public static function getMyApp($is_3rd = 1): ?WwAppList
        {
            $adminUid = Admin::user()->id;
            if (Admin::user()->parent_id) {
                $adminUid = Admin::user()->parent_id;
            }
            /** @var WwAppList $wwApp */
            $wwApp = self::query()->where("admin_uid", $adminUid)->where('is_3rd', $is_3rd)->first();
            if (!$wwApp) {
                $wwApp = self::query()->find(1);
            }
            return $wwApp ?? null;
        }

        public static function getMyAppQrcode()
        {
            /** @var AdminUser $adminUser */
            $adminUser = Admin::user();
            $adminUid = $adminUser->id;
            if ($adminUser->parent_id) {
                $adminUid = $adminUser->parent_id;
            }
            /** @var WwAppList $wwApp */
            $wwApp = self::query()->where("admin_uid", $adminUid)->where('is_3rd', 1)->first();
            if (!$wwApp) {
                $wwApp = self::query()->find(1);
            }
            /** @var WwAppAdminQrcode $appQrcode */
            $appQrcode = WwAppAdminQrcode::query()->where("ww_app_id", $wwApp->id)->where("admin_uid", $adminUser->id)->first();
            if ($appQrcode) {
                return $appQrcode->qrcode;
            } else {
                try {
                    $app = WorkWeixin3rdController::getApp('', $wwApp->md5_id);
                    $qrcodeRes = $app->getClient()->withAccessToken($app->getSuiteAccessToken())->postJson('cgi-bin/service/get_app_qrcode?debug=1', [
                        'suite_id' => $wwApp->suite_id,
                        'state' => $adminUser->id,
                        'style' => 2,
                        'result_type' => 2
                    ])->toArray();
                    if ($qrcodeRes['errcode'] == 0 && !empty($qrcodeRes['qrcode'])) {
                        $appQrcodeObj = new WwAppAdminQrcode();
                        $appQrcodeObj->ww_app_id = $wwApp->id;
                        $appQrcodeObj->admin_uid = $adminUser->id;
                        $appQrcodeObj->qrcode = $qrcodeRes['qrcode'];
                        $appQrcodeObj->save();
                        return $appQrcodeObj->qrcode;
                    } else {
                        return '';
                    }
                } catch (BadResponseException|InvalidArgumentException|TransportExceptionInterface|ServerExceptionInterface|RedirectionExceptionInterface|DecodingExceptionInterface|ClientExceptionInterface $e) {
                    $errMessage = makeErrorLog('[系统报错][API调用][get_app_qrcode]', ['admin_uid' => $adminUser->id, 'app_id' => $wwApp->id], $e);
                    NotifySendService::sendWorkWeixinForError("[系统报错][API调用][get_app_qrcode]" . $errMessage);
                    return '';
                }
            }
        }


        /**
         * 获取自建应用的二维码
         * @param int $is_3rd
         * @return false|HigherOrderBuilderProxy|mixed|void
         */
        public static function getSelfAppQrCode(int $is_3rd = 0)
        {
            /** @var AdminUser $adminUser */
            $adminUser = Admin::user();
            $adminUid = $adminUser->id;
            if ($adminUser->parent_id) {
                $adminUid = $adminUser->parent_id;
            }

            /** @var WwAppList $wwApp */
            $wwApp = self::query()->where("admin_uid", $adminUid)->where('is_3rd', $is_3rd)->first();
            if (!$wwApp) {
                return false;
            }
            /** @var WwAppAdminQrcode $appQrcode */
            $appQrcode = WwAppAdminQrcode::query()->where("ww_app_id", $wwApp->id)->where("admin_uid", $adminUser->id)->orderByDesc("id")->first();
            if ($appQrcode && $appQrcode->expires_at > date("Y-m-d H:i:s",time())) {
                return [
                    'qrcode'=>$appQrcode->qrcode,
                    'suite_name'=>$wwApp->suite_name
                ];
            } else {
                $appQrcode = WwSelfCorpApi::get_customized_auth_url($wwApp,$adminUser->id);
                if (empty($appQrcode['qrcode_url'])) {
                    return false;
                }
                $url = $appQrcode['qrcode_url'];
                $imgPath = QrcodeService::createQrcodeFile('self_app_auth_qrcode_' . $adminUser->id, $url);
                $appQrcodeObj = new WwAppAdminQrcode();
                $appQrcodeObj->ww_app_id = $wwApp->id;
                $appQrcodeObj->admin_uid = $adminUser->id;
                $appQrcodeObj->qrcode = $imgPath;
                $appQrcodeObj->expires_at = date("Y-m-d H:i:s", (time() + $appQrcode['expires_in'] - 86400));

                $appQrcodeObj->save();
                return [
                    'qrcode'=>$appQrcodeObj->qrcode,
                    'suite_name'=>$wwApp->suite_name
                ];
            }
        }
    }
