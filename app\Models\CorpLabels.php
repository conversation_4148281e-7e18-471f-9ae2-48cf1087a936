<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class CorpLabels extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'corp_labels';

    public function corpInfo(): BelongsTo
    {
        return $this->BelongsTo(WwCorpInfo::class, 'corp_id', 'id');
    }

    public function adminInfo(): BelongsTo
    {
        return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
    }

    /**
     * 根据corpAuthRecord 获取企微标签列表
     * @param $corpAuthRecord
     * @return mixed
     */
    public static function getCorpLabelsByCorpAuth($corpAuthRecord)
    {

        $corpLabelData = self::query()
            ->where('corp_id', $corpAuthRecord->corpInfo->id)
            ->select('ww_corp_label_id', 'group_name', 'ww_corp_label_name')
            ->get()
            ->mapWithKeys(function ($item) {
                return [
                    $item->ww_corp_label_id => "「{$item->group_name}」{$item->ww_corp_label_name}"
                ];
            })->all();
        return $corpLabelData ?? null;
    }
}
