<?php

	namespace App\Console\Commands\Tads\Dmp;

	use App\Models\TencentAdAccount;
	use App\Models\DmpCustomAudience;
	use Illuminate\Console\Command;
	use Illuminate\Support\Facades\Http;

	class GetAudiences extends Command
	{
		/**
		 * The name and signature of the console command.
		 *
		 * @var string
		 */
		protected $signature = 'DMP:GetAudiences';

		/**
		 * The console command description.
		 *
		 * @var string
		 */
		protected $description = 'Command description';

		/**
		 * Execute the console command.
		 *
		 * @return int
		 */
		public function handle()
		{
			$accountId = [********];
			$accounts  = TencentAdAccount::query()->whereIn("account_id", $accountId)->get();
			foreach ($accounts as $account) {
				$this->getList($account);
			}
			return Command::SUCCESS;
		}

		public function getList(TencentAdAccount $account, $page = 1): void
		{
			$account_id = $account->account_id;
			$interface  = 'custom_audiences/get';
			$url        = 'https://api.e.qq.com/v3.0/' . $interface;


			$common_parameters = [
				'access_token' => $account->access_token,
				'timestamp'    => time(),
				'nonce'        => md5(uniqid('', true)),
				'account_id'   => $account->account_id,
				'page'         => $page,
				'page_size'    => 100
			];

			$res = Http::get($url . "?" . http_build_query($common_parameters))->json();
			if ($res['code'] == 0) {
				foreach ($res['data']['list'] as $datum) {
					/** @var DmpCustomAudience $dmpCustomAudience */
					$dmpCustomAudience = DmpCustomAudience::query()->where("audience_id", $datum['audience_id'])->first();
					if (!$dmpCustomAudience) {
						$dmpCustomAudience                  = new DmpCustomAudience();
						$dmpCustomAudience->auto_push       = 0;
						$dmpCustomAudience->owner_admin_uid = 0;

					}
					$dmpCustomAudience->account_id         = $account_id;
					$dmpCustomAudience->audience_id        = $datum['audience_id'];
					$dmpCustomAudience->name               = $datum['name'];
					$dmpCustomAudience->outer_audience_id  = $datum['outer_audience_id'];
					$dmpCustomAudience->description        = $datum['description'];
					$dmpCustomAudience->cooperated         = (int)$datum['cooperated'];
					$dmpCustomAudience->type               = $datum['type'];
					$dmpCustomAudience->source             = $datum['source'];
					$dmpCustomAudience->status             = $datum['status'];
					$dmpCustomAudience->online_status      = $datum['online_status'] ?? "OFFLINE";
					$dmpCustomAudience->is_own             = (int)$datum['is_own'];
					$dmpCustomAudience->error_code         = $datum['error_code'];
					$dmpCustomAudience->user_count         = $datum['user_count'];
					$dmpCustomAudience->created_time       = $datum['created_time'];
					$dmpCustomAudience->last_modified_time = $datum['last_modified_time'];
					$dmpCustomAudience->audience_spec      = isset($datum['audience_spec']) ? json_encode($datum['audience_spec']) : "[]";
					$dmpCustomAudience->save();
				}
				if ($res['data']['page_info']['total_page'] > $page) {
					$page++;
					$this->getList($account, $page);
				}
			}
		}
	}
