<?php

    namespace App\Admin\Controllers;

    use Admin;
    use App\Admin\Forms\AdminActionLog\ActionLogTableSelectorForm;
    use App\Admin\Forms\AdminActionLog\QueryAdminUserActionLogTableForm;
    use App\Models\AdminActionLog;
    use App\Models\AdminSubUser;
    use App\Models\AdminUser;
    use Dcat\Admin\Form;
    use Dcat\Admin\Grid;
    use Dcat\Admin\Http\Controllers\AdminController;
    use Dcat\Admin\Http\JsonResponse;
    use Dcat\Admin\Layout\Content;
    use Dcat\Admin\Show;
    use Dcat\Admin\Widgets\Card;
    use Dcat\Admin\Widgets\Modal;
    use Dcat\Admin\Widgets\Tooltip;
    use Illuminate\Http\Response;

    class AdminActionLogController extends AdminController
    {
        /**
         * Index interface.
         *
         * @param Content $content
         *
         * @return Content
         */
        public function index(Content $content): Content
        {
            return $content
                ->title($this->title())
                ->description($this->description()['index'] ?? trans('admin.list'))
                ->body($this->tableSelector())
                ->body($this->grid());
        }

        /**
         * 记录表选择器
         */
        protected function tableSelector()
        {
            // 只有管理员才能查看表选择器
            if (!AdminUser::isSystemOp()) {
                return '';
            }
            return Card::make('操作日志表选择器', new ActionLogTableSelectorForm());
        }

        /**
         * Make a grid builder.
         *
         * @return Grid
         */
        protected function grid(): Grid
        {
            $adminUid = Admin::user()->id;
            $selectedTableId = request('table_id');

            // 非管理员不能查看其他表，强制使用自己的默认表
            if (!AdminUser::isSystemOp()) {
                $selectedTableId = null;
            }

            // 如果选择了特定表，则使用选择的表，否则使用用户默认分配的表
            if ($selectedTableId && $selectedTableId !== '') {
                $Repositories = AdminActionLog::getAdminUserActionLogTableRepositoriesById($selectedTableId);
            } else {
                $Repositories = AdminActionLog::getAdminUserActionLogTableRepositories($adminUid);
            }

            return Grid::make($Repositories, function (Grid $grid) use ($selectedTableId) {
                $grid->paginate(AdminUser::getPaginate());
                $grid->disableActions();
                $grid->disableBatchActions();
                $grid->disableCreateButton();

                // 如果选择了特定表，显示该表的所有数据，否则只显示当前用户相关数据
                if ($selectedTableId && $selectedTableId !== '' && AdminUser::isSystemOp()) {
                    $grid->model()->orderByDesc("id");
                } else {
                    $grid->model()->whereIn("admin_uid", AdminSubUser::getAdminUids(Admin::user()->id))->orderByDesc("id");
                }
                if (AdminUser::isSystemOp()) {
                    $grid->tools(function (Grid\Tools $tools) {
                        $modal = Modal::make()
                            ->lg()
                            ->title('查询用户所在表')
                            ->body(QueryAdminUserActionLogTableForm::make())
                            ->button('<button class="btn btn-primary" ><i class="feather icon-aperture"></i>&nbsp&nbsp查询用户所在表</button>');
                        $tools->append($modal);
                    });
                }

                $grid->column('id')->sortable();
                $grid->column('adminInfo.username', '用户');
                $grid->column('rel_id');
                $grid->column('action_desc');
                $grid->column('message')
                    ->display('详情') // 设置按钮名称
                    ->expand(function () {
                        // 返回显示的详情
                        // 这里返回 content 字段内容，并用 Card 包裹起来
                        $card = new Card(null, $this->message);

                        return "<div style='padding:10px 10px 0'>$card</div>";
                    });

                $grid->column("ip");
                $grid->column('updated_at')->sortable();

                $grid->filter(function (Grid\Filter $filter) use ($selectedTableId) {
                    $filter->expand();
                    $filter->panel();

                    // 如果选择了特定表且是管理员，可以查看所有用户，否则只能查看子账号
                    if ($selectedTableId && $selectedTableId !== '' && AdminUser::isSystemOp()) {
                        $filter->equal('admin_uid', "操作者")->select(AdminUser::pluck('username', 'id')->toArray())->width(2);
                    } else {
                        $filter->equal('admin_uid', "操作者")->select(AdminSubUser::getAdminUsers(Admin::user()->id))->width(2);
                    }

                    $filter->equal('ip')->width(2);
                    $filter->between("updated_at","操作时间")->datetime()->width(4);
                    $filter->like("message")->width(2);
                });
            });
        }

        /**
         * Make a show builder.
         *
         * @param mixed $id
         *
         * @return Show
         */
        protected function detail(mixed $id): Show
        {
            $adminUid = Admin::user()->id;
            $selectedTableId = request('table_id');

            // 非管理员不能查看其他表，强制使用自己的默认表
            if (!AdminUser::isSystemOp()) {
                $selectedTableId = null;
            }

            if ($selectedTableId && $selectedTableId !== '') {
                $Repositories = AdminActionLog::getAdminUserActionLogTableRepositoriesById($selectedTableId);
            } else {
                $Repositories = AdminActionLog::getAdminUserActionLogTableRepositories($adminUid);
            }

            return Show::make($id, $Repositories, function (Show $show) {
                if (!AdminUser::isAdmin($show->model())) {
                    $show->field('N')->value("无数据");
                } else {
                    $show->field('id');
                }
            });
        }

        /**
         * Make a form builder.
         *
         * @return Form
         */
        protected function form(): Form
        {
            $adminUid = Admin::user()->id;
            $selectedTableId = request('table_id');

            // 非管理员不能查看其他表，强制使用自己的默认表
            if (!AdminUser::isSystemOp()) {
                $selectedTableId = null;
            }

            if ($selectedTableId && $selectedTableId !== '') {
                $Repositories = AdminActionLog::getAdminUserActionLogTableRepositoriesById($selectedTableId);
            } else {
                $Repositories = AdminActionLog::getAdminUserActionLogTableRepositories($adminUid);
            }

            return Form::make(new $Repositories, function (Form $form) {
                if (!$form->isCreating() && !AdminUser::isAdmin($form->model())) {
                    $form->display('N')->value("无数据");
                } else {
                    $form->display('id');
                }
            });
        }

        public function destroy($id): Response|JsonResponse
        {
            return $this->form()->response()->error("无权限操作");
        }
    }
