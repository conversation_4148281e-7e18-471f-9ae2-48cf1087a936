<?php

	namespace App\Admin\Actions\Grid\wwUserAddRecord;

	use App\Jobs\AdminActionLogJob;
    use App\Models\AdminActionLog;
    use App\Models\AdminUser;
    use App\Models\WwUserAddRecord;
    use App\Services\Ocpx\OcpxSubmitService;
    use Dcat\Admin\Actions\Response;
    use Dcat\Admin\Admin;
    use Dcat\Admin\Grid\RowAction;
    use Dcat\Admin\Traits\HasPermissions;
    use Illuminate\Contracts\Auth\Authenticatable;
    use Illuminate\Database\Eloquent\Model;

    class OcpxUploadGridAction extends RowAction
	{
		/**
		 * @return string
		 */
		protected $title = '<i class="feather icon-upload-cloud" style="margin:0 10px 0 10px">上报</i>';

		/**
		 * Handle the action request.
		 *
         *
		 * @return Response
		 */
		public function handle(): Response
        {
			$id = $this->getKey();
			/** @var WwUserAddRecord $addRecord */
			$addRecord = WwUserAddRecord::query()->find($id);
			if (!$addRecord || !AdminUser::isAdmin($addRecord)) {
				return $this->response()->error('记录未找到，请刷新页面后尝试');
			}
			//所有的手动上报都按照添加成功来进行操作
			$ocpxObj = new OcpxSubmitService();
			$ocpxObj->up($addRecord, 'hand_action', 0);

            //记录操作日志
            //添加操作日志队列
            AdminActionLogJob::dispatch(
                'ocpx_upload',
                $id,
                AdminActionLog::ACTION_TYPE['加粉'],
                '手动上报「' . Admin::user()->username . '」，加粉明细ID：' . $id,
                getIp(),
                Admin::user()->id,
            )->onQueue('admin_action_log_job');
			return $this->response()->success('上报完成，请查看上报结果')->refresh();
		}

		/**
		 * @return array
         */
		public function confirm(): array
        {
			return ['确认手动上报?', '您确认手动上报吗？同行为重复上报会去重'];
		}

		/**
		 * @param Model|Authenticatable|HasPermissions|null $user
		 *
		 * @return bool
		 */
		protected function authorize($user): bool
		{
			return true;
		}

		/**
		 * @return array
		 */
		protected function parameters(): array
        {
			return [
				//	         'id' => $this->row->id
			];
		}
	}
