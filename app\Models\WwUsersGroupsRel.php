<?php

	namespace App\Models;

	use Dcat\Admin\Admin;
    use Dcat\Admin\Traits\HasDateTimeFormatter;
	use Illuminate\Database\Eloquent\Model;
	use Illuminate\Database\Eloquent\SoftDeletes;

	/**
	 * @property mixed $ww_group_id
	 * @property mixed $ww_user_id
	 */
	class WwUsersGroupsRel extends Model
	{
		use HasDateTimeFormatter;
		use SoftDeletes;

		protected $table = 'ww_users_groups_rel';


        /**
         * 关联销售分组
         * @param $wwUser   //销售
         * @param $groupIds //分组ids
         * @return true
         */
        public static function createWwGroupRel($wwUser,$groupIds):bool
        {
            $groupRelData = [];
            foreach ($groupIds as $groupId) {
                if (!self::query()->where("ww_user_id", $wwUser->id)->where("ww_group_id", $groupId)->exists()) {
                    $groupRelData[] = [
                        'admin_uid' => $wwUser->admin_uid,
                        'ww_user_id' => $wwUser->id,
                        'ww_group_id' => $groupId,
                        'created_at' => date('Y-m-d H:i:s', time()),
                        'updated_at' => date('Y-m-d H:i:s', time())
                    ];
                }
            }
            if (!empty($groupRelData)) {
                self::query()->insert($groupRelData);
            }
            return true;
        }



        /**
         * 根据分组ID获取分组下的销售ID
         * @param $groupId
         * @return array
         */
        public static function getWwUserIdsByGroupId($groupId):array
        {
           return self::query()->where("ww_group_id", $groupId)->pluck("ww_user_id")->toArray();
        }


        /**
         * 判断某个销售操作下线前，是否是所在分组的最后一个销售
         * @param $groupId
         * @return mixed
         */
        public static function getGroupOnlineWwUsers($wwUser)
        {
            //获取该销售所有分组ID
            $wwGroupIds = self::query()->where('ww_user_id', $wwUser->id)->pluck('ww_group_id')->toArray();
            if(empty($wwGroupIds)) {
                return [];
            }
            // 2. 批量查询这些分组中所有销售成员的ID
            $allMembers = self::query()
                ->whereIn('ww_group_id', $wwGroupIds)
                ->select('ww_group_id', 'ww_user_id')
                ->get()
                ->groupBy('ww_group_id');

            // 3. 批量查询所有相关销售的在线状态
            $wwUserIds = collect($allMembers)->flatten()->pluck('ww_user_id')->unique()->toArray();
            $onlineWwUsers = WwUser::query()
                ->whereIn('id', $wwUserIds)
                ->pluck('online_status', 'id')
                ->toArray();
            $emptyGroups = [];
            foreach ($wwGroupIds as $groupId) {
                $members = $allMembers[$groupId] ?? [];
                $otherActive = false;

                foreach ($members as $member) {
                    // 不是当前销售且状态为在线
                    if ($member->ww_user_id != $wwUser->id &&
                        ($onlineWwUsers[$member->ww_user_id] ?? 0) == 1) {
                        $otherActive = true;
                        break;
                    }
                }

                if (!$otherActive) {
                    $emptyGroups[] = $groupId;
                }
            }
            return $emptyGroups;
        }

    }
