<?php

namespace App\Admin\Forms\WwUser;

use App\Jobs\WwUser\ImportWwUserByUserIdJob;
use App\Models\AdminSubUser;
use App\Models\AdminSubUserAuthCorp;
use App\Models\AdminUser;
use App\Models\CorpLabels;
use App\Models\WwAppList;
use App\Models\WwUsersGroup;
use App\Models\WwUsersImportTask;
use App\Services\CacheService;
use App\Services\Corp\WwCorpApiService;
use App\Services\Tools\LogService;
use Dcat\Admin\Admin;
use Dcat\Admin\Widgets\Form;
use Dcat\EasyExcel\Excel;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ImportWwUserFromUserId extends Form
{
    public $importLimit = 20;
    protected $lockTime = 5;

    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        //如果是单个添加
        if($input['import_type'] == 0){
            if(empty($input['ww_user_list'])) {
                return $this->response()->alert()->info('提示')->error("请添加销售。");
            }
            $userId = array_keys($input['ww_user_list']);
            $userNames = $input['ww_user_list'];
        }


        //如果是excel导入
        if($input['import_type'] == 1){
            $excelFilePath = storage_path('app/public' . $input['ww_users_excel']);
            $excelData = Excel::import($excelFilePath)->toArray();
            if (empty($excelData) || empty($excelData['Sheet1'])) {
                return $this->response()->alert()->error('提示')->detail('Excel数据不能为空');
            }

            if(!array_key_exists('账号ID',$excelData['Sheet1'][2])){
                return $this->response()->alert()->error('提示')->detail('未读取到账号ID字段。');
            }
            if(!array_key_exists('销售姓名',$excelData['Sheet1'][2])){
                return $this->response()->alert()->error('提示')->detail('未读取到销售姓名字段。');
            }

            //处理excel数据，将销售账号ID做为key 姓名做为value
            $excelData = collect($excelData['Sheet1'])
                ->mapWithKeys(function ($item) {
                    return [$item['账号ID'] => $item['销售姓名']];
                })
                ->all();
            $userId =  array_keys($excelData);
            $userNames = $excelData;
        }

        //账号名称导入
        if($input['import_type'] == 2){
            $userId    = explode(PHP_EOL, $input['user_id_list']);
            $userNames = explode(PHP_EOL, $input['user_name_list']);
            $userId    = array_filter($userId, fn($v) => $v === 0 || $v === '0' || !empty($v));
            $userNames = array_filter($userNames, function ($value) {
                return !empty($value);
            });
            foreach ($userId as $key => $value) {
                $userId[$key] = trim($value);
                if (!isset($userNames[$key])) {
                    return $this->response()->withValidation([
                        'user_name_list' => '销售名称与销售ID个数不一致'
                    ])->error('销售名称与销售ID个数不一致');
                }
                $userNames[$userId[$key]] = trim($userNames[$key]);// 处理成ID与名称一一对应
            }
        }

        LogService::inputLog('Tools', '销售客服管理-导入销售【账号ID】', $input, Admin::user()->id, Admin::user()->username);


//        $lockAdminUids = AdminUser::getAllAdminUidsByAdminUid(Admin::user()->id);
//        $lockAdminUids = implode(',', $lockAdminUids);
//        $key = 'import_ww_user_lock_' . $lockAdminUids . '_' . $input['corp_auth_id'] . '_' . json_encode($userId);
//        $lock = CacheService::requestLock($key, $this->lockTime);
//        if (!$lock) {
//            return $this->response()->alert()->info('提示')->detail("正在执行导入同样的销售，请{$this->lockTime}秒后再操作。");
//        }

        $corp_auth_id = $input['corp_auth_id'];
        /** @var AdminSubUserAuthCorp $corpAuthRecord */
        $corpAuthRecord = AdminSubUserAuthCorp::query()->with("corpInfo")->find($corp_auth_id);
        if (!$corpAuthRecord || !$corpAuthRecord->corpInfo) {
            return $this->response()->withValidation([
                'corp_auth_id' => '请检查企业微信是否已授权'
            ])->error('请检查企业微信是否已授权');
        }
        if (empty($corpAuthRecord->corpInfo->customer_link_quota_update_time)) {
            return $this->response()->withValidation([
                'corp_auth_id' => '企业微信获客助手未同意，无法导入销售'
            ])->error('企业微信获客助手未同意，无法导入销售');
        }

        $userOpenIds = WwCorpApiService::userid_to_openuserid($corpAuthRecord->corpInfo, $userId);
        if (!isset($userOpenIds['open_userid_list'])) {
            return $this->response()->withValidation([
                'corp_auth_id' => '请检查企业微信是否已授权'
            ])->error('请检查企业微信是否已授权');
        }
        if (count($userOpenIds['open_userid_list']) < count($userId)) {
            $errorOpenUserId = array_diff($userId, array_column($userOpenIds['open_userid_list'], 'userid'));
            if (empty($errorOpenUserId)) {
                //说明有重复 ID
                return $this->response()
                    ->withValidation([
                        'user_id_list' => "包含重复 ID，请剔除后重试"
                    ])
                    ->error("存在相同的销售ID，请删除重试。");
            }
            return $this->response()
                ->withValidation([
                    'user_id_list' => "「" . implode(",", $errorOpenUserId) . "」" . '，请检查ID是否正确，或是否在可见范围内'
                ])
                ->error(implode(",", $errorOpenUserId) . '，请检查ID是否正确，或是否在可见范围内');
        }

        /** @var WwAppList $wwApp */
        $wwApp = WwAppList::query()->where("suite_id", $corpAuthRecord->corpInfo->suite_id)->first();// 查询的
        if (!$wwApp) {
            return $this->response()->alert()->info('提示')->detail('导入失败，企微应用不存在，请联系客服。');
        }

        /** 处理欢迎语配置开始 */
        if ($input['welcome_message_content']) {
            $res = [];
            $res['text'] = [
                'content' => $input['welcome_message_content'],
            ];
            // 根据附件类型决定存储哪种附件
            $attachmentType = array_filter($input['welcome_message_type'])[0];
            if ($attachmentType === 'image' && $input['welcome_message_image_url']) {
                $res['attachments'] = [
                    [
                        'msgtype' => 'image',
                        'image' => [
                            'pic_url' => $input['welcome_message_image_url'],
                        ]
                    ]
                ];
            }
            if ($attachmentType === 'link' && $input['welcome_message_link_url']) {
                $res['attachments'] = [
                    [
                        'msgtype' => 'link',
                        'link' => [
                            'title' => $input['welcome_message_link_title'] ?: '',
                            'url' => $input['welcome_message_link_url'],
                        ]
                    ]
                ];
            }
            unset($input['welcome_message_content']);
            unset($input['welcome_message_type']);
            unset($input['welcome_message_image_url']);
            unset($input['welcome_message_link_url']);
            unset($input['welcome_message_link_title']);
            $input['welcome_message'] = json_encode($res,JSON_UNESCAPED_UNICODE);
        }
        /** 处理欢迎语配置结束 */

        $importTask = new WwUsersImportTask();
        $importTask->admin_uid = Admin::user()->id;
        $importTask->corp_id = $corpAuthRecord->corpInfo->id;
        $importTask->total = sizeof($userId);
        $importTask->input = json_encode($input, JSON_UNESCAPED_UNICODE);
        $importTask->save();

        //队列处理
        foreach ($userOpenIds['open_userid_list'] as $key => $userIdInfo) {
            ImportWwUserByUserIdJob::dispatch($importTask, $userIdInfo, $userNames, $corpAuthRecord, $wwApp, Admin::user()->id, $input, getIp())->onQueue('import_ww_user');
        }
        return $this->response()->alert()->info('提示')->detail('本次添加销售：' . count($userId) . '个，请稍后刷新页面查看。')->refresh();

    }

    /**
     * Build a form here.
     */
    public function form()
    {

        $importType = [
//            0 => '单个导入',
            1 => 'excel批量导入',
            2 => '普通导入',
        ];
        $corpAuthRecord = AdminSubUserAuthCorp::getCorpAuthListBuyAdminUid(Admin::user()->id);
        $this->select("corp_auth_id", "企业微信")->options($corpAuthRecord)->required();
        $this->multipleSelect("ww_corp_label_ids", "企微标签")
            ->ajax("api/asyncGetWwCorpLabel") // 启用异步搜
            ->saveAsJson()
            ->help('如未查询到标签，请到企微管理同步。')
            ->placeholder('请输入标签名称搜索。');
        $this->select('import_type','添加销售方式')
            ->when([1],function (){
                $this->file('ww_users_excel','excel导入销售')
                    ->disk('public')
                    ->accept('xls,xlsx')
                    ->uniqueName()
                    ->autoUpload()
                    ->move('/import')
                    ->help('支持xls，xlsx &nbsp;<a href="https://smart-ark.oss-cn-beijing.aliyuncs.com/import_template/智投方舟-导入销售模板.xlsx">&nbsp;点击下载导入销售模板</a>');
            })
            ->when([0],function (){
                $this->keyValue('ww_user_list','销售名称')->setKeyLabel('销售ID')->setValueLabel('销售名称');
            })
            ->when([2],function (){
                $this->textarea("user_name_list", '销售名称')->placeholder("一行一个，例如\r\n销售1\r\n销售2")->help("每行一个，回车换行。")->required();
                $this->textarea("user_id_list", '销售账号ID')->placeholder("一行一个，例如\r\nabc1\r\nabc2")->help("每行一个，回车换行。")->required();
            })
            ->options($importType)->default(2)->required();

        $this->listbox('group_id', '销售分组')->options(WwUsersGroup::getAllGroupList(Admin::user()->id))->help("导入后，销售将归属于该分组.");

        $this->radio("online_status", '状态')->options([0 => '下线', 1 => '上线'])->default(0)->required();
        $this->radio('auto_status_config', '自动上下线')->options([
            0 => '不设置',
            1 => '按时间段',
            2 => '按加粉量',
        ])->when([1, 2], function ($form) {
            $this->time('up_time', '上线时间');
        })->when([1], function ($form) {
            $form->time('down_time', '下线时间')->help('如果选择按时间段自动上下线，且要设置下线时间，下线时间请大于上线时间。');
        })->when([2], function ($form) {
            $form->number('down_add_count', '下线加粉量')->help("当日加够数量大于等于改数量，将会自动下线该销售");
        })->default(0);
        $this->number('weight', '接粉权重')->help("可以配置1-10的数字，数字越大，该销售展示概率越高")->default(1)->min(0);
//        $this->switch("repeat_status", '重复导入')->default(0)->help('选择开启，即可重复导入销售。');
//        $this->switch("is_task", '使用队列导入')->default(0)->help('使用队列导入，则会后台执行，稍后在【导入任务】中查看。');
//        $this->switch("check_qrcode", '不验证二维码额度')->default(0)->help('若导入提示：该企业对外二维码创建额度已满，请联系客户或运营进行删除。可打开此开关跳过。');
        $this->text("wind_label", "智投管理标签")->help("该标签为智投后台批量管理销售客服使用的标签，可通过标签批量配置客服，不是企业微信后台的进粉标签");
        $this->fieldset('欢迎语',function (Form $form){
            $form->text('welcome_message_content','欢迎语文字内容');
            $form->checkbox('welcome_message_type', '欢迎语附件')->options([
                'link' => '链接卡片',
            ])->when(['image'], function () use ($form) {
                //  $form->image('welcome_message_image_url', '图片地址')->url('users/files?corp_id=' . $form->model()->corp_id);
                $form->text('welcome_message_image_url', '图片地址');

            })->when(['link'], function ()  use ($form) {
                $form->text('welcome_message_link_url', '链接地址');
                $form->text('welcome_message_link_title', '链接标题');
            });
        });

        // 添加JavaScript代码实现动态更新ajax URL
        Admin::script(<<<JS
            // 监听企业微信选择变化
            $('select[name="corp_auth_id"]').on('change', function() {
                var corpAuthId = $(this).val();
                var \$labelSelect = $('select[name="ww_corp_label_ids[]"]');

                if (corpAuthId) {
                    // 重新初始化select2并设置新的ajax URL
                    \$labelSelect.select2('destroy').select2({
                        ajax: {
                            url: 'api/asyncGetWwCorpLabel?corp_auth_id=' + corpAuthId,
                            dataType: 'json',
                            delay: 250,
                            data: function (params) {
                                return {
                                    q: params.term,
                                    page: params.page
                                };
                            },
                            processResults: function (data, params) {
                                params.page = params.page || 1;
                                return {
                                    results: data.data,
                                    pagination: {
                                        more: data.pagination && data.pagination.more
                                    }
                                };
                            }
                        },
                        multiple: true,
                        placeholder: '请选择...',
                        allowClear: true
                    });

                    // 清空当前选中的值
                    \$labelSelect.val(null).trigger('change');
                } else {
                    // 如果没有选择企业微信，则清空标签选择
                    \$labelSelect.val(null).trigger('change');
                }
            });
JS
        );
    }

    /**
     * The data of the form.
     *
     * @return array
     */
    public function default()
    {
        return [

        ];
    }
}

