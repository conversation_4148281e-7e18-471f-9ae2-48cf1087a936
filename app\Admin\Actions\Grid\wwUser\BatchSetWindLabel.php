<?php

namespace App\Admin\Actions\Grid\wwUser;

use App\Admin\Extensions\Grid\BatchActionPlus;
use App\Admin\Forms\WwUser\BatchSetWindLabelForm;

class BatchSetWindLabel extends BatchActionPlus
{

    public $title = '智投标签';
    /**
     * @return string
     */
    public $buttonText = '<button class="btn btn-primary ww_user_batch_btn"><i class="feather icon-tag"></i><span class="selected"></span>智投标签</button>';

    public function form(): BatchSetWindLabelForm
    {
        // 表单渲染时
        return BatchSetWindLabelForm::make();
    }
}
