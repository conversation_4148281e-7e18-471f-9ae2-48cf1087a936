<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

/**
 * @property mixed $geo_regions
 * @property mixed $gender
 * @property mixed $excluded_custom_audience
 * @property mixed $custom_audience
 * @property mixed $max_age
 * @property mixed $min_age
 * @property mixed $resp
 */
class AdBatchUpdateTargeting extends Model
{
	use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'ad_batch_update_targeting';

}
