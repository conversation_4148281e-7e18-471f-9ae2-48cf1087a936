{"__meta": {"id": "01K1J2W3TS33S9YQT6FVPDFC3Y", "datetime": "2025-08-01 13:49:56", "utime": **********.9547, "method": "GET", "uri": "/ztfz/sale", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[13:49:56] LOG.error: The class [Illuminate\\Database\\Eloquent\\Collection] must be a type of [Dcat\\Admin\\Contracts\\Repository]. {\n    \"exception\": {}\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.602938, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.562597, "end": **********.954716, "duration": 1.3921189308166504, "duration_str": "1.39s", "measures": [{"label": "Booting", "start": **********.562597, "relative_start": 0, "end": **********.867757, "relative_end": **********.867757, "duration": 0.****************, "duration_str": "305ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.867772, "relative_start": 0.****************, "end": **********.954717, "relative_end": 9.5367431640625e-07, "duration": 1.***************, "duration_str": "1.09s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.893364, "relative_start": 0.****************, "end": **********.900005, "relative_end": **********.900005, "duration": 0.0066411495208740234, "duration_str": "6.64ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "31MB"}, "exceptions": {"count": 1, "exceptions": [{"type": "Dcat\\Admin\\Exception\\InvalidArgumentException", "message": "The class [Illuminate\\Database\\Eloquent\\Collection] must be a type of [Dcat\\Admin\\Contracts\\Repository].", "code": 0, "file": "vendor/dcat/laravel-admin/src/Admin.php", "line": 251, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:85</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"44 characters\">vendor/dcat/laravel-admin/src/Grid/Model.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>138</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">repository</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Dcat\\Admin\\Admin</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"38 characters\">vendor/dcat/laravel-admin/src/Grid.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>202</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">__construct</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Dcat\\Admin\\Grid\\Model</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"38 characters\">vendor/dcat/laravel-admin/src/Grid.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>865</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">__construct</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Dcat\\Admin\\Grid</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"36 characters\">app/Admin/Traits/RecycleBinTrait.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>39</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">make</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Dcat\\Admin\\Grid</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"36 characters\">app/Admin/Traits/RecycleBinTrait.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>24</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getRecycleBinContent</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">App\\Admin\\Controllers\\WwUserController</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"42 characters\">app/Admin/Controllers/WwUserController.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>232</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">recycleBinTool</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">App\\Admin\\Controllers\\WwUserController</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"56 characters\">vendor/dcat/laravel-admin/src/Grid/Concerns/HasTools.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>53</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"31 characters\">App\\Admin\\Controllers\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">App\\Admin\\Controllers\\WwUserController</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"42 characters\">app/Admin/Controllers/WwUserController.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>235</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">tools</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Dcat\\Admin\\Grid</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"31 characters\">App\\Admin\\Controllers\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">App\\Admin\\Controllers\\WwUserController</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"38 characters\">vendor/dcat/laravel-admin/src/Grid.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>511</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">call_user_func</span>\"\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>38</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">callBuilder</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Dcat\\Admin\\Grid</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"38 characters\">vendor/dcat/laravel-admin/src/Grid.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>487</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">processFilter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Dcat\\Admin\\Grid</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"38 characters\">vendor/dcat/laravel-admin/src/Grid.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1008</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">build</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Dcat\\Admin\\Grid</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"38 characters\">vendor/dcat/laravel-admin/src/Grid.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1100</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">render</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Dcat\\Admin\\Grid</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"48 characters\">vendor/dcat/laravel-admin/src/Support/Helper.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>107</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__toString</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Dcat\\Admin\\Grid</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"48 characters\">vendor/dcat/laravel-admin/src/Widgets/Widget.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>171</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">render</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Dcat\\Admin\\Support\\Helper</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"45 characters\">vendor/dcat/laravel-admin/src/Widgets/Tab.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>45</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">toString</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Dcat\\Admin\\Widgets\\Widget</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"42 characters\">app/Admin/Controllers/WwUserController.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>90</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">add</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Dcat\\Admin\\Widgets\\Tab</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"31 characters\">App\\Admin\\Controllers\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">App\\Admin\\Controllers\\WwUserController</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"48 characters\">vendor/dcat/laravel-admin/src/Layout/Content.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>219</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">call_user_func</span>\"\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"42 characters\">app/Admin/Controllers/WwUserController.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>105</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">row</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Dcat\\Admin\\Layout\\Content</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"62 characters\">vendor/laravel/framework/src/Illuminate/Routing/Controller.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>54</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">App\\Admin\\Controllers\\WwUserController</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>43</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callAction</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Routing\\Controller</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>260</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Routing\\ControllerDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>205</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">runController</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>798</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">run</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>141</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/dcat/laravel-admin/src/Http/Middleware/WebUploader.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Dcat\\Admin\\Http\\Middleware\\WebUploader</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/dcat/laravel-admin/src/Http/Middleware/Session.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>12</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Dcat\\Admin\\Http\\Middleware\\Session</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"60 characters\">vendor/dcat/laravel-admin/src/Http/Middleware/Permission.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>39</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Dcat\\Admin\\Http\\Middleware\\Permission</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"59 characters\">vendor/dcat/laravel-admin/src/Http/Middleware/Bootstrap.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>19</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Dcat\\Admin\\Http\\Middleware\\Bootstrap</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"54 characters\">vendor/dcat/laravel-admin/src/Http/Middleware/Pjax.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Dcat\\Admin\\Http\\Middleware\\Pjax</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"62 characters\">vendor/dcat/laravel-admin/src/Http/Middleware/Authenticate.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>26</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Dcat\\Admin\\Http\\Middleware\\Authenticate</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"32 characters\">app/Http/Middleware/OpRecord.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>52</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Http\\Middleware\\OpRecord</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>78</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>121</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>67</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/dcat/laravel-admin/src/Http/Middleware/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>15</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Dcat\\Admin\\Http\\Middleware\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>116</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>799</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>776</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>740</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>729</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>63</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>190</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>64</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>141</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>65</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>66</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>67</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>68</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>69</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>70</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>71</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>72</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>73</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"87 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>74</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>75</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>86</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>76</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>77</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>78</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>79</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>39</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>80</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>81</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>116</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>82</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>165</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>83</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>134</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n  <span class=sf-dump-index>84</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>52</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["        if (! $repository instanceof Repository) {\n", "            $class = is_object($repository) ? get_class($repository) : $repository;\n", "\n", "            throw new InvalidArgumentException(\"The class [{$class}] must be a type of [\".Repository::class.'].');\n", "        }\n", "\n", "        return $repository;\n"], "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FAdmin.php&line=251", "ajax": false, "filename": "Admin.php", "line": "251"}}]}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.20", "PHP Version": "8.0.2", "Environment": "local", "Debug Mode": "Enabled", "URL": "sen.test", "Timezone": "PRC", "Locale": "zh_CN"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 7, "nb_statements": 6, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.47383000000000003, "accumulated_duration_str": "474ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Admin.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", "line": 145}, {"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 36}], "start": **********.936531, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "wind_rich", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `wr_admin_users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Admin.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", "line": 145}, {"index": 18, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.94617, "duration": 0.2331, "duration_str": "233ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wind_rich", "explain": null, "start_percent": 0, "width_percent": 49.195}, {"sql": "insert into `wr_operation_logs` (`admin_uid`, `user_name`, `path`, `method`, `ip`, `input`, `updated_at`, `created_at`) values (1, 'ZtFzSuperAdminPro', 'ztfz/sale', 'GET', '127.0.0.1', '[]', '2025-08-01 13:49:56', '2025-08-01 13:49:56')", "type": "query", "params": [], "bindings": [1, "ZtFzSuperAdminPro", "ztfz/sale", "GET", "127.0.0.1", "[]", "2025-08-01 13:49:56", "2025-08-01 13:49:56"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 51}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.184521, "duration": 0.049210000000000004, "duration_str": "49.21ms", "memory": 0, "memory_str": null, "filename": "OpRecord.php:51", "source": {"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FHttp%2FMiddleware%2FOpRecord.php&line=51", "ajax": false, "filename": "OpRecord.php", "line": "51"}, "connection": "wind_rich", "explain": null, "start_percent": 49.195, "width_percent": 10.386}, {"sql": "select * from `wr_announcements` where `wr_announcements`.`deleted_at` is null order by `id` desc limit 7", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Announcement.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\Announcement.php", "line": 24}, {"index": 15, "namespace": null, "name": "app/Admin/bootstrap.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\bootstrap.php", "line": 62}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Admin.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", "line": 164}, {"index": 17, "namespace": null, "name": "app/Admin/bootstrap.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\bootstrap.php", "line": 80}, {"index": 19, "namespace": "middleware", "name": "admin.bootstrap", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\Bootstrap.php", "line": 14}], "start": **********.2407758, "duration": 0.047130000000000005, "duration_str": "47.13ms", "memory": 0, "memory_str": null, "filename": "Announcement.php:24", "source": {"index": 14, "namespace": null, "name": "app/Models/Announcement.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\Announcement.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAnnouncement.php&line=24", "ajax": false, "filename": "Announcement.php", "line": "24"}, "connection": "wind_rich", "explain": null, "start_percent": 59.58, "width_percent": 9.947}, {"sql": "select `wr_admin_roles`.*, `wr_admin_role_users`.`user_id` as `pivot_user_id`, `wr_admin_role_users`.`role_id` as `pivot_role_id`, `wr_admin_role_users`.`created_at` as `pivot_created_at`, `wr_admin_role_users`.`updated_at` as `pivot_updated_at` from `wr_admin_roles` inner join `wr_admin_role_users` on `wr_admin_roles`.`id` = `wr_admin_role_users`.`role_id` where `wr_admin_role_users`.`user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/HasPermissions.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\HasPermissions.php", "line": 88}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/HasPermissions.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\HasPermissions.php", "line": 76}, {"index": 21, "namespace": "middleware", "name": "admin.permission", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\Permission.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 23, "namespace": "middleware", "name": "admin.bootstrap", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\Bootstrap.php", "line": 19}], "start": **********.40745, "duration": 0.0474, "duration_str": "47.4ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:88", "source": {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/HasPermissions.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\HasPermissions.php", "line": 88}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FHasPermissions.php&line=88", "ajax": false, "filename": "HasPermissions.php", "line": "88"}, "connection": "wind_rich", "explain": null, "start_percent": 69.527, "width_percent": 10.004}, {"sql": "select * from `wr_ww_users` where `wr_ww_users`.`deleted_at` is not null limit 10", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Admin/Traits/RecycleBinTrait.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Traits\\RecycleBinTrait.php", "line": 37}, {"index": 15, "namespace": null, "name": "app/Admin/Traits/RecycleBinTrait.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Traits\\RecycleBinTrait.php", "line": 24}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 232}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasTools.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasTools.php", "line": 53}, {"index": 18, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 235}], "start": **********.493265, "duration": 0.046020000000000005, "duration_str": "46.02ms", "memory": 0, "memory_str": null, "filename": "RecycleBinTrait.php:37", "source": {"index": 14, "namespace": null, "name": "app/Admin/Traits/RecycleBinTrait.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Traits\\RecycleBinTrait.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FTraits%2FRecycleBinTrait.php&line=37", "ajax": false, "filename": "RecycleBinTrait.php", "line": "37"}, "connection": "wind_rich", "explain": null, "start_percent": 79.531, "width_percent": 9.712}, {"sql": "insert into `wr_exception_record` (`code`, `file`, `line`, `message`, `url`, `ip`, `server`, `updated_at`, `created_at`) values (0, 'D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php', 251, 'The class [Illuminate\\Database\\Eloquent\\Collection] must be a type of [Dcat\\Admin\\Contracts\\Repository].', 'http://sen.test/ztfz/sale', '127.0.0.1', '{\\\"123pan\\\":\\\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\123pan\\\\123pan.exe\\\",\\\"ALIBABA_CLOUD_ACCESS_KEY_ID\\\":\\\"LTAI5tDTYvCoDdw3catZHJMv\\\",\\\"ALIBABA_CLOUD_ACCESS_KEY_SECRET\\\":\\\"******************************\\\",\\\"ALLUSERSPROFILE\\\":\\\"C:\\\\ProgramData\\\",\\\"ANTHROPIC_AUTH_TOKEN\\\":\\\"sk-6DHfg4tPsff3NAS6dOna7fuEqUbuRcl42WFDYHQCJB7tMEkL\\\",\\\"ANTHROPIC_BASE_URL\\\":\\\"https:\\/\\/anyrouter.top\\\",\\\"APPDATA\\\":\\\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\",\\\"AQUA_VM_OPTIONS\\\":\\\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\aqua.vmoptions\\\",\\\"ChocolateyInstall\\\":\\\"C:\\\\ProgramData\\\\chocolatey\\\",\\\"ChocolateyLastPathUpdate\\\":\\\"133893600769215303\\\",\\\"CLION_VM_OPTIONS\\\":\\\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\clion.vmoptions\\\",\\\"CommonProgramFiles\\\":\\\"C:\\\\Program Files\\\\Common Files\\\",\\\"CommonProgramFiles(x86)\\\":\\\"C:\\\\Program Files (x86)\\\\Common Files\\\",\\\"CommonProgramW6432\\\":\\\"C:\\\\Program Files\\\\Common Files\\\",\\\"COMPUTERNAME\\\":\\\"DESKTOP-ONBJ5CG\\\",\\\"ComSpec\\\":\\\"C:\\\\Windows\\\\system32\\\\cmd.exe\\\",\\\"DATAGRIP_VM_OPTIONS\\\":\\\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\datagrip.vmoptions\\\",\\\"DATASPELL_VM_OPTIONS\\\":\\\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\dataspell.vmoptions\\\",\\\"DEVECOSTUDIO_VM_OPTIONS\\\":\\\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\devecostudio.vmoptions\\\",\\\"DriverData\\\":\\\"C:\\\\Windows\\\\System32\\\\Drivers\\\\DriverData\\\",\\\"GATEWAY_VM_OPTIONS\\\":\\\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\gateway.vmoptions\\\",\\\"GEMINI_API_KEY\\\":\\\"AIzaSyBOhpirEna6-GsoeweNSjVhGL5fN7Z2h20\\\",\\\"GOLAND_VM_OPTIONS\\\":\\\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\goland.vmoptions\\\",\\\"HOMEDRIVE\\\":\\\"C:\\\",\\\"HOMEPATH\\\":\\\"\\\\Users\\\\sen\\\",\\\"IDEA_VM_OPTIONS\\\":\\\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\idea.vmoptions\\\",\\\"JAVA17_HOME\\\":\\\"C:\\\\Program Files\\\\Java\\\\jdk-17.0.11\\\",\\\"JETBRAINSCLIENT_VM_OPTIONS\\\":\\\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\jetbrainsclient.vmoptions\\\",\\\"JETBRAINS_CLIENT_VM_OPTIONS\\\":\\\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\jetbrains_client.vmoptions\\\",\\\"LOCALAPPDATA\\\":\\\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\",\\\"LOGONSERVER\\\":\\\"\\\\\\\\DESKTOP-ONBJ5CG\\\",\\\"MAVEN_HOME\\\":\\\"D:\\\\apache-maven-3.8.8\\\",\\\"MYSQL8_HOME\\\":\\\"D:\\\\software\\\\MySQL8\\\",\\\"MYSQL_HOME\\\":\\\"D:\\\\software\\\\MySQL\\\\mysql-5.7.24\\\",\\\"NUMBER_OF_PROCESSORS\\\":\\\"8\\\",\\\"OneDrive\\\":\\\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\",\\\"OS\\\":\\\"Windows_NT\\\",\\\"Path\\\":\\\"C:\\\\Program Files\\\\Java\\\\jdk-17.0.11\\\\bin;C:\\\\Program Files (x86)\\\\Common Files\\\\Oracle\\\\Java\\\\javapath;C:\\\\Windows\\\\system32;C:\\\\Windows;C:\\\\Windows\\\\System32\\\\Wbem;C:\\\\Windows\\\\System32\\\\WindowsPowerShell\\\\v1.0\\\\;C:\\\\Windows\\\\System32\\\\OpenSSH\\\\;D:\\\\software\\\\Microsoft VS Code\\\\bin;D:\\\\apache-maven-3.8.8\\\\bin;C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\platform-tools;D:\\\\Windows Kits\\\\10\\\\Windows Performance Toolkit\\\\;D:\\\\tools\\\\opencv\\\\build\\\\x64\\\\vc16\\\\bin;D:\\\\tools\\\\scrcpy-win64-v2.5\\\\scrcpy-win64-v2.5;D:\\\\software\\\\MySQL8\\\\bin;D:\\\\software\\\\MySQL\\\\mysql-5.7.24\\\\bin;C:\\\\Program Files\\\\dotnet\\\\;C:\\\\ProgramData\\\\Git\\\\cmd;C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Programs\\\\Python\\\\Python310\\\\Scripts\\\\;C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Programs\\\\Python\\\\Python310\\\\;C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Microsoft\\\\WindowsApps;D:\\\\software\\\\JetBrains\\\\IntelliJ IDEA 2023.1\\\\bin;C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\npm;D:\\\\software\\\\Fiddler;C:\\\\Users\\\\<USER>\\\\.dotnet\\\\tools;c:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Programs\\\\cursor\\\\resources\\\\app\\\\bin;C:\\\\ProgramData\\\\ComposerSetup\\\\bin;C:\\\\ProgramData\\\\chocolatey\\\\bin;C:\\\\Program Files\\\\nodejs\\\\;C:\\\\Qt\\\\6.9.0\\\\mingw_64\\\\bin;C:\\\\Program Files\\\\OpenSSL-Win64\\\\bin;C:\\\\Program Files\\\\ffmpeg-7.0.2-essentials_build\\\\bin;D:\\\\Documents\\\\VisualStudioRepos\\\\WeChatHider\\\\x64\\\\Release;D:\\\\software\\\\phpstudy_pro\\\\Extensions\\\\php\\\\php8.0.2nts;D:\\\\software\\\\phpstudy_pro\\\\Extensions\\\\redis3.0.504;C:\\\\Program Files\\\\PowerShell\\\\7\\\\;C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Programs\\\\cursor\\\\resources\\\\app\\\\bin\\\",\\\"PATHEXT\\\":\\\".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC\\\",\\\"PhpStorm\\\":\\\"D:\\\\software\\\\JetBrains\\\\PhpStorm 2024.3.1\\\\bin;\\\",\\\"PHPSTORM_VM_OPTIONS\\\":\\\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\phpstorm.vmoptions\\\",\\\"POWERSHELL_DISTRIBUTION_CHANNEL\\\":\\\"MSI:Windows 10 Home\\\",\\\"POWERSHELL_UPDATECHECK\\\":\\\"off\\\",\\\"PROCESSOR_ARCHITECTURE\\\":\\\"AMD64\\\",\\\"PROCESSOR_IDENTIFIER\\\":\\\"Intel64 Family 6 Model 126 Stepping 5, GenuineIntel\\\",\\\"PROCESSOR_LEVEL\\\":\\\"6\\\",\\\"PROCESSOR_REVISION\\\":\\\"7e05\\\",\\\"ProgramData\\\":\\\"C:\\\\ProgramData\\\",\\\"ProgramFiles\\\":\\\"C:\\\\Program Files\\\",\\\"ProgramFiles(x86)\\\":\\\"C:\\\\Program Files (x86)\\\",\\\"ProgramW6432\\\":\\\"C:\\\\Program Files\\\",\\\"PSModulePath\\\":\\\"C:\\\\Program Files\\\\WindowsPowerShell\\\\Modules;C:\\\\Windows\\\\system32\\\\WindowsPowerShell\\\\v1.0\\\\Modules\\\",\\\"PUBLIC\\\":\\\"C:\\\\Users\\\\<USER>\\\",\\\"PYCHARM_VM_OPTIONS\\\":\\\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\pycharm.vmoptions\\\",\\\"RIDER_VM_OPTIONS\\\":\\\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\rider.vmoptions\\\",\\\"RUBYMINE_VM_OPTIONS\\\":\\\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\rubymine.vmoptions\\\",\\\"RUSTROVER_VM_OPTIONS\\\":\\\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\rustrover.vmoptions\\\",\\\"STUDIO_VM_OPTIONS\\\":\\\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\studio.vmoptions\\\",\\\"SystemDrive\\\":\\\"C:\\\",\\\"SystemRoot\\\":\\\"C:\\\\Windows\\\",\\\"TEMP\\\":\\\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\",\\\"TMP\\\":\\\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\",\\\"USERDOMAIN\\\":\\\"DESKTOP-ONBJ5CG\\\",\\\"USERDOMAIN_ROAMINGPROFILE\\\":\\\"DESKTOP-ONBJ5CG\\\",\\\"USERNAME\\\":\\\"sen\\\",\\\"USERPROFILE\\\":\\\"C:\\\\Users\\\\<USER>\\\",\\\"WEBIDE_VM_OPTIONS\\\":\\\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\webide.vmoptions\\\",\\\"WEBSTORM_VM_OPTIONS\\\":\\\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\webstorm.vmoptions\\\",\\\"windir\\\":\\\"C:\\\\Windows\\\",\\\"ZES_ENABLE_SYSMAN\\\":\\\"1\\\",\\\"__COMPAT_LAYER\\\":\\\"RunAsAdmin\\\",\\\"HTTP_COOKIE\\\":\\\"XSRF-TOKEN=eyJpdiI6Imp1VDZRdTBLNURERDJHSHBabjZjMFE9PSIsInZhbHVlIjoiUmpqQ0NlL2p2anIvcXEzTFhCQUw4RFhZS1ZlWEFwQ3FHcWxpOHpUY2NMT2hvV3gvRnY0ek5lb3l6K3pBaVBjWTVObUdLb28yQTNQQ3EyVXlXeEdDMXA4SElGYlc5c1JEa1l3T2xndW9DRmNCcUcxL1dJdzU1S2FYR2t0NXFDM1oiLCJtYWMiOiI1ZjRmYzNjNGY0YWI5MTcxN2I5MTU4NWE5MTU4ZDJkZWIzNzY0MDY5OTE5MTBkOWRiNTRiZGIwYzMxODdmMjQ1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjIyek5wdUhXbUFQQzRWbDhRZG9UOFE9PSIsInZhbHVlIjoiQnhpMktGY1VJWkxka3lmR1pMRHJ4SVQ5QTF5bGVGbXhscVQ5U05VOU4yQnhNcHg1NENCUFlDWTNtaUUvWU5Cb2Q3SGlxcTRaUEIvcUp3d0V1TWZ2STZyU0NJeWUzc0U5bHhuaGdGbzh1U2xUWjBhTDNkNGdlcWFVdG5vY0w0a1IiLCJtYWMiOiIxZDg5YzYzMTgwYTNkOTVmMTAyZDg0ZDBiNWNhMDg0MjFjNDNhNzRkZGMyNWEyOWZhNmYzZTViNjhjYzg2ODU1IiwidGFnIjoiIn0%3D\\\",\\\"HTTP_ACCEPT_LANGUAGE\\\":\\\"zh-CN,zh;q=0.9\\\",\\\"HTTP_ACCEPT_ENCODING\\\":\\\"gzip, deflate\\\",\\\"HTTP_ACCEPT\\\":\\\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\\\",\\\"HTTP_USER_AGENT\\\":\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\\\",\\\"HTTP_UPGRADE_INSECURE_REQUESTS\\\":\\\"1\\\",\\\"HTTP_CACHE_CONTROL\\\":\\\"max-age=0\\\",\\\"HTTP_CONNECTION\\\":\\\"keep-alive\\\",\\\"HTTP_HOST\\\":\\\"sen.test\\\",\\\"REDIRECT_STATUS\\\":\\\"200\\\",\\\"SERVER_NAME\\\":\\\"sen.test\\\",\\\"SERVER_PORT\\\":\\\"80\\\",\\\"SERVER_ADDR\\\":\\\"127.0.0.1\\\",\\\"REMOTE_PORT\\\":\\\"51459\\\",\\\"REMOTE_ADDR\\\":\\\"127.0.0.1\\\",\\\"SERVER_SOFTWARE\\\":\\\"nginx\\/1.15.11\\\",\\\"GATEWAY_INTERFACE\\\":\\\"CGI\\/1.1\\\",\\\"REQUEST_SCHEME\\\":\\\"http\\\",\\\"SERVER_PROTOCOL\\\":\\\"HTTP\\/1.1\\\",\\\"DOCUMENT_ROOT\\\":\\\"D:\\/Documents\\/PHP\\/NewProject\\/wind_rich\\/public\\\",\\\"DOCUMENT_URI\\\":\\\"\\/index.php\\\",\\\"REQUEST_URI\\\":\\\"\\/ztfz\\/sale\\\",\\\"SCRIPT_NAME\\\":\\\"\\/index.php\\\",\\\"CONTENT_LENGTH\\\":\\\"\\\",\\\"CONTENT_TYPE\\\":\\\"\\\",\\\"REQUEST_METHOD\\\":\\\"GET\\\",\\\"QUERY_STRING\\\":\\\"\\\",\\\"PATH_TRANSLATED\\\":\\\"D:\\/Documents\\/PHP\\/NewProject\\/wind_rich\\/public\\\",\\\"PATH_INFO\\\":\\\"\\\",\\\"SCRIPT_FILENAME\\\":\\\"D:\\/Documents\\/PHP\\/NewProject\\/wind_rich\\/public\\/index.php\\\",\\\"FCGI_ROLE\\\":\\\"RESPONDER\\\",\\\"PHP_SELF\\\":\\\"\\/index.php\\\",\\\"REQUEST_TIME_FLOAT\\\":**********.562597,\\\"REQUEST_TIME\\\":**********,\\\"APP_NAME\\\":\\\"Laravel\\\",\\\"APP_ENV\\\":\\\"local\\\",\\\"APP_KEY\\\":\\\"base64:gm03dyY8wwoV\\/ZjY1y4V2Um3MQhZpheJn3TbwghWLoo=\\\",\\\"APP_DEBUG\\\":\\\"true\\\",\\\"APP_URL\\\":\\\"http:\\/\\/sen.test\\\",\\\"LOG_CHANNEL\\\":\\\"stack\\\",\\\"LOG_DEPRECATIONS_CHANNEL\\\":\\\"null\\\",\\\"LOG_LEVEL\\\":\\\"debug\\\",\\\"DB_CONNECTION\\\":\\\"mysql\\\",\\\"DB_HOST\\\":\\\"*************\\\",\\\"DB_PORT\\\":\\\"3306\\\",\\\"DB_DATABASE\\\":\\\"wind_rich\\\",\\\"DB_USERNAME\\\":\\\"wind_rich\\\",\\\"DB_PASSWORD\\\":\\\"k6CrxbzNjYahnTsC\\\",\\\"BROADCAST_DRIVER\\\":\\\"log\\\",\\\"CACHE_DRIVER\\\":\\\"file\\\",\\\"FILESYSTEM_DISK\\\":\\\"local\\\",\\\"QUEUE_CONNECTION\\\":\\\"sync\\\",\\\"SESSION_DRIVER\\\":\\\"file\\\",\\\"SESSION_LIFETIME\\\":\\\"120\\\",\\\"MEMCACHED_HOST\\\":\\\"127.0.0.1\\\",\\\"REDIS_HOST\\\":\\\"*************\\\",\\\"REDIS_PASSWORD\\\":\\\"ztfz2025\\\",\\\"REDIS_PORT\\\":\\\"6379\\\",\\\"MAIL_MAILER\\\":\\\"smtp\\\",\\\"MAIL_HOST\\\":\\\"mailpit\\\",\\\"MAIL_PORT\\\":\\\"1025\\\",\\\"MAIL_USERNAME\\\":\\\"null\\\",\\\"MAIL_PASSWORD\\\":\\\"null\\\",\\\"MAIL_ENCRYPTION\\\":\\\"null\\\",\\\"MAIL_FROM_ADDRESS\\\":\\\"<EMAIL>\\\",\\\"MAIL_FROM_NAME\\\":\\\"Laravel\\\",\\\"AWS_ACCESS_KEY_ID\\\":\\\"\\\",\\\"AWS_SECRET_ACCESS_KEY\\\":\\\"\\\",\\\"AWS_DEFAULT_REGION\\\":\\\"us-east-1\\\",\\\"AWS_BUCKET\\\":\\\"\\\",\\\"AWS_USE_PATH_STYLE_ENDPOINT\\\":\\\"false\\\",\\\"PUSHER_APP_ID\\\":\\\"\\\",\\\"PUSHER_APP_KEY\\\":\\\"\\\",\\\"PUSHER_APP_SECRET\\\":\\\"\\\",\\\"PUSHER_HOST\\\":\\\"\\\",\\\"PUSHER_PORT\\\":\\\"443\\\",\\\"PUSHER_SCHEME\\\":\\\"https\\\",\\\"PUSHER_APP_CLUSTER\\\":\\\"mt1\\\",\\\"VITE_PUSHER_APP_KEY\\\":\\\"\\\",\\\"VITE_PUSHER_HOST\\\":\\\"\\\",\\\"VITE_PUSHER_PORT\\\":\\\"443\\\",\\\"VITE_PUSHER_SCHEME\\\":\\\"https\\\",\\\"VITE_PUSHER_APP_CLUSTER\\\":\\\"mt1\\\",\\\"TELESCOPE_ENABLED\\\":\\\"true\\\",\\\"OSS_ACCESS_KEY\\\":\\\"LTAI5t8v26E4Y9sZgVewieFy\\\",\\\"OSS_SECRET_KEY\\\":\\\"GhH3ZUniJeCqqk5zCvw4E7Times42M\\\",\\\"OSS_ENDPOINT\\\":\\\"oss-cn-beijing.aliyuncs.com\\\",\\\"OSS_BUCKET\\\":\\\"smart-ark\\\",\\\"OSS_IS_CNAME\\\":\\\"false\\\",\\\"CDN_URL\\\":\\\"https:\\/\\/oss.smart-ark.cn\\\",\\\"ALGOLIA_APP_ID\\\":\\\"LMGHG5DZ9E\\\",\\\"ALGOLIA_SECRET\\\":\\\"********************************\\\",\\\"SCOUT_IDENTIFY\\\":\\\"true\\\"}', '2025-08-01 13:49:56', '2025-08-01 13:49:56')", "type": "query", "params": [], "bindings": [0, "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", 251, "The class [Illuminate\\Database\\Eloquent\\Collection] must be a type of [Dcat\\Admin\\Contracts\\Repository].", "http://sen.test/ztfz/sale", "127.0.0.1", "{\"123pan\":\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\123pan\\\\123pan.exe\",\"ALIBABA_CLOUD_ACCESS_KEY_ID\":\"LTAI5tDTYvCoDdw3catZHJMv\",\"ALIBABA_CLOUD_ACCESS_KEY_SECRET\":\"******************************\",\"ALLUSERSPROFILE\":\"C:\\\\ProgramData\",\"ANTHROPIC_AUTH_TOKEN\":\"sk-6DHfg4tPsff3NAS6dOna7fuEqUbuRcl42WFDYHQCJB7tMEkL\",\"ANTHROPIC_BASE_URL\":\"https:\\/\\/anyrouter.top\",\"APPDATA\":\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\",\"AQUA_VM_OPTIONS\":\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\aqua.vmoptions\",\"ChocolateyInstall\":\"C:\\\\ProgramData\\\\chocolatey\",\"ChocolateyLastPathUpdate\":\"133893600769215303\",\"CLION_VM_OPTIONS\":\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\clion.vmoptions\",\"CommonProgramFiles\":\"C:\\\\Program Files\\\\Common Files\",\"CommonProgramFiles(x86)\":\"C:\\\\Program Files (x86)\\\\Common Files\",\"CommonProgramW6432\":\"C:\\\\Program Files\\\\Common Files\",\"COMPUTERNAME\":\"DESKTOP-ONBJ5CG\",\"ComSpec\":\"C:\\\\Windows\\\\system32\\\\cmd.exe\",\"DATAGRIP_VM_OPTIONS\":\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\datagrip.vmoptions\",\"DATASPELL_VM_OPTIONS\":\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\dataspell.vmoptions\",\"DEVECOSTUDIO_VM_OPTIONS\":\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\devecostudio.vmoptions\",\"DriverData\":\"C:\\\\Windows\\\\System32\\\\Drivers\\\\DriverData\",\"GATEWAY_VM_OPTIONS\":\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\gateway.vmoptions\",\"GEMINI_API_KEY\":\"AIzaSyBOhpirEna6-GsoeweNSjVhGL5fN7Z2h20\",\"GOLAND_VM_OPTIONS\":\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\goland.vmoptions\",\"HOMEDRIVE\":\"C:\",\"HOMEPATH\":\"\\\\Users\\\\sen\",\"IDEA_VM_OPTIONS\":\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\idea.vmoptions\",\"JAVA17_HOME\":\"C:\\\\Program Files\\\\Java\\\\jdk-17.0.11\",\"JETBRAINSCLIENT_VM_OPTIONS\":\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\jetbrainsclient.vmoptions\",\"JETBRAINS_CLIENT_VM_OPTIONS\":\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\jetbrains_client.vmoptions\",\"LOCALAPPDATA\":\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\",\"LOGONSERVER\":\"\\\\\\\\DESKTOP-ONBJ5CG\",\"MAVEN_HOME\":\"D:\\\\apache-maven-3.8.8\",\"MYSQL8_HOME\":\"D:\\\\software\\\\MySQL8\",\"MYSQL_HOME\":\"D:\\\\software\\\\MySQL\\\\mysql-5.7.24\",\"NUMBER_OF_PROCESSORS\":\"8\",\"OneDrive\":\"C:\\\\Users\\\\<USER>\\\\OneDrive\",\"OS\":\"Windows_NT\",\"Path\":\"C:\\\\Program Files\\\\Java\\\\jdk-17.0.11\\\\bin;C:\\\\Program Files (x86)\\\\Common Files\\\\Oracle\\\\Java\\\\javapath;C:\\\\Windows\\\\system32;C:\\\\Windows;C:\\\\Windows\\\\System32\\\\Wbem;C:\\\\Windows\\\\System32\\\\WindowsPowerShell\\\\v1.0\\\\;C:\\\\Windows\\\\System32\\\\OpenSSH\\\\;D:\\\\software\\\\Microsoft VS Code\\\\bin;D:\\\\apache-maven-3.8.8\\\\bin;C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\platform-tools;D:\\\\Windows Kits\\\\10\\\\Windows Performance Toolkit\\\\;D:\\\\tools\\\\opencv\\\\build\\\\x64\\\\vc16\\\\bin;D:\\\\tools\\\\scrcpy-win64-v2.5\\\\scrcpy-win64-v2.5;D:\\\\software\\\\MySQL8\\\\bin;D:\\\\software\\\\MySQL\\\\mysql-5.7.24\\\\bin;C:\\\\Program Files\\\\dotnet\\\\;C:\\\\ProgramData\\\\Git\\\\cmd;C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Programs\\\\Python\\\\Python310\\\\Scripts\\\\;C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Programs\\\\Python\\\\Python310\\\\;C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Microsoft\\\\WindowsApps;D:\\\\software\\\\JetBrains\\\\IntelliJ IDEA 2023.1\\\\bin;C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\npm;D:\\\\software\\\\Fiddler;C:\\\\Users\\\\<USER>\\\\.dotnet\\\\tools;c:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Programs\\\\cursor\\\\resources\\\\app\\\\bin;C:\\\\ProgramData\\\\ComposerSetup\\\\bin;C:\\\\ProgramData\\\\chocolatey\\\\bin;C:\\\\Program Files\\\\nodejs\\\\;C:\\\\Qt\\\\6.9.0\\\\mingw_64\\\\bin;C:\\\\Program Files\\\\OpenSSL-Win64\\\\bin;C:\\\\Program Files\\\\ffmpeg-7.0.2-essentials_build\\\\bin;D:\\\\Documents\\\\VisualStudioRepos\\\\WeChatHider\\\\x64\\\\Release;D:\\\\software\\\\phpstudy_pro\\\\Extensions\\\\php\\\\php8.0.2nts;D:\\\\software\\\\phpstudy_pro\\\\Extensions\\\\redis3.0.504;C:\\\\Program Files\\\\PowerShell\\\\7\\\\;C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Programs\\\\cursor\\\\resources\\\\app\\\\bin\",\"PATHEXT\":\".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC\",\"PhpStorm\":\"D:\\\\software\\\\JetBrains\\\\PhpStorm 2024.3.1\\\\bin;\",\"PHPSTORM_VM_OPTIONS\":\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\phpstorm.vmoptions\",\"POWERSHELL_DISTRIBUTION_CHANNEL\":\"MSI:Windows 10 Home\",\"POWERSHELL_UPDATECHECK\":\"off\",\"PROCESSOR_ARCHITECTURE\":\"AMD64\",\"PROCESSOR_IDENTIFIER\":\"Intel64 Family 6 Model 126 Stepping 5, GenuineIntel\",\"PROCESSOR_LEVEL\":\"6\",\"PROCESSOR_REVISION\":\"7e05\",\"ProgramData\":\"C:\\\\ProgramData\",\"ProgramFiles\":\"C:\\\\Program Files\",\"ProgramFiles(x86)\":\"C:\\\\Program Files (x86)\",\"ProgramW6432\":\"C:\\\\Program Files\",\"PSModulePath\":\"C:\\\\Program Files\\\\WindowsPowerShell\\\\Modules;C:\\\\Windows\\\\system32\\\\WindowsPowerShell\\\\v1.0\\\\Modules\",\"PUBLIC\":\"C:\\\\Users\\\\<USER>\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\pycharm.vmoptions\",\"RIDER_VM_OPTIONS\":\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\rider.vmoptions\",\"RUBYMINE_VM_OPTIONS\":\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\rubymine.vmoptions\",\"RUSTROVER_VM_OPTIONS\":\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\rustrover.vmoptions\",\"STUDIO_VM_OPTIONS\":\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\studio.vmoptions\",\"SystemDrive\":\"C:\",\"SystemRoot\":\"C:\\\\Windows\",\"TEMP\":\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\",\"TMP\":\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\",\"USERDOMAIN\":\"DESKTOP-ONBJ5CG\",\"USERDOMAIN_ROAMINGPROFILE\":\"DESKTOP-ONBJ5CG\",\"USERNAME\":\"sen\",\"USERPROFILE\":\"C:\\\\Users\\\\<USER>\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\webide.vmoptions\",\"WEBSTORM_VM_OPTIONS\":\"D:\\\\software\\\\JetBrains\\\\PyCharm 2025.1\\\\PyCharm 2025.1-jihuotools\\\\jetbra\\\\vmoptions\\\\webstorm.vmoptions\",\"windir\":\"C:\\\\Windows\",\"ZES_ENABLE_SYSMAN\":\"1\",\"__COMPAT_LAYER\":\"RunAsAdmin\",\"HTTP_COOKIE\":\"XSRF-TOKEN=eyJpdiI6Imp1VDZRdTBLNURERDJHSHBabjZjMFE9PSIsInZhbHVlIjoiUmpqQ0NlL2p2anIvcXEzTFhCQUw4RFhZS1ZlWEFwQ3FHcWxpOHpUY2NMT2hvV3gvRnY0ek5lb3l6K3pBaVBjWTVObUdLb28yQTNQQ3EyVXlXeEdDMXA4SElGYlc5c1JEa1l3T2xndW9DRmNCcUcxL1dJdzU1S2FYR2t0NXFDM1oiLCJtYWMiOiI1ZjRmYzNjNGY0YWI5MTcxN2I5MTU4NWE5MTU4ZDJkZWIzNzY0MDY5OTE5MTBkOWRiNTRiZGIwYzMxODdmMjQ1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjIyek5wdUhXbUFQQzRWbDhRZG9UOFE9PSIsInZhbHVlIjoiQnhpMktGY1VJWkxka3lmR1pMRHJ4SVQ5QTF5bGVGbXhscVQ5U05VOU4yQnhNcHg1NENCUFlDWTNtaUUvWU5Cb2Q3SGlxcTRaUEIvcUp3d0V1TWZ2STZyU0NJeWUzc0U5bHhuaGdGbzh1U2xUWjBhTDNkNGdlcWFVdG5vY0w0a1IiLCJtYWMiOiIxZDg5YzYzMTgwYTNkOTVmMTAyZDg0ZDBiNWNhMDg0MjFjNDNhNzRkZGMyNWEyOWZhNmYzZTViNjhjYzg2ODU1IiwidGFnIjoiIn0%3D\",\"HTTP_ACCEPT_LANGUAGE\":\"zh-CN,zh;q=0.9\",\"HTTP_ACCEPT_ENCODING\":\"gzip, deflate\",\"HTTP_ACCEPT\":\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\",\"HTTP_USER_AGENT\":\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\",\"HTTP_UPGRADE_INSECURE_REQUESTS\":\"1\",\"HTTP_CACHE_CONTROL\":\"max-age=0\",\"HTTP_CONNECTION\":\"keep-alive\",\"HTTP_HOST\":\"sen.test\",\"REDIRECT_STATUS\":\"200\",\"SERVER_NAME\":\"sen.test\",\"SERVER_PORT\":\"80\",\"SERVER_ADDR\":\"127.0.0.1\",\"REMOTE_PORT\":\"51459\",\"REMOTE_ADDR\":\"127.0.0.1\",\"SERVER_SOFTWARE\":\"nginx\\/1.15.11\",\"GATEWAY_INTERFACE\":\"CGI\\/1.1\",\"REQUEST_SCHEME\":\"http\",\"SERVER_PROTOCOL\":\"HTTP\\/1.1\",\"DOCUMENT_ROOT\":\"D:\\/Documents\\/PHP\\/NewProject\\/wind_rich\\/public\",\"DOCUMENT_URI\":\"\\/index.php\",\"REQUEST_URI\":\"\\/ztfz\\/sale\",\"SCRIPT_NAME\":\"\\/index.php\",\"CONTENT_LENGTH\":\"\",\"CONTENT_TYPE\":\"\",\"REQUEST_METHOD\":\"GET\",\"QUERY_STRING\":\"\",\"PATH_TRANSLATED\":\"D:\\/Documents\\/PHP\\/NewProject\\/wind_rich\\/public\",\"PATH_INFO\":\"\",\"SCRIPT_FILENAME\":\"D:\\/Documents\\/PHP\\/NewProject\\/wind_rich\\/public\\/index.php\",\"FCGI_ROLE\":\"RESPONDER\",\"PHP_SELF\":\"\\/index.php\",\"REQUEST_TIME_FLOAT\":**********.562597,\"REQUEST_TIME\":**********,\"APP_NAME\":\"Laravel\",\"APP_ENV\":\"local\",\"APP_KEY\":\"base64:gm03dyY8wwoV\\/ZjY1y4V2Um3MQhZpheJn3TbwghWLoo=\",\"APP_DEBUG\":\"true\",\"APP_URL\":\"http:\\/\\/sen.test\",\"LOG_CHANNEL\":\"stack\",\"LOG_DEPRECATIONS_CHANNEL\":\"null\",\"LOG_LEVEL\":\"debug\",\"DB_CONNECTION\":\"mysql\",\"DB_HOST\":\"*************\",\"DB_PORT\":\"3306\",\"DB_DATABASE\":\"wind_rich\",\"DB_USERNAME\":\"wind_rich\",\"DB_PASSWORD\":\"k6CrxbzNjYahnTsC\",\"BROADCAST_DRIVER\":\"log\",\"CACHE_DRIVER\":\"file\",\"FILESYSTEM_DISK\":\"local\",\"QUEUE_CONNECTION\":\"sync\",\"SESSION_DRIVER\":\"file\",\"SESSION_LIFETIME\":\"120\",\"MEMCACHED_HOST\":\"127.0.0.1\",\"REDIS_HOST\":\"*************\",\"REDIS_PASSWORD\":\"ztfz2025\",\"REDIS_PORT\":\"6379\",\"MAIL_MAILER\":\"smtp\",\"MAIL_HOST\":\"mailpit\",\"MAIL_PORT\":\"1025\",\"MAIL_USERNAME\":\"null\",\"MAIL_PASSWORD\":\"null\",\"MAIL_ENCRYPTION\":\"null\",\"MAIL_FROM_ADDRESS\":\"<EMAIL>\",\"MAIL_FROM_NAME\":\"Laravel\",\"AWS_ACCESS_KEY_ID\":\"\",\"AWS_SECRET_ACCESS_KEY\":\"\",\"AWS_DEFAULT_REGION\":\"us-east-1\",\"AWS_BUCKET\":\"\",\"AWS_USE_PATH_STYLE_ENDPOINT\":\"false\",\"PUSHER_APP_ID\":\"\",\"PUSHER_APP_KEY\":\"\",\"PUSHER_APP_SECRET\":\"\",\"PUSHER_HOST\":\"\",\"PUSHER_PORT\":\"443\",\"PUSHER_SCHEME\":\"https\",\"PUSHER_APP_CLUSTER\":\"mt1\",\"VITE_PUSHER_APP_KEY\":\"\",\"VITE_PUSHER_HOST\":\"\",\"VITE_PUSHER_PORT\":\"443\",\"VITE_PUSHER_SCHEME\":\"https\",\"VITE_PUSHER_APP_CLUSTER\":\"mt1\",\"TELESCOPE_ENABLED\":\"true\",\"OSS_ACCESS_KEY\":\"LTAI5t8v26E4Y9sZgVewieFy\",\"OSS_SECRET_KEY\":\"GhH3ZUniJeCqqk5zCvw4E7Times42M\",\"OSS_ENDPOINT\":\"oss-cn-beijing.aliyuncs.com\",\"OSS_BUCKET\":\"smart-ark\",\"OSS_IS_CNAME\":\"false\",\"CDN_URL\":\"https:\\/\\/oss.smart-ark.cn\",\"ALGOLIA_APP_ID\":\"LMGHG5DZ9E\",\"ALGOLIA_SECRET\":\"********************************\",\"SCOUT_IDENTIFY\":\"true\"}", "2025-08-01 13:49:56", "2025-08-01 13:49:56"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Exceptions/Handler.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Exceptions\\Handler.php", "line": 74}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php", "line": 49}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 143}, {"index": 18, "namespace": "middleware", "name": "admin.upload", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\WebUploader.php", "line": 22}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.544656, "duration": 0.05097, "duration_str": "50.97ms", "memory": 0, "memory_str": null, "filename": "Handler.php:74", "source": {"index": 15, "namespace": null, "name": "app/Exceptions/Handler.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Exceptions\\Handler.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FExceptions%2FHandler.php&line=74", "ajax": false, "filename": "Handler.php", "line": "74"}, "connection": "wind_rich", "explain": null, "start_percent": 89.243, "width_percent": 10.757}]}, "models": {"data": {"App\\Models\\WwUser": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwUser.php&line=1", "ajax": false, "filename": "WwUser.php", "line": "?"}}, "Dcat\\Admin\\Models\\Role": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Dcat\\Admin\\Models\\Administrator": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FAdministrator.php&line=1", "ajax": false, "filename": "Administrator.php", "line": "?"}}, "App\\Models\\Announcement": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAnnouncement.php&line=1", "ajax": false, "filename": "Announcement.php", "line": "?"}}}, "count": 15, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "500 Internal Server Error", "full_url": "http://sen.test/ztfz/sale", "action_name": "dcat.admin.sale.index", "controller_action": "App\\Admin\\Controllers\\WwUserController@index", "uri": "GET ztfz/sale", "controller": "App\\Admin\\Controllers\\WwUserController@index<a href=\"phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwUserController.php&line=72\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Admin\\Controllers", "prefix": "/ztfz", "file": "<a href=\"phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwUserController.php&line=72\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Admin/Controllers/WwUserController.php:72-108</a>", "middleware": "admin.app:admin, web, admin", "duration": "1.41s", "peak_memory": "6MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-83786253 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-83786253\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-801398802 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-801398802\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-228614520 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Imp1VDZRdTBLNURERDJHSHBabjZjMFE9PSIsInZhbHVlIjoiUmpqQ0NlL2p2anIvcXEzTFhCQUw4RFhZS1ZlWEFwQ3FHcWxpOHpUY2NMT2hvV3gvRnY0ek5lb3l6K3pBaVBjWTVObUdLb28yQTNQQ3EyVXlXeEdDMXA4SElGYlc5c1JEa1l3T2xndW9DRmNCcUcxL1dJdzU1S2FYR2t0NXFDM1oiLCJtYWMiOiI1ZjRmYzNjNGY0YWI5MTcxN2I5MTU4NWE5MTU4ZDJkZWIzNzY0MDY5OTE5MTBkOWRiNTRiZGIwYzMxODdmMjQ1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjIyek5wdUhXbUFQQzRWbDhRZG9UOFE9PSIsInZhbHVlIjoiQnhpMktGY1VJWkxka3lmR1pMRHJ4SVQ5QTF5bGVGbXhscVQ5U05VOU4yQnhNcHg1NENCUFlDWTNtaUUvWU5Cb2Q3SGlxcTRaUEIvcUp3d0V1TWZ2STZyU0NJeWUzc0U5bHhuaGdGbzh1U2xUWjBhTDNkNGdlcWFVdG5vY0w0a1IiLCJtYWMiOiIxZDg5YzYzMTgwYTNkOTVmMTAyZDg0ZDBiNWNhMDg0MjFjNDNhNzRkZGMyNWEyOWZhNmYzZTViNjhjYzg2ODU1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">zh-CN,zh;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">sen.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-228614520\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1099113250 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0esJyfQEGYXbNzItiQn8t75dzSvXZmiKqhcwIHBs</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Sc1mCMq4OSwvqqSk8mbaIttmOBzjSrhvaXfSjoYm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1099113250\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1338735993 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Aug 2025 05:49:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1338735993\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-255284750 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>prev</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://sen.test/ztfz/sale</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">admin.prev.url</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0esJyfQEGYXbNzItiQn8t75dzSvXZmiKqhcwIHBs</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://sen.test/ztfz/sale</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-255284750\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "500 Internal Server Error", "full_url": "http://sen.test/ztfz/sale", "action_name": "dcat.admin.sale.index", "controller_action": "App\\Admin\\Controllers\\WwUserController@index"}, "badge": "500 Internal Server Error"}}