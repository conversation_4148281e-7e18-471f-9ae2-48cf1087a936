<?php

namespace App\Admin\Forms\WwUser;

use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Models\AdminUser;
use App\Models\WwUser;
use App\Models\WwUsersGroup;
use App\Models\WwUsersGroupsRel;
use App\Models\WwUsersOnlineLogs;
use App\Services\Tools\LogService;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class BatchSetOnlieStatusForm extends Form implements LazyRenderable
{
    use LazyWidget;

    // 处理请求
    public function handle(array $input)
    {

        LogService::inputLog('Tools','销售客服管理-批量上下线', $input, Admin::user()->id, Admin::user()->username);
        if(!$input['id']){
            return $this->response()->alert()->error('提示')->detail('请选择销售。');
        }
        // id转化为数组
        $id = explode(',', $input['id']);
        if (! $id) {
            return $this->response()->alert()->error('提示')->detail('参数错误。');
        }
        $wwUsers = WwUser::query()->find($id);

        if ($wwUsers->isEmpty()) {
            return $this->response()->alert()->error('提示')->detail('销售不存在。');
        }
        $onlineStatusDesc = '下线';
        if($input['online_status'] > 0){
            $onlineStatusDesc = '上线';
        }
        /** @var WwUser $wwUser */
        foreach($wwUsers as $wwUser){
            if(!AdminUser::isAdmin($wwUser)){
                return $this->response()->alert()->error('提示')->detail('无操作权限。');
            }
            $wwUser->online_status = $input['online_status'];
            $wwUser->save();
            //记录操作日志队列
            if ($input['online_status'] == 1) {
                $source = '客户-批量操作上线';
                $onlineCheck = WwUser::onlineCheck($wwUser,$source,Admin::user()->id);
                if (!$onlineCheck['status']) {
                    return $this->response()->error($wwUser->name . "-" . $onlineCheck['message'])->refresh();
                }
                //上线成功后，添加销售上线记录日志
                WwUsersOnlineLogs::addOnLineStatusLogs($wwUser, $input['online_status'], $source, Admin::user()->id);
            }
            //添加销售下线记录日志
            if($input['online_status'] == 0){
                if($wwUser->adminInfo->ww_user_offline_check == 1){ //判断销售下线时，否是分组内的最后一个在线销售
                    $emptyGroups = WwUsersGroupsRel::getGroupOnlineWwUsers($wwUser);
                    if(!empty($emptyGroups)){
                        $groupNames = WwUsersGroup::query()
                            ->whereIn('id', $emptyGroups)
                            ->pluck('title')
                            ->toArray();
                        return $this->response()->alert()->error('提示')->detail('【' . $wwUser->name . '】是下列分组的最后一个在线销售: '.implode('，', $groupNames));
                    }
                }
                $source = '客户-批量操作下线';
                WwUsersOnlineLogs::addOnLineStatusLogs($wwUser, $input['online_status'], $source,Admin::user()->id);
            }
            AdminActionLogJob::dispatch(
                'batch_set_online_status',
                $wwUser->id,
                AdminActionLog::ACTION_TYPE['销售'],
                '【' . $wwUser->name . '】，修改状态为：' . $onlineStatusDesc,
                getIp(),
                Admin::user()->id
            )->onQueue('admin_action_log_job');

        }
        return $this->response()->alert()->success('提示')->detail('操作成功。')->refresh();
    }

    public function form()
    {
        $this->radio('online_status','状态')->options([0 => '下线',1 => '上线'])->default(0)->required();
        $this->hidden('id')->value($this->payload['ids'] ?? '');
        $this->confirm('确认提交？');
    }

    // 返回表单数据，如不需要可以删除此方法
    public function default()
    {
        return [
            'password'         => '',
            'password_confirm' => '',
        ];
    }
}
