<?php

	namespace App\Console\Commands\Tads;

	use App\Models\TencentAdAccount;
	use Illuminate\Console\Command;
	use Illuminate\Support\Facades\DB;
	use Illuminate\Support\Facades\Http;

	class GetAccountVccByDay extends Command
	{
		/**
		 * The name and signature of the console command.
		 *
		 * @var string
		 */
		protected $signature = 'Tads:GetAccountVccByDay';

		/**
		 * The console command description.
		 *
		 * @var string
		 */
		protected $description = 'Command description';

		/**
		 * Execute the console command.
		 *
		 * @return int
		 */
		public function handle()
		{
            TencentAdAccount::withTrashed()->chunkById(100, function ($rows) {
                if($rows->isEmpty()) {
                    return false;
                }
				foreach ($rows as $adAccount) {
					$this->getDailyReports($adAccount);
				}
			});
			return Command::SUCCESS;
		}

		public function getDailyReports(TencentAdAccount $adAccount, $page = 1)
		{
			$interface = 'daily_reports/get';
			$url       = 'https://api.e.qq.com/v3.0/' . $interface;

			$common_parameters = array(
				'access_token' => $adAccount->access_token,
				'timestamp'    => time(),
				'nonce'        => md5(uniqid('', true))
			);
			if (empty($adAccount->wechat_account_id)) {
				$level = 'REPORT_LEVEL_ADVERTISER';
			} else {
				$level = 'REPORT_LEVEL_ADVERTISER_WECHAT';
			}
			$parameters = array(
				'account_id' => $adAccount->account_id,
				'level'      => $level,
				'date_range' => [
					'start_date' => date("Y-m-d", (time() - (86400 * 30))),
					'end_date'   => date("Y-m-d", time()),
				],
				'group_by'   => ["date"],
				'fields'     => ['view_count', 'valid_click_count', 'cost', 'date', 'account_id', 'conversions_count', 'conversions_rate', 'conversions_cost'],
				'page'       => $page,
				'page_size'  => 100
			);
			$parameters = array_merge($common_parameters, $parameters);
			foreach ($parameters as $key => $value) {
				if (!is_string($value)) {
					$parameters[$key] = json_encode($value);
				}
			}

			$resp = Http::get($url, $parameters)->json();
			if ($resp['code'] == 0 && !empty($resp['data']['list'])) {
				//$this->info($adAccount->account_id);
				foreach ($resp['data']['list'] as $tempData) {
					$where      = [
						'account_id' => $adAccount->account_id,
						'date'       => $tempData['date'],
					];
					$updateData = [
						'view_count'        => $tempData['view_count'],
						'valid_click_count' => $tempData['valid_click_count'],
						'ad_cost'           => $tempData['cost'],
						'conversions_count' => $tempData['conversions_count'],
						'conversions_rate'  => $tempData['conversions_rate'],
						'conversions_cost'  => $tempData['conversions_cost'],
						'updated_at'        => date("Y-m-d H:i:s", time())
					];
					$lastData   = DB::table("ad_account_vcc_daily")->where($where)->select("id")->first();
					if ($lastData) {
						DB::table("ad_account_vcc_daily")->where("id", $lastData->id)->update($updateData);
					} else {
						$updateData = array_merge($updateData, [
							'admin_uid'  => $adAccount->admin_uid,
							'account_id' => $adAccount->account_id,
							'date'       => $tempData['date'],
							'created_at' => date("Y-m-d H:i:s", time())
						]);
						DB::table("ad_account_vcc_daily")->insert($updateData);
					}
				}
			}
		}
	}
