<?php

	namespace App\Http\Controllers\SuitePage;

	use App\Http\Controllers\WorkWeixinAppController;
    use App\Models\AdminSubUserAuthCorp;
    use App\Models\AdminUser;
    use App\Models\Corp3rdViewUser;
	use App\Models\WwCorpInfo;
	use Illuminate\Http\Request;
    use Illuminate\Http\Response;
    use Illuminate\Support\Facades\Crypt;
    use Illuminate\Support\Facades\Http;
    use Illuminate\Support\Facades\Log;

    class WwPage3rdController
	{
		public function mobileIndex(Request $request)
		{
			$sid  = $request->get("sid");
			$app  = WorkWeixinAppController::getApp('', $sid);
			$link = $app->getOAuth($app->getAccount()->getSuiteId())->withState($sid)->scopes(["snsapi_userinfo"])->redirect(route("mobileIndexLoginCallback"));
			return redirect($link);
		}

		public function mobileIndexLoginCallback(Request $request)
		{
			$sid          = $request->get("state");
			$app          = WorkWeixinAppController::getApp('', $sid);
			$userBaseData = $app->getOAuth($app->getAccount()->getSuiteId())->userFromCode($request->get("code"))->getRaw();
			/** @var WwCorpInfo $corpInfo */
			$corpInfo = WwCorpInfo::query()->where("suite_id", $app->getAccount()->getSuiteId())->where("corp_id", $userBaseData['CorpId'])->first();
			if (!$corpInfo) {
				return redirect(route('mobileError'));
			}
			//获取企业的管理员列表
			/** @var Corp3rdViewUser $viewUser */
			$viewUser = Corp3rdViewUser::query()->where("corp_id", $corpInfo->id)->where("userid", $userBaseData['UserId'])->first();
			if (!$viewUser) {
				$viewUser = new Corp3rdViewUser();
			}
			$viewUser->corp_id     = $corpInfo->id;
			$viewUser->userid      = $userBaseData['UserId'] ?? "";
			$viewUser->device_id   = $userBaseData['DeviceId'] ?? "";
			$viewUser->parents     = json_encode($userBaseData['parents'] ?? []);
			$viewUser->open_userid = $userBaseData['open_userid'] ?? "";
			$viewUser->suite_id    = $corpInfo->suite_id;
			$viewUser->save();

			$viewUser->md5_id = md5($viewUser->id . "TigerView-^4girl@&xau637b*aTEborn(nQ%indeedtraceOXbtwicepLBcn@^paideatDJfar5L6influence35even06");
			$viewUser->save();
			return redirect(route("mobilePage", ['uuid' => $viewUser->md5_id, 'sid' => $sid]));
		}

		public function mobilePage(Request $request)
		{

			/** @var Corp3rdViewUser $viewUser */
			$viewUser = Corp3rdViewUser::query()->where(['md5_id' => $request->get("uuid")])->first();
			if (!$viewUser) {
				return redirect(route('mobileError'));
			}
			/** @var WwCorpInfo $corpInfo */
			$corpInfo = WwCorpInfo::query()->find($viewUser->corp_id);
//            dd($corpInfo);
			if (!$corpInfo) {
				return redirect(route('mobileError'));
			}
			$adminList = WorkWeixinAppController::getApp('', $request->get("sid"))->getClient()->postJson('cgi-bin/agent/get_admin_list?access_token=' . WorkWeixinAppController::getCorpToken($corpInfo))->toArray();
			$adminIds  = [];
			if (isset($adminList['admin'])) {
				foreach ($adminList['admin'] as $item) {
					if ($item['auth_type'] == 1) {
						$adminIds[] = $item['userid'];
					}
				}
			}
			if (in_array($viewUser->userid, $adminIds)) {
				return view("3rdPage.mange", [
                    'corp_id'                         => Crypt::encrypt($corpInfo->id),
					'corp_round_logo_url'             => $corpInfo->corp_round_logo_url,
					'corp_name'                       => $corpInfo->corp_name,
					'created_at'                      => $corpInfo->created_at,
					'customer_link_quota_balance'     => $corpInfo->customer_link_quota_balance,
					'customer_link_quota_update_time' => $corpInfo->customer_link_quota_update_time
				]);
			} else {
				return redirect(route('mobileError', ['msg' => '非管理员，无管理权限']));
			}
		}

		public function webSetting(Request $request)
		{
			$authCode = $request->get("auth_code");
			$app      = WorkWeixinAppController::getApp('', $request->get("sid"));
			$userInfo = $app->getClient()->postJson(
				'cgi-bin/service/get_login_info?access_token=' . $app->getProviderAccessToken()->getToken(), [
					'auth_code' => $authCode
				]
			)->toArray();
			if (!isset($userInfo['corp_info'])) {
				return redirect(route('mobileError', ['msg' => '未授权']));
			}
			/** @var WwCorpInfo $corpInfo */
			$corpInfo = WwCorpInfo::query()->where("suite_id", $app->getAccount()->getSuiteId())->where("corp_id", $userInfo['corp_info']['corpid'])->first();
			if (!$corpInfo) {
				return redirect(route('mobileError'));
			}
			//获取企业的管理员列表
			/** @var Corp3rdViewUser $viewUser */
			$viewUser = Corp3rdViewUser::query()->where("corp_id", $corpInfo->id)->where("userid", $userInfo['user_info']['userid'])->first();
			if (!$viewUser) {
				$viewUser = new Corp3rdViewUser();
			}
			$viewUser->corp_id     = $corpInfo->id;
			$viewUser->userid      = $userInfo['user_info']['userid'];
			$viewUser->device_id   = "";
			$viewUser->parents     = '[]';
			$viewUser->open_userid = "";
			$viewUser->suite_id    = $corpInfo->suite_id;
			$viewUser->save();

			$viewUser->md5_id = md5($viewUser->id . "TigerView-^4girl@&xau637b*aTEborn(nQ%indeedtraceOXbtwicepLBcn@^paideatDJfar5L6influence35even06");
			$viewUser->save();

			return redirect(route("mobilePage", ['uuid' => $viewUser->md5_id, 'sid' => $request->get('sid')]));

		}

		public function mobileError(Request $request)
		{
			$msg = '系统错误，请退出后重试';
			if ($request->get("msg")) {
				$msg = $request->get('msg');
			}
			return view("3rdPage.error", ['message' => $msg]);
		}


        /**
         * 共享企微给其他智投账号
         * @param Request $request
         * @return \Illuminate\Http\JsonResponse
         */
        public function shareCorp(Request $request)
        {
            $encryptAdminAccount = $request->post("sb_account");
            $encryptCorpId = $request->post("sb_id");

            if(!$encryptAdminAccount){
                return response()->json(['code' => 1, 'data' => [], 'message' => '请输入账号加密串']);
            }

            //获取要共享的用户信息
            $adminUser = AdminUser::query()->where('encrypt_id', $encryptAdminAccount)->first();
            if (!$adminUser) {
                $adminUser = AdminUser::query()->where('username', $encryptAdminAccount)->first();
            }
            if (!$adminUser) {
                return response()->json(['code' => 1, 'data' => [], 'message' => '账号不存在-2']);
            }

            if(!$encryptCorpId){
                return response()->json(['code' => 1, 'data' => [], 'message' => '共享的企微不存在-1']);
            }
            //获取共享的企微信息
            try {
                $corpId = Crypt::decrypt($encryptCorpId);
            }catch (\Exception $e){
                return response()->json(['code' => 1, 'data' => [], 'message' => '共享数据解密失败']);
            }

            $corpInfo = WwCorpInfo::query()->where('id', $corpId)->first();
            if(!$corpInfo){
                return response()->json(['code' => 1, 'data' => [], 'message' => '共享的企微不存在-3']);
            }
            //创建企微共享数据
            AdminSubUserAuthCorp::addCorpRecord($adminUser->id, $corpInfo);
            return response()->json(['code' => 0, 'data' => [], 'message' => '共享成功']);
        }
	}
