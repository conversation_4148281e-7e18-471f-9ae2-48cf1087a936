<?php

namespace App\Admin\Forms\WwUser;

use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Models\AdminUser;
use App\Models\WwUser;
use App\Services\Tools\LogService;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class BatchSetAddMethodForm extends Form implements LazyRenderable
{
    use LazyWidget;

    // 处理请求
    public function handle(array $input)
    {
        LogService::inputLog('Tools','销售客服管理 -配置模式', $input, Admin::user()->id, Admin::user()->username);
        if(!$input['id']){
            return $this->response()->alert()->error('提示')->detail('请选择销售客服。');
        }
        // id转化为数组
        $id = explode(',', $input['id'] ?? null);
        if (! $id) {
            return $this->response()->alert()->error('提示')->detail('参数错误。');
        }


        $wwUsers = WwUser::query()->find($id);

        if ($wwUsers->isEmpty()) {
            return $this->response()->alert()->error('提示')->detail('销售不存在。');
        }
        /** @var WwUser $wwUser */
        switch ($input['add_method']){
            case 1:
                $addMethod = '获客助手';
                break;
            case 2:
                $addMethod = '二维码';
                break;
            default:
                $addMethod = '';
        }
        foreach($wwUsers as $wwUser){
            if(!AdminUser::isAdmin($wwUser)){
                return $this->response()->alert()->error('提示')->detail('无操作权限。');
            }

            $wwUser->add_method = $input['add_method'];
            $wwUser->save();
            //添加操作日志队列
            AdminActionLogJob::dispatch(
                'batch_set_add_method',
                $wwUser->id,
                AdminActionLog::ACTION_TYPE['销售'],
                '【' . $wwUser->name . '】，设置加粉方式：' . $addMethod,
                getIp(),
                Admin::user()->id
            )->onQueue('admin_action_log_job');

        }
        return $this->response()->alert()->success('提示')->detail('配置成功')->refresh();
    }

    public function form()
    {
        $this->radio('add_method')
            ->options([1 => '获客助手', 2 => '二维码'])
            ->help("如更改加粉模式为【二维码】后，系统会开始创建销售的二维码的，大概耗时15分钟左右，请注意切换时机。")->default(1)->required();
        $this->hidden('id')->value($this->payload['ids'] ?? ''); // 批量操作时使用BatchActionPlus 就可以$this->payload['ids'] 获取批量选中的ids
        $this->confirm('确认提交？');
    }

    // 返回表单数据，如不需要可以删除此方法
    public function default()
    {
        return [
            'password'         => '',
            'password_confirm' => '',
        ];
    }
}
