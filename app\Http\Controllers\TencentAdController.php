<?php

namespace App\Http\Controllers;


use App\Models\TenCentAdTrackClickConfig;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TencentAdController
{


    public function monitor(Request $request)
    {

//        Log::info('腾讯广告点击监测');
        $data = $request->all();
//        Log::info(json_encode($data));
        if (empty($data)) {
            return response(['ret' => 1, 'msg' => 'no-1'], 200);
        }

        //验签

        if (!isset($data['sb'])) {
            return response(['ret' => 1, 'msg' => 'no-2'], 200);
        }
        $salt = TenCentAdTrackClickConfig::SALT;
        $token = md5($salt . $data['account_id']);
        if ($data['sb'] !== $token) {
            return response(['ret' => 1, 'msg' => 'no-3'], 200);
        }


        DB::beginTransaction();
        try {
            DB::table('ad_track_click_logs')->insert([
                'click_id' => $data['click_id'],
                'click_time' => date('Y-m-d H:i:s', $data['click_time']),
                'adgroup_id' => $data['adgroup_id'],
                'ad_platform_type' => $data['ad_platform_type'],
                'ad_type' => $data['ad_type'],
                'account_id' => $data['account_id'],
                'site_set_name' => $data['site_set_name'] ?? "",
                'agency_id' => $data['agency_id'],
                'click_sku_id' => $data['click_sku_id'],
                'billing_event' => $data['billing_event'],
                'device_os_type' => $data['device_os_type'],
                'process_time' => date('Y-m-d H:i:s', $data['process_time']),
                'request_id' => $data['request_id'],
                'callback' => $data['callback'],
                'adgroup_name' => $data['adgroup_name'],
                'dynamic_creative_id' => $data['dynamic_creative_id'],
                'dynamic_creative_name' => $data['dynamic_creative_name'],
                'creative_components_info' => $data['creative_components_info'],
                'element_info' => $data['element_info'],
                'marketing_goal' => $data['marketing_goal'],
                'marketing_sub_goal' => $data['marketing_sub_goal'],
                'marketing_target_id' => $data['marketing_target_id'],
                'marketing_carrier_id' => $data['marketing_carrier_id'],
                'marketing_sub_carrier_id' => $data['marketing_sub_carrier_id'],
                'marketing_asset_id' => $data['marketing_asset_id'],
                'material_package_id' => $data['material_package_id'],
                'impression_id' => $data['impression_id'] ?? '',
                'muid' => $data['muid'] ?? '',
                'hash_android_id' => $data['hash_android_id'] ?? '',
                'ip_md5' => $data['ip_md5'] ?? '',
                'ipv6_md5' => $data['ipv6_md5'] ?? '',
                'ipv6' => $data['ipv6'] ?? '',
                'hash_oaid' => $data['hash_oaid'] ?? '',
                'caid' => $data['caid'] ?? null,
                'encrypted_position_id' => $data['encrypted_position_id'] ?? '',
                'impression_time' => date('Y-m-d H:i:s', $data['impression_time']),
                'promoted_object_type' => $data['promoted_object_type'] ?? 0,
                'model' => $data['model'] ?? '',
                'user_agent' => $data['user_agent'] ?? '',
                'ip' => $data['ip'] ?? '',
                'page_url' => $data['page_url'] ?? '',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ]);
            DB::commit();
            return response(['ret' => 0, 'msg' => 'ok'], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('腾讯广告点击监测链接数据接收失败：' . $e->getMessage());
            return response(['ret' => 1, 'msg' => 'no-4'], 200);
        }
    }
}


