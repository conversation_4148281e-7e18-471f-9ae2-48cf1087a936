<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property WwLink    $wwLinkInfo

 */
class FormData extends Model
{
    use HasDateTimeFormatter;

    protected $table = 'form_data';

    public function adminInfo(): BelongsTo
    {
        return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
    }

    public function wwLinkInfo(): BelongsTo
    {
        return $this->BelongsTo(WwLink::class, 'ww_link_id', 'id')->withTrashed();
    }
}
