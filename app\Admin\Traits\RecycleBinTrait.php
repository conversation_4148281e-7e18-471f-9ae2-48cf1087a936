<?php

namespace App\Admin\Traits;

use Dcat\Admin\Widgets\Modal;

/**
 * 回收站
 */
trait RecycleBinTrait
{
    /**
     * 回收站工具按钮
     *
     * @param string $title 按钮显示的文字
     * @return Modal 返回一个模态框组件
     */
    protected function recycleBinTool(string $title = '回收站'): Modal
    {
        return Modal::make()
            ->xl()  // 设置模态框为超大尺寸
            ->title($title)  // 设置模态框标题
            ->body($this->getRecycleBinContent())  // 改为调用方法获取内容
            ->button("<button class='btn btn-warning'><i class='feather icon-trash-2'></i>&nbsp;&nbsp;$title</button>");  // 设置触发按钮的样式和文字
    }

    /**
     * 获取回收站内容
     */
    private function getRecycleBinContent(): string
    {
        // 获取模型类
        $modelClass = $this->getRecycleBinModelClass();

        // 获取软删除的数据（只取前10条）
        $trashedData = $modelClass::onlyTrashed()->limit(10)->get();

        if ($trashedData->isEmpty()) {
            return '<div class="alert alert-info">
                <i class="feather icon-info"></i> 回收站为空
            </div>';
        }

        // 构建简单的表格
        $html = '<div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>userID</th>
                        <th>name</th>
                        <th>删除时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>';

        foreach ($trashedData as $item) {
            $html .= '<tr>
                <td>' . $item->id . '</td>
                <td>' . $item->user_id . '</td>
                <td>' . $item->name . '</td>
                <td>' . $item->deleted_at->format('Y-m-d H:i:s') . '</td>
                <td>
                    <button class="btn btn-sm btn-success">恢复</button>
                    <button class="btn btn-sm btn-danger">永久删除</button>
                </td>
            </tr>';
        }

        $html .= '</tbody></table></div>';

        return $html;
    }

    /**
     * 获取回收站模型类（子类必须实现这个方法）
     */
    abstract protected function getRecycleBinModelClass(): string;
}