<?php

namespace App\Admin\Forms\WwLink;

use App\Models\AdminSubUser;
use App\Models\ShieldPolicy;
use App\Models\TencentAdAccount;
use App\Models\WwLink;
use App\Models\WwTpl;
use App\Models\WwUsersGroup;
use App\Services\Tools\LogService;
use Dcat\Admin\Admin;
use Dcat\Admin\Widgets\Form;
use Dcat\EasyExcel\Excel;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class WwLinkConfigImportForm extends Form
{
    const TENCENT_TYPE = [
        '预约' => 'RESERVATION',
        '下单' => 'COMPLETE_ORDER',
        '扫码' => 'SCANCODE_WX',
        '添加微信成功' => 'ADD_WECHAT',
        '加企业微信客服' => 'SCANCODE',
        '低价课首次参课' => 'PARTICIPATED',
    ];
        //预约,下单,扫码,添加微信成功,加企业微信客服,低价课首次参课
    public function handle(array $input)
    {
        LogService::inputLog('Tools','ww_link-链接列表-批量导入', $input, Admin::user()->id, Admin::user()->username);

        //当前登录ID
        /** @var Admin::User $AdminUser */
        $AdminUser = Admin::user();
        $AdminUserId = $AdminUser->id;
        $AdminUserParentId = $AdminUser->parent_id;
        // 获取上传的文件路径
        $filePath = storage_path('app/public' . $input['import_file']);
        // 如果用的是maatwebsite/excel或者其他, 把读取数据这一步改改就好了
        // 读取excel文件
        $data = Excel::import($filePath)->toArray();
        if (empty($data) || empty($data['Sheet1'])) {
            return $this->response()->alert()->error('提示')->detail('导入数据不能为空');
        }
        //读取Sheet1表
        $data = $data['Sheet1'];
        //判断每列是否为空
        foreach ($data as $k => $v) {
            if (!$v['链接启用开关'] || !isset($v['链接启用开关'])) {
                return $this->response()->alert()->error('提示')->detail('链接启用开关不能为空');
            }
            if (!$v['账户ID'] || !isset($v['账户ID'])) {
                return $this->response()->alert()->error('提示')->detail('账户ID不能为空');
            }
            if (!$v['添加成功上报行为'] || !isset($v['添加成功上报行为'])) {
                return $this->response()->alert()->error('提示')->detail('添加成功上报行为不能为空');
            }
            if (!$v['投放页面ID'] || !isset($v['投放页面ID'])) {
                return $this->response()->alert()->error('提示')->detail('投放页面不能为空');
            }
            if (!$v['投放销售分组'] || !isset($v['投放销售分组'])) {
                if (!$v['投放页面ID'] == -1) {
                    return $this->response()->alert()->error('提示')->detail('投放销售分组不能为空');
                }
            }
            if (!$v['转化'] || !isset($v['转化'])) {
                return $this->response()->alert()->error('提示')->detail('转化不能为空');
            }
            if (!$v['上报'] || !isset($v['上报'])) {
                return $this->response()->alert()->error('提示')->detail('上报不能为空');
            }
            if ($v['屏蔽规则ID'] === ''  || !isset($v['屏蔽规则ID'])) {
                return $this->response()->alert()->error('提示')->detail('屏蔽规则ID不能为空');
            }
            if (!$v['是否开启屏蔽'] || !isset($v['是否开启屏蔽'])) {
                return $this->response()->alert()->error('提示')->detail('是否开启屏蔽不能为空');
            }
        }
        //提取表格的账户di，能否重复创建链接。如果不能，判断表格里的账户id是否存在于查询结果里，如果有提示不能重复创建链接
        $accountId = array_column($data, '账户ID');
        $repeatWwLink = $AdminUser->repeat_ww_link;
        if ($repeatWwLink == 0) {
            $checkRepeat  = WwLink::query()
                ->where('admin_uid', $AdminUserId)
                ->whereIn('account_id', $accountId)
                ->pluck('account_id')->toArray();
            if ($checkRepeat){
                return $this->response()
                    ->alert()
                    ->error('提示')
                    ->detail('不能重复创建投放链接');
            }
        }

        //查询是否有错误的账户ID
        $queryAccountId = TencentAdAccount::query()
            ->where('admin_uid', $AdminUserId)
            ->whereIn('account_id',$accountId)
            ->pluck('account_id')
            ->toArray();
        if (!empty(array_diff($accountId, $queryAccountId))) {
            return $this->response()->alert()->error('提示')->detail('导入数据中有错误账户ID<br>'.implode('<br>', array_diff($accountId, $queryAccountId)));
        }


        //查询是否有错误分组ID
        $groupId = array_column($data, '投放销售分组');

        $groupIdFilter = array_filter($groupId, function ($value) {
            return $value != ""; // 过滤空
        });
        $queryGroupId = [];
        if ($AdminUserParentId != 0){
            $subUserAuthGroupIds = AdminSubUser::query()
                ->where('admin_uid', $AdminUserId)
                ->value('ww_user_group_ids');
            $subUserAuthGroupIds = json_decode($subUserAuthGroupIds, true);
            $selfGroupIds = WwUsersGroup::query()
                ->where('admin_uid', $AdminUserId)
                ->where('type',1)
                ->pluck('id')
                ->toArray();
            $subUserAuthGroupIds = array_merge($subUserAuthGroupIds, $selfGroupIds);
            if (empty($subUserAuthGroupIds)) {
                return $this->response()->alert()->error('提示')->detail('导入数据中投放分组ID未授权<br>');
            }
            if (!empty(array_diff($groupIdFilter, $subUserAuthGroupIds))) {
                return $this->response()->alert()->error('提示')->detail('导入数据中有错误分组ID<br>'.implode('<br>', array_diff($groupIdFilter, $subUserAuthGroupIds)));
            }
            $queryGroupId = WwUsersGroup::query()->whereIn('id', $subUserAuthGroupIds)->pluck('id')->toArray();
        }
        if ($AdminUserParentId == 0){
            $queryGroupId = WwUsersGroup::query()
                ->where('admin_uid', $AdminUserId)
                ->whereIn('id',$groupIdFilter)
                ->where('type',1)
                ->pluck('id')
                ->toArray();

        }
        if (!empty(array_diff($groupIdFilter, $queryGroupId))) {
            return $this->response()->alert()->error('提示')->detail('导入数据中有错误分组ID<br>'.implode('<br>', array_diff($groupIdFilter, $queryGroupId)));
        }




        //是否有错误投放页面
        $tplId = array_column($data, '投放页面ID');

        $tplIdFilter = array_filter($tplId, function ($value) {
            return $value != -1; // 过滤掉 -1
        });
        $queryTplId = [];
        if ($AdminUserParentId != 0){
            $subUserAuthTplIds = AdminSubUser::query()
                ->where('admin_uid', $AdminUserId)
                ->value('tpl_ids');
            $subUserAuthTplIds = json_decode($subUserAuthTplIds, true);
            if (!$subUserAuthTplIds){
                return $this->response()->alert()->error('提示')->detail('导入数据中投放页面ID未授权<br>');
            }
            if (!empty(array_diff($tplIdFilter, $subUserAuthTplIds))) {
                return $this->response()->alert()->error('提示')->detail('导入数据中有错误页面ID<br>'.implode('<br>', array_diff($tplIdFilter, $subUserAuthTplIds)));
            }
            $queryTplId = WwTpl::query()->whereIn('id', $subUserAuthTplIds)->pluck('id')->toArray();
        }
        if ($AdminUserParentId == 0){
            $queryTplId = WwTpl::query()
                ->where('admin_uid', $AdminUserId)
                ->whereIn('id', $tplIdFilter)
                ->pluck('id')
                ->toArray();
        }
        if (!empty(array_diff($tplIdFilter, $queryTplId))) {
            return $this->response()->alert()->error('提示')->detail('导入数据中有错误投放页面ID<br>'.implode('<br>', array_diff($tplIdFilter, $queryTplId)));
        }


        $shieldPolicyId = array_column($data, '屏蔽规则ID');
        //查屏蔽规则
        $shieldPolicyIdFilter = array_filter($shieldPolicyId, function ($value) {
            return $value != 0; // 过滤掉 0 和 '0'
        });
        $queryShieldPolicyId = ShieldPolicy::query()
            ->whereIn('id', $shieldPolicyIdFilter)
            ->whereIn('admin_uid', AdminSubUser::getALLAdminUids($AdminUserId))
            ->pluck('id')
            ->toArray();

        if (!empty(array_diff($shieldPolicyIdFilter, $queryShieldPolicyId))) {
            return $this->response()->alert()->error('提示')->detail('导入数据中有错误屏蔽规则ID<br>'.implode('<br>',array_diff($shieldPolicyIdFilter, $queryShieldPolicyId)));
        }
        //更改键名，新的数组。用来新增
        $newData = [];

        foreach ($data as $item) {
            if ($item['账户ID']){
                if (!isset(self::TENCENT_TYPE[$item['添加成功上报行为']])) {
                    return $this->response()->alert()->error('提示')->detail('添加成功上报行为不存在');
                }
                if (!filter_var($item['上报'], FILTER_VALIDATE_INT, array("options" => array("min_range" => 1))) ||
                    !filter_var($item['转化'], FILTER_VALIDATE_INT, array("options" => array("min_range" => 1)))) {
                    return $this->response()->alert()->error('提示')->detail('转化或者上报存在非正整数');
                }
                $newData[] = [
                    'admin_uid' => $AdminUserId,
                    'media_type' => $input['media_type'],
                    'shield_policy_id' => $item['屏蔽规则ID'],
                    'is_open' => ($item['链接启用开关'] === '是') ? 1 : 0,
                    'account_id' => $item['账户ID'],
                    'add_succeeded_action_type' => '["'.self::TENCENT_TYPE[$item['添加成功上报行为']].'"]',
                    'tpl_type' => ($item['投放页面ID'] === -1) ? 2 : 1,
                    'tpl_id' => $item['投放页面ID'],
                    'tpl_link' => ($item['投放页面ID'] === -1) ? $item['自建链接'] : '',
                    'need_shield' => ($item['是否开启屏蔽'] === '是') ? 1 : 0,
                    'ww_user_group_id' => ($item['投放页面ID'] === -1) ? 0 : $item['投放销售分组'],
                    'conv_count' => $item['转化'],
                    'tads_count' => $item['上报'],
                    'remark' => $item['备注'],
                    'created_at' => date('Y-m-d H:i:s', time()),
                    'updated_at' => date('Y-m-d H:i:s', time()),
                ];
            }
        }
        DB::beginTransaction();
        foreach ($newData as $item) {
            //循环插入
            try {
                $resultId = WwLink::query()->insertGetId($item);
                DB::commit();
            }catch (\Exception $e){
                DB::rollBack();
                Log::error('插入失败: ' . $e->getMessage());
                return $this->response()->alert()->error('错误')->detail('创建投放链接失败 错误代码:-2');//插入失败
            }
            //查询添加结果
            $lastData = WwLink::query()->find($resultId);
            //生成AD_ID
            if ($lastData->media_type !== '腾讯广告' && $lastData->media_type !== 'OPPO广告' && $lastData->media_type !== 'VIVO广告' && $lastData->media_type !== '优酷广告') {
                $lastData->account_id = $lastData->id + ********;
            }
            if (!WwLink::query()->where("link_ad_id", $lastData->account_id)->exists()) {
                $lastData->link_ad_id = $lastData->account_id;
            } else {
                $lastData->link_ad_id = $lastData->account_id . "_" . WwLink::withTrashed()->where("account_id", $lastData->account_id)->count();
            }
            $lastData->save();
            DB::commit();
        }
        return $this->response()->alert()->success('提示')->detail('导入成功')->refresh();
    }

    /**
     * Build a form here.
     */
    public function form()
    {


        // 禁用重置表单按钮
        $this->disableResetButton();
        $this->display('media_type')
            ->options([
                '腾讯广告' => '腾讯广告',
            ])
            ->default("腾讯广告")
            ->required()
            ->help('目前仅支持腾讯广告。');
        // 文件上传
        $this->file('import_file', '文件')
            ->disk('public')
            ->accept('xls,xlsx')
            ->uniqueName()
            ->autoUpload()
            ->required()
            ->move('/import')
            ->help('支持xls,xlsx <a href="https://smart-ark.oss-cn-beijing.aliyuncs.com/import_template/智投方舟-企微投放链接导入模板.xlsx">下载模板</a>');
    }

}
