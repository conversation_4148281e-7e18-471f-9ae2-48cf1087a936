<?php

namespace App\Admin\Forms\AdminActionLog;

use Admin;
use App\Models\AdminUser;
use Dcat\Admin\Widgets\Form;
use Illuminate\Support\Facades\Log;

class QueryAdminUserActionLogTableForm extends Form
{
    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        $adminId = $input['admin_uid'];
        if (empty($adminId)) {
            return $this->response()->error('请选择用户');
        }
        
        // 查询用户分配的表ID
        $userTableId = AdminUser::query()
            ->where('id', $adminId)
            ->value('action_log_table');
            
        if ($userTableId === null) {
            Log::info("查询用户记录表失败，用户ID: {$adminId}，返回的table_id: {$userTableId}");
            return $this->response()->error('该用户未分配操作日志表');
        }
        
        $url = admin_url("action_logs");
        $param = ['table_id' => $userTableId];

        return $this
            ->response()
            ->success("用户所在表：操作日志表{$userTableId}，正在跳转...")
            ->redirect($url . "?" . http_build_query($param));
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        $this->select("admin_uid", '用户')->options(AdminUser::query()->orderBy('id')->pluck("username", "id"))->required();
    }

    /**
     * The data of the form.
     *
     * @return array
     */
    public function default()
    {
        return [
            'table_id' => request('table_id', ''),
        ];
    }
}
