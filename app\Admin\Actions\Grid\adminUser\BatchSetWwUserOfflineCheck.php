<?php

namespace App\Admin\Actions\Grid\adminUser;

use App\Admin\Extensions\Grid\BatchActionPlus;
use App\Admin\Forms\Admin\BatchSetWwUserOfflineCheckForm;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\BatchAction;
use Dcat\Admin\Widgets\Modal;
use Illuminate\Http\Request;

class BatchSetWwUserOfflineCheck extends BatchActionPlus
{
    /**
     * @return string
     */
    public $title = '<button class="btn btn-primary ww_user_batch_btn">设置销售下线检查</button>';
    public $buttonText = '<button class="btn btn-primary ww_user_batch_btn">设置销售下线检查</button>';

    public function form():BatchSetWwUserOfflineCheckForm
    {
        // 实例化表单类
        return BatchSetWwUserOfflineCheckForm::make();
    }
}
