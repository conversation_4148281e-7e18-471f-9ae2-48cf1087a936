<?php

namespace App\Admin\Actions\Grid\wwUser;

use App\Admin\Extensions\Grid\BatchActionPlus;
use App\Admin\Forms\WwUser\BatchSetAddMethodForm;

class BatchSetAddMethod extends BatchActionPlus
{
    /**
     * @return string
     */
    public $title = '加粉模式';
    
    public $buttonText = '<button class="btn btn-primary ww_user_batch_btn" ><i class="feather icon-repeat"></i><span class="selected"></span>加粉模式</button>';



    public function form(): BatchSetAddMethodForm
    {
        // 表单渲染时
        return BatchSetAddMethodForm::make();
    }
}
