<?php

namespace App\Admin\Actions\Grid\wwUser;

use App\Models\WwUser;
use App\Services\Corp\WwCorpApiService;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Traits\HasPermissions;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class CreateCusAcqLink extends RowAction
{
    /**
     * @return string
     */
    protected $title = '<i title="刷新获客助手" class="feather icon-refresh-ccw" style="">&nbsp;&nbsp;</i>';

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle(Request $request)
    {
        $id = $this->getKey();
        $wwUser = WwUser::query()->find($id);
        if (!$wwUser) {
            return $this->response()->error('销售不存在');
        }
        if ($wwUser->type != 1) {
            return $this->response()->error('无权限操作');
        }

        if (!$wwUser->cus_acq_link_status) { //判断有没有获客助手ID 没有则创建
            //先删除旧的获客助手链接
            $delResp = WwCorpApiService::delete_link($wwUser->corpInfo, $wwUser);
            if (isset($delResp['errcode']) && $delResp['errcode'] != 0) {
                Log::info('删除旧的获客助手链接失败-1：' . json_encode($delResp));
                return $this->response()->error('刷新失败-1')->refresh();
            }

            $linkData = WwCorpApiService::create_link($wwUser->corpInfo, $wwUser);
            if (isset($linkData['errcode']) && $linkData['errcode'] != 0) { //创建失败
                Log::info('创建新的获客助手失败-2：' . json_encode($linkData));
                $wwUser->cus_acq_link_status = 0;
                $wwUser->cus_acq_link_status_message = $linkData['errmsg'] ?? '';
                $wwUser->save();
                return $this->response()->error('刷新失败-2')->refresh();
            } else { //创建成功
                $wwUser->cus_acq_link_status = 1;
                $wwUser->cus_acq_link_status_message = $linkData['errmsg'] ?? '';
                $wwUser->cus_acq_link_id = $linkData['link']['link_id'];
                $wwUser->cus_acq_link_name = $linkData['link']['link_name'];
                $wwUser->cus_acq_link_url = $linkData['link']['url'];
                $wwUser->cus_acq_link_skip_verify = 1;
                $wwUser->save();
            }
        }

        return $this->response()->success('刷新成功')->refresh();
    }

    /**
     * @return string|array|void
     */
    public function confirm()
    {
        return ['确认刷新获客助手?'];
    }

    /**
     * @param Model|Authenticatable|HasPermissions|null $user
     *
     * @return bool
     */
    protected function authorize($user): bool
    {
        return true;
    }

    /**
     * @return array
     */
    protected function parameters()
    {
        return [
            //	         'id' => $this->row->id
        ];
    }
}
