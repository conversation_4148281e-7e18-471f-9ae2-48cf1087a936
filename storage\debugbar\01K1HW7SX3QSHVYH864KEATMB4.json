{"__meta": {"id": "01K1HW7SX3QSHVYH864KEATMB4", "datetime": "2025-08-01 11:53:59", "utime": **********.972585, "method": "POST", "uri": "/ztfz/auth/login", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.054058, "end": **********.972603, "duration": 0.9185450077056885, "duration_str": "919ms", "measures": [{"label": "Booting", "start": **********.054058, "relative_start": 0, "end": **********.398029, "relative_end": **********.398029, "duration": 0.*****************, "duration_str": "344ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.398045, "relative_start": 0.*****************, "end": **********.972605, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "575ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.429061, "relative_start": 0.****************, "end": **********.433603, "relative_end": **********.433603, "duration": 0.004542112350463867, "duration_str": "4.54ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.20", "PHP Version": "8.0.2", "Environment": "local", "Debug Mode": "Enabled", "URL": "sen.test", "Timezone": "PRC", "Locale": "zh_CN"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 4, "nb_statements": 3, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.33536, "accumulated_duration_str": "335ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 51}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.480429, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "OpRecord.php:51", "source": {"index": 10, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FHttp%2FMiddleware%2FOpRecord.php&line=51", "ajax": false, "filename": "OpRecord.php", "line": "51"}, "connection": "wind_rich", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "insert into `wr_operation_logs` (`admin_uid`, `user_name`, `path`, `method`, `ip`, `input`, `updated_at`, `created_at`) values (0, '', 'ztfz/auth/login', 'POST', '127.0.0.1', '{\\\"_token\\\":\\\"Ia65Wa6468qgGAUSdE8rbvZeydWIRojrJeZ6TDDr\\\",\\\"username\\\":\\\"ZtFzSuperAdminPro\\\",\\\"password\\\":\\\"ZTFZ2025-dF4-yR0~yA\\\"}', '2025-08-01 11:53:59', '2025-08-01 11:53:59')", "type": "query", "params": [], "bindings": [0, "", "ztfz/auth/login", "POST", "127.0.0.1", "{\"_token\":\"Ia65Wa6468qgGAUSdE8rbvZeydWIRojrJeZ6TDDr\",\"username\":\"ZtFzSuperAdminPro\",\"password\":\"ZTFZ2025-dF4-yR0~yA\"}", "2025-08-01 11:53:59", "2025-08-01 11:53:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 51}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.490607, "duration": 0.*****************, "duration_str": "246ms", "memory": 0, "memory_str": null, "filename": "OpRecord.php:51", "source": {"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FHttp%2FMiddleware%2FOpRecord.php&line=51", "ajax": false, "filename": "OpRecord.php", "line": "51"}, "connection": "wind_rich", "explain": null, "start_percent": 0, "width_percent": 73.497}, {"sql": "select * from `wr_admin_users` where `username` = 'ZtFzSuperAdminPro' limit 1", "type": "query", "params": [], "bindings": ["ZtFzSuperAdminPro"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Admin/Controllers/AuthController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\AuthController.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.764646, "duration": 0.04399, "duration_str": "43.99ms", "memory": 0, "memory_str": null, "filename": "AuthController.php:34", "source": {"index": 15, "namespace": null, "name": "app/Admin/Controllers/AuthController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\AuthController.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FAuthController.php&line=34", "ajax": false, "filename": "AuthController.php", "line": "34"}, "connection": "wind_rich", "explain": null, "start_percent": 73.497, "width_percent": 13.117}, {"sql": "select * from `wr_admin_users` where `username` = 'ZtFzSuperAdminPro' limit 1", "type": "query", "params": [], "bindings": ["ZtFzSuperAdminPro"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 381}, {"index": 17, "namespace": null, "name": "app/Admin/Controllers/AuthController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\AuthController.php", "line": 55}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.815071, "duration": 0.04489, "duration_str": "44.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:139", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=139", "ajax": false, "filename": "EloquentUserProvider.php", "line": "139"}, "connection": "wind_rich", "explain": null, "start_percent": 86.614, "width_percent": 13.386}]}, "models": {"data": {"App\\Models\\AdminUser": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminUser.php&line=1", "ajax": false, "filename": "AdminUser.php", "line": "?"}}, "Dcat\\Admin\\Models\\Administrator": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FAdministrator.php&line=1", "ajax": false, "filename": "Administrator.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://sen.test/ztfz/auth/login", "action_name": "dcat.admin.", "controller_action": "App\\Admin\\Controllers\\AuthController@postLogin", "uri": "POST ztfz/auth/login", "controller": "App\\Admin\\Controllers\\AuthController@postLogin<a href=\"phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FAuthController.php&line=23\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/ztfz", "file": "<a href=\"phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FAuthController.php&line=23\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Admin/Controllers/AuthController.php:23-62</a>", "middleware": "admin.app:admin, web, admin", "duration": "922ms", "peak_memory": "2MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2108395034 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2108395034\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-411024471 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Ia65Wa6468qgGAUSdE8rbvZeydWIRojrJeZ6TDDr</span>\"\n  \"<span class=sf-dump-key>username</span>\" => \"<span class=sf-dump-str title=\"17 characters\">ZtFzSuperAdminPro</span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-411024471\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2138633544 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IklJQXd6dkNOWWttSGYzZ2lnaVNCU0E9PSIsInZhbHVlIjoiM0s2ZHJPWGxpMDdUenVhekkvSElGM2xwUTVocDZNV1k0bnRZVjlZcitDYlFxZGlTUy8weHZUKzJEdlliUEdvekZOc244KzhwWXRrV0ptclkzNE8xTjV1Q1VRMitFTWNtZHVrcE5YcTRJamZlTUhtekpqOFNOUjY0cStqK3B5VDUiLCJtYWMiOiJlNTVkMzM0YWViMWY4ZWIzYzBiZWUwOWZkM2Y4YzEwNGZlZjQ2OTQ4YzZlZDk2ODU2OTU0NjYzNTRiNjQ4OTFjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjVjSTNVcTEvaEdQTTJkSENLSVNnOGc9PSIsInZhbHVlIjoicmtuTXZUQjVzNXlmVEFQM09pdnRpbGk4NWdqMTJrMlp3dDlzcjBlR0FTUllibE1KOTNwTWpuVStGZTg3N2pyd1pleVUveitDdmhrNDRnN1hsbEY2dVliM3l4Q1BUWGdTTzBJZHpqTVdwTUVRUC9XQ2V6K0ZqWmVmRFUzdGVqS0MiLCJtYWMiOiI0ZDUxNjQ3OWE5MDU5N2E3Mzk0MmI2YzZmMWJmY2EyMzk1MTBjZTc4ZGRiNzE5MzAzYjkwNjVmMGU1Y2MyMjBiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">zh-CN,zh;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">http://sen.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">151</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">sen.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2138633544\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1305905972 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Ia65Wa6468qgGAUSdE8rbvZeydWIRojrJeZ6TDDr</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">uyEAzLeesCBRScai53ESzn27IqQX3yEsZcU5V6gY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1305905972\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1769289568 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Aug 2025 03:53:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1769289568\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2029047378 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>prev</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://sen.test/ztfz/auth/login</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">admin.prev.url</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0esJyfQEGYXbNzItiQn8t75dzSvXZmiKqhcwIHBs</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://sen.test/ztfz/auth/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2029047378\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://sen.test/ztfz/auth/login", "action_name": "dcat.admin.", "controller_action": "App\\Admin\\Controllers\\AuthController@postLogin"}, "badge": null}}