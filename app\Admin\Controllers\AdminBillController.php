<?php

    namespace App\Admin\Controllers;

    use App\Admin\Forms\Admin\Recharge;
    use App\Admin\Repositories\AdminBill;
    use App\Admin\Repositories\AdminBillNew;
    use App\Admin\Repositories\AdminUser;
    use App\Models\AdminBill as AdminBillModel;
    use Dcat\Admin\Admin;
    use Dcat\Admin\Form;
    use Dcat\Admin\Grid;
    use Dcat\Admin\Show;
    use Dcat\Admin\Http\Controllers\AdminController;
    use Dcat\Admin\Widgets\Modal;

    class AdminBillController extends AdminController
    {
        /**
         * Make a grid builder.
         *
         * @return Grid
         */
        protected function grid()
        {
            return Grid::make(new AdminBillNew(['adminInfo']), function (Grid $grid) {
                $modal = Modal::make()
                    ->xl()
                    ->title('充值')
                    ->body(Recharge::make())
                    ->button('<button class="btn btn-primary disable-outline float-right" style="color: white;margin-left: 5px"><i class="feather icon-plus-square"></i>&nbsp充值</button>');
                $grid->tools($modal);
                $grid->disableActions();
                $grid->disableBatchActions();
                $grid->disableCreateButton();
                $grid->paginate(100);
                $grid->export()->rows(function ($rows) {
                    foreach ($rows as &$row) {
                        $row['adminInfo.username'] = $row->adminInfo ? $row->adminInfo->username : "";
                        $row['internal_detail'] = round(($row->internal_shield_price / 100), 2) . '元';
                        $row['external_detail'] = round(($row->external_shield_price / 100), 2) . '元';
                        $row['total_detail'] = (
                                round(($row->internal_shield_price / 100), 2) +
                                round(($row->internal_no_shield_price / 100), 2) +
                                round(($row->external_shield_price / 100), 2) +
                                round(($row->external_no_shield_price / 100), 2)
                            ) . '元';
                    }
                    return $rows;
                })->chunkSize(1000);

                $grid->model()->orderByDesc("date")->orderByDesc("id");
                $grid->column('id')->sortable();
                $grid->column('adminInfo.username', '管理员')->display(function ($value) {
                    $param['date'] = $_GET['date'] ?? "";
                    $param['adminInfo']['username'] = $value;
                    $link = env('APP_URL') .'/ztfz/nE2QhUbom6XyzKEFU6E9?' . http_build_query($param);
                    $value = mb_substr($value, 0, 10);
                    return "<a href='" . $link . "'>" . $value . "</a>";
                });
                $grid->column('date');
                $grid->column('internal_detail', '智投链接')->display(function () {
                    $subtotal = round($this->internal_shield_price / 100, 2) + round($this->internal_no_shield_price / 100, 2);
                    return '<table class="table table-bordered" style="margin-bottom:0;">
                                <tr>
                                    <th>类型</th><th>单价(元)</th><th>进粉量</th><th>金额(元)</th>
                                </tr>
                                <tr>
                                    <td>屏蔽</td>
                                    <td>' . round($this->internal_shield_unit_price / 100, 2) . '</td>
                                    <td>' . $this->internal_shield_count . '</td>
                                    <td>' . round($this->internal_shield_price / 100, 2) . '</td>
                                </tr>
                                <tr>
                                    <td>非屏蔽</td>
                                    <td>' . round($this->internal_no_shield_unit_price / 100, 2) . '</td>
                                    <td>' . $this->internal_no_shield_count . '</td>
                                    <td>' . round($this->internal_no_shield_price / 100, 2) . '</td>
                                </tr>
                                <tr>
                                    <td colspan="3" style="text-align:right;">小计</td>
                                    <td>' . $subtotal . '</td>
                                </tr>
                            </table>';
                });

                $grid->column('external_detail', '外部链接')->display(function () {
                    $subtotal = round($this->external_shield_price / 100, 2) + round($this->external_no_shield_price / 100, 2);
                    return '<table class="table table-bordered" style="margin-bottom:0;">
                                <tr>
                                    <th>类型</th><th>单价(元)</th><th>点击量</th><th>金额(元)</th>
                                </tr>
                                <tr>
                                    <td>屏蔽</td>
                                    <td>' . round($this->external_shield_unit_price / 100, 2) . '</td>
                                    <td>' . $this->external_shield_count . '</td>
                                    <td>' . round($this->external_shield_price / 100, 2) . '</td>
                                </tr>
                                <tr>
                                    <td>非屏蔽</td>
                                    <td>' . round($this->external_no_shield_unit_price / 100, 2) . '</td>
                                    <td>' . $this->external_no_shield_count . '</td>
                                    <td>' . round($this->external_no_shield_price / 100, 2) . '</td>
                                </tr>
                                <tr>
                                    <td colspan="3" style="text-align:right;">小计</td>
                                    <td>' . $subtotal . '</td>
                                </tr>
                            </table>';
                });

                $grid->column('total_detail', '总计')->display(function () {
                    $internalSubtotal = round($this->internal_shield_price / 100, 2) + round($this->internal_no_shield_price / 100, 2);
                    $externalSubtotal = round($this->external_shield_price / 100, 2) + round($this->external_no_shield_price / 100, 2);
                    $total = $internalSubtotal + $externalSubtotal;
                    return '<span style="font-weight:bold;color:#21b978;">' . $total . ' 元</span>';
                });
                $grid->column('updated_at')->sortable();

                $grid->filter(function (Grid\Filter $filter) {
                    $filter->expand();
                    $filter->panel();
                    $filter->like('adminInfo.username', "管理员")->width(2);
                    $filter->between("date")->date()->width(2);

                });

                $grid->footer(function ($collection) {
                    $internalTotal = $collection->sum(function ($item) {
                        return round($item->internal_shield_price / 100, 2) + round($item->internal_no_shield_price / 100, 2);
                    });
                    $externalTotal = $collection->sum(function ($item) {
                        return round($item->external_shield_price / 100, 2) + round($item->external_no_shield_price / 100, 2);
                    });

                    return '<div style="font-weight:bold;">
                            内部总计：' . $internalTotal . ' 元&nbsp;&nbsp; +
                            外部总计：' . $externalTotal . ' 元
                             = 总计：' . ($internalTotal + $externalTotal) . ' 元
                        </div>';
                });
            });
        }

        // 备份
        protected function gridBackup()
        {
            return Grid::make(new AdminBill(['adminInfo']), function (Grid $grid) {
                $modal = Modal::make()
                    ->xl()
                    ->title('充值')
                    ->body(Recharge::make())
                    ->button('<button class="btn btn-primary disable-outline float-right" style="color: white;margin-left: 5px"><i class="feather icon-plus-square"></i>&nbsp充值</button>');
                $grid->tools($modal);
                $grid->disableActions();
                $grid->disableBatchActions();
                $grid->disableCreateButton();
                $grid->paginate(100);
                $grid->export()->rows(function ($rows) {
                    foreach ($rows as &$row) {
                        $row['adminInfo.username'] = $row->adminInfo ? $row->adminInfo->username : "";
                        $row['sum_ad_cost'] = round($row->sum_ad_cost/100,2)."元";
                        $row['ad_price_rate'] = ($row->ad_price_rate * 100) . "%";
                        $row['ad_price'] = round($row->ad_price/100,2)."元";
                        $row['nos_price_unit'] = round($row->nos_price_unit/100,2)."元";
                        $row['nos_price'] = round($row->nos_price/100,2)."元";
                        $row['sum_price'] = round($row->sum_price/100,2)."元";
                        $row['add_price'] = round($row->add_price/100,2)."元";
                        $row['balance'] = round($row->balance/100,2)."元";
                    }
                    return $rows;
                })->chunkSize(1000);

                $grid->model()->orderByDesc("date")->orderByDesc("id");
                $grid->column('id')->sortable();
                $grid->column('adminInfo.username', '管理员')->display(function ($value) {
                    $param['date'] = $_GET['date'] ?? "";
                    $param['adminInfo']['username'] = $value;
                    $link = env('APP_URL') .'/ztfz/nE2QhUbom6XyzKEFU6E9?' . http_build_query($param);
                    $value = mb_substr($value, 0, 5);
                    return "<a href='" . $link . "'>" . $value . "</a>";
                });
                $grid->column('date');
                $grid->column('sum_ad_cost')->display(function ($value) {
                    return "¥" . round(($value / 1000000), 2) . "万";
                })->sortable();
                $grid->column('ad_price_rate', '点位')->display(function ($value) {
                    return $value * 100 . "%";
                })->sortable();
                $grid->column('ad_price')->display(function ($value) {
                    return "¥" . round(($value / 100), 4);
                })->sortable();
                $grid->column('sum_nos_view_count', "非屏点击量 (千)")->display(function ($value) {
                    return round(($value / 1000), 2) . " k";
                })->sortable();
                $grid->column('nos_price_unit', "非屏点击单价")->display(function ($value) {
                    return round(($value / 100), 4);
                })->sortable();
                $grid->column('nos_price', "非屏计费")->display(function ($value) {
                    return round(($value / 100), 4);
                })->sortable();
                $grid->column('sum_price', "今日合计")->display(function ($value) {
                    return round(($value / 100), 4);
                })->sortable();
                $grid->column('add_price')->display(function ($value) {
                    return "¥" . round(($value / 100), 4);
                })->sortable();
                $grid->column('balance')->display(function ($value) {
                    $value = round(($value / 100), 4);
                    if ($value >= 2000) {
                        $color = '#21b978';
                    } elseif ($value >= 0) {
                        $color = '#edc30e';
                    } else {
                        $color = '#ea5455';
                    }
                    return '<span class="label" style="background:' . $color . '">￥' . round($value, 2) . '元</span>';
                })->sortable();
                $grid->column('sum_all_ad_cost', '全部消耗')->display(function ($value) {
                    return "¥" . round(($value / 1000000), 2) . "万";
                })->help("需要屏蔽账户与不需要屏蔽账户一起计算");
                // $grid->column('sum_ad_view_count');
                // $grid->column('sum_ad_valid_click_count');
                // $grid->column('sum_ad_conversions_count');
                $grid->column('sum_wr_view_count')->sortable();
                // $grid->column('wr_price_unit')->display(function ($value) {
                //     return "¥" . round(($value / 100), 4);
                // });
                // $grid->column('wr_price')->display(function ($value) {
                //     return "¥" . round(($value / 100), 4);
                // });
                $grid->column('updated_at')->sortable();

                $grid->filter(function (Grid\Filter $filter) {
                    $filter->expand();
                    $filter->panel();
                    $filter->like('adminInfo.username', "管理员")->width(2);
                    $filter->between("date")->date()->width(4);

                });
            });
        }

        /**
         * Make a show builder.
         *
         * @param mixed $id
         *
         * @return Show
         */
        protected function detail($id)
        {
            return Show::make($id, new AdminBill(), function (Show $show) {
                $show->field('id');
                $show->field('admin_uid');
                $show->field('sum_ad_cost');
                $show->field('sum_ad_view_count');
                $show->field('sum_ad_valid_click_count');
                $show->field('sum_ad_conversions_count');
                $show->field('ad_price_rate');
                $show->field('ad_price');
                $show->field('sum_wr_view_count');
                $show->field('wr_price_unit');
                $show->field('wr_price');
                $show->field('add_price');
                $show->field('balance');
                $show->field('date');
                $show->field('created_at');
                $show->field('updated_at');
            });
        }

        /**
         * Make a form builder.
         *
         * @return Form
         */
        protected function form()
        {
            return Form::make(new AdminBill(), function (Form $form) {
                $form->display('id');
                $form->text('admin_uid');
                $form->text('sum_ad_cost');
                $form->text('sum_ad_view_count');
                $form->text('sum_ad_valid_click_count');
                $form->text('sum_ad_conversions_count');
                $form->text('ad_price_rate');
                $form->text('ad_price');
                $form->text('sum_wr_view_count');
                $form->text('wr_price_unit');
                $form->text('wr_price');
                $form->text('add_price');
                $form->text('balance');
                $form->text('date');

                $form->display('created_at');
                $form->display('updated_at');
            });
        }
    }
