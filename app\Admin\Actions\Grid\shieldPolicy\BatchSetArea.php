<?php

namespace App\Admin\Actions\Grid\shieldPolicy;

use App\Admin\Extensions\Grid\BatchActionPlus;
use App\Admin\Forms\ShieldPolicy\BatchSetAreaForm;

class BatchSetArea extends BatchActionPlus
{
    /**
     * @return string
     */
    public $title = '设置屏蔽地域';
    public $buttonText = '<button class="btn btn-primary"><i class="feather icon-minimize-2"></i>&nbsp&nbsp<span class="selected"></span>设置屏蔽地域</button>';
    public $warning = '请选择屏蔽规则！';

    public function form():BatchSetAreaForm
    {
        // 实例化表单类
        return BatchSetAreaForm::make();
    }
}
