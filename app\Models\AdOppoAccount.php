<?php

	namespace App\Models;

	use Dcat\Admin\Traits\HasDateTimeFormatter;
    use Illuminate\Database\Eloquent\Model;
    use Illuminate\Database\Eloquent\Relations\BelongsTo;
    use Illuminate\Database\Eloquent\SoftDeletes;


	class AdOppoAccount extends Model
	{
		use HasDateTimeFormatter;
		use SoftDeletes;

		protected $table = 'ad_oppo_account';

        public function adminInfo(): BelongsTo
        {
            return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
        }
	}
