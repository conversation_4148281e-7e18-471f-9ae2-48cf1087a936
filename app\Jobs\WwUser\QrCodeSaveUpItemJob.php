<?php

namespace App\Jobs\WwUser;

use App\Models\WwUserQrcode;
use App\Services\Corp\WwCorpApiService;
use App\Services\NotifySendService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;


class QrCodeSaveUpItemJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $wwUser;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($wwUser)
    {
        //
        $this->wwUser = $wwUser;
    }

    /**
     * Execute the job.
     *
     * @return mixed
     */
    public function handle()
    {
        $wwUser = $this->wwUser;
        $state = getLinkState();
        $contactWay = WwCorpApiService::add_contact_way($wwUser->corpInfo, $wwUser->open_user_id, (bool)$wwUser->skip_verify, $state);
        if (isset($contactWay['config_id'])) {
            $qrcodeObj = new WwUserQrcode();
            $qrcodeObj->ww_user_id = $wwUser->id;
            $qrcodeObj->admin_uid = $wwUser->admin_uid;
            $qrcodeObj->ww_app_id = $wwUser->ww_app_id;
            $qrcodeObj->corp_id = $wwUser->corp_id;
            $qrcodeObj->corp_auth_id = $wwUser->corp_auth_id;
            $qrcodeObj->is_used = 0;
            $qrcodeObj->ww_user_type = $wwUser->type;
            $qrcodeObj->qrcode_type = 1;
            $qrcodeObj->qrcode_scene = 2;
            $qrcodeObj->qrcode_remark = '';
            $qrcodeObj->skip_verify = $wwUser->skip_verify;
            $qrcodeObj->state = $state;
            $qrcodeObj->config_id = $contactWay['config_id'];
            $qrcodeObj->qr_code = $contactWay['qr_code'];

            //以下字段是群码字段，使用默认值即可
            $qrcodeObj->auto_create_room = 0;
            $qrcodeObj->room_base_name = '';
            $qrcodeObj->room_base_id = 0;
            $qrcodeObj->qr_code_link = '';
            $qrcodeObj->save();
//            $wwUser->have_qrcode = 1;//标记该销售有二维码
//            $wwUser->save();
        } else {
            $qrcodeErrMessage = isset($contactWay['errmsg']) ? explode(",", $contactWay['errmsg'])[0] : "";
//            makeErrorLog('[销售二维码储备队列-子任务][新建失败]' . $qrcodeErrMessage, ['ww_user_id' => $wwUser->id]);
//            NotifySendService::sendWorkWeixinForError("[销售二维码储备队列-子任务][新建失败] 销售：" . $wwUser->id . " 新建创建二维码失败，失败原因是：" . $qrcodeErrMessage);
        }
    }

}
