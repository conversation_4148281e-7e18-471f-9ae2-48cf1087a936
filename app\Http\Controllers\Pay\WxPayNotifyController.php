<?php

namespace App\Http\Controllers\Pay;

use App\Models\CorpLicenseOrders;
use App\Models\CustomerOrders;
use App\Services\ProviderService;
use App\Services\WxPayService;
use Closure;
use EasyWeChat\Kernel\Exceptions\InvalidArgumentException;
use EasyWeChat\Kernel\Exceptions\RuntimeException;
use EasyWeChat\Pay\Message;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Psr\Http\Message\ResponseInterface;
use ReflectionException;

use Throwable;

class WxPayNotifyController
{

    /**
     * 微信支付回调
     * @param Request $request
     * @return ResponseInterface
     * @throws InvalidArgumentException
     * @throws RuntimeException
     * @throws ReflectionException
     * @throws Throwable
     */
    public function payNotify(Request $request)
    {
//        Log::info("微信支付回调" . json_encode($request->all()));
        $config = WxPayService::getConfig();
        $server = WxPayService::getApp($config)->getServer();
        $server->handlePaid(function (Message $message, Closure $next) {
            Log::info('购买企业许可证-微信支付回调message：' . $message);
            $message = json_decode($message, true);
            // $message->out_trade_no 获取商户订单号
            // $message->payer['openid'] 获取支付者 openid
            if($message['trade_state'] == 'SUCCESS'){
                $amountInfo = $message['amount'];
                $order = CorpLicenseOrders::query()
                    ->where('wx_pay_order_no', $message['out_trade_no'])
                    ->where('ww_order_price', $amountInfo['total'])
                    ->first();
                if (!$order || $order->is_wx_pay) {
                    Log::info('微信支付回调,订单号不存在或已支付：' . $message['out_trade_no']);
                    return true;
                }

                $order->wx_pay_time = date("Y-m-d H:i:s", time());
                $order->wx_pay_price = $amountInfo['total'];
                $order->is_wx_pay = 1;
                $order->save();
                //调用企业微信API，使用余额支付该订单
                $resp = ProviderService::submitPayJob($order->order_id);
                if (isset($resp['jobid'])) {
                    $jobIds = json_decode($order->prov_pay_jobid, true);
                    if (!$jobIds){
                        $jobIds = [];
                    }
                    $jobIds[$resp['jobid']] = [
                        'time' => date("Y-m-d H:i:s")
                    ];
                    $order->prov_pay_jobid = json_encode($jobIds);
                    $order->is_prov_pay_submit = 1;
                    $order->save();
                    return true;
                } else {
                    throw new Exception($order->id . "，企微许可证订单，支付失败：" . $resp['errmsg'] ?? '');
                }
            }
            return $next($message);
        });

        // 默认返回 ['code' => 'SUCCESS', 'message' => '成功']
        return $server->serve();
    }


    /**
     * @throws InvalidArgumentException
     * @throws Throwable
     * @throws ReflectionException
     * @throws RuntimeException
     */
    public function customerPayNotify(Request $request)
    {
        Log::info("客户-微信支付回调" . json_encode($request->all()));
        $config = WxPayService::getHeJunConfig();
        $server = WxPayService::getApp($config)->getServer();
        $server->handlePaid(function (Message $message, Closure $next) {
            Log::info('客户-微信支付回调message：' . $message);
            $message = json_decode($message, true);
//            $message->out_trade_no; // 获取商户订单号
//            $message->payer['openid'];// 获取支付者 openid
//            $openId = $message['openid'];
            if($message['trade_state'] == 'SUCCESS'){
                $amountInfo = $message['amount'];
                $order = CustomerOrders::query()
                    ->where('order_no', $message['out_trade_no'])
                    ->where('order_price', $amountInfo['total'])
                    ->first();
                if (!$order || $order->status == 2) {
                    Log::info('客户微信支付回调,订单号不存在或已支付：' . $message['out_trade_no']);
                    return true;
                }
                $order->status = 2;//支付成功
                $order->pay_time = date("Y-m-d H:i:s", time());
                $order->callback_time = date("Y-m-d H:i:s", time());
                $order->callback_data = json_encode($message,JSON_UNESCAPED_UNICODE);
                $order->save();
            }
            return $next($message);
        });

        // 默认返回 ['code' => 'SUCCESS', 'message' => '成功']
        return $server->serve();
    }
}
