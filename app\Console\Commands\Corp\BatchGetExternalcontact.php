<?php

    namespace App\Console\Commands\Corp;

    use App\Http\Controllers\WorkWeixinAppController;
    use App\Jobs\AddExternalContactJob;
    use App\Models\LinkViewRecord;
    use App\Models\WwCorpInfo;
    use App\Models\WwUser;
    use App\Models\WwUserAddRecord;
    use App\Services\NotifySendService;
    use EasyWeChat\Kernel\Exceptions\BadResponseException;
    use EasyWeChat\Kernel\Exceptions\InvalidArgumentException;
    use Illuminate\Console\Command;
    use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
    use Symfony\Contracts\HttpClient\Exception\DecodingExceptionInterface;
    use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
    use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
    use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

    class BatchGetExternalcontact extends Command
    {
        /**
         * The name and signature of the console command.
         *
         * @var string
         */
        protected $signature = 'WwApi:BatchGetExternalcontact';

        /**
         * The console command description.
         *
         * @var string
         */
        protected $description = '通过企业微信API，把智投后台授权的所有企业微信，重新拉取一下进粉记录';

        /**
         * Execute the console command.
         *
         * @return int
         */
        public function handle()
        {
            /** @var WwCorpInfo $corpInfo */
            $corpIds = [
//                41,39,36,13,33,//
//                245,
                273
            ];
            $corpInfos = WwCorpInfo::query()
                ->whereIn('id',$corpIds)
                ->where("id",">","1")
                ->get();
            foreach ($corpInfos as $corpInfo) {
                $this->alert($corpInfo->id . "-" . $corpInfo->corp_name);
                $this->getWwUserExUsers($corpInfo);
            }

            return Command::SUCCESS;
        }

        public function getWwUserExUsers($corpInfo){
            $wwUserList = WwUser::query()->where("corp_id", $corpInfo->id)->get()->toArray();
            $exUserList = [];
            $this->batchExternalcontactGet($corpInfo, array_column($wwUserList, "open_user_id"), '', $exUserList);
            foreach ($exUserList as $item) {
                $state = $item['follow_info']['state'] ?? "";
                if (empty($state)) {
                    //$this->info("State不存在 - " . $item['external_contact']['name'] . "-" . date("Y-m-d H:i:s", $item['follow_info']['createtime']));
                    continue;
                }
                if (!str_contains($state, 'zt_t2')) {
                    continue;
                }
                /** @var LinkViewRecord $linkViewRecord */
                $linkViewRecord = LinkViewRecord::query()->where("state", $state)->first();
                if (!$linkViewRecord) {
                    $this->info("LinkViewRecord不存在 - "." ".date("Y-m-d H:i:s",$item['follow_info']['createtime'])." " . $state . " - " . $item['external_contact']['name'] . "-" . date("Y-m-d H:i:s", $item['follow_info']['createtime']));
                    continue;
                }
                $viewWwUserInfo = $linkViewRecord->wwUserInfoWithTrashed;
                if (!$viewWwUserInfo) {
                    $this->info("viewWwUserInfo不存在 - " . $state . " - " . $linkViewRecord->id);
                    continue;
                }
                $addRecord = WwUserAddRecord::query()->where([
                    'admin_uid' => $linkViewRecord->admin_uid,
                    'ww_link_id' => $linkViewRecord->ww_link_id,
                    'external_userid' => $item['external_contact']['external_userid'],
                    'ww_user_id' => $viewWwUserInfo->id
                ])->first();
                if (!$addRecord) {
                    $message = [
                        'State' => $state,
                        'AuthCorpId' => $corpInfo->corp_id,
                        'UserID' => $item['follow_info']['userid'],
                        'ExternalUserID' => $item['external_contact']['external_userid'],
                    ];

                    //计算消息中 'State' 字段的 CRC32 哈希值，并对 10 取模，得到一个 0 到 9 之间的索引值 ,这个索引值将用于确定要使用的队列名称。
                    $jobIndex = crc32($state) % 10;
                    $this->info($jobIndex);
                    //初始化队列名称为
                    $job = 'add_external_contact';

                    //检查计算得到的索引值是否不等于 0。如果不等于 0，则将索引值添加到队列名称的末尾，形成一个更具体的队列名称。例如，如果索引值为 3，则队列名称将变为 'add_external_contact_3'。
                    if($jobIndex){ //如果不等于0，那么就加上JobIndex
                        $job = 'add_external_contact_' . $jobIndex;
                    }
                    AddExternalContactJob::dispatch($message)->onQueue($job);
                    $this->info("不存在 - 加入队列 - " . date("Y-m-d H:i:s", $item['follow_info']['createtime']) . "-" . $item['follow_info']['state'] . "-" . $item['external_contact']['name'] . "-");
                } else {
                    //$this->info("存在 - " . date("Y-m-d H:i:s", $item['follow_info']['createtime']) . "-" . $item['external_contact']['name'] . "-");
                }
            }
        }

        public function batchExternalcontactGet(WwCorpInfo $corpInfo, $userList, $next_cursor, &$exUserList): array
        {
            try {
                $app = WorkWeixinAppController::getApp($corpInfo->suite_id);
                $resp = $app->getClient()->postJson('cgi-bin/externalcontact/batch/get_by_user?access_token=' . WorkWeixinAppController::getCorpToken($corpInfo), [
                    'userid_list' => $userList,
                    'cursor' => $next_cursor,
                    'limit' => 100
                ])->toArray();
                if (!empty($resp['external_contact_list'])) {
                    $createTime = date("Y-m-d H:i:s", $resp['external_contact_list'][0]['follow_info']['createtime']);
                    if ($createTime < date("Y-m-d",time())." 00:00:00") {
                        return [];
                    }
                    $exUserList = array_merge($exUserList, $resp['external_contact_list']);
                }
                if (!empty($resp['next_cursor'])) {
                    return $this->batchExternalcontactGet($corpInfo, $userList, $resp['next_cursor'], $exUserList);
                }
                return [];
            } catch (BadResponseException|TransportExceptionInterface|ServerExceptionInterface|RedirectionExceptionInterface|DecodingExceptionInterface|ClientExceptionInterface|InvalidArgumentException $e) {
                $errMessage = makeErrorLog('[系统报错][API调用][users_get]', ['corp_id' => $corpInfo->id], $e);
                NotifySendService::sendWorkWeixinForError("[系统报错][API调用][users_get]" . $errMessage);
                return [];
            }
        }
    }
