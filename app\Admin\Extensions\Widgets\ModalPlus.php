<?php

namespace App\Admin\Extensions\Widgets;

use Dcat\Admin\Support\Helper;
use Dcat\Admin\Widgets\Modal as BaseModal;
/**
 * ModalPlus 类扩展了 Dcat Admin 的 Modal 类 用法一致
 * 增加了 disable 方法用于渲染时禁用按钮
 * 增加了 button 方法用于按钮点击后执行自定义 JavaScript 代码
 * 
 */
class ModalPlus extends BaseModal
{
    protected $disabled = false;

    protected string $clickCallBack = '';

    public function __construct($title = null, $content = null)
    {
        return parent::__construct($title, $content);

    }

    public function disable($disabled = true)
    {
        $this->disabled = $disabled;
        return $this;
    }

    public function button($button = null, string $clickCallBack = '')
    {

        $this->clickCallBack = $clickCallBack;
        $this->button = $button;
        return $this;
    }


    protected function renderButton()
    {
        if (! $this->button) {
            return '';
        }

        $button = Helper::render($this->button);

        // 如果没有HTML标签则添加一个 a 标签
        if (! preg_match('/(\<\/[\d\w]+\s*\>+)/i', $button)) {
            $button = "<a href=\"javascript:void(0)\">{$button}</a>";
        }

        // 根据 disabled 状态决定是否添加 modal 触发属性
        $modalAttrs = $this->disabled ? '' : 'data-toggle="modal" data-target="#' . $this->id() . '"';
        
        // 处理 JavaScript 代码
        $clickHandler = '';
        if ($this->clickCallBack) {
            // 生成唯一的函数名
            $functionName = 'modalClick_' . uniqid();
            
            // 将 JavaScript 代码封装到全局函数中
            $clickHandler = "window.{$functionName}(event)";
            
            // 添加脚本到页面
            $script = <<<JS
window.{$functionName} = function(event) {
    {$this->clickCallBack}
};
JS;
            // 将脚本添加到页面
            \Dcat\Admin\Admin::script($script);
        }
        
        return <<<HTML
<span style="cursor: pointer" {$modalAttrs}
 onclick="{$clickHandler}"
>{$button}</span>

HTML;
    }


    protected function getRenderableScript() {
        if (!$this->getRenderable()) {
            return;
        }

        $url = $this->renderable->getUrl();

        return <<<JS


target.on('{$this->target}:load', function () {

    var key = Dcat.grid.selected('')

    Dcat.helpers.asyncRender('{$url}&ids='+key, function (html) {
        body.html(html);

        {$this->loadScript}

        target.trigger('{$this->target}:loaded');
    });
});
JS;
    }
}