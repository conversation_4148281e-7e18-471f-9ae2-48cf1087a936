<?php

namespace App\Console\Commands\WwUser;

use App\Models\WwUser;
use App\Server\NotifySend;
use App\Server\WwCorpApi;
use App\Services\Corp\WwCorpApiService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ChangeWwUserSkipVerify extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'wwUser:ChangeWwUserSkipVerify';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '修改销售的免验证状态';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $adminUids = [163, 164, 165, 166, 167, 169, 172, 173, 174, 250];
        $userIds   = ['ltn58213', 'ltn5910', 'ltn1379', 'ltn9637', 'ltn8546', 'ltn9637', 'ltn7479', 'ltn5404', 'ltn9495', 'ltn5667', 'ltn6339', 'ltn9387', 'ltn6663', 'ltn5315', 'ltn7561', 'ltn4970', 'csk5936', 'ltn2960', 'ltn9167', 'ltn5490', 'ltn3376', 'ltn1829', 'ltn9329', 'ltn9629', 'ltn2560', 'csk7484', 'ltn3688', 'ltn5107', 'ltn0932', 'ltn1868', 'ltn1379', 'ltn5910', 'ltn1178', 'ltn2171', 'ltn6684', 'ltn4567', 'ltn5381', 'ltn4151', 'ltn9112', 'ltn88805', 'ltn04857', 'ltn5065', 'ltn8986', 'ltn6616', 'ltn2309', 'ltn7623', 'ltn6618', 'csk6799', 'ltn9896', 'ltn30821', 'ltn9932', 'ltn2282', 'ltn3669', 'ltn6577', 'ltn5633', 'ltn0428', 'ltn2776', 'csk9695', 'ltn8833', 'ltn1233', 'ltn7573', 'ltn9993', 'ltn16288', 'ltn1277', 'ltn0794', 'ltn1288', 'ltn99888', 'ntn535', 'ltn5558', 'ltn9968', 'ltn6658', 'ltn5533', 'ltn99666', 'ltn1618', 'ltn5758', 'ltn58213', 'ltn-8750', 'ltn8589', 'ltn82589', 'ltn5747', 'ltn8951', 'ltn7632', 'ltn0959', 'lth7977', 'gcd85679', 'ltn6549', 'ltn0697', 'ltn6318', 'ltn3972', 'ltn6098', 'ltn8181', 'ltn1388', 'ltn82590', 'ltn5279', 'ltn2063', 'ltn8629', 'ltn5031', 'ltn2514', 'ltn2916'
        ];
        $wwUsers   = WwUser::query()->whereIn("admin_uid", $adminUids)->whereIn("user_id", $userIds)->get();
        /** @var WwUser $wwUser */
        foreach ($wwUsers as $wwUser) {
            $wwUser->skip_verify = 1;
            $this->alert("销售 ID：" . $wwUser->id);
            if ($wwUser->skip_verify != $wwUser->cus_acq_link_skip_verify) {
                $delResp = WwCorpApiService::delete_link($wwUser->corpInfo, $wwUser); // 先删除旧的获客助手链接
                if (isset($delResp['errcode']) && $delResp['errcode'] != 0) {
                    $this->error("删除获客助手失败，失败原因如下");
                    $this->info(json_encode($delResp));
                } else {
                    $this->info("删除获客助手成功");
                    $this->info(json_encode($delResp));
                }
                //删除原链接，创建新链接
                $linkData = WwCorpApiService::create_link($wwUser->corpInfo, $wwUser, (bool)$wwUser->skip_verify);
                if ($linkData['errcode'] != 0) { // 创建失败
                    $this->error("创建获客助手失败，失败原因如下");
                    $this->info(json_encode($linkData));
                    break;
                } else { // 创建成功
                    $wwUser->cus_acq_link_status         = 1;
                    $wwUser->cus_acq_link_status_message = $linkData['errmsg'];
                    $wwUser->cus_acq_link_id             = $linkData['link']['link_id'];
                    $wwUser->cus_acq_link_name           = $linkData['link']['link_name'];
                    $wwUser->cus_acq_link_url            = $linkData['link']['url'];
                    $wwUser->cus_acq_link_skip_verify    = $wwUser->skip_verify;
                    $this->info("创建获客助手成功");
                    $this->info(json_encode($linkData));
                }
            }else{
                $this->info("无需修改");
            }

            $wwUser->save();
        }
        return Command::SUCCESS;
    }
}
