<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;


class GeneratePage extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'generate_page';
    const HTML_TYPE = [
        0 => '问答', 2 => '表单', 3 => '多图+按钮'
    ];

    public function adminInfo(): BelongsTo
    {
        return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
    }


}
