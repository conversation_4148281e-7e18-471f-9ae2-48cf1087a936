<?php

	namespace App\Http\Controllers\WorkWeixin3rd;

	use App\Http\Controllers\WorkWeixinAppController;
    use App\Jobs\AdminActionLogJob;
    use App\Models\AdminActionLog;
	use App\Models\AdminSubUserAuthCorp;
	use App\Models\AdminUser;
	use App\Models\WwCorpInfo;
	use App\Models\WwUser;
    use App\Services\Corp\WwProvApiService;
    use App\Services\NotifySendService;
    use Dcat\Admin\Admin;
    use Illuminate\Support\Facades\Log;

	class AuthController
	{
		public function handler($message, $param): bool
        {
            $message = json_decode($message, true);
            Log::info($message);
			if (isset($message['InfoType'])) {
				switch ($message['InfoType']) {
					case 'create_auth':
						Log::info('create_auth');
						self::createAuth($message);
						break;
					case 'reset_permanent_code':
						Log::info('reset_permanent_code');
						self::createAuth($message);
						break;
					case "cancel_auth":
						Log::info('cancel_auth');
						$app = WorkWeixinAppController::getApp(); // TODO 这里需要根据GET参数获取不同的应用
						/** @var WwCorpInfo $corpInfo */
						$corpInfo = WwCorpInfo::withTrashed()->where("corp_id", $message['AuthCorpId'])->where("suite_id", $app->getAccount()->getSuiteId())->first();
						if ($corpInfo) {
							//查询授权的记录删除
							$corpUsers = AdminSubUserAuthCorp::query()->where("corp_id", $corpInfo->id)->get();
							/** @var AdminSubUserAuthCorp $corpUser */
							foreach ($corpUsers as $corpUser) {
								$corpUser->delete();

                                $logMessage =  '【' . $corpUser->adminInfo->username .'】' . '，企业微信「' . $corpInfo->corp_name . '」取消授权，相关联的企业微信、企业微信子账号授权记录、销售列表 均自动删除。';
                                //添加操作日志
                                AdminActionLogJob::dispatch(
                                    'del_corp',
                                    $corpUser->id,
                                    AdminActionLog::ACTION_TYPE['企微'],
                                    $logMessage,
                                    getIp() ?? '',
                                    $corpUser->admin_uid,
                                )->onQueue('admin_action_log_job');

                                //发送报警
                                NotifySendService::sendWorkWeixinForError($logMessage);
							}
							//该企业微信下的所有销售，全部删除
							WwUser::query()->where("corp_id", $corpInfo->id)->delete();
							//删除企微
							$corpInfo->delete();
						}
						break;
					case 'change_auth':
						Log::info('change_auth ');
						break;
				}
			}
			return true;
		}

		public static function createAuth($message): bool
		{
            Log::info('创建授权-1');
			$AuthCode                = $message['AuthCode'];
			$sid                     = $message['sid'] ?? "";
			$state = $message['state'] ?? "";
			$app                     = WorkWeixinAppController::getApp('', $sid);
            Log::info('创建授权-2');
			$get_permanent_code_resp = $app->getClient()->withAccessToken($app->getSuiteAccessToken())->postJson('cgi-bin/service/get_permanent_code', [
				'auth_code' => $AuthCode,
			]);
            Log::info('创建授权-3');
			$authInfo                = $get_permanent_code_resp->toArray();
			Log::info('授权信息：' . json_encode($authInfo));
			if (!isset($authInfo['auth_corp_info'])) {
				return false;
			}
			$corpInfo = WwCorpInfo::withTrashed()->where("corp_id", $authInfo['auth_corp_info']['corpid'])->where("suite_id", $app->getAccount()->getSuiteId())->first();
			if (!$corpInfo) {
				$corpInfo          = new WwCorpInfo();
				$corpInfo->corp_id = $authInfo['auth_corp_info']['corpid'];
			}
			$corpInfo->secret     = $authInfo['permanent_code'];
			$corpInfo->deleted_at = null;
			$corpInfo->state = !empty($state) ? $state : ($authInfo['state'] ?? 1);
			$corpInfo->at_ex_time = date("Y-m-d H:i:s", time() - 100);

			$corpInfo->corp_name            = $authInfo['auth_corp_info']['corp_name'] ?? "";
			$corpInfo->corp_type            = $authInfo['auth_corp_info']['corp_type'] ?? "";
			$corpInfo->corp_round_logo_url  = $authInfo['auth_corp_info']['corp_round_logo_url'] ?? "";
			$corpInfo->corp_square_logo_url = $authInfo['auth_corp_info']['corp_square_logo_url'] ?? "";
			$corpInfo->corp_user_max        = $authInfo['auth_corp_info']['corp_user_max'] ?? "";
			$corpInfo->corp_wxqrcode        = $authInfo['auth_corp_info']['corp_wxqrcode'] ?? "";
			$corpInfo->corp_full_name       = $authInfo['auth_corp_info']['corp_full_name'] ?? "";
			$corpInfo->subject_type         = $authInfo['auth_corp_info']['subject_type'] ?? "";
			$corpInfo->verified_end_time    = $authInfo['auth_corp_info']['verified_end_time'] ?? "";
			$corpInfo->corp_scale           = $authInfo['auth_corp_info']['corp_scale'] ?? "";
			$corpInfo->corp_industry        = $authInfo['auth_corp_info']['corp_industry'] ?? "";
			$corpInfo->corp_sub_industry    = $authInfo['auth_corp_info']['corp_sub_industry'] ?? "";

			if (isset($authInfo['auth_info']['agent'])) {
				if (count($authInfo['auth_info']['agent']) > 1) {
					NotifySendService::sendWorkWeixinForError("[业务逻辑][企微授权][错误错误]授权的企业微信Agent数组大于1，系统无法处理，请立即查看系统运行状态，相关数据如下：" . json_encode($authInfo));
				}
				$agentInfo                         = $authInfo['auth_info']['agent'][0];
				$corpInfo->agent_agentid           = $agentInfo['agentid'];
				$corpInfo->agent_name              = $agentInfo['name'];
				$corpInfo->agent_square_logo_url   = $agentInfo['square_logo_url'];
				$corpInfo->agent_privilege         = json_encode($agentInfo['privilege']);
				$corpInfo->agent_auth_mode         = $agentInfo['auth_mode'] ?? -1;
				$corpInfo->agent_is_customized_app = (int)$agentInfo['is_customized_app'];
			}
			$corpInfo->dealer_corp_info = json_encode($authInfo['dealer_corp_info'] ?? []);
			$corpInfo->auth_user_info   = json_encode($authInfo['auth_user_info'] ?? []);
			$corpInfo->suite_id         = $app->getAccount()->getSuiteId();
			//新授权的企业，需要取消获客助手之前的授权
			$corpInfo->customer_link_quota_update_time = null;
			$corpInfo->save();

			WwProvApiService::get_app_license_info($corpInfo);


			//企业授权完成后，在系统中做授权
			/** @var AdminUser $adminUserInfo */
			$adminUserInfo = AdminUser::query()->find($corpInfo->state);
			if ($adminUserInfo) {
				if ($adminUserInfo->parent_id == 0) {
					//主管理员，授权给自己
					AdminSubUserAuthCorp::addCorpRecord($adminUserInfo->id, $corpInfo);
				} else {
					//子账号，授权给主账号以及子账号
					$adminUserIds = [$adminUserInfo->id, $adminUserInfo->parent_id];
					foreach ($adminUserIds as $adminUserId) {
						AdminSubUserAuthCorp::addCorpRecord($adminUserId, $corpInfo);
					}
				}
			}
			return true;
		}
	}
