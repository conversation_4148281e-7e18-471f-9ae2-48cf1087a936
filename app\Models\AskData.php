<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property WwLink    $wwLinkInfo

 */
class AskData extends Model
{
    use HasDateTimeFormatter;

    protected $table = 'ask_data';

    public function adminInfo(): BelongsTo
    {
        return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
    }

    public function wwLinkInfo(): BelongsTo
    {
        return $this->BelongsTo(WwLink::class, 'ww_link_id', 'id')->withTrashed();
    }

    public function tplInfo(): BelongsTo
    {
        return $this->BelongsTo(WwTpl::class, 'tpl_id', 'id');
    }

    public function linkViewRecordInfoIsAdd()
    {
        return $this->hasOne(LinkViewRecord::class, 'id', 'vid');
    }

    /**
     * 获取加粉的访问记录
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function viewRecordIsAddWwUser():HasOne
    {
        return $this->hasOne(LinkViewRecord::class, 'id', 'vid')
            ->select('id','add_ww_user_id')
            ->whereNotNull('add_ww_user_id');
    }

}
