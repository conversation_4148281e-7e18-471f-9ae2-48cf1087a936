<?php

namespace App\Admin\Extensions\Grid;

use App\Admin\Extensions\Widgets\ModalPlus;
use Dcat\Admin\Widgets\Form;
use Dcat\Admin\Grid\BatchAction;

/**
 * Class BatchActionPlus
 *
 * 继承后定义 form() 方法或 handle() 方法
 * - form() 方法用于返回一个表单实例，通常用于批量操作的表单。
 * - handle() 方法用于处理批量操作的逻辑。
 */
abstract class BatchActionPlus extends BatchAction
{
    /**
     * @return string
     */
    public $title = '批量操作';

    public $warning = '请选择数据';

    public $buttonText = null;

    public function render()
    {
        if (method_exists($this, 'handle')) {
            return parent::render();
        }
        if (method_exists($this, 'form')) {
            // 实例化表单类
            $form = $this->form();
            return ModalPlus::make()
                ->lg()
                ->title($this->title)
                ->button(
                    $this->getButtonText(),
                    <<<JS
                        const keys = {$this->getSelectedKeysScript()}
                        if (keys.length === 0) {
                            Dcat.swal.warning('{$this->warning}');
                            event.preventDefault();
                            event.stopPropagation();
                            return false;
                        }
                    JS
                )
                ->body($form);
        }
        throw new \Exception('BatchActionPlus must implement form() method or handle() method.');
        
    }

    protected function getButtonText()
    {
        return $this->buttonText ?: '<button class="btn btn-primary">' . $this->title . '</button>';
    }

    public function form()
    {
        // 默认返回一个空的表单实例
        return Form::make();
    }

    /**
     * {@inheritdoc}
     */
    protected function actionScript()
    {
        $warning = $this->warning;

        return <<<JS
function (data, target, action) { 
    var key = {$this->getSelectedKeysScript()}
    
    if (key.length === 0) {
        Dcat.warning('{$warning}');
        return false;
    }
    
    // 设置主键为复选框选中的行ID数组
    action.options.key = key;
}
JS;
    }
}
