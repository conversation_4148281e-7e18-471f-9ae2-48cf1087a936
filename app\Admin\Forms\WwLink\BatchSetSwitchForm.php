<?php

namespace App\Admin\Forms\WwLink;

use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Models\AdminUser;
use App\Models\WwLink;
use App\Services\Tools\LogService;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class BatchSetSwitchForm extends Form implements LazyRenderable
{
    use LazyWidget;

    // 处理请求
    public function handle(array $input)
    {

        LogService::inputLog('Tools','投放链接管理-批量开关链接', $input, Admin::user()->id, Admin::user()->username);
        if(!$input['id']){
            return $this->response()->alert()->error('提示')->detail('请选择记录。');
        }
        // id转化为数组
        $id = explode(',', $input['id']);
        if (!$id) {
            return $this->response()->alert()->error('提示')->detail('参数错误。');
        }
        $wwLinks = WwLink::query()->find($id);

        if ($wwLinks->isEmpty()) {
            return $this->response()->alert()->error('提示')->detail('链接不存在。');
        }
        /** @var WwLink $wwLink */
        foreach($wwLinks as $wwLink){
            if(!AdminUser::isAdmin($wwLink)){
                return $this->response()->alert()->error('提示')->detail('无操作权限。');
            }
            $wwLink->is_open = $input['is_open'];
            $wwLink->save();
            $isOpenStr = '';
            //记录操作日志队列
            if ($input['is_open'] == 1) {
                $isOpenStr = '打开';
            }
            //添加销售下线记录日志
            if($input['is_open'] == 0){
                $isOpenStr = '关闭';
            }
            AdminActionLogJob::dispatch(
                'batch_set_ww_link_switch',
                $wwLink->id,
                AdminActionLog::ACTION_TYPE['链接'],
                '投放链接ID【' . $wwLink->id . '】，修改状态为：' . $isOpenStr,
                getIp(),
                Admin::user()->id
            )->onQueue('admin_action_log_job');

        }
        return $this->response()->alert()->success('提示')->detail('配置成功')->refresh();
    }

    public function form(): void
    {
        $this->select('is_open','状态')->options([1 => '打开',0 => '关闭'])->default(0)->required();
        $this->hidden('id')->attribute('id', 'reset-batch_link_switch_id');
    }
}
