<?php

namespace App\Admin\Forms\TencentAd;

use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Services\Tools\LogService;
use Dcat\Admin\Admin;
use App\Models\TencentAdAccount;
use App\Models\AdminSubUser;
use App\Models\AdminUser;
use App\Models\WwLink;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class AdAccountSwitchAccountsForm extends Form implements LazyRenderable
{
    use LazyWidget;
    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        LogService::inputLog('Tools','腾讯账户管理-批量转移账户', $input, Admin::user()->id, Admin::user()->username);
        // id转化为数组
        $id = explode(',', $input['id'] ?? null);
        if (!$id) {
            return $this->response()->error('参数错误');
        }
        if (AdminUser::isSystemOp()) {
            $accountIdList = TencentAdAccount::query()->find($id);
        } else {
            $accountIdList = TencentAdAccount::query()->whereIn("admin_uid",AdminSubUser::getALLAdminUids(Admin::user()->id))->find($id);
        }

        if ($accountIdList->isEmpty()) {
            return $this->response()->alert()->error('提示')->detail('账户不存在。');
        }
        $accountIds = array_column($accountIdList->toArray(), "account_id");

        $message = '转移成功';
        // 检验该账户是否存在链接，如存在链接，不允许迁移
        $wwLinks = WwLink::query()->whereIn("account_id", $accountIds)->get();
        if ($wwLinks->isNotEmpty()) {
            if (AdminUser::isSystemOp()) {
                return $this->response()->alert()->error('提示')->detail('无法迁移，当前账号存在使用中的微信投放链接，请删除后再转移。');
            }
            $message = '转移成功'.PHP_EOL."企微投放链接，已同步转移";
            /** @var WwLink $wwLink */
            foreach($wwLinks as $wwLink){
                $wwLink->admin_uid = $input['admin_uid'];
                $wwLink->save();
            }
        }
        $adminUser = AdminUser::query()->find($input['admin_uid']);
        foreach ($accountIdList as $value) {
            AdminActionLogJob::dispatch(
                'tencent_ad_account_switch',
                $value->admin_uid,
                AdminActionLog::ACTION_TYPE['账号'],
                '腾讯广告账户ID ：' . $value->account_id  . '，从用户 【' . $value->adminInfo->username. '】，转移至用户【' . $adminUser->username . '】',
                getIp(),
                Admin::user()->id
            )->onQueue('admin_action_log_job');

            $value->admin_uid = $input['admin_uid'];
            $value->save();
        }
        return $this->response()->alert()->success($message)->refresh();
    }

    /**
     * Build a form here.
     */
    public function form()
    {

        if (AdminUser::isSystemOp()) {
            $this->select('admin_uid','选择用户')->options(AdminUser::query()->pluck("username", 'id')->toArray())->required()->help("广告账户授权后可迁移至其他账号，如A账户已授权，可以分配至B账号");
        } else {
            $adAccountIds = AdminUser::query()->whereIn("id", AdminSubUser::getALLAdminUids(Admin::user()->id))->pluck("username", 'id')->toArray();
            $this->select('admin_uid','选择用户')->options($adAccountIds)->required()->help("可迁移至其他账号。");
        }
        $this->hidden('id')->value($this->payload['ids'] ?? '');
        $this->confirm('确认转移？');
    }

    /**
     * The data of the form.
     *
     * @return array
     */
    public function default()
    {
        return [];
    }
}
