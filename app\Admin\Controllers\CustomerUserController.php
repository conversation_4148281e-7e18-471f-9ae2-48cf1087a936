<?php

namespace App\Admin\Controllers;


use Admin;
use App\Models\AdminUser;
use App\Services\NotifySendService;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Auth\Permission;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Http\Repositories\Administrator;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Models\Administrator as AdministratorModel;
use Dcat\Admin\Models\Role;
use Dcat\Admin\Support\Helper;


class CustomerUserController extends AdminController
{
    public function title(): string
    {
        return '客户管理';
    }

    public function index(Content $content): Content
    {
        return $content
            ->translation($this->translation())
            ->title($this->title())
            ->description($this->description()['index'] ?? trans('admin.list'))
            ->body($this->grid());
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid(): Grid
    {
        return Grid::make(Administrator::with(['roles']), function (Grid $grid) {
            /** 禁止查看超级管理员 */
            $grid->model()
                ->whereNot('id', 1)
                ->orderBy('id', 'desc');

            $grid->disableViewButton();
            $grid->disableRowSelector();
            // 快捷编辑
            $grid->showQuickEditButton();
            // 对话框创建
            $grid->enableDialogCreate();
            // 显示列选择器
            $grid->showColumnSelector();
            // 禁用编辑按钮
            $grid->disableEditButton();

            $grid->column('id')->sortable();
            $grid->column('username')->copyable();
            $grid->column('name')->copyable();

            // 角色列
            $grid->column('roles')
                ->pluck('name')
                ->label('primary', 3);

            $grid->column('created_at');
            $grid->column('updated_at');

            // 快捷搜索
            $grid->quickSearch(['id', 'name', 'username']);

            $grid->filter(function (Grid\Filter $filter) use ($grid) {
                $filter->panel();
                $filter->expand();
                $filter->equal('id')->width(2);
                $filter->like('username')->width(2);
                $filter->like('name')->width(2);
            });


        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form(): Form
    {
        return Form::make(Administrator::with(['roles']), function (Form $form) {
            $form->disableViewButton();
            $form->disableViewCheck();
            $form->disableDeleteButton();

            if ($form->isEditing()) {
                $key = $form->getKey();
                // 如果尝试编辑超管 1/edit 返回无权访问
                if ($key == AdministratorModel::DEFAULT_ID) {
                    Permission::error(); // 终止并返回无权访问
                }
            }
            // 获取配置的连接名
            $userTable = config('admin.database.users_table');
            // 获取表名
            $connection = config('admin.database.connection');

            $id = $form->getKey();
            $form->display('id', 'ID');

            $form->text('username', '用户名')
                ->required()
                // 创建时校验是否存在 表示在 mysql 连接的 admin_users 表中检查唯一性。
                ->creationRules(['required', "unique:$connection.$userTable"])
                // 编辑时排除当前记录
                ->updateRules(['required', "unique:$connection.$userTable,username,$id"]);

            $form->text('name', '昵称')
                ->required();

            if ($id) {
                // 编辑模式：密码非必填，清空显示
                $form->password('password', trans('admin.password'))
                    ->minLength(5)
                    ->maxLength(20)
                    ->customFormat(function () {
                        return '';
                    });
            } else {
                // 创建模式：密码必填
                $form->password('password', trans('admin.password'))
                    ->required()
                    ->minLength(5)
                    ->maxLength(20);
            }
            // 确认密码
            $form->password('password_confirmation', trans('admin.password_confirmation'))
                ->same('password');
            // 忽略确认密码字段
            $form->ignore(['password_confirmation']);

            $form->multipleSelect('roles', trans('admin.roles'))
                ->options(function () {
                    /** @var Role $roleModel */
                    $roleModel = config('admin.database.roles_model');

                    return $roleModel::query()
                        ->whereNot('id', 1)
                        ->get()
                        ->pluck('name', 'id');
                })
                ->customFormat(function ($v) {
                    return array_column($v, 'id');
                });


        })->saving(function (Form $form) {
            if ($form->roles) {
                $rolesArray = $form->roles;
                // 如果权限数组存在 1 "超管角色"
                if (in_array(Role::ADMINISTRATOR_ID, $rolesArray)) {
                    /** @var AdminUser $adminUser */
                    $adminUser = Admin::user();
                    NotifySendService::sendWorkWeixinForError(
                        message: "【安全警告】【客户管理】" . PHP_EOL . PHP_EOL .
                        "用户「" . $adminUser->username . "」 用户ID「".$adminUser->id."」".PHP_EOL .
                        "IP「".getIp()."」".PHP_EOL .
                        "尝试给客户分配超管角色，已被系统阻止"
                    );
                    return $form->response()->alert()->error('保存失败')->detail('请稍后重试')->refresh();
                }

            }
            if ($form->password && $form->model()->get('password') != $form->password) {
                // 密码变更时加密
                $form->password = bcrypt($form->password);
            }

            if (!$form->password) {
                // 密码为空时移除该字段
                $form->deleteInput('password');
            }
        });

    }


    public function destroy($id)
    {
        //如果超管存在删除数组中 返回错误
        if (in_array(AdministratorModel::DEFAULT_ID, Helper::array($id))) {
            Permission::error();
        }
        // 调用父级删除方法
        return parent::destroy($id);
    }

    protected function detail()
    {
        return null;
    }

}
