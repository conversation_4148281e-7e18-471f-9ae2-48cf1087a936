<?php

namespace App\Console\Commands\WwUser;

use App\Models\WwCorpInfo;
use App\Models\WwUser;
use App\Services\Corp\WwProvApiService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CheckWwUserLicenseTime extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'WwUser:CheckWwUserLicenseTime';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '整体刷新所有企微下的销售的许可证状态';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Log::info('整体刷新所有企微下的销售的许可证状态');
        $corpList = WwCorpInfo::query()
//            ->whereIn('id',[138])
            ->orderBy('id','desc')->get();
        $i = 0;
        if($corpList->isNotEmpty()){
            $count = count($corpList->toArray());
            foreach ($corpList as $corpInfo) {
                WwProvApiService::checkLicenseTime($corpInfo);
                $i++;
                $this->info('执行进度：' . round($i / $count * 100, 2) . '%');
            }
        }
    }
}
