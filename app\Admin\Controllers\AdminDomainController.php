<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Grid\domains\BatchDomainStatus;
use App\Admin\Actions\Grid\domains\DnsResolveAction;
use App\Admin\Actions\Grid\domains\GetCertificateDetailAction;
use App\Admin\Actions\Grid\domains\RefreshListenerStatusAction;
use App\Admin\Extensions\Tools\Domains\BatchDissociateCertificatesTools;
use App\Admin\Extensions\Tools\Domains\BatchRelatedLoadBalanceSsLTools;
use App\Admin\Extensions\Tools\Domains\BatchUploadCertificateTools;
use App\Admin\Forms\Domains\BatchCreateDomainForm;
use App\Admin\Repositories\AdminDomain;
use App\Models\AdminDomain as AdminDomainModels;
use App\Models\AdminUser;
use App\Services\BaoTaService;
use App\Services\Tools\UtilsService;
use Carbon\Carbon;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Show;
use Dcat\Admin\Widgets\Modal;
use Illuminate\Support\Facades\Request;

/**
 * @property $admin_uid
 * @property $input
 */
class AdminDomainController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid(): Grid
    {
        return Grid::make(new AdminDomain(), function (Grid $grid) {
            /** 对话框新增 */
            $grid->disableCreateButton();
            $grid->tools(function (Grid\Tools $tools) {
                $className = collect(Request::segments())->last();
                $tools->append(UtilsService::dialogForm('添加', Request::url() . '/create', "create-{$className}"));
            });
            $grid->model()->orderBy('id', 'desc');
            $grid->column('id')->sortable();
            $grid->column('admin_uid')->copyable();
            if (AdminUser::isSystemOp() || Admin::user()->isRole("domain")) {
                $grid->model()->orderByDesc("id");
                $grid->column("admin_name", "用户")->display(function () {
                    return Admin::user()->where("id", $this->admin_uid)->value('name');
                });
            } else {
                $grid->model()->where("admin_uid", Admin::user()->id)->orderByDesc("id");
            }
            $grid->column('ali_cert_id')->copyable();
            $grid->column('domain')->copyable();
            $grid->column('host', 'DNS解析')->action(DnsResolveAction::class);
            $grid->column('status')->using([1 => '可用', 0 => '不可用'])->badge([
                'default' => 'primary', // 设置默认颜色，不设置则默认为 default
                1 => 'success',
                0 => 'danger',
            ]);
            $grid->column('listener_status','证书关联负载均衡')->using(AdminDomainModels::LISTENTER_STATUS)->badge([
                'noAssocia' => 'grey', // 设置默认颜色，不设置则默认为 default
                'Associating' => 'warning',
                'Associated' => 'success',
                'Diassociating' => 'danger',
            ]);
            $grid->column('刷新关联状态')->action(RefreshListenerStatusAction::class);
            //$grid->column('获取证书信息')->action(GetCertificateDetailAction::class);
            $grid->column('upload_status','证书上传')->using(AdminDomainModels::CERT_UPLOAD_STATUS)->badge([
                0 => 'grey', // 设置默认颜色，不设置则默认为 default
                1 => 'success',
                2 => 'danger',
                3 => 'pink',
            ]);
            $grid->column('ssl_ex_time')->display(function ($value) {
                if (empty($value)) {
                    return '';
                }

                $date = Carbon::parse($value);
                /** 使用 Carbon 的 isPast() 方法检查是否过期 */
                $isExpired = $date->isPast();

                return sprintf(
                    '<span style="%s">%s%s</span>',
                    $isExpired ? 'color: #ff4d4f; font-weight: bold;' : 'color: #52c41a;',
                    $date->format('Y-m-d'),
                    $isExpired ? ' (已过期)' : ''
                );
            });

            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) use ($grid) {
                $filter->panel();
                $filter->expand();
                $filter->equal('id')->width(2);
//                    $filter->equal('admin_uid');
                $filter->like('domain')->width(2);
                //获取域名admin_uid列表
                $adminUidList = AdminDomainModels::query()->pluck("admin_uid")->toArray();
                //过滤掉1（超级管理员）
                $adminUidList = array_filter($adminUidList, function ($value) {
                    return $value != 1; // 删除 'banana'
                });
                //select列表 查询用户表的id为adminUidList的用户，pluck创建一个键值对
                $adminUidListName = AdminUser::query()->whereIn("id", $adminUidList)->pluck("username", "id");
                $filter->equal('admin_uid', '用户')->select($adminUidListName)->width(2);
                $filter->equal('status')->select([0 => '不可用', 1 => '可用'])->width(2);
                $filter->lt('ssl_ex_time', '过期时间小于')->width(2);
                $filter->equal('upload_status', '证书上传')->width(2)->select(AdminDomainModels::LISTENTER_STATUS);
                $filter->equal('listener_status', '证书关联负载均衡')->width(2)->select(AdminDomainModels::CERT_UPLOAD_STATUS);
            });
            $grid->disableViewButton();
//                $grid->disableBatchActions();
            $grid->tools([new BatchDomainStatus()]);//批量配置状态

            //批量关联证书到负载均衡
            if (AdminUser::isSystemOp()) {
                $grid->tools([new BatchUploadCertificateTools()]);//上传证书
                $grid->tools([new BatchRelatedLoadBalanceSsLTools()]);//关联证书到负载均衡
                $grid->tools([new BatchDissociateCertificatesTools()]);//解除负载均衡关联
                $grid->tools(
                    Modal::make()
                        ->xl()
                        ->title('域名管理操作说明')
                        ->button('<button class="btn btn-primary"><i class="feather icon-info"></i>&nbsp;&nbsp;说明</button>')
                        ->body(view("admin.illustrate.adminDomainIllustrate"))
                );}
//            $modal = Modal::make()
//                ->lg()
//                ->title('批量新增')
//                ->body(BatchCreateDomainForm::make())
//                ->button('<button class="btn btn-primary float-right" style="margin-left: 5px"><i class="feather icon-star"></i>&nbsp&nbsp批量新增</button>');
//            $grid->tools($modal);
            $grid->column('info', '信息')->display(function () {
                $name = Admin::user()->where("id", $this->admin_uid ?? "0")->value('name');
                $domain = parse_url($this->domain ?? 'https://?????.com', PHP_URL_HOST) ?: $this->domain ?? '未知用户';
                $date = substr($this->ssl_ex_time ?? "1999-00-00", 0, 10);
                return $name . ' ' . $domain . ' ' . $date;
            })->ClickCopy(0, '复制');
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail(mixed $id): Show
    {
        return Show::make($id, new AdminDomain(), function (Show $show) {
            $show->field('id');
            $show->field('admin_uid');
            $show->field('ali_cert_id');
            $show->field('domain');
            $show->field('ssl_ex_time');
            $show->field('status');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form(): Form
    {
        return Form::make(new AdminDomain(), function (Form $form) {

            $form->display('id');
            $form->hidden('id');

            if (AdminUser::isSystemOp() || Admin::user()->isRole("domain")) {
                $form->select("admin_uid", '用户')->options(AdminUser::query()->orderBy('id')->pluck("username", "id"))->required();
            } else {
                $form->hidden("admin_uid")->default(Admin::user()->id);
            }
            $form->text('ali_cert_id');
            $form->url('domain')->default('https://')->required();
            $form->date('ssl_ex_time')
                ->default(Carbon::now()->addDays(90)->format('Y-m-d'))
                ->required();
            $form->radio('status')->options([0 => '不可用', 1 => '可用'])->required()->default(1);
            $form->radio('upload_status','证书上传')->options(AdminDomainModels::CERT_UPLOAD_STATUS)->required();
            $form->radio('listener_status','证书关联负载均衡')->options(AdminDomainModels::LISTENTER_STATUS)->required();

            $form->disableViewButton();
            $form->disableViewCheck();
            $form->disableDeleteButton();

        })->saving(function (Form $form) {
            $check = AdminDomainModels::query()->where('domain', $form->input('domain'))->first();
            if ($form->isCreating()) {
                if ($check) {
                    return $form->response()->alert()->error('提示')->detail('该域名已存在：' . $form->input('domain'));
                }
            }
            //同步添加域名到宝塔
//            $websites = BaoTaService ::Websites();
//            if ($websites && isset($websites['data'])) {
//                $data = $websites['data'];
//                $id = $data[2]['id'];
//                $webname = 'api.smart-ark.cn';//网站名称
//                $domain = $form->input('domain');//要添加的域名
//                $addrRes = BaoTaService::WebAddDomain($id, $webname, $domain);
//                if ($addrRes && isset($addrRes['status']) && !$addrRes['status']) {
//                    return $form->response()->error('宝塔添加域名失败：' . $addrRes['msg'] ?? '');
//                }
//            } else {
//                return $form->response()->error('获取网址列表失败。');
//            }
        });

    }


}
