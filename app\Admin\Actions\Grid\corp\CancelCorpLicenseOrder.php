<?php

namespace App\Admin\Actions\Grid\corp;

use App\Models\AdminUser;
use App\Models\CorpLicenseOrders;
use App\Services\NotifySendService;
use App\Services\ProviderService;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Traits\HasPermissions;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Psr\SimpleCache\InvalidArgumentException;

class CancelCorpLicenseOrder extends RowAction
{
    /**
     * @return string
     */
    protected $title = '<i class="feather icon-x" style="margin:0 10px 0 10px">取消订单</i>';

    /**
     * Handle the action request.
     *
     *
     * @return Response
     * @throws InvalidArgumentException
     */
    public function handle()
    {
        $id = $this->getKey();

        $order = CorpLicenseOrders::query()->find($id);
        if (!$order || !AdminUser::isAdmin($order)) {
            return $this->response()->alert()->error('提示')->detail('订单未找到，请刷新页面后重试。');
        }
        if($order->is_wx_pay == 1){
            return $this->response()->alert()->error('提示')->detail('订单已支付，无法取消。');
        }
        $res = ProviderService::cancelOrder($order->corpInfo->corp_id, $order->order_id);
        if(!isset($res) || $res['errcode'] != 0){
            $message = '企微许可证订单，ID' . $order->id . '，管理员手动取消订单失败，原因；' . $res['errmsg'] ?? '';
            NotifySendService::sendWorkWeixinForError($message);
            return $this->response()->alert()->error('提示')->detail('订单取消失败，原因：' . $res['errmsg'] ?? '');
        }
        $order->is_wx_pay = 2;//管理员手动取消
        $order->save();
        $message = '企微许可证订单，ID' . $order->id . '后台管理员已手动取消订单';
        NotifySendService::sendWorkWeixinForError($message);
        return $this->response()->alert()->success('提示')->detail('订单取消成功');
    }

    /**
     * @return array
     */
    public function confirm(): array
    {
        return ['确认取消订单？'];
    }

    /**
     * @param Model|Authenticatable|HasPermissions|null $user
     *
     * @return bool
     */
    protected function authorize($user): bool
    {
        return true;
    }

    /**
     * @return array
     */
    protected function parameters()
    {
        return [
            //	         'id' => $this->row->id
        ];
    }
}
