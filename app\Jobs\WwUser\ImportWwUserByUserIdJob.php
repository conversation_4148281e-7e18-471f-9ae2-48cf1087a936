<?php

namespace App\Jobs\WwUser;


use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Models\AdminSubUserAuthCorp;
use App\Models\AdminUser;
use App\Models\CorpLabels;
use App\Models\WwAppList;
use App\Models\WwUser;
use App\Models\WwUsersGroupsRel;
use App\Models\WwUsersImportTask;
use App\Server\WwCorpApi;
use App\Services\Corp\WwCorpApiService;
use App\Services\Corp\WwProvApiService;
use Dcat\Admin\Admin;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ImportWwUserByUserIdJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private WwUsersImportTask $importTask;
    private mixed $userIdInfo;
    private mixed $userNames;
    private AdminSubUserAuthCorp $corpAuthRecord;
    private WwAppList $wwApp;
    private mixed $adminUid;
    private mixed $input;

    protected $ip;


    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(WwUsersImportTask $importTask, $userIdInfo, $userNames, AdminSubUserAuthCorp $corpAuthRecord, WwAppList $wwApp, $adminUid, $input, $ip = '')
    {
        $this->importTask = $importTask;
        $this->userIdInfo = $userIdInfo;
        $this->userNames = $userNames;
        $this->corpAuthRecord = $corpAuthRecord;
        $this->wwApp = $wwApp;
        $this->adminUid = $adminUid;
        $this->input = $input;
        $this->ip = $ip;
    }


    /**
     * 防止任务重叠。 expireAfter 设置锁的最大持有时间（防止任务崩溃导致死锁）
     *
     * @return array
     */
    public function middleware()
    {
        $userid = $this->userIdInfo['userid'] ?? "";
        if (empty($userid)) {
            return [];
        }
        return [(new WithoutOverlapping($userid))->releaseAfter(10)->expireAfter(15)];
    }


    /**
     * Execute the job.
     *
     * @return mixed
     */
    public function handle()
    {
        $importTask = $this->importTask;
        $userIdInfo = $this->userIdInfo;
        $userNames = $this->userNames;
        $corpAuthRecord = $this->corpAuthRecord;
        $wwApp = $this->wwApp;
        $adminUid = $this->adminUid;
        $input = $this->input;
        $date = date('Y-m-d H:i:s');

        // 导入任务更新为导入中
        Log::info('导入任务更新为导入中');
        $importTask->status = 1;
        $importTask->save();
        $subLogData = [
            'task_id' => $importTask->id,
            'open_userid' => $userIdInfo['open_userid'] ?? "",
            'user_id' => $userIdInfo['userid'] ?? "",
            'user_name' => $userNames[$userIdInfo['userid']] ?? "",
            'ww_user_id' => 0,
            'created_at' => $date,
            'updated_at' => $date
        ];

        // //获取企微标签数据
        $tagData = CorpLabels::getCorpLabelsByCorpAuth($corpAuthRecord);

        //判断是否重复导入
        $repeatStatus = $input['repeat_status'] ?? 0;
        if (!$repeatStatus) {
            /** @var WwUser $wwUser */
            $wwUser = WwUser::query()->where([
                "admin_uid" => $adminUid,
                'corp_id' => $corpAuthRecord->corpInfo->id,
                "corp_auth_id" => $corpAuthRecord->id, // 授权记录ID
                'open_user_id' => $userIdInfo['open_userid'],
                'type' => 1 // 类型 1-销售 2-群
            ])->first();

            if (!$wwUser) {
                $wwUser = new WwUser();
                $wwUser->add_method = 1; // 添加方式 1-获客助手 2-二维码
            }
        } else {
            $wwUser = new WwUser();
            $wwUser->add_method = 1; // 添加方式 1-获客助手 2-二维码
        }
        // 基础信息
        $wwUser->admin_uid = $adminUid;
        $wwUser->ww_app_id = $wwApp->id;
        $wwUser->corp_id = $corpAuthRecord->corpInfo->id;
        $wwUser->corp_auth_id = $corpAuthRecord->id;
        $wwUser->type = 1;
        $wwUser->open_user_id = $userIdInfo['open_userid'];
        $wwUser->user_id = $userIdInfo['userid'];

        //判断可见范围
        $wwUserData = WwCorpApiService::users_get($corpAuthRecord->corpInfo, $wwUser->open_user_id);
        if (!isset($wwUserData['userid'])) {
            // 失败了
            $subLogData['desc'] = '请检查用户是否在可见范围';
            DB::table("ww_users_import_task_sub_log")->insert($subLogData);
            return false;
        }
        $wwUser->name = $userNames[$userIdInfo['userid']] ?? $wwUserData['name'];
        $wwUser->alias = $wwUserData['alias'] ?? "";
        $wwUser->status = $wwUserData['status'] ?? ""; // 激活状态: 1=已激活，2=已禁用，4=未激活，5=退出企业


        //判断是否需要创建联系我 二维码
        if (empty($wwUser->qrcode) && isset($input['check_qrcode']) && $input['check_qrcode'] == 0) {
            $contactWay = WwCorpApiService::add_contact_way($corpAuthRecord->corpInfo, $wwUser->open_user_id, true, getLinkState());
            if (isset($contactWay['config_id'])) {
                $wwUser->qrcode           = $contactWay['qr_code'];
                $wwUser->qrcode_config_id = $contactWay['config_id'];
            } else {
                $subLogData['desc'] = isset($contactWay['errmsg']) ? WwUser::getCusAcqLinkErrString($contactWay['errmsg']) : "";
                DB::table("ww_users_import_task_sub_log")->insert($subLogData);
                return false;
            }
        }

        //处理销售标签
        $wwUser->ww_corp_label_ids = $input['ww_corp_label_ids'];
        $wwUser->ww_corp_label_names = WwUser::handleWwUserCorlLabel($tagData, $input['ww_corp_label_ids']);

        //判断有没有获客助手ID 没有则创建
        if (!$wwUser->cus_acq_link_id) {
            $wwUser = WwUser::createCusAcqLink($corpAuthRecord->corpInfo, $wwUser);
        }

        //其他数据整理
        $wwUser->weight = $input['weight'];
        $wwUser->wind_label = $input['label'] ?? "";
        $wwUser->auto_status_config = $input['auto_status_config'];
        $wwUser->up_time = $input['up_time'];
        $wwUser->down_time = $input['down_time'];
        $wwUser->down_add_count = $input['down_add_count'];
        $wwUser->subscribe = 1;// 可见范围是正常的

        //欢迎语
        $wwUser->welcome_message  = $input['welcome_message'] ?? "";


        // 如果勾选的上线并且获客助手的状态是1 则可以进行上线
        $online_status = 0;
        if ($input['online_status'] == 1 && $wwUser->cus_acq_link_status == 1) {
            $online_status = 1;
        }
        $wwUser->online_status = $online_status;
        $wwUser->save();

        //添加销售分组数据
        if (!empty($input['group_id'])) {
            WwUsersGroupsRel::createWwGroupRel($wwUser, $input['group_id']);
        }

        //处理操作导入销售日志
        WwUser::importWwUserActionLogHandle($wwUser, $input, $adminUid, $this->ip);

        //销售今日展示量清0队列
        WwUser::reSetTodayShowCount($adminUid);

        // 判断企业微信的试用期，是否存在，如果不存在，需要刷新一下
        if (empty($corpAuthRecord->corpInfo->trail_end_time) || $corpAuthRecord->corpInfo->trail_end_time == '1970-01-01 08:00:00') {
            WwProvApiService::get_app_license_info($corpAuthRecord->corpInfo);
        }
        //检查许可证有效期队列
        CheckLicenseTimeJob::dispatch($corpAuthRecord->corpInfo)->onQueue('check_license_time');;
        $subLogData['desc'] = '成功';
        $subLogData['ww_user_id'] = $wwUser->id;
        DB::table("ww_users_import_task_sub_log")->insert($subLogData);
        return true;
    }
}
