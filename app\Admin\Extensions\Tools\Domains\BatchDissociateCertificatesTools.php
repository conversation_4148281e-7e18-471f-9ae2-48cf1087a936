<?php

namespace App\Admin\Extensions\Tools\Domains;

use Admin;
use App\Admin\Extensions\Grid\BatchActionPlus;
use App\Models\AdminDomain;
use App\Models\AdminUser;
use App\Services\AlibabaCloudService;
use App\Services\NotifySendService;
use Dcat\Admin\Grid\BatchAction;

class BatchDissociateCertificatesTools extends BatchActionPlus
{
    public $title = '<button class="btn btn-primary" style="margin-left: 5px"><i class="feather icon-minimize-2"></i>&nbsp;&nbsp;解除负载均衡关联</button>';

    protected string $listenerId = 'lsn-muwx0iancv5jwv6bb5';

    // 处理请求
    public function handle()
    {
        $keys = $this->getKey();

        $domains = AdminDomain::query()
            ->where('listener_status', 'Associated')
            ->whereIn('id', $keys)
            ->get();
        if ($domains->isEmpty()) {
            return $this->response()
                ->alert()
                ->error('提示')
                ->detail('域名不存在');
        }

        $ali_cert_id = array_column($domains->toArray(), 'ali_cert_id');
        $response = AlibabaCloudService::dissociateCertificates($this->listenerId, $ali_cert_id);
        if (!$response['success']) {
            $errMsg = $response['message'];
            $errCodeMsg = [
                'ResourceNotAssociated.Certificate' => '证书资源未关联'
            ];
            if (preg_match('/\b(\d+-[a-z]+-[a-z]+)\b/', $response['message'], $matches)) {
                $errMsg = '阿里证书ID:' . $matches[1] . '没有关联。<br>请手动刷新关联状态';
            }
            return $this->response()
                ->alert()
                ->error($errCodeMsg[$response['errors']['errCode']] ?? $response['errors']['errCode'])
                ->detail($errMsg)
                ->refresh();
        }
        /** @var AdminUser $adminUser */
        $adminUser = Admin::user();
        /** 发送消息到运营群 */
        NotifySendService::sendWopForMessage(
            title: "域名管理-取消关联证书通知",
            contentLines:[
                "用户ID" => $adminUser->id,
                "用户姓名" => $adminUser->username,
                "操作域名ID" => implode(',',$keys),
                "域名证书ID" => implode(',',$ali_cert_id),
                "操作时间" => date("Y-m-d H:i:s")
            ]
        );
        return $this->response()
            ->alert()
            ->success("请求完成")
            ->detail("请手动刷新关联状态")
            ->refresh();


    }

    public function confirm()
    {
        return ['提示', '确认要解除证书关联负载均衡？'];
    }

    // 设置请求参数
    public function parameters()
    {

    }

}
