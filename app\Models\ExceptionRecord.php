<?php

	namespace App\Models;

	use Dcat\Admin\Traits\HasDateTimeFormatter;
	use Illuminate\Database\Eloquent\Model;

	/**
	 * @property false|mixed|string $server
	 * @property bool|mixed|string  $ip
	 * @property mixed|string       $url
	 * @property mixed|string       $message
	 * @property int|mixed          $line
	 * @property mixed|string       $file
	 * @property int|mixed          $code
	 */
	class ExceptionRecord extends Model
	{
		use HasDateTimeFormatter;

		protected $table = 'exception_record';

	}
