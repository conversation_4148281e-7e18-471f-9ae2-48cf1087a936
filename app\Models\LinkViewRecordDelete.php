<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property mixed|string $area
 * @property mixed|string $district
 * @property mixed|string $city
 * @property mixed|string $prov
 * @property mixed|string $isp
 * @property mixed|string $owner
 * @property mixed|string $accuracy
 * @property bool|mixed|string $ip
 * @property false|mixed|string $other
 * @property mixed $referer
 * @property mixed $ua
 * @property mixed|string $click_id
 * @property mixed $admin_uid
 * @property int|mixed $ww_link_id
 * @property mixed $user_id
 * @property mixed $link_click_record_id
 * @property mixed $need_shield
 * @property int|mixed $is_pre_view
 * @property int|mixed $is_ad_user
 * @property int|mixed $is_deny_ip
 * @property mixed $vc_count
 * @property mixed $add_count
 * @property int|mixed $is_wechat
 * @property int|mixed $is_mobile
 * @property int|mixed $is_area
 * @property int|mixed $is_white
 * @property int|mixed $view_count
 * @property mixed|string $page_type
 * @property int|mixed $is_black
 * @property mixed|string $shield_reason
 * @property int|mixed $xt_status
 * @property mixed $qrcode_id
 * @property mixed $ww_user_name
 * @property mixed $ww_user_id
 * @property mixed $state
 * @property WwUser $wwUserInfo
 * @property WwUser $wwUserInfoWithTrashed
 * @property mixed $id
 * @property mixed $created_at
 * @property mixed|string $add_time
 * @property mixed $add_ww_user_id
 * @property mixed $ww_user_group_id
 * @property WwUsersGroup $wwUserGroup
 * @property int|mixed $deep
 */
class LinkViewRecordDelete extends Model
{
    use HasDateTimeFormatter;

    protected $table = 'link_view_record_delete';

    public function wwUserInfo(): BelongsTo
    {
        return $this->BelongsTo(WwUser::class, 'ww_user_id', 'id');
    }

    public function wwUserInfoWithTrashed(): BelongsTo
    {
        return $this->BelongsTo(WwUser::class, 'ww_user_id', 'id')->withTrashed();
    }

    public function wwUserGroup(): BelongsTo
    {
        return $this->BelongsTo(WwUsersGroup::class, 'ww_user_group_id', 'id')->withTrashed();
    }

    public function linkInfo(): BelongsTo
    {
        return $this->BelongsTo(WwLink::class, 'ww_link_id', 'id')->withTrashed();
    }
}
