<?php

namespace App\Jobs\WwUserAddRecord;

use App\Http\Controllers\WorkWeixin3rd\ChangeExternalContact;
use App\Http\Controllers\WorkWeixinAppController;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class AgainSetTagJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $task;
    protected $addRecordTotal;

    protected $addRecord;
    protected $addTags;
    protected $removeTags;


    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($task, $addRecordTotal, $addRecord, $addTags, $removeTags)
    {
        $this->task = $task;
        $this->addRecordTotal = $addRecordTotal;
        $this->addRecord = $addRecord;
        $this->addTags = $addTags;
        $this->removeTags = $removeTags;
    }

//    public function __destruct()
//    {
//        // 任务完成时执行
//        if (!$this->job->hasFailed()) {
//            // 记录任务完成
//            $this->task->status = 2;
//            $this->task->save();
//        }
//    }

    /**
     * Execute the job.
     *
     * @return mixed
     */
    public function handle()
    {
        $task = $this->task;
        $addRecordTotal = $this->addRecordTotal;
        $addTags = $this->addTags;
        $removeTags = $this->removeTags;
        $addRecord = $this->addRecord;
        $corpInfo = $addRecord->corpInfo;

        $app = WorkWeixinAppController::getApp($corpInfo->suite_id);
        $return = $app->getClient()->postJson('cgi-bin/externalcontact/mark_tag?access_token=' . WorkWeixinAppController::getCorpToken($corpInfo), [
            'userid' => $addRecord->follow_user_id,
            'external_userid' => $addRecord->external_userid,
            'add_tag' => $addTags ?? [],
            'remove_tag' => $removeTags ?? [],
        ])->toArray();
        Log::info('进粉记录ID： ' . $addRecord->id . '，补打标签队列执行结果：' . json_encode($return));
        $obj = new ChangeExternalContact();
        $message = [
            'ToUserName' => $corpInfo->corp_id,
            'UserID' => $addRecord->follow_user_id,
            'ExternalUserID' => $addRecord->external_userid,
        ];
        $obj->edit_external_contact($message);

        return true;
    }

}
