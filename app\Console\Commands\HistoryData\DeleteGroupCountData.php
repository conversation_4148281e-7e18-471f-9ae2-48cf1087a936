<?php

namespace App\Console\Commands\HistoryData;

use App\Models\LinkViewRecord;
use App\Models\WwUsersGroupsCount;
use App\Services\NotifySendService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DeleteGroupCountData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'Data:DeleteGroupCountData';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '删除wr_ww_users_groups_count 15天前历史数据';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $deleteDate = date('y-m-d H:i:s', strtotime('-15 day'));
        try {
            $deletedCount = WwUsersGroupsCount::query()
                ->where('created_at', '<=', $deleteDate)
                ->chunkById(1000, function ($records) {
                    $ids = $records->pluck('id')->toArray();
                    WwUsersGroupsCount::whereIn('id', $ids)->delete();
                });
            NotifySendService::sendWorkWeixinForError('已完成销售分组历史数据清理，数量： ' . $deletedCount);
        } catch (\Exception $e) {
            $errorMsg = '删除销售分组统计数据失败：' . $e->getMessage();
            NotifySendService::sendWorkWeixinForError($errorMsg);
            throw $e;
        }
        return Command::SUCCESS;
    }

}
