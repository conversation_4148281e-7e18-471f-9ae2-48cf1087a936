<?php

namespace App\Admin\Actions\Grid\domains;

use App\Models\AdminDomain;
use App\Services\AlibabaCloudService;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;

class GetCertificateDetailAction extends RowAction
{

    /**
     * @return string
     */
    public function title(): string
    {
        return '<i class="fa fa-info-circle"></i> 获取证书详情';
    }


    public function handle(): Response
    {
        $key = $this->getKey();
        $adminDomain = AdminDomain::query()->find($key);

        if (!$adminDomain) {
            return $this->response()->alert()->error('错误')->detail('未找到域名信息');
        }

        $getCertDetail = AlibabaCloudService::getUserCertificateDetail(
            certId: $adminDomain->ali_cert_id
        );
        if (!$getCertDetail['success']) {
            return $this->response()->alert()->error('获取证书详情失败')->detail($getCertDetail['message']."<br>".$getCertDetail['errors']['requestId']);
        }

        $data = $getCertDetail['data'];

        // 构建证书详情信息
        $detail = $this->buildCertificateDetail($data, $adminDomain);

        return $this->response()->alert()->success('证书详情')->detail($detail);
    }

    /**
     * 构建证书详情信息 - 整齐对齐格式
     */
    private function buildCertificateDetail($data, $adminDomain): string
    {
        $name = $data['Name'] ?? '未知';
        $certId = $data['CertId'] ?? '未知';
        $certIdentifier = $data['CertIdentifier'] ?? '未知';
        $common = $data['Common'] ?? '未知';
        $notAfter = $data['NotAfter'] ?? '未知';
        $expired = $data['Expired'] ? '是' : '否';

        // 处理SAN域名列表
        $sans = (isset($data['Sans']) && is_array($data['Sans']))
            ? implode(', ', $data['Sans'])
            : '无';

        return "
<table style='width: 100%; font-size: 14px; line-height: 1.8; border-collapse: collapse;'>
    <tr><td style='font-weight: bold; width: 80px; padding: 4px 0; vertical-align: top;'>域名：</td><td style='padding: 4px 0;'>$adminDomain->domain</td></tr>
    <tr><td colspan='2' style='padding: 8px 0;'></td></tr>
    <tr><td style='font-weight: bold; padding: 4px 0; vertical-align: top;'>证书名称：</td><td style='padding: 4px 0; word-break: break-all;'>$name</td></tr>
    <tr><td style='font-weight: bold; padding: 4px 0; vertical-align: top;'>证书ID：</td><td style='padding: 4px 0; font-family: monospace;'>$certId</td></tr>
    <tr><td style='font-weight: bold; padding: 4px 0; vertical-align: top;'>证书标识：</td><td style='padding: 4px 0; font-family: monospace; word-break: break-all;'>$certIdentifier</td></tr>
    <tr><td style='font-weight: bold; padding: 4px 0; vertical-align: top;'>主域名：</td><td style='padding: 4px 0; font-weight: 500;'>$common</td></tr>
    <tr><td style='font-weight: bold; padding: 4px 0; vertical-align: top;'>到期时间：</td><td style='padding: 4px 0; font-weight: 500;'>$notAfter</td></tr>
    <tr><td style='font-weight: bold; padding: 4px 0; vertical-align: top;'>是否过期：</td><td style='padding: 4px 0; color: " . ($data['Expired'] ? '#ff4757' : '#2ed573') . "; font-weight: bold;'>$expired</td></tr>
    <tr><td style='font-weight: bold; padding: 4px 0; vertical-align: top;'>SAN域名：</td><td style='padding: 4px 0;'>$sans</td></tr>
</table>
        ";
    }




//    /**
//     * @return mixed
//     */
//    public function confirm()
//    {
//        // 不需要确认对话框
//      return ['确认刷新？提交后将会给客户发送消息提醒。'];
//    }
}
