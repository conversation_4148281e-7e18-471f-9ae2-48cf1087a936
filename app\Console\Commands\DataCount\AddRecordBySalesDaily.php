<?php

namespace App\Console\Commands\DataCount;

use App\Models\AdminUser;
use App\Models\CountCorpsData;
use App\Models\WwUser;
use App\Models\WwUserAddRecord;
use App\Models\WwUserCorpsDataByDay;
use App\Models\WwUserGroupsDataByDay;
use App\Models\WwUsersDataByDay;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AddRecordBySalesDaily extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'Data:count';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '按销售、按天聚合、统计进粉数据';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $dates = [
            date("Y-m-d", time()),
            date("Y-m-d", (time() - 86400)),
        ];
        foreach ($dates as $date) {
            $this->info($date);
            $this->countData($date);//
            $this->counCorpData($date);//
        }

        return 0;
    }

    public function countData($date)
    {
        try {
            $addRecordData = [];
            //查询这一天所有的进粉数据
            WwUserAddRecord::query()->whereBetween("external_contact_created_at", [
                $date . " 00:00:00",
                $date . " 23:59:59",
            ])
                ->select('id', 'corp_id', 'admin_uid', 'ww_user_id', 'ww_user_group_id', 'is_delete', 'admin_uid', 'customer_start_chat', 'ocpx_result')
                ->orderByDesc('id')
                ->chunkById(1000, function ($addRecords) use (&$addRecordData, $date) {
//            $this->info("处理" . count($addRecordData));

                foreach ($addRecords as $addRecord) {
                    //初始化统计表
                    $data_key_list = [
                        'ww_user' => $addRecord->admin_uid . '_wwuser_' . $addRecord->ww_user_id,
                        'ww_user_group' => $addRecord->admin_uid . '_wwusergroup_' . $addRecord->ww_user_group_id,
                        'corp' => $addRecord->admin_uid . '_corp_' . $addRecord->corp_id,
                    ];
                    foreach ($data_key_list as $data_key => $data_key_value) {
                        if (!isset($addRecordData[$data_key_value])) {
                            $addRecordData[$data_key_value] = [
                                'admin_uid' => $addRecord->admin_uid,
                                'corp_id' => $addRecord->corp_id ?? 0,
                                'add_count' => 0,
                                'cus_del_count' => 0,
                                'ww_del_count' => 0,
                                'pure_add_count' => 0,
                                'customer_start_chat_count' => 0,
                                'ocpx_count' => 0,
                                'ocpx_x_count' => 0,
                                'ocpx_json' => [
                                    '未配置行为' => 0,
                                    '未开启回传' => 0,
                                    '扣量' => 0,
                                    '同行为' => 0,
                                    '上报成功' => 0,
                                    '上报失败' => 0,
                                    '审核不报' => 0,
                                    '过滤[SCANCODE_WX]' => 0,
                                    '审核[RESERVATION]' => 0,
                                    '未知' => 0
                                ],
                                'data_key' => $data_key,
                            ];
                        }
                        //添加量
                        $addRecordData[$data_key_value]['add_count']++;
                        //客户删除
                        if ($addRecord->is_delete == 1) {
                            $addRecordData[$data_key_value]['cus_del_count']++;
                        }
                        //销售删除
                        if ($addRecord->is_delete == 2) {
                            $addRecordData[$data_key_value]['ww_del_count']++;
                        }
                        //净增
                        $addRecordData[$data_key_value]['pure_add_count'] = $addRecordData[$data_key_value]['add_count'] - $addRecordData[$data_key_value]['cus_del_count'] - $addRecordData[$data_key_value]['ww_del_count'];

                        //客户开口量 customer_start_chat_count
                        if ($addRecord->customer_start_chat) {
                            $addRecordData[$data_key_value]['customer_start_chat_count']++;
                        }
                    }
                }
            });


            foreach ($addRecordData as $key => $datum) {

                switch ($datum['data_key']) {
                    case 'ww_user':
                        $table = new WwUsersDataByDay();
                        $table_field = 'ww_user_id';
                        break;
                    case 'ww_user_group':
                        $table = new WwUserGroupsDataByDay();
                        $table_field = 'ww_user_group_id';
                        break;
                    case 'corp':
                        $table = new WwUserCorpsDataByDay();
                        $table_field = 'ww_user_corp_id';
                        break;
                }
                $idInfo = explode("_", $key);
                $admin_uid = $idInfo[0];
                $table_field_id = $idInfo[2];
                $where = [
                    $table_field => $table_field_id,
                    'admin_uid' => $admin_uid,
                    'date' => $date
                ];

                unset($datum['data_key']);
                $dataObj = $table::query()->where($where)->first();
                if (!$dataObj) {
                    $dataObj = $table;
                    $dataObj->$table_field = $table_field_id;
                    $dataObj->date = $date;
                }
                foreach ($datum as $k => $v) {
                    if (is_array($v)) {
                        $v = json_encode($v, JSON_UNESCAPED_UNICODE);
                    }
                    $dataObj->$k = $v;
                }
                $dataObj->save();
                if($table_field == 'ww_user_id'){ //如果是销售的统计 同步一下销售姓名和企微名称
                    $dataObj->ww_user_name = $dataObj->wwUser->name ?? '';
                    $dataObj->corp_name = $dataObj->corpInfo->corp_name ?? '';
                    $dataObj->save();
                }
            }
        } catch (\Exception $exception) {
            Log::error($exception->getMessage());
        }
    }

    public function counCorpData($date)
    {

        $data = WwUserCorpsDataByDay::query()->where('date', $date)->get();
        if ($data) {
            $dataRecord = [];
            foreach ($data as $data_key => $v) {
                //查询账户的主ID

                $adminInfo = AdminUser::query()->find($v->admin_uid);
                if ($adminInfo->parent_id) {
                    $adminUid = $adminInfo->parent_id;
                } else {
                    $adminUid = $adminInfo->id;
                }
                $flag_key = $adminUid . "_" . $v->ww_user_corp_id;
                if (!isset($dataRecord[$flag_key])) {
                    $dataRecord[$flag_key] = [
                        'admin_uid' => $adminUid,
                        'corp_id' => $v->ww_user_corp_id ?? 0,
                        'add_count' => 0,
                        'cus_del_count' => 0,
                        'ww_del_count' => 0,
                        'pure_add_count' => 0,
                        'customer_start_chat_count' => 0,
                    ];
                }

                $dataRecord[$flag_key]['add_count'] += $v->add_count;
                $dataRecord[$flag_key]['cus_del_count'] += $v->cus_del_count;
                $dataRecord[$flag_key]['ww_del_count'] += $v->ww_del_count;
                $dataRecord[$flag_key]['pure_add_count'] += $v->pure_add_count;
                $dataRecord[$flag_key]['customer_start_chat_count'] += $v->customer_start_chat_count;
            }


            foreach ($dataRecord as $key => $rv) {
                $idInfo = explode("_", $key);
                $admin_uid = $idInfo[0];
                $corp_id = $idInfo[1];
                $where = [
                    'date' => $date,
                    'admin_uid' => $admin_uid,
                    'corp_id' => $corp_id,
                ];
                $dataObj = CountCorpsData::query()->where($where)->first();
                if (!$dataObj) {
                    $dataObj = new CountCorpsData();
                    $dataObj->admin_uid = $admin_uid;
                    $dataObj->date = $date;
//                    $dataObj->save();
                }
//
                foreach ($rv as $k => $v) {
                    $dataObj->$k = $v;
                }
                $dataObj->save();
            }

        }

    }

}
