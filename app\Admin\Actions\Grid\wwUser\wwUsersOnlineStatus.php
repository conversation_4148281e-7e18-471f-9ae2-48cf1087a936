<?php

	namespace App\Admin\Actions\Grid\wwUser;

	use App\Jobs\AdminActionLogJob;
    use App\Models\AdminActionLog;
    use App\Models\AdminUser;
    use App\Models\WwUser;
    use App\Models\WwUsersOnlineLogs;
    use Dcat\Admin\Admin;
    use Dcat\Admin\Grid\BatchAction;
    use Illuminate\Http\Request;

    class wwUsersOnlineStatus extends BatchAction
	{
		protected mixed $action;

		// 注意action的构造方法参数一定要给默认值
		public function __construct($title = null, $action = 1)
		{
			$this->title  = $title;
			$this->action = $action;
			parent::__construct($title);
		}

		// 确认弹窗信息
		public function confirm()
		{
			//	    return '您确定要选中的页面吗？';
		}

		// 处理请求
		public function handle(Request $request)
		{
			// 获取页面的分组
			$keys = $this->getKey();

			// 获取请求参数
			$action = $request->get('action');
            $adminUid = Admin::user()->id;
			if ($adminUid == 1) {
				$wwUsers = WwUser::query()->whereIn("id", $keys)->get();
			} else {
				$wwUsers = WwUser::query()->where("admin_uid", $adminUid)->whereIn("id", $keys)->get();
			}

            $message = $action ? '上线成功' : '下线成功';
			/** @var WwUser $wwUser */
			foreach ($wwUsers as $wwUser) {
                if(!AdminUser::isAdmin($wwUser)){
                    return $this->response()->error("无权限操作");
                }
                $wwUser->online_status = $action;
                $wwUser->save();
				if ($action == 1) {
                    $source = '客户-批量操作上线';

					$checkData = WwUser::onlineCheck($wwUser,$source,$adminUid);
					if (!$checkData['status']) {
						return $this->response()->error($wwUser->name . "-" . $checkData['message'])->refresh();
					}
				}
                if($action == 0){
                    //添加销售下线记录
                    $onlineStatus = $action;//下线
                    $source = '客户-批量操作下线';
                    WwUsersOnlineLogs::addOnLineStatusLogs($wwUser, $onlineStatus, $source,$adminUid);
                }
                AdminActionLogJob::dispatch(
                    'set_online_status',
                    $wwUser->id,
                    AdminActionLog::ACTION_TYPE['销售'],
                    '批量上下线「' . $wwUser->name . '」批量'. $message,
                    getIp(),
                    Admin::user()->id,
                )->onQueue('admin_action_log_job');

			}

			return $this->response()->success($message)->refresh();
		}

		// 设置请求参数
		public function parameters()
		{
			return [
				'action' => $this->action,
			];
		}
	}
