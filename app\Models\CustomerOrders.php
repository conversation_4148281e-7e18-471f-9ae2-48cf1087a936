<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CustomerOrders extends Model
{
    use HasDateTimeFormatter;
    protected $table = 'customer_orders';

    const STATUS = [
        0 => '待支付',
        1 => '支付中',
        2 => '支付成功',
        3 => '支付失败'
    ];

    public function adminInfo(): BelongsTo
    {
        return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
    }

    public static function makeOrderNo($id = 0)
    {
        return  'WIND' . date('YmdHis') . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);
    }
}
