<?php
	
	namespace App\Models;
	
	use Dcat\Admin\Traits\HasDateTimeFormatter;
	use Illuminate\Database\Eloquent\Model;
	
	/**
	 * @property mixed             $id
	 * @property mixed|string      $md5_id
	 * @property int|mixed         $parent_uid
	 * @property mixed|string      $access_token_expires_in
	 * @property mixed|string      $refresh_token
	 * @property mixed|string      $access_token
	 * @property bool|mixed|string $ip
	 * @property mixed|string      $unionid
	 * @property mixed             $openid
	 * @property mixed|string      $appid
	 * @property mixed             $uuid
	 * @property mixed             $is_black
	 * @property mixed             $vc_count
	 * @property mixed             $add_count
	 * @property mixed $updated_at
	 * @property mixed $created_at
	 */
	class WechatUser extends Model
	{
		use HasDateTimeFormatter;
		
		protected $table = 'wechat_users';
		
		public static function getNewBlack(WechatUser $user, $isBlack)
		{
			if ($user->is_black <= 0) {
				$user->is_black = $isBlack;
			}
			return $user->is_black;
		}
	}
