<?php /** @noinspection PhpInconsistentReturnPointsInspection */

namespace App\Admin\Controllers;



use App\Admin\Actions\Grid\wwUser\BatchSetAddMethod;
use App\Admin\Actions\Grid\wwUser\BatchSetAutoUpOrOffine;
use App\Admin\Actions\Grid\wwUser\BatchSetGroup;
use App\Admin\Actions\Grid\wwUser\BatchSetOnlineStatus;
use App\Admin\Actions\Grid\wwUser\BatchSetWeight;
use App\Admin\Actions\Grid\wwUser\BatchSetWelcomeMessage;
use App\Admin\Actions\Grid\wwUser\BatchSetWindLabel;
use App\Admin\Actions\Grid\wwUser\BatchSetWwCorpLabel;
use App\Admin\Actions\Grid\wwUser\CreateCusAcqLink;
use App\Admin\Forms\WwUser\ImportWwUserFromHandUpload;
use App\Admin\Forms\WwUser\ImportWwUserFromUserId;
use App\Admin\Forms\WwUser\checkLicenseTimeForm;
use App\Admin\Forms\WwUser\ImportWwUserGroupFromOwner;
use App\Admin\Forms\WwUser\TransferLicenseFrom;
use App\Admin\Renderable\WwUser\ImportWwUserTask;
use App\Admin\Repositories\WwUser;
use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Models\AdminSubUser;
use App\Models\AdminSubUserAuthCorp;
use App\Models\AdminUser;
use App\Models\CorpLabels;
use App\Models\WwCorpInfo;
use App\Models\WwUserQrcode;
use App\Models\WwUsersGroup;
use App\Models\WwUsersGroupsRel;
use App\Models\WwUsersOnlineLogs;
use App\Services\Corp\WwCorpApiService;
use App\Services\Tools\UtilsService;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Show;
use Dcat\Admin\Widgets\Modal;
use Dcat\Admin\Widgets\Tab;
use Dcat\Admin\Widgets\Tooltip;
use EasyWeChat\Kernel\Form\File;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;


/**
 * @property $id
 * @property $down_add_count
 * @property $up_time
 * @property $down_time
 * @property $input
 * @property $alias
 * @property $name
 * @property $skip_verify
 */
class WwUserController extends AdminController
{


    public function index(Content $content)
    {
        // 工具栏按钮宽度
        Admin::style(
            <<<style
.custom-toolbar .btn{
    min-width: 150px;
}
style
        );
        /** 调整过滤器文本框宽度 */
        UtilsService::adjustFilterColumns();

        $content->row(function (Row $row) use ($content) {

            $type = request('_t', 1);
            $tab  = new Tab();
            if ($type == 1) {
                $tab->add('销售', $this->grid(1));
                $tab->addLink('群聊', request()->fullUrlWithQuery(['_t' => 2]));
                $tab->addLink('手动上传', request()->fullUrlWithQuery(['_t' => 3]));


            } elseif ($type == 2) {
                $tab->addLink('销售', request()->fullUrlWithQuery(['_t' => 1]));
                $tab->add('群聊', $this->grid(2), true);
                $tab->addLink('手动上传', request()->fullUrlWithQuery(['_t' => 3]));
            } else {
                $tab->addLink('销售', request()->fullUrlWithQuery(['_t' => 1]));
                $tab->addLink('群聊', request()->fullUrlWithQuery(['_t' => 2]));
                $tab->add('手动上传', $this->grid(3), true);
            }
            $row->column(12, $tab);
        });

        return $content->header("销售列表");
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid($type = '')
    {
        return Grid::make(new WwUser(['corpInfo', 'adminInfo', 'wwUserGroups','notUseQrCode']), function (Grid $grid) use ($type) {

            $grid->paginate(AdminUser::getPaginate());
            if (!AdminUser::isSystemOp()) {
                $grid->model()->whereIn("admin_uid", AdminSubUser::getAdminUids(Admin::user()->id));
            }
            /** @var AdminUser $adminUser */
            $adminUser = Admin::user();
            if (Admin::user()->isRole('customer') || AdminUser::isSystemOp()) { //如果是主账户，显示自身全部销售，以及子账户的全部销售
                $grid->column('归属账号')->display(function () use ($adminUser) {
                    if ($adminUser->id == $this->admin_uid) { //如果是自己创建的则直接显示主账号
                        return '主账号';
                    } else {
                        $username = $this->adminInfo->username ?? '';
                        return str_replace($adminUser->username, '', $username);
                    }
                });
            }
            $titles = [
                'id'                 => '销售ID',
                'name'               => '昵称',
                'online_status'      => '状态',
                'user_id'            => '销售ID',
                'wwUserGroups.title' => '分组(系统内名称)',
                'auto_status_config' => '自动上下线',
                'up_time'            => '上线时间(格式00:00:00)',
                'down_add_count'     => '下线加粉量(数字)',
                'down_time'          => '下线时间(格式00:00:00)',
                'weight'             => '权重',
                'corp_name'          => '企微主体',
                'ww_corp_label_names' => '企微标签',
                'wind_label'          => '智投标签',
            ];

            $grid->export()->titles($titles)->rows(function ($rows) {
                $data = [];
                foreach ($rows as $index => &$row) {

                    $auto_status_config = '';
                    switch ($row->auto_status_config) {
                        case 0:
                            $auto_status_config = '关闭';
                            break;
                        case 1:
                            $auto_status_config = '按时间段';
                            break;
                        case 2:
                            $auto_status_config = '按加粉量';
                            break;
                    }
                    $ww_corp_label_names = '';
                    if($row->ww_corp_label_names){
                        $ww_corp_label_names = implode(',',json_decode($row->ww_corp_label_names,true));
                    }
                    $tempData = [
                        'id'                 => $row->id,
                        'name'               => $row->name,
                        'user_id'            => $row->user_id,
                        'wwUserGroups.title' => $row->wwUserGroups->pluck('title')->implode(','),
                        'auto_status_config' => $auto_status_config,
                        'up_time'            => $row->up_time,
                        'online_status'      => $row->online_status ? "是" : "否",
                        'down_add_count'     => (int)$row->down_add_count ?? '',
                        'down_time'          => $row->down_time,
                        'weight'             => $row->weight,
                        'corp_name'          => $row->corpInfo->corp_name ?? '',
                        'ww_corp_label_names' => $ww_corp_label_names,
                        'wind_label'          => $row->wind_label ?? '',
                    ];
                    $data[]   = $tempData;
                }
                return collect($data);
            });
//            if (Admin::user()->isRole("wop")) {
//                $grid->disableActions();
//                $grid->disableBatchActions();
//            }
                $grid->fixColumns(0, -1);
                $grid->model()->where("type", $type)->orderByDesc("id");


                $grid->tools(function (Grid\Tools $tools) use ($type) {
                $tools->append('<div class="custom-toolbar-wrap" id="custom-toolbar-wrap" style="padding-top: 10px;">');

                $tools->append('<div class="custom-toolbar"  style="margin: 7px 0;text-align: right;padding: 3px;border-radius: .25rem;display: flex;flex-flow: wrap;gap:10px;">');
//                $tools->append('<div style="padding: .54rem 1.2rem!important;color: #888;">批量工具栏</div>');

                $tools->append(new BatchSetOnlineStatus()); // 批量上下线
                $tools->append(new BatchSetAutoUpOrOffine());
                $tools->append(new BatchSetWindLabel()); // 配置智投标签
                $tools->append(new BatchSetGroup());//批量配置分组
                $tools->append(new BatchSetWeight());//批量配置权重
                $tools->append(new BatchSetAddMethod());//批量配置加粉模式
                $tools->append(new BatchSetWelcomeMessage());//批量配置欢迎语
                if ($type != 3) {
                    $tools->append(new BatchSetWwCorpLabel());//批量配置企微标签
                }
                $tools->append('</div>');

                $tools->append('<div class="custom-toolbar"  style="margin: 7px 0;text-align: right;padding: 3px;border-radius: .25rem;display: flex;flex-flow: wrap;gap:10px;">');
//                $tools->append('<div style="padding: .54rem 1.2rem!important;color: #888;">其他工具栏</div>');
                $modal = Modal::make()
                    ->xl()
                    ->title('导入销售任务')
                    ->body(ImportWwUserTask::make()) // Modal 组件支持直接传递 渲染类实例
                    ->button('<button class="btn btn-primary" ><i class="feather icon-calendar"></i>&nbsp;导入销售任务</button>');
                $tools->append($modal);

                $modal = Modal::make()
                    ->lg()
                    ->title('刷新许可证')
                    ->body(checkLicenseTimeForm::make())
                    ->button('<button class="btn btn-primary" ><i class="feather icon-aperture"></i>&nbsp&nbsp刷新许可证</button>');
                $tools->append($modal);
                $tools->append('</div>');
                $tools->append('</div>');
            });




            $grid->showColumnSelector();
            $grid->disableCreateButton();
            $grid->disableViewButton();
            if ($type == 1) {
                $modal = Modal::make()
                    ->xl()
                    ->title('添加销售')
                    ->body(ImportWwUserFromUserId::make())
                    ->button('<button class="btn btn-primary float-right" style="margin-left: 5px"><i class="feather icon-user-plus"></i>&nbsp&nbsp添加销售</button>');
                $grid->tools($modal);
            }
            if ($type == 2) {
                $modal = Modal::make()
                    ->xl()
                    ->title('添加销售群（群主）')
                    ->body(ImportWwUserGroupFromOwner::make())
                    ->button('<button class="btn btn-primary float-right" style="margin-left: 5px"><i class="feather icon-plus-square"></i>&nbsp&nbsp添加销售群</button>');
                $grid->tools($modal);
            }
            if ($type == 3) {
                $modal = Modal::make()
                    ->xl()
                    ->title('手动添加')
                    ->body(ImportWwUserFromHandUpload::make())
                    ->button('<button class="btn btn-primary float-right" style="margin-left: 5px"><i class="feather icon-plus-square"></i>&nbsp&nbsp手动上传</button>');
                $grid->tools($modal);
            }


            $grid->column('id')->sortable();
            // $grid->fixColumns(0, 0);
            $grid->scrollbarX();
            $grid->column('corpInfo.id', "企微ID");
            $grid->column('corpInfo.corp_name', "企微");



            $grid->column('online_status')->switch('', true);
            if ($type == 1) {
                $grid->column('add_method')->radio([
                    1 => '获客助手',
                    2 => '二维码'
                ])->help('点击可切换加粉模式');
            } else {
                $grid->column('add_method')->using([
                    1 => '获客助手',
                    2 => '二维码'
                ])->help('点击可切换加粉模式');
            }
            $grid->column('auto_status_config')->display(function ($value) {
                $text       = '暂未配置';
                $configName = '暂未配置';
                switch ($value) {
                    case 0:
                        $configName = '手动操作';
                        $text       = '<p>手动上下线，未启用自动规则</p>';
                        break;
                    case 1:
                        $configName = '按时间段';
                        $text       = '<p>按时间段</p><p>上线时间:' . $this->up_time . '</p><p>下线时间:' . $this->down_time . '</p>';
                        break;
                    case 2:
                        $configName = '按加粉量';
                        $text       = '<p>按加粉量</p><p>上线时间:' . $this->up_time . '</p><p>下线条件:当日加粉量超过' . $this->down_add_count . '</p>';
                        break;
                }

                Tooltip::make('.help_message' . $this->id)->title($text);
                return '<div class="p4"><i class="feather icon-help-circle help_message' . $this->id . '">' . $configName . '</i></div>';
            });

            $grid->column('today_show_count')->sortable();  //增加排序
            if ($type != 3) {
                $grid->column('today_add_count')->sortable();   //增加排序
            }
            switch ($type) {
                case 1:
                    $grid->column('name');
                    $grid->column('user_id');
                    $grid->column('qrcode')->image('', '80');
                    break;
                case 2:
                    $grid->column('name', '群名称');
                    $grid->column('qrcode')->image('', '80');
                    break;
                case 3:
                    $grid->column('name', '群名称');
                    $grid->column('qrcode')->image('', '100');
                    break;
            }
            if (AdminUser::isSystemOp()) {

                $grid->column('notUseQrCode','二维码数量')->display(function ($notUseQrCode) {
                    return count($notUseQrCode);
                });
            }
            if ($type != 3) {
                $grid->column('subscribe')->using([
                    1 => '可见',
                    0 => '取消'
                ]);
            }

            $grid->column('weight');

            $grid->column('wwUserGroups', '所属分组')->display(function ($value) {
                if (count($value) <= 0) {
                    return "无";
                }
                $groups = [];
                foreach ($value as $item) {
                    $groups[] = $item->title;
                }
                return implode("<br/>", $groups);
            });
            $grid->column('wind_label')->display(function ($value) {
                if (empty($value)) {
                    return '无';
                }
                return $value;
            })->help("智投方舟管理标签，可通过标签批量给销售配置标签，<span style='color: pink'>非企业微信外部联系人标签。</span>");

            //如果是销售且获客助手状态为0，就添加刷新按钮，重新生成链接
            $grid->actions(function (Grid\Displayers\Actions $actions) use ($type) {
                $row = $actions->row;
                if ($type == 1) { //如果是销售的tab
                    if ($row['type'] == 1 && $row['cus_acq_link_status'] == 0) {
                        $actions->prepend(new CreateCusAcqLink());
                    }
                }

            });
            if ($type != 3) {
                $grid->column("ww_corp_label_names", "企微标签")->display(function ($value) {
                    $data = json_decode($value, true);
                    if (empty($data)) {
                        return "无";
                    }
                    $string = implode("<br/>", $data);
                    $string = trim($string);
                    $width  = 120;
                    Tooltip::make('.ww_corp_label_names_help_message' . $this->id)->title($string);
                    return '<div style="max-width: ' . $width . 'px; white-space: nowrap; overflow: hidden;text-overflow: ellipsis;" class="ww_corp_label_names_help_message' . $this->id . '">' . $string . '</div>';
                })->help("企业微信标签");
            }
            if ($type == 1) {
                $grid->column('welcome_message', '欢迎语')->display(function($value) {
                    if (empty($value)) {
                        return '<span class="text-muted">未设置</span>';
                    }

                    // 解析JSON格式的欢迎语，提取文本内容
                    $data = json_decode($value, true);
                    if ($data && isset($data['text']['content'])) {
                        $content = $data['text']['content'];
                    } else {
                        $content = $value; // 兼容旧格式
                    }

                    // 显示前30个字符
                    $shortContent = mb_strlen($content) > 30 ? mb_substr($content, 0, 30) . '...' : $content;

                    // 参考cus_acq_link_status的Tooltip实现方式
                    $class = '.welcome_msg_tooltip' . $this->id;

                    // 构建完整的欢迎语信息显示在Tooltip中
                    $tooltipContent = '欢迎语内容：<br>' . nl2br(htmlspecialchars($content));

                    // 如果有附件信息，也显示出来
                    if ($data && isset($data['attachments'][0])) {
                        $attachment = $data['attachments'][0];
                        $tooltipContent .= '<br><br>附件类型：';
                        if ($attachment['msgtype'] === 'image') {
                            $tooltipContent .= '图片<br>图片地址：' . htmlspecialchars($attachment['image']['pic_url'] ?? '');
                        } elseif ($attachment['msgtype'] === 'link') {
                            $tooltipContent .= '链接<br>链接标题：' . htmlspecialchars($attachment['link']['title'] ?? '') . '<br>链接地址：' . htmlspecialchars($attachment['link']['url'] ?? '');
                        }
                    }

                    Tooltip::make($class)->title($tooltipContent);

                    return '<span class="welcome_msg_tooltip' . $this->id . '" style="color: #1890ff; font-style: italic;">"' . htmlspecialchars($shortContent) . '"</span>';
                });
                $grid->column("cus_acq_link_status")->display(function ($value) {
                    if ($value) {
                        return '<span class="label" style="background:#21b978">正常</span>';
                    } else {
                        $class = '.link_name_ex_string' . $this->id;
                        Tooltip::make($class)->title(\App\Models\WwUser::getCusAcqLinkErrString($this->cus_acq_link_status_message));
                        return '<span class="label link_name_ex_string' . $this->id . '" style="background:#ea5455">异常</span>';
                    }
                });
            }
            if ($type != 3) {
                $grid->column('license_c_status')->using([0 => '过期', 1 => '有效', 2 => '预警'])->label([0 => 'danger', 1 => 'success', 2 => 'warning']);
                $grid->column('transfer_license')->display('转移许可证')->label()->modal(function (Grid\Displayers\Modal $modal) {
                    // 标题
                    $modal->title('转移许可证');
                    // 自定义图标
                    $modal->icon('');// feather icon-check-circle
                    $modal->xl();
                    // 传递当前行字段值
                    $data = [
                        'ww_user_id' => $this->id
                    ];
                    return TransferLicenseFrom::make()->payload($data);
                });
                $grid->column('license_c_ex_time')->display(function ($value) {
                    return explode(" ", $value)[0];
                });

            }



            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();

                $corpAuthRecord = AdminSubUserAuthCorp::getCorpAuthListBuyAdminUid(Admin::user()->id);
                $filter->equal("id", "id")->width(2);

                $filter->equal("corp_auth_id", "企业微信")->select($corpAuthRecord)->width(2);
                $filter->where('name', function ($query) {
                    $keyWord = trim($this->input);
                    if (empty($keyWord)) {
                        return;
                    }
                    $keyWords = UtilsService::splitKeywords($keyWord);
                    if (count($keyWords) > 1) {
                        $query->where(function ($query) use ($keyWords) {
                            foreach ($keyWords as $keyWord) {
                                $query->orWhere("name", "like", "%" . $keyWord . "%");
                            }
                        });
                    } else {
                        $query->where("name", "like", "%" . $keyWord . "%");
                    }
                })->width(2);
                $filter->where('alias', function ($query) {
                    $keyWord = trim($this->input);
                    if (empty($keyWord)) {
                        return;
                    }
                    $keyWords = UtilsService::splitKeywords($keyWord);
                    if (count($keyWords) > 1) {
                        $query->whereIn("alias", $keyWords);
                    } else {
                        $query->where("alias", "like", "%" . $keyWord . "%");
                    }
                })->width(2);
                $filter->where('user_id', function ($query) {
                    $keyWord = trim($this->input);
                    if (empty($keyWord)) {
                        return;
                    }
                    $keyWords = UtilsService::splitKeywords($keyWord);
                    if (count($keyWords) > 1) {
                        $query->whereIn("user_id",$keyWords);
                    } else {
                        $query->where("user_id", "like", "%" . $keyWord . "%");
                    }
                })->width(2);
                $filter->equal('auto_status_config', '自动规则')->select([0 => '关闭', 1 => '按时间段', 2 => '按进粉量'])->width(2);
                $filter->equal("online_status")->select([0 => '下线', 1 => '上线'])->width(2);
                $filter->like('wind_label')->width(2);
                $groupList = [];
                if(AdminUser::isSystemOp()){
                    $groupList = WwUsersGroup::query()->orderBy('id','desc')->pluck('title', 'id');
                }else{
                    if (Admin::user()->isRole('customer')) { //如果是“客户”的角色 也就是主账号
                        $groupList = WwUsersGroup::query()->whereIn("admin_uid", AdminSubUser::getAdminUids(Admin::user()->id))->pluck('title', 'id');
                    } else if(Admin::user()->isRole('customer_op') || Admin::user()->isRole('sub_user_ww')){ //如果是客户运营的角色 也就是子账号
                        $groupList = WwUsersGroup::query()->where("admin_uid", Admin::user()->id)->pluck('title', 'id');
                    }
                }

                $filter->where('ww_group_id', function ($query) {
//                    $wwUserIds = WwUsersGroupsRel::query()->where("ww_group_id", $this->input)->pluck("ww_user_id");
                    $wwUserIds = WwUsersGroupsRel::getWwUserIdsByGroupId($this->input);
                    $query->whereIn('id', $wwUserIds);
                }, '分组')->select($groupList)->width(2);
                $filter->equal("license_c_status")->select([0 => '过期', 1 => '有效', 2 => '预警'])->width(2);
                //所有带「所属账户」的列表，主账户都增加对应筛选
                if (AdminUser::isSystemOp()) {
                    $adminUsers = AdminUser::query()->pluck('username', 'id');
                    $filter->equal('admin_uid','用户')->select($adminUsers)->width(2);
                } elseif (Admin::user()->parent_id == 0) { //主账号
                    $adminIds = AdminSubUser::getALLAdminUids(Admin::user()->id);
                    $filter->equal("admin_uid", '操作账号')->select(AdminUser::query()->whereIn("id", $adminIds)->pluck("username", 'id')->toArray())->width(2);
                }
                $filter->equal("cus_acq_link_status")->select([0 => '异常', 1 => '正常'])->width(2);
                $filter->like("ww_corp_label_names", "企微标签")->width(2);
                $filter->equal("add_method")->select([
                    1 => '获客助手',
                    2 => '二维码'
                ])->width(2);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new WwUser(), function (Show $show) {
            if (!AdminUser::isAdmin($show->model())) {
                $show->field('N')->value("无数据");
            } else {
                $show->field('id');
                $show->field('user_id');
                $show->field('name');
            }
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new WwUser(['wwUserGroups']), function (Form $form) {
            if (!$form->isCreating() && !AdminUser::isAdmin($form->model())) {
                $form->display('N')->value("无数据");
            } else {
                $form->display('id');
                $form->hidden('id');
                $form->hidden('name');
                $form->hidden('corp_auth_id');
                $form->hidden('ww_corp_label_names');
                $form->hidden('auto_status_config');
                $form->disableViewCheck();
                $form->disableEditingCheck();
                $form->disableViewButton();
                $form->disableDeleteButton();
                $form->tree('ww_user_groups', '销售分组')
                    ->nodes(WwUsersGroup::getGroupListByAdminUid(Admin::user()->id))
                    ->customFormat(function ($v) {
                        if (!$v) {
                            return [];
                        }
                        // 这一步非常重要，需要把数据库中查出来的二维数组转化成一维数组
                        return array_column($v, 'id');
                    });
                $form->switch('online_status', '状态');
                $form->radio('auto_status_config', '自动上下线')->options([
                    0 => '关闭',
                    1 => '按时间段',
                    2 => '按加粉量',
                ])->when([1, 2], function ($form) {
                    $form->time('up_time', '上线时间')->format("HH:mm")->default(date("H:", time()) . "00", true);
                })->when([1], function ($form) {
                    $form->time('down_time', '下线时间')->format("HH:mm")->default(date("H:", time()) . "00", true)->help('如果选择按时间段自动上下线，且要设置下线时间，下线时间请大于上线时间。');
                })->when([2], function ($form) {
                    $form->number('down_add_count', '下线加粉量')->help("当日加够此数量的进粉后，该销售会自动下线。");
                })->default(0);
                $form->number('weight', '权重')->help("可以配置1-10的数字，数字越大，该销售展示概率越高")->default(1);
                $form->text("wind_label", "智投管理标签")->help("该标签为智投方舟后台批量管理销售使用的标签，可通过标签批量配置客服，不非企业微信后台的外部联系人标签。");
                $form->select("add_method")->options([
                    1 => '获客助手',
                    2 => '二维码'
                ]);


                //获取销售的企微标签
                $corpAuthId = 0;
                $tagList    = [];
                $tagData    = [];
                if ($form->isEditing()) {
                    $corpAuthId = $form->model()->corp_auth_id;//获取当前销售的corp_auth_id
                }

                if ($corpAuthId) {
                    $tagList = $this->getWwCorpLabel($corpAuthId);
                }
                if ($tagList) {
                    foreach ($tagList as $tag) {
                        $tagData[$tag['id']] = $tag['text'];
                    }
                }

                /** 欢迎语 */
                if ($form->isEditing()) {
                    if ($form->model()->welcome_message) {
                        $welcomeData = json_decode($form->model()->welcome_message, true);
                        if (is_array($welcomeData)) {
                            // 回显文本内容
                            if (isset($welcomeData['text']['content'])) {
                                $form->model()->welcome_message_content = $welcomeData['text']['content'];
                            }
                            // 回显附件数据
                            if (isset($welcomeData['attachments'][0])) {
                                $attachment = $welcomeData['attachments'][0];
                                $form->model()->welcome_message_type = $attachment['msgtype'];

                                switch ($attachment['msgtype']) {
                                    case 'image':
                                        if (isset($attachment['image']['pic_url'])) {
                                            $form->model()->welcome_message_image_url = $attachment['image']['pic_url'];
                                        }
                                        break;

                                    case 'link':
                                        if (isset($attachment['link']['url'])) {
                                            $form->model()->welcome_message_link_url = $attachment['link']['url'];
                                        }
                                        if (isset($attachment['link']['title'])) {
                                            $form->model()->welcome_message_link_title = $attachment['link']['title'];
                                        }
                                        break;
                                }
                            }
                        }
                    }
                }
                /** 欢迎语 */

                $form->multipleSelect("ww_corp_label_ids", "企微标签")
                    ->options($tagData)
                    ->ajax("/api/asyncGetWwCorpLabel?corp_auth_id=" . $corpAuthId) // 启用异步搜
                    ->saveAsJson()
                    ->help('如未搜索到标签，请至企微管理-企微微信进行同步。')
                    ->placeholder('请输入标签名称搜索。');

                /** 欢迎语 */
                $form->fieldset('欢迎语', function ($form) {
                    $form->hidden('welcome_message', '欢迎语内容');
                    $form->text('welcome_message_content', '欢迎语文字内容');
                    $form->checkbox('welcome_message_type', '欢迎语附件')->options([
                        'link' => '链接卡片',
                    ])->when(['image'], function ($form) {
                      //  $form->image('welcome_message_image_url', '图片地址')->url('users/files?corp_id=' . $form->model()->corp_id);
                        $form->text('welcome_message_image_url', '图片地址');

                    })->when(['link'], function ($form) {
                        $form->text('welcome_message_link_url', '链接地址');
                        $form->text('welcome_message_link_title', '链接标题');
                    });
                });
                /** 欢迎语 */


                $form->saving(function (Form $form) use ($tagData) {
                    if (!$form->isCreating() && !AdminUser::isAdmin($form->model())) {
                        return $this->form()->response()->alert()->error('提示')->detail('无权限操作');
                    }

                    if ($form->input('ww_corp_label_ids')) {
                        $ww_corp_label_ids   = array_filter($form->input('ww_corp_label_ids'), function ($value) {
                            return $value !== '' && $value !== null;
                        });
                        $ww_corp_label_names = [];
                        foreach ($ww_corp_label_ids as $tagId) {
                            if (isset($tagData[$tagId])) {
                                $ww_corp_label_names[] = $tagData[$tagId];
                            }
                        }
                        $form->ww_corp_label_names = json_encode($ww_corp_label_names, JSON_UNESCAPED_UNICODE);
                    }

                    /** 欢迎语 */
                    if ($form->input('welcome_message_content')) {
                        $res = [];
                        $res['text'] = [
                            'content' => $form->input('welcome_message_content'),
                        ];

                        // 根据附件类型决定存储哪种附件
                        $attachmentType = array_filter($form->input('welcome_message_type'))[0] ?? false;
                        if ($attachmentType === 'image' && $form->input('welcome_message_image_url')) {
                            $res['attachments'] = [
                                [
                                    'msgtype' => 'image',
                                    'image' => [
                                        'pic_url' => $form->input('welcome_message_image_url'),
                                    ]
                                ]
                            ];
                        }
                        if ($attachmentType === 'link' && $form->input('welcome_message_link_url')) {
                            $res['attachments'] = [
                                [
                                    'msgtype' => 'link',
                                    'link' => [
                                        'title' => $form->input('welcome_message_link_title') ?: '',
                                        'url' => $form->input('welcome_message_link_url'),
                                    ]
                                ]
                            ];
                        }

                        $form->welcome_message = json_encode($res,JSON_UNESCAPED_UNICODE);

                    }
                    // 删除临时字段，避免保存到数据库
                    // 同时删除模型中的这些属性
                    unset($form->model()->welcome_message_content);
                    unset($form->model()->welcome_message_type);
                    unset($form->model()->welcome_message_image_url);
                    unset($form->model()->welcome_message_link_url);
                    unset($form->model()->welcome_message_link_title);
                    /** 欢迎语 */
                    $form->deleteInput(['welcome_message_type','welcome_message_image_url','welcome_message_link_url','welcome_message_link_title','welcome_message_content']);

                    if ($form->isEditing()) {
                        //添加编辑销售的操作日志
                        \App\Models\WwUser::addEditWwUserLogs($form->input(), $form->model());

                        //只要编辑了销售 今日展示就清0
                        if (count($form->input()) > 3) { //判断一下是否是在列表页面操作了上下线按钮，如果操作了$form->input() 只有三个参数 ，因为如果切换为下线，也会走这块代码
                            \App\Models\WwUser::reSetTodayShowCount(Admin::user()->id);
                        }
                    }
                    $adminUid = Admin::user()->id;
                    $onlineStatus = $form->input("online_status");
                    if ($onlineStatus == 1 && $form->model()->online_status == 0) { //这里判断一下当前数据库销售的上线状态 如果表单online_status值为=1，数据库online_statu值为0，才执行onlineCheck
                        /** @var \App\Models\WwUser $wwUser */
                        $wwUser = $form->model();
                        //TODO 暂时注释销上线检查，
                        $resp   = \App\Models\WwUser::onlineCheck($wwUser);
                        if (!$resp['status']) {
                            return $form->response()->error($resp['message'] ?? '上线失败。');
                        }
                        $source = '客户-编辑上线';
                        WwUsersOnlineLogs::addOnLineStatusLogs($wwUser, $onlineStatus, $source,$adminUid);
                    }
                    if($onlineStatus == 0) {
                        $wwUser = $form->model();
                        $source = '客户-编辑下线';
                        WwUsersOnlineLogs::addOnLineStatusLogs($wwUser, $onlineStatus, $source,$adminUid);
                    }
                });

            }
        });
    }

    /**
     * 获取企微标签
     * @param $corpAuthId
     * @return Collection
     */
    public function getWwCorpLabel($corpAuthId)
    {
        $corpAuthInfo = AdminSubUserAuthCorp::query()->with("corpInfo")->find($corpAuthId);
        if (!$corpAuthInfo || !$corpAuthInfo->corpInfo) {
            return Collection::make([['id' => '无效', 'text' => '无效',]]);
        }
        if (!AdminUser::isAdmin($corpAuthInfo)) {
            return Collection::make([['id' => '无效', 'text' => '无效',]]);
        }
        $labelList  = CorpLabels::query()
            ->select('id','ww_corp_label_id','group_name','ww_corp_label_name')
            ->where('corp_id', $corpAuthInfo->corp_id)->get();
        $returnData = [];
        foreach ($labelList as $k => $label) {
            $returnData[] = [
                'id'   => $label['ww_corp_label_id'],
                'text' => "「" . $label['group_name'] . "」" . $label['ww_corp_label_name']
            ];
        }

        if (empty($returnData)) {
            return Collection::make([['id' => '无效', 'text' => '无效',]]);
        }
        return Collection::make($returnData);
    }

    public function destroy($id)
    {
        $data = \App\Models\WwUser::query()->whereIn("id", explode(",", $id))->get();
        foreach ($data as $datum) {
            if (!AdminUser::isAdmin($datum)) {
                return $this->form()->response()->alert()->error('提示')->detail('无权限操作');
            }
            /** 删除获客链接 */
            if ($datum && $datum->type == 1) {
                if ($datum && $datum->type == 1) {
                    //判断当前销售的获客链接存在于该企微下的获利链接列表才去删除，否则会删除失败
                    $allLinkIdList = WwCorpApiService::getAllLinkList($datum->corpInfo);
                    if(in_array($datum->cus_acq_link_id, $allLinkIdList)){
                        $delResp = WwCorpApiService::delete_link($datum->corpInfo, $datum);
                        if (!isset($delResp['errcode']) || $delResp['errcode'] != 0) {
                            $datum->cus_acq_link_status_message = $delResp['errmsg'] ?? "删除失败";
                            $datum->save();
                        }
                    }
                }
            }
            //判断被删除的销售有没有二维码，然后标记状态
            WwUserQrcode::checkhaveQrcode($datum);
            /** 记录删除操作日志 */
            $delIds = explode(",", $id);
            foreach ($delIds as $delId) {
                AdminActionLogJob::dispatch(
                    'ww_user_delete',
                    $delId,
                    AdminActionLog::ACTION_TYPE['销售'],
                    '删除销售ID:'.$delId,
                    getIp(),
                    Admin::user()->id, // 当前操作用户ID
                )->onQueue('admin_action_log_job');
            }
            $datum->delete();
        }
        return $this->form()->response()->alert()->success('提示')->detail('删除成功');

    }
//    public function uploadImage(Request $request){
//
//        $path = $request->file()->store('files', 'public');
//
//        $corpInfo = WwCorpInfo::query()->where("id", $request->input('corp_id'))->first();
//        $options = Form::create(
//            [
//                'media' => File::fromPath($image->getRealPath()),
//            ]
//        )->toArray();
//
//        $res = WwCorpApiService::uploadImage($corpInfo,$options);
//        dd($res);
//    }
}
