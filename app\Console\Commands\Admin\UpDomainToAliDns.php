<?php

namespace App\Console\Commands\Admin;


use App\Services\AliService;
use App\Services\NotifySendService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use RuntimeException;
use Vtiful\Kernel\Excel;
use App\Models\MakeDomain;

class UpDomainToAliDns extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'UpDomainToAliDns';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '生成域名并且解析到阿里云';
    protected $makeCount = 1000;

    protected $length = 20;

    protected $type = 'CNAME';

    protected $cname = 'alb-cicl5gzyvudtqz9ej3.cn-beijing.alb.aliyuncsslb.com';

    protected $domains = [
        'smart-ark.com'
    ];

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {


        $baseDomain = 'smart-ark.com';

        $count = DB::table('page_domain')->where('status', 1)->count();
        if ($count > 1000) {
            $message = 'page_domain 可用域名大于1000个';
            NotifySendService::sendWorkWeixinForError($message);
            return false;
        }
        $prefixList = $this->generateDomainPrefixes($this->makeCount, $this->length);
        //加入数据库
        $insertData = [];
        $successTotal = 0;
        $date = date('Y-m-d H:i:s');
        foreach ($prefixList as $prefix) {
            $res = AliService::addDomainRecord($baseDomain, $prefix, $this->type, $this->cname);
            if ($res == 200) {
                $insertData[] = [
                    'host' => 'http://' . $prefix . '.' . $baseDomain,
                    'status' => 1,
                    'created_at' => $date,
                    'updated_at' => $date,
                ];
                $successTotal++;
            }
        }

        if (!empty($insertData)) {
            $chunks = array_chunk($insertData, 200); // 每批50条
            foreach ($chunks as $chunk) {
                DB::table('page_domain')->insert($chunk);
            }
        }
        $message = "生成域名并解析到阿里云成功，\n主域名：  {$baseDomain} ，\n准备解析数量： {$this->makeCount} ，\n实际解析数量： {$successTotal} ，\nCNAME： {$this->cname}";
        NotifySendService::sendWorkWeixinForError($message);
        return Command::SUCCESS;
    }


    /**
     * 生成固定数量的域名前缀
     *
     * @param int $count 需要生成的数量
     * @param int $length 前缀长度（默认8）
     * @return array 生成的域名前缀数组
     */
    public function generateDomainPrefixes(int $count, int $length = 8): array
    {
        // 验证输入参数
        if ($count <= 0) {
            $this->info('数量必须大于0');
            return [];
        }

        if ($length < 3 || $length > 20) {
            $this->info('长度必须在3');
            return [];
        }

        $prefixes = [];
        $characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
        $charCount = strlen($characters);

        while (count($prefixes) < $count) {
            $prefix = '';

            // 生成随机前缀
            for ($i = 0; $i < $length; $i++) {
                $prefix .= $characters[rand(0, $charCount - 1)];
            }

            // 确保唯一性
            if (!in_array($prefix, $prefixes)) {
                $prefixes[] = $prefix;
            }

            // 防止无限循环（安全机制）
            if (count($prefixes) > $count * 100) {
                throw new RuntimeException('生成唯一前缀失败，请尝试减少数量或增加长度');
            }
        }

        return $prefixes;
    }

    /**
     * 验证域名前缀是否符合规则
     */
    public function isValidDomainPrefix(string $prefix): bool
    {
        // 长度检查 (3-63个字符)
        $len = strlen($prefix);
        if ($len < 3 || $len > 63) {
            return false;
        }

        // 只允许字母、数字和连字符
        if (!preg_match('/^[a-z0-9-]+$/', $prefix)) {
            return false;
        }

        // 不能以连字符开头或结尾
        if ($prefix[0] === '-' || $prefix[$len - 1] === '-') {
            return false;
        }

        // 不能包含连续连字符
        if (strpos($prefix, '--') !== false) {
            return false;
        }

        return true;
    }

}
