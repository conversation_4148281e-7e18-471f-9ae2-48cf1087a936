<?php

namespace App\Jobs;

use Admin;
use App\Models\AdminActionLog;
use App\Models\AdminActionLog\AdminActionLog1;
use App\Models\AdminActionLog\AdminActionLog2;
use App\Models\AdminActionLog\AdminActionLog3;
use App\Models\AdminActionLog\AdminActionLog4;
use App\Models\AdminActionLog\AdminActionLog5;
use App\Models\AdminActionLog\AdminActionLog6;
use App\Models\AdminActionLog\AdminActionLog7;
use App\Models\AdminActionLog\AdminActionLog8;
use App\Models\AdminActionLog\AdminActionLog9;
use App\Models\AdminActionLog\AdminActionLog10;
use Dcat\Admin\Repositories\EloquentRepository;
use App\Models\AdminUser;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

/**
 * 管理员操作日志记录Job
 *
 * 用于异步记录管理员的操作行为，包括登录、删除、修改等操作
 *
 * 构造参数说明：
 * - action: 操作 {@see AdminActionLog::ACTION ACTION}
 * - relId: int 相关ID
 * - actionType: 操作类型 {@see AdminActionLog::ACTION_TYPE ACTION_TYPE}
 * - message: String 内容
 * - ip: IP地址 通常通过 ```getIp()``` 方法获取
 * - adminUid: 操作人ID 通常通过 ```Admin::user()->id``` 获取
 *
 * 使用示例：
 * ```
 * AdminActionLogJob::dispatch(
 *      'del_sub_user',
 *      $id,
 *      AdminActionLog1::ACTION_TYPE['账号'],
 *      "删除子账号ID：".$id,
 *      getIp(),
 *      Admin::user()->id
 * )->onQueue('admin_action_log_job');
 * ```
 */
class AdminActionLogJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    protected $action;
    protected $relId;
    protected $actionType;

    protected $message;

    protected $ip;
    protected $adminUid;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($action, $relId, $actionType, $message, $ip, $adminUid)
    {

        $this->action = $action;
        $this->relId = $relId;
        $this->actionType = $actionType;
        $this->message = $message;
        $this->ip = $ip;
        $this->adminUid = $adminUid;


    }

    /**
     * Execute the job.
     *
     * @return mixed
     */
    public function handle()
    {
        $action = $this->action;
        $relId = $this->relId;
        $actionType = $this->actionType;
        $message = $this->message;
        $ip = $this->ip;
        $adminUid = $this->adminUid;
        $model = AdminActionLog::getAdminUserActionLogTableModel($adminUid);
        self::create($model, $action, $relId, $actionType, $message, $ip, $adminUid);
        return true;
    }

    public static function create($model, $action, $rel_id, $action_type, $message, $ip, $adminUid = 0): void
    {
        if (!$ip) {
            $ip = '';
        }
        $newObj = $model;
        $newObj->admin_uid = $adminUid;
        $newObj->action = $action;
        $newObj->action_desc = AdminActionLog::ACTION[$action];
        $newObj->rel_id = $rel_id;
        $newObj->action_type = $action_type;
        $newObj->message = $message ?? "无备注";
        $newObj->ip = $ip;
        $newObj->save();
    }

    /*public static function GetAdminUIdInArray($value): int|string
    {
        // 首先检查是否为子账户，如果是则获取主账号ID
        $adminUser = AdminUser::query()->find($value);
        if ($adminUser && $adminUser->parent_id != 0) {
            // 如果是子账户，使用主账号ID
            $value = $adminUser->parent_id;
        }

        $res = AdminUser::query()->select(['id'])->where('parent_id', 0)->pluck('id')->toArray();
        // 将id按每10个分组
        $chunkedIds = array_chunk($res, 10);

        // 查找 $value 属于第几个数组
        $groupIndex = 0;

        foreach ($chunkedIds as $index => $group) {
            if (in_array($value, $group)) {
                $groupIndex = $index;
                break;
            }
        }

        return $groupIndex;
    }*/
}
