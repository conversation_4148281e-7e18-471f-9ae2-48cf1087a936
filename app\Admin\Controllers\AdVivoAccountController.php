<?php /** @noinspection PhpInconsistentReturnPointsInspection */

namespace App\Admin\Controllers;

use Admin;
use App\Admin\Repositories\AdVivoAccount;
use App\Models\AdminSubUser;
use App\Models\AdminUser;
use App\Services\NotifySendService;
use App\Services\Tools\UtilsService;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Show;
use Dcat\Admin\Widgets\Alert;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Session;

/**
 * @property int $id
 * @property     $input
 */
class AdVivoAccountController extends AdminController
{

    private $clientId = ***********;
    private $clientSecret = 'BAA09273FF154157187E6DCB2C0E1F447211BE3227296C1E136E372476E1FFE3';

    public function authCallBack(Request $request)
    {
        if (empty(\Dcat\Admin\Admin::user()->id)) {
            return view('admin.ad_account.auth_error',['error_code' => 0,'massage' => '请先登录后台账户后再进行绑定，绑定期间不要更换浏览器。','back_path' => '/ztfz/vivo']);
        }
        Log::info('vivo广告授权回调');
        Log::info(json_encode($request->all()));
        $param = $request->all();
        if (empty($param['code']) || empty($param['state']) || empty($param['clientId'])) {
            Log::info('vivo广告授权回调-缺少参数：' . json_encode($param));
            return view('admin.ad_account.auth_error',['error_code' => -1,'massage' => '缺少参数。','back_path' => '/ztfz/vivo']);
        }
        if ($param['state'] != 'wra') {
            Log::info('vivo广告授权回调-参数错误：' . json_encode($param));
            return view('admin.ad_account.auth_error',['error_code' => -2,'massage' => '缺少参数。']);
        }
        $tokensRes = $this->getAccessToken($param['code']);
        if (!$tokensRes || $tokensRes['code'] != 0) {
            Log::info('vivo广告授权回调-获取tAccessToken失败：' . json_encode($tokensRes,JSON_UNESCAPED_UNICODE));
            NotifySendService::sendWorkWeixinForError("[系统报错][API调用][vivo广告授权回调][获取AccessToken],用户「" . Admin::user()->id . "」 " . Admin::user()->username . " 的广告账户授权失败，失败原因是：" . ($tokensRes['message'] ?? ''));
            return view('admin.ad_account.auth_error',['error_code' => -3,'massage' => '获取AccessToken失败。','back_path' => '/ztfz/vivo']);
        }
        //获取授权用户信息
        $tokens = $tokensRes['data'];
        $authAccountInfo = $this->accountInfoFetch($tokens['access_token']);
        if(!$authAccountInfo){
            Log::info('vivo广告授权回调-获取授权用户信息失败：' . json_encode($authAccountInfo,JSON_UNESCAPED_UNICODE));
            return view('admin.ad_account.auth_error',['error_code' => -4,'massage' => '获取授权账户信息失败。','back_path' => '/ztfz/vivo']);
        }
        $adAccount = \App\Models\AdVivoAccount::query()->where("account_id", $authAccountInfo['data']['uuid'])->first();
        if ($adAccount) {
            $adAccount->access_token = $tokens['access_token'];
            $adAccount->refresh_token = $tokens['refresh_token'];
            $adAccount->access_token_expires_in = date("Y-m-d H:i:s", ($tokens['token_date'] / 1000));
            $adAccount->refresh_token_expires_in = date("Y-m-d H:i:s", ($tokens['refresh_token_date'] / 1000));
            $adAccount->save();
            return view('admin.tencent_ad.notice',['account_id' =>  $authAccountInfo['data']['uuid']]);
        }
        // 如果账户不存在，获取账户信息
        $adAccount = new \App\Models\AdVivoAccount();
        $adAccount->admin_uid = Admin::user()->id;
        $adAccount->account_id = $authAccountInfo['data']['uuid'];
        $adAccount->account_name = $authAccountInfo['data']['name'];
        $adAccount->corporation_name = $authAccountInfo['data']['companyName'];
        $adAccount->access_token = $tokens['access_token'];
        $adAccount->refresh_token = $tokens['refresh_token'];
        $adAccount->access_token_expires_in = date("Y-m-d H:i:s", ($tokens['token_date'] / 1000));
        $adAccount->refresh_token_expires_in = date("Y-m-d H:i:s", ($tokens['refresh_token_date'] / 1000));
        $adAccount->type = $authAccountInfo['data']['type'];
        $adAccount->level = $authAccountInfo['data']['level'];
        $adAccount->save();
        if ($tokens && isset($tokens['access_token'])) {
            Session::flash("vivo_ad_account_auth_status", 1);
        } else {
            Session::flash("vivo_ad_account_auth_status", 0);
        }
        return Redirect::to('/'.UtilsService::getConfigRoutePrefix().'/vivo_ad_account');
    }


    /**
     * 获取账号信息
     * @param $access_token
     * @return array|mixed
     */
    public function accountInfoFetch($access_token)
    {
        $url = 'https://marketing-api.vivo.com.cn/openapi/v1/account/fetch';
        $data = [
            'access_token' => $access_token,
            'timestamp' => time() * 1000,
            'nonce' => microtime() . mt_rand(100000, 999999),
        ];
        $res = Http::get($url, $data)->json();
        if ($res && isset($res['code']) && $res['code'] == 0) {
            Log::info($res);
            return $res;
        }
        return [];
    }

    /**
     * 获取token
     * @param $code
     * @return array|mixed
     */
    public function getAccessToken($code)
    {
        $url = 'https://marketing-api.vivo.com.cn/openapi/v1/oauth2/token';
        $data = [
            'client_id' => $this->clientId,
            'client_secret' => $this->clientSecret,
            'grant_type' => 'code',
            'code' => $code,
        ];
        $res = Http::get($url, $data)->json();
        Log::info($res);
        return $res;
    }

    /**
     * 暂时不上 直接返回403
     * @param Content $content
     * @return void
     */
    public function index(Content $content): void
    {
        abort(403, '无权访问');
    }
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {

        return Grid::make(new AdVivoAccount(['adminInfo']), function (Grid $grid) {
            $grid->disableViewButton();
            $grid->disableDeleteButton();
//            $grid->disableBatchDelete();
            $grid->disableCreateButton();
            //回调地址路由
            $redirectUri = admin_route('vivo-ad-account.auth.callback');
            $authUrl = "https://open-ad.vivo.com.cn/OAuth?clientId={$this->clientId}&state=wra&redirectUri={$redirectUri}";
            $grid->tools("<a href='{$authUrl}' target='_blank' class='btn btn-primary disable-outline float-right' style='color: white;margin-left: 5px'><i class='feather icon-plus-square'></i>绑定VIVO广告账户</a>");
            $grid->model()->orderByDesc("id");
            if (\Dcat\Admin\Admin::user()->id !== 1) { //如果不是超级管理员
                if (Admin::user()->isRole('customer')) { //如果是“客户”的角色 也就是主账号
                    $grid->model()->whereIn("admin_uid", AdminSubUser::getAdminUids(Admin::user()->id));//如果是主账号，则获取所有子账号的id，包括自己
                    $grid->column('归属账号')->display(function () {
                        if (Admin::user()->id == $this->admin_uid) { //如果是自己创建的则直接显示主账号
                            return '主账号';
                        } else {
                            $username = Admin::user()->where('id', $this->admin_uid)->value('username');
                            $username = str_replace(Admin::user()->username, '', $username);
                            return $username;
                        }
                    });
                } else { //如果是客户运营的角色 也就是子账号
                    $grid->model()->where("admin_uid", Admin::user()->id);
                }
            }

            $grid->column('id');
            $grid->column('用户名称')->display(function () {
                return $this->adminInfo->username ?? '';
            });
            $grid->column('corporation_name','企业名称')->copyable();
            $grid->column('account_name')->copyable();
            $grid->column('account_id')->copyable();
            $grid->column('src_id')->editable();
            $grid->column('access_token_expires_in','有效期');
//            $grid->column('created_at');
            $grid->column('updated_at');
            $grid->filter(function (Grid\Filter $filter) use ($grid) {
                $filter->expand();
                $filter->panel();
                $filter->equal("account_id")->width(2);
                $filter->like("account_name")->width(2);
                $filter->like("corporation_name")->width(2);
                //所有带「所属账户」的列表，主账户都增加对应筛选
                if (Admin::user()->parent_id == 0) { //主账号
                    $adminIds = AdminSubUser::getALLAdminUids(Admin::user()->id);
                    $filter->equal("admin_uid", '操作账号')
                        ->select(AdminUser::query()
                            ->whereIn("id", $adminIds)
                            ->pluck("username", 'id')
                            ->toArray())->width(2);
                }
                $filter->equal("created_at")->date()->width(2);
            });
        });
    }

    public function destroy($id)
    {
        $data = \App\Models\AdVivoAccount::query()->whereIn("id", explode(",", $id))->get();
        foreach ($data as $datum) {
            if (!AdminUser::isAdmin($datum)) {
                return $this->form()->response()->error("无权限操作");
            }
        }
        return $this->form()->destroy($id);
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new AdVivoAccount(), function (Form $form) {
            if (!$form->isCreating() && !AdminUser::isAdmin($form->model()) && !AdminUser::isSystemOp()) {
                $form->display('N')->value("无数据");
            } else {
                $form->display('id');
                $form->hidden('id');
                $form->hidden('admin_uid');
                $form->display('corporation_name');
                $form->display('account_name');
                $form->display('account_id');
                $form->text('src_id')->required()->help('事件源ID，在VIVO营销平台事件管理工具中新建，每个产品在每个账号下仅可新建一个');
                $form->saving(function (Form $form) {
                    $form->admin_uid = Admin::user()->id;
                    $check = \App\Models\AdVivoAccount::query()->where('account_id', $form->input('account_id'))->first();
                    if ($form->isCreating()) {
                        if ($check) {
                            return $form->response()->alert()->error('提示')->detail('账户已存在-1');
                        }
                    }
                    if ($check && $check['id'] != $form->input('id')) {
                        return $form->response()->alert()->error('提示')->detail('账户已存在-2');
                    }

                });
                $form->disableViewButton();
                $form->disableViewCheck();
                $form->disableEditingCheck();
                $form->disableDeleteButton();
            }
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new AdVivoAccount(), function (Show $show) {
            if (!AdminUser::isAdmin($show->model())) {
                $show->field('N')->value("无数据");
            } else {
                $show->field('id');
                $show->field('corporation_name');
                $show->field('account_id');
            }
        });
    }

    public function getAdAccount(Request $request): LengthAwarePaginator
    {
        $keyword = $request->get("q", 0);
        $paginator = \App\Models\AdVivoAccount::query()
            ->where(function ($query) use ($keyword) {
                $query->where('account_id', 'like', "{$keyword}%")
                    ->orWhere("account_name", 'like', "{$keyword}%");
            })
            ->where('admin_uid', Admin::user()->id)
            ->select('account_id', 'account_name')
            ->paginate();

        // 手动转换字段名
        $paginator->getCollection()->transform(function ($item) {
            return [
                'id' => $item->account_id,
                'text' => $item->account_id . '-' . $item->account_name
            ];
        });

        return $paginator;
    }

}
