<?php

namespace App\Console\Commands;

use App\Jobs\AdminActionLogJob;
use App\Models\AdminUser;
use App\Models\IpInfect as IpInfectModel;
use App\Models\WwAppList;
use App\Models\WwCorpInfo;
use App\Models\WwLink;
use App\Models\WwUser;
use App\Models\WwUserAddRecord;
use App\Models\WwUserQrcode;
use App\Models\WwUsersGroup;
use App\Services\AliService;
use App\Services\CacheService;
use App\Services\Corp\WwCorpApiService;
use App\Services\NotifySendService;
use App\Services\PinyinService;
use App\Services\System\SpeedLogService;
use Dcat\Admin\Admin;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Overtrue\Pinyin\Pinyin;
use Symfony\Component\Console\Command\Command as CommandAlias;

class SauryTest extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';


    public function handle()
    {
        SpeedLogService::info("00025");//性能日志，单独做记录使用
        dd(1);
//        dd(CacheService::getIpInfectData());
        $configAdminUids = IpInfectModel::query()
            ->where('status',0)
            ->pluck("admin_uid")
            ->toArray();
        debug($configAdminUids);
//        $configAdminUids = [7];
        $adminUidsList = AdminUser::getAllAdminUidsByAdminUids($configAdminUids);
        dd($adminUidsList);
//        $days = 7;
//        dd(date("Y-m-d H:i:s", time() - 86400 * $days));

        $wwLink = WwLink::query()->where('id', 6)->first();
        $res = WwLink::getShowWwUserPolicy($wwLink);
        dd($res);
        $res = encrypt(1);
        $str = '"eyJpdiI6Ijlpb05QcVhhWFZIRFMwYjgrczVWelE9PSIsInZhbHVlIjoiRU84d2hEWkFHcnFscWFQd2x0SzMwQT09IiwibWFjIjoiODU1NDQxZmQyNGE3ZTRjNTljMjMzNGNiYjExODg3YjZkNzhlMWMwNWZmM2NlOTRlMDQ2ZmNmMGIxODZlZjMzMCIsInRhZyI6IiJ9';
        $res = decrypt($res);
        dd($res);
        $domainName = "smart-ark.com";
        $rr = 'saury-test-888';
        $type = 'CNAME';
        $value = 'alb-cicl5gzyvudtqz9ej3.cn-beijing.alb.aliyuncsslb.com';
        $res = AliService::main($domainName, $rr, $type, $value);
        dd($res);
        $blockAreaIds = AdminUser::query()->find(4)->block_area ?? [];
        dd($blockAreaIds);
        $blackAreas = WwLink::getLinkAdminBlackAreas(1);
        dd($blackAreas);
        $add = WwUserAddRecord::query()->where('id', 7)->first();
        dd($add->adminInfo->audit_ocpx);
        $password = 'ZTFZ2025-dF4-yR0~yA';
        $res = bcrypt($password);
        dd($res);
        $corpInfo = WwCorpInfo::query()->where('id', 3)->first();
        $res = WwCorpApiService::getUnassignedList($corpInfo);
        dd($res);
        $state = 'zt_t2_686f3a69124ea1.92533463';
        $jobIndex = crc32($state) % 10;
        if ($jobIndex) { //如果不等于0，那么就加上JobIndex
            $job = 'add_external_contact_' . $jobIndex;
        }
        dd($job);
        $res = array_map(function ($i) {
            return "add_external_contact_{$i}";
        }, range(1, 10));
        dd($res);
//        $wwUser = WwUser::query()->where('id',8)->first();
//        $state = getLinkState();
        //efba7d89ab975204557269c051dc18b5
        $corpInfo = WwCorpInfo::query()->where('id', 3)->first();
        $qrCode = WwUserQrCode::query()->get()->toArray();
        foreach ($qrCode as $qr) {
            $res = WwCorpApiService::del_contact_way($corpInfo, $qr['config_id']);
            $this->info(json_encode($res));
        }

        dd('ok');
        $contactWay = WwCorpApiService::add_contact_way($wwUser->corpInfo, $wwUser->open_user_id, (bool)$wwUser->skip_verify, $state);
        dd($contactWay);
    }

    public function deleteTable()
    {
        $tables = [
            'wr_ww_user_add_record',
            'wr_ww_user_add_record_delete',
            'wr_ww_user_add_record_ex',
            'wr_link_click_record',
            'wr_link_view_record',
            'wr_link_view_data_by_minute',
            'wr_ww_link_conv_tads',
            'wr_admin_action_logs_1',
            'wr_admin_action_logs_2',
            'wr_admin_action_logs_3',
            'wr_admin_action_logs_4',
            'wr_admin_action_logs_5',
            'wr_admin_action_logs_6',
            'wr_admin_action_logs_7',
            'wr_admin_action_logs_8',
            'wr_admin_action_logs_9',
            'wr_admin_action_logs_10',
            'wr_again_set_tag_task',
            'wr_ask_data',
            'wr_form_data',
            'wr_export_openid',
            'wr_export_task',
            'wr_generate_page',
            'wr_ip_infect',
            'wr_make_domain_task',
            'wr_ocpx_record',
            'wr_operation_logs',
            'wr_shield_ips',
            'wr_shield_policies',
            'wr_shield_policy_rules',
            'wr_switch_native_page',
            'wr_wechat_users',
            'wr_white_user_list',
            'wr_ww_app_admin_qrcode',
            'wr_ww_corp_info',
            'wr_admin_sub_user_auth_corps',
            'wr_admin_sub_users',
            'wr_ww_link',
            'wr_ww_tpl',
            'wr_ww_user_corps_data_by_days',
            'wr_ww_user_groups_data_by_days',
            'wr_ww_users_data_by_days',
            'wr_count_corps_data',
            'wr_ww_user_qrcodes',
            'wr_ww_users',
            'wr_ww_users_groups',
            'wr_ww_users_groups_count',
            'wr_ww_users_groups_rel',
            'wr_ww_users_import_task',
            'wr_ww_users_import_task_sub_log',
            'wr_ww_users_online_logs',
            'wr_ad_account_monitor',
            'wr_ad_account_monitor_count',
            'wr_ad_account_vcc_daily',
            'wr_corp_labels',

        ];
        foreach ($tables as $table) {
            DB::table($table)->truncate();
        }

    }
}
