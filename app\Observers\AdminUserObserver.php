<?php

namespace App\Observers;

use App\Models\AdminUser;
use App\Services\NotifySendService;
use Dcat\Admin\Models\Administrator;
use Exception;
use Illuminate\Redis\Connections\PhpRedisConnection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class AdminUserObserver
{

    /**
     * 创建用户后的处理，此时可以获取到用户ID
     * @param AdminUser $adminUser
     */
    public function created(AdminUser $adminUser): void
    {
        /** 如果action_log_table为空就设置随机值 */
        if (empty($adminUser->action_log_table)) {
            $rand = mt_rand(0, 10);
            try {
                /** 更新字段 */
                $res  = Administrator::query()
                    ->where('id', $adminUser->id)
                    ->update(['action_log_table' => $rand]);
                Log::info("修改行数".$res.'随机值：'.$rand);
            } catch (Exception $e) {
                NotifySendService::sendWorkWeixinForError('创建客户时操作日志表给值失败'.$adminUser->id.$e->getMessage());
            }
        }

        Log::info("用户信息".json_encode($adminUser));

        //更新未读公告-加入新用户
        /** @var PhpRedisConnection $redis */
        $redis = Redis::connection('announcement');
        $redis->sAdd('NotReadAnnouncement', $adminUser->id);
    }

}
