<?php

    namespace App\Console\Commands\DataCount;

    use Illuminate\Console\Command;
    use Illuminate\Support\Facades\DB;

    class ViewRecordByLinkMinute extends Command
    {
        /**
         * The name and signature of the console command.
         *
         * @var string
         */
        protected $signature = 'Data:ViewRecordByLinkMinute';

        /**
         * The console command description.
         *
         * @var string
         */
        protected $description = '按链接、按分钟、统计浏览量数据-PV';

        /**
         * Execute the console command.
         *
         * @return int
         */
        public function handle()
        {
            $stopTime = strtotime(date("Y-m-d H:i", time() - 300) . ":00");
            $data = [];
            DB::table("link_view_record")->select("id", "ww_link_id", "admin_uid", "created_at")->orderByDesc("id")->chunk(1000, function ($rows) use (&$data, $stopTime) {
                if($rows->isEmpty()) {
                    return false;
                }
                foreach ($rows as $row) {
                    if (strtotime($row->created_at) < $stopTime) {
                        return false;
                    }
                    $timeKey = strtotime(date("Y-m-d H:i:", strtotime($row->created_at)) . "00");
                    if (!isset($data[$timeKey][$row->admin_uid][$row->ww_link_id])) {
                        $data[$timeKey][$row->admin_uid][$row->ww_link_id] = 0;
                    }
                    $data[$timeKey][$row->admin_uid][$row->ww_link_id]++;
                }
//                $this->info("view_" . $row->created_at);
            });
            $clickData = [];
            DB::table("link_click_record")->select("id", "ww_link_id", "admin_uid", "created_at")->orderByDesc("id")->chunk(1000, function ($rows) use (&$clickData, $stopTime) {
                foreach ($rows as $row) {
                    if (strtotime($row->created_at) < $stopTime) {
                        return false;
                    }
                    $timeKey = strtotime(date("Y-m-d H:i:", strtotime($row->created_at)) . "00");
                    if (!isset($clickData[$timeKey . "_" . $row->admin_uid . "_" . $row->ww_link_id])) {
                        $clickData[$timeKey . "_" . $row->admin_uid . "_" . $row->ww_link_id] = 0;
                    }
                    $clickData[$timeKey . "_" . $row->admin_uid . "_" . $row->ww_link_id]++;
                }
//                $this->info("click_" . $row->created_at);
            });


            foreach ($data as $timeKey => $datum) {
                foreach ($datum as $adminUid => $value) {
                    foreach ($value as $linkId => $count) {
                        $where = [
                            'date' => date("Y-m-d H:i:s", $timeKey),
                            'admin_uid' => $adminUid,
                            'ww_link_id' => $linkId
                        ];
                        $updateData = [];
                        $updateData['view_count'] = $count;
                        $updateData['updated_at'] = date("Y-m-d H:i:s", time());
                        if (isset($clickData[$timeKey . "_" . $adminUid . "_" . $linkId])) {
                            $updateData['click_count'] = $clickData[$timeKey . "_" . $adminUid . "_" . $linkId];
                        }
                        $lastRecord = DB::table("link_view_data_by_minute")->where($where)->first();
                        if ($lastRecord) {
                            DB::table("link_view_data_by_minute")->where("id", $lastRecord->id)->update($updateData);
                        } else {
                            $updateData['date'] = date("Y-m-d H:i:s", $timeKey);
                            $updateData['admin_uid'] = $adminUid;
                            $updateData['ww_link_id'] = $linkId;
                            $updateData['created_at'] = date("Y-m-d H:i:s", time());
//                            $this->info(json_encode($where));
//                            $this->info(json_encode($lastRecord));
                            DB::table("link_view_data_by_minute")->insert($updateData);
                        }
                    }
                }
            }
            return Command::SUCCESS;
        }
    }
