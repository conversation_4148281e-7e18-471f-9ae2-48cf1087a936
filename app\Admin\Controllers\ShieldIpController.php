<?php

	namespace App\Admin\Controllers;

	use App\Admin\Repositories\ShieldIp;
    use App\Models\AdminSubUser;
    use App\Models\AdminUser;
    use App\Services\Tools\UtilsService;
    use Dcat\Admin\Admin;
    use Dcat\Admin\Form;
    use Dcat\Admin\Grid;
    use Dcat\Admin\Http\Controllers\AdminController;
    use Dcat\Admin\Show;
    use Illuminate\Support\Facades\DB;
    use Illuminate\Support\Facades\Request;

    class ShieldIpController extends AdminController
	{
		/**
		 * Make a grid builder.
		 *
		 * @return Grid
		 */
		protected function grid()
		{
			return Grid::make(new ShieldIp(['adminInfo']), function (Grid $grid) {
                $grid->paginate(AdminUser::getPaginate());
                if (!AdminUser::isSystemOp()) {
                    $grid->model()->whereIn("admin_uid", AdminSubUser::getAdminUids(Admin::user()->id));
                }
                if(Admin::user()->isRole("wop")){
                    $grid->disableActions();
                    $grid->disableBatchDelete();
                }
				$grid->model()->orderByDesc("updated_at");
				$grid->disableViewButton();
				$grid->disableEditButton();
                $grid->disableCreateButton();
                $grid->tools(function (Grid\Tools $tools) {
                    $tools->append(UtilsService::dialogForm('添加',Request::url().'/create','create-shield-ip'));
                });
				$grid->column('id')->sortable();
				$grid->column('adminInfo.username', '操作人');
				$grid->column('ip');
				$grid->column('remark');
				$grid->column('updated_at')->sortable();
//				$grid->quickSearch("ip","adminInfo.username");
                $grid->filter(function (Grid\Filter $filter) {
                    $filter->expand();
                    $filter->panel();
                    $filter->equal('id')->width(2);
                    $filter->equal('ip')->width(2);
                    $filter->equal('adminInfo.username','操作人')->width(2);
                });
			});
		}

		/**
		 * Make a show builder.
		 *
		 * @param mixed $id
		 *
		 * @return Show
		 */
		protected function detail($id)
		{
			return Show::make($id, new ShieldIp(), function (Show $show) {
                if (!AdminUser::isAdmin($show->model())) {
                    $show->field('N')->value("无数据");
                }else {
                    $show->field('id');
                    $show->field('admin_uid');
                    $show->field('ip');
                    $show->field('created_at');
                    $show->field('updated_at');
                }
			});
		}

		/**
		 * Make a form builder.
		 *
		 * @return Form
		 */
		protected function form()
		{
			return Form::make(new ShieldIp(), function (Form $form) {
                if (!$form->isCreating() && !AdminUser::isAdmin($form->model())) {
                    $form->display('N')->value("无数据");
                } else {
                    $form->display('id');
                    $form->textarea('ip')->placeholder("一行一个，例如\r\n192.168.1.1\r\n192.168.1.2");
                    $form->text('remark')->placeholder("拉黑原因");
                    $form->saving(function (Form $form) {
                        if(!$form->isCreating() && !AdminUser::isAdmin($form->model())){
                            return $form->response()->error("无权限操作")->refresh();
                        }
                        $pattern = '/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/'; // IP地址的正则表达式
                        $ips = explode(PHP_EOL, $form->input('ip'));
                        DB::beginTransaction();
                        foreach ($ips as $k => $ip) {
                            $ip = trim($ip);
                            if (!preg_match($pattern, $ip)) {
                                DB::rollBack();
                                return $form->response()->error("第" . ($k + 1) . "行的「" . $ip . "」不是标准的IP格式");
                            }
                            $ipData = \App\Models\ShieldIp::query()->where("admin_uid", Admin::user()->id)->where("ip", $ip)->first();
                            if ($ipData) {
                                $ipData->updated_at = date("Y-m-d H:i:s", time());
                                $ipData->save();
                                continue;
                            }
                            $obj = new \App\Models\ShieldIp();
                            $obj->admin_uid = Admin::user()->id;
                            $obj->ip = $ip;
                            $obj->remark = $form->input("remark");
                            $obj->save();
                        }
                        DB::commit();
                        return $form->response()->success('提交完成')->redirect("/shield_ips");
                    });
                    $form->display('created_at');
                    $form->display('updated_at');
                }
			});
		}

        public function destroy($id)
        {
            $data = \App\Models\ShieldIp::query()->whereIn("id", explode(",", $id))->get();
            foreach ($data as $datum) {
                if (!AdminUser::isAdmin($datum)) {
                    return $this->form()->response()->error("无权限操作");
                }
            }
            return $this->form()->destroy($id);
        }
	}
