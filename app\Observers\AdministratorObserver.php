<?php

namespace App\Observers;

use Dcat\Admin\Models\Administrator;
use Illuminate\Redis\Connections\PhpRedisConnection;
use Illuminate\Support\Facades\Redis;

class AdministratorObserver
{
    /**
     * 创建用户前的处理
     * @param Administrator $adminUser
     */
    public function creating(Administrator $adminUser): void
    {
        if (empty($adminUser->action_log_table)) {
            /** 简单随机分配 0-10 */
            $adminUser->action_log_table = mt_rand(0, 10);
        }
    }

    /**
     * 创建用户后的处理，此时可以获取到用户ID
     * @param Administrator $adminUser
     */
    public function created(Administrator $adminUser): void
    {
        //更新未读公告-加入新用户
        /** @var PhpRedisConnection $redis */
        $redis = Redis::connection('announcement');
        $redis->sAdd('NotReadAnnouncement', $adminUser->id);
    }

}
