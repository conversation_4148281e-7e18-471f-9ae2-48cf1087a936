<?php

	namespace App\Admin\Forms\Corp;

	use App\Models\AdminSubUserAuthCorp;
    use App\Models\AdminUser;
    use App\Models\WwCorpInfo;
    use Dcat\Admin\Admin;
	use Dcat\Admin\Widgets\Form;

	class UpdateCorpNameForm extends Form
	{
		/**
		 * Handle the form request.
		 *
		 * @param array $input
		 *
		 * @return mixed
		 */
		public function handle(array $input)
		{
			$corpAuthId = $input['corp_auth_id'];

            $corpAuthInfo = AdminSubUserAuthCorp::query()->where('id', $corpAuthId)->first();

            if(!AdminUser::isAdmin($corpAuthInfo)){
                return $this->response()->error('无操作权限.');
            }
			/** @var WwCorpInfo $corpInfo */

			$corpInfo = WwCorpInfo::query()->find($corpAuthInfo->corp_id);
            if(!$corpInfo){
                return $this->response()->error('企微不存在.');
            }
            $corpInfo->corp_name = $input['corp_name'];
            $corpInfo->save();
			return $this->response()->success('修改成功.')->refresh();
		}

		/**
		 * Build a form here.
		 */
		public function form()
		{
            $list = AdminSubUserAuthCorp::getCorpAuthListBuyAdminUid(Admin::user()->id);
			$this->select('corp_auth_id','选择企微')->options($list)->required();
            $this->text('corp_name','新企微名称')->help('修改前，请先跟客户确认，最好让客户提供企业微信后台修改后的名称截图。')->placeholder('请输入新企微名称')->required();
            $this->confirm('确认提交？');
		}

		/**
		 * The data of the form.
		 *
		 * @return array
		 */
		public function default()
		{
			return [

			];
		}
	}
