<?php

    namespace App\Jobs\TencentAd;

    use App\Models\TencentAdAccount;
    use App\Models\AdAccountDynamicCreativesDatum;
    use Illuminate\Bus\Queueable;
    use Illuminate\Contracts\Queue\ShouldQueue;
    use Illuminate\Foundation\Bus\Dispatchable;
    use Illuminate\Queue\InteractsWithQueue;
    use Illuminate\Queue\SerializesModels;

    class GetDynamicCreativesDataJob implements ShouldQueue
    {
        use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

        protected mixed $adAccount;


        /**
         * Create a new job instance.
         *
         * @return void
         */
        public function __construct($adAccount)
        {
            $this->adAccount = $adAccount;
        }

        /**
         * Execute the job.
         *
         * @return mixed
         */
        public function handle()
        {
            $this->daily_reports_get($this->adAccount);
            return true;
        }

        function daily_reports_get(TencentAdAccount $adAccount, $page = 1): bool
        {
            $interface = 'daily_reports/get';
            $url       = 'https://api.e.qq.com/v3.0/' . $interface;

            $common_parameters = array(
                'access_token' => $adAccount->access_token,
                'timestamp'    => time(),
                'nonce'        => md5(uniqid('', true))
            );

            $parameters = array(
                'account_id' => $adAccount->account_id,
                'level'      => 'REPORT_LEVEL_DYNAMIC_CREATIVE',
                'time_line'  => 'REQUEST_TIME',
                'page_size'  => 1000,
                'group_by'   => [
                    'date', 'dynamic_creative_id', 'site_set'
                ],
                'date_range' => [
                    'start_date' => date("Y-m-d", time() - 86400 * 7),
                    'end_date'   => date("Y-m-d", time()),
                ],
                'fields'     => [
                    'site_set', 'system_industry_id', 'account_id', 'admin_uid', 'adgroup_id', 'dynamic_creative_id', 'view_count', 'view_user_count', 'valid_click_count', 'click_user_count', 'cpc', 'ctr', 'valuable_click_count', 'valuable_click_cost', 'cost', 'acquisition_cost', 'thousand_display_price', 'real_cost_top', 'conversions_count', 'conversions_rate', 'conversions_cost', 'deep_conversions_count', 'deep_conversions_rate', 'deep_conversions_cost', 'video_outer_play_count', 'video_outer_play_user_count', 'avg_user_play_count', 'video_outer_play_time_count', 'video_outer_play_time_avg_rate', 'video_outer_play_rate', 'video_outer_play_cost', 'video_outer_play3s_count', 'video_outer_play3s_rate', 'video_outer_play5s_count', 'video_outer_play7s_count', 'no_interest_count', 'click_image_count', 'click_head_count', 'click_detail_count', 'zone_header_click_count', 'platform_page_view_count', 'platform_page_view_rate', 'lan_button_click_count', 'lan_jump_button_clickers', 'lan_button_click_cost', 'lan_jump_button_ctr', 'lan_jump_button_click_cost', 'fir_og_conv_auto_acquisition_pv', 'sec_og_conv_auto_acquisition_pv', 'date'
                ],
                'page'       => $page,
            );

            $parameters = array_merge($common_parameters, $parameters);

            foreach ($parameters as $key => $value) {
                if (!is_string($value)) {
                    $parameters[$key] = json_encode($value);
                }
            }

            $request_url = $url . '?' . http_build_query($parameters);

            $curl = curl_init();
            curl_setopt($curl, CURLOPT_URL, $request_url);
            curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 60);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
            $response = curl_exec($curl);
            curl_close($curl);
            $data = json_decode($response, true);
            if (isset($data['data']['list'])) {
                foreach ($data['data']['list'] as $datum) {
                    $adDc = AdAccountDynamicCreativesDatum::query()
                        ->where("dynamic_creative_id", $datum['dynamic_creative_id'])
                        ->where("date", $datum['date'])
                        ->where("site_set", $datum['site_set'])
                        ->first();
                    if (!$adDc) {
                        $adDc = new AdAccountDynamicCreativesDatum();
                    }
                    $adDc->system_industry_id              = $adAccount->system_industry_id;
                    $adDc->account_id                      = $datum['account_id'];
                    $adDc->admin_uid                       = $adAccount->admin_uid;
                    $adDc->adgroup_id                      = $datum['adgroup_id'];
                    $adDc->dynamic_creative_id             = $datum['dynamic_creative_id'];
                    $adDc->view_count                      = $datum['view_count'];
                    $adDc->view_user_count                 = $datum['view_user_count'];
                    $adDc->valid_click_count               = $datum['valid_click_count'];
                    $adDc->click_user_count                = $datum['click_user_count'];
                    $adDc->cpc                             = $datum['cpc'];
                    $adDc->ctr                             = $datum['ctr'];
                    $adDc->valuable_click_count            = $datum['valuable_click_count'];
                    $adDc->valuable_click_cost             = $datum['valuable_click_cost'];
                    $adDc->cost                            = $datum['cost'];
                    $adDc->acquisition_cost                = $datum['acquisition_cost'];
                    $adDc->thousand_display_price          = $datum['thousand_display_price'];
                    $adDc->real_cost_top                   = $datum['real_cost_top'];
                    $adDc->conversions_count               = $datum['conversions_count'];
                    $adDc->conversions_rate                = $datum['conversions_rate'];
                    $adDc->conversions_cost                = $datum['conversions_cost'];
                    $adDc->deep_conversions_count          = $datum['deep_conversions_count'];
                    $adDc->deep_conversions_rate           = $datum['deep_conversions_rate'];
                    $adDc->deep_conversions_cost           = $datum['deep_conversions_cost'];
                    $adDc->video_outer_play_count          = $datum['video_outer_play_count'];
                    $adDc->video_outer_play_user_count     = $datum['video_outer_play_user_count'];
                    $adDc->avg_user_play_count             = $datum['avg_user_play_count'];
                    $adDc->video_outer_play_time_count     = $datum['video_outer_play_time_count'];
                    $adDc->video_outer_play_time_avg_rate  = $datum['video_outer_play_time_avg_rate'];
                    $adDc->video_outer_play_rate           = $datum['video_outer_play_rate'];
                    $adDc->video_outer_play_cost           = $datum['video_outer_play_cost'];
                    $adDc->video_outer_play3s_count        = $datum['video_outer_play3s_count'];
                    $adDc->video_outer_play3s_rate         = $datum['video_outer_play3s_rate'];
                    $adDc->video_outer_play5s_count        = $datum['video_outer_play5s_count'];
                    $adDc->video_outer_play7s_count        = $datum['video_outer_play7s_count'];
                    $adDc->no_interest_count               = $datum['no_interest_count'];
                    $adDc->click_image_count               = $datum['click_image_count'];
                    $adDc->click_head_count                = $datum['click_head_count'];
                    $adDc->click_detail_count              = $datum['click_detail_count'];
                    $adDc->zone_header_click_count         = $datum['zone_header_click_count'];
                    $adDc->platform_page_view_count        = $datum['platform_page_view_count'];
                    $adDc->platform_page_view_rate         = $datum['platform_page_view_rate'];
                    $adDc->lan_button_click_count          = $datum['lan_button_click_count'];
                    $adDc->lan_jump_button_clickers        = $datum['lan_jump_button_clickers'];
                    $adDc->lan_button_click_cost           = $datum['lan_button_click_cost'];
                    $adDc->lan_jump_button_ctr             = $datum['lan_jump_button_ctr'];
                    $adDc->lan_jump_button_click_cost      = $datum['lan_jump_button_click_cost'];
                    $adDc->fir_og_conv_auto_acquisition_pv = $datum['fir_og_conv_auto_acquisition_pv'];
                    $adDc->sec_og_conv_auto_acquisition_pv = $datum['sec_og_conv_auto_acquisition_pv'];
                    $adDc->date                            = $datum['date'];
                    $adDc->site_set                        = $datum['site_set'];
                    $adDc->save();
                }
            }
            if(isset($data['data']['page_info']['total_page']) && $data['data']['page_info']['total_page'] > $page){
                return $this->daily_reports_get($adAccount,$page+1);
            }
            return true;
        }
    }
