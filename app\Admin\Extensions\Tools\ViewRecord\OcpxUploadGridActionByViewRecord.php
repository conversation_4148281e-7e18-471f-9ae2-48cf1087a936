<?php

	namespace App\Admin\Extensions\Tools\ViewRecord;

    use App\Jobs\AdminActionLogJob;
    use App\Models\AdminActionLog;
    use App\Models\AdminUser;
    use App\Models\LinkViewRecord;
    use App\Models\LinkViewRecordOcpx;
    use App\Models\TencentAdAccount;
    use App\Models\WwLink;
    use App\Services\NotifySendService;
    use Dcat\Admin\Actions\Response;
    use Dcat\Admin\Admin;
    use Dcat\Admin\Grid\RowAction;
    use Dcat\Admin\Traits\HasPermissions;
    use Illuminate\Contracts\Auth\Authenticatable;
    use Illuminate\Database\Eloquent\Model;
    use Illuminate\Http\Request;
    use Illuminate\Support\Facades\Log;

    class OcpxUploadGridActionByViewRecord extends RowAction
	{
		/**
		 * @return string
		 */
		protected $title = '<i class="feather icon-upload-cloud" style="margin:0 10px 0 10px">上报</i>';

		/**
		 * Handle the action request.
		 *
		 * @param Request $request
		 *
		 * @return Response
		 */
		public function handle(Request $request)
		{
            //获取当前行的id
			$id = $this->getKey();
            //查询访客记录(LinkViewRecord)表里的ww_link_id 也就是企微投放链接的链接id
			$linkRecord = LinkViewRecord::query()->find($id);
            $mediaType = WwLink::query()->where('id', $linkRecord->ww_link_id)->value('media_type');
            if ($mediaType != "腾讯广告") {
                return $this->response()->error('该记录非腾讯广告');
            }
            //判断一下访客记录存不存在
			if (!$linkRecord || !AdminUser::isAdmin($linkRecord)) {
				return $this->response()->error('记录未找到，请刷新页面后尝试');
			}
            //调用上报方法
			$this->tencentAdOcpx($linkRecord);
            //记录操作日志
            AdminActionLogJob::dispatch(
                'link_view_record_ocpx_upload',
                $id,
                AdminActionLog::ACTION_TYPE['账号'],
                '实时访客手动上报「' . Admin::user()->username . '」，实时访客记录ID：' . $id,
                getIp(),
                Admin::user()->id
            )->onQueue('admin_action_log_job');
			return $this->response()->success('上报完成，请查看上报结果')->refresh();
		}

        public function tencentAdOcpx($linkRecord)
        {
            //获取传进来的访问记录，用它的企微投放链接的id去查企微投放链接的信息
            /** @var WwLink $linkInfo */
            $linkInfo = WwLink::query()->where('id', $linkRecord->ww_link_id)->first();
            //如果不存在或者该链接的添加成功上报行为为空，返回错误
            if (!$linkInfo) {
//                return 0;
                Log::info('访问记录上报-投放链接不存在，投放链接ID' . $linkRecord->ww_link_id ?? 0);
                return $this->response()->error('投放链接不存在');
            }
            if (!$linkInfo->add_succeeded_action_type) {
                Log::info('访问记录上报-未配置添加成功行为，投放链接ID' . $linkInfo->id ?? 0);
                return $this->response()->error('未配置添加成功行为');
            }

            /** @var TencentAdAccount $account */
            //查询腾讯账户表的推广账户id
            $account = TencentAdAccount::query()->where("account_id", $linkInfo->account_id)->first();

            //将添加成功上报行转为字符串
            $actionTypes = json_decode($linkInfo->add_succeeded_action_type, true);
            foreach($actionTypes as $actionType){
                $interface = 'user_actions/add';
                $url = 'https://api.e.qq.com/v3.0/' . $interface;

                $common_parameters = [
                    'access_token' => $account->access_token,
                    'timestamp' => time(),
                    'nonce' => md5(uniqid('', true))
                ];
                $actionTime = time();
                $parameters = [
                    'account_id' => $account->account_id,
                    'user_action_set_id' => $account->data_source_id,
                    'actions' => [
                        [
                            'url' => 'http://www.qq.com',
                            'action_time' => $actionTime,
                            'action_type' => $actionType,
                            'outer_action_id' => $actionType . $linkRecord->click_id . 'WIND',
                            'trace' => [
                                'click_id' => $linkRecord->click_id,
                            ]
                        ],
                    ]
                ];

                $parameters = json_encode($parameters);
                $request_url = $url . '?' . http_build_query($common_parameters);

                $curl = curl_init();
                curl_setopt($curl, CURLOPT_URL, $request_url);
                curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 60);
                curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($curl, CURLOPT_POST, 1);
                curl_setopt($curl, CURLOPT_POSTFIELDS, $parameters);
                curl_setopt($curl, CURLOPT_HTTPHEADER, [
                    "Content-Type:application/json"
                ]);
                $response = curl_exec($curl);
                if (curl_error($curl)) {
                    $error_msg = curl_error($curl);
                    $error_no = curl_errno($curl);
                    curl_close($curl);
                    $errMessage = makeErrorLog('[系统报错][OCPX上报][tencentAdOcpx]', ['account_id' => $account->account_id, 'action_type' => $actionType]);
                    NotifySendService::sendWorkWeixinForError("[系统报错][OCPX上报][tencentAdOcpx] 上报失败" . 'ERROR_NO:' . $error_no . ',ERROR_MSG:' . $error_msg . $errMessage);
                }
                curl_close($curl);

                //上报成功保存信息
                $ocpxRecord = new LinkViewRecordOcpx();
                $ocpxRecord->link_id = $linkRecord->ww_link_id;
                $ocpxRecord->link_view_record_id = $linkRecord->id;
                $ocpxRecord->traceid = $linkRecord->click_id;
                $ocpxRecord->type = 'add_external_contact';
                $ocpxRecord->action_type = $actionType;
                $ocpxRecord->action_time = date("Y-m-d H:i:s", $actionTime);
                $ocpxRecord->push_json = $parameters;
                $ocpxRecord->return_json = $response;
                $ocpxRecord->media_type = $linkInfo->media_type;
                $resp = json_decode($response, true);
                if (isset($resp['code']) && $resp['code'] == 0) {
                    $ocpxRecord->result = 1;
                } else {
                    $ocpxRecord->result = 2;
                }
                $ocpxRecord->save();
            }
            return true;
        }
		/**
		 * @return string|array|void
		 */
		public function confirm()
		{
			return ['确认手动上报?', '您确认手动上报吗？同行为重复上报会去重<br>目前只支持腾讯'];
		}

		/**
		 * @param Model|Authenticatable|HasPermissions|null $user
		 *
		 * @return bool
		 */
		protected function authorize($user): bool
		{
			return true;
		}

		/**
		 * @return array
		 */
		protected function parameters()
		{
			return [
				//	         'id' => $this->row->id
			];
		}
	}
