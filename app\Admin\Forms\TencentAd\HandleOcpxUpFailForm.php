<?php

namespace App\Admin\Forms\TencentAd;

use App\Models\TencentAdAccount;
use App\Models\OcpxRecord;
use App\Models\WwLink;
use Dcat\Admin\Widgets\Form;
use Illuminate\Support\Facades\DB;

class HandleOcpxUpFailForm extends Form
{
    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        $account_id = $input['account_id'];
        $account_id = explode(',', $account_id);
        $accountInfo = TencentAdAccount::query()->whereIn('account_id', $account_id)->get();
        if ($accountInfo->isEmpty()) {
            return $this->response()->error('账户不存在.');
        }
        try {
            DB::beginTransaction();
            //删除腾讯账号
            if($input['action_type'] == 0){ //账户跟失败的回传记录都删除
                TencentAdAccount::query()->whereIn('account_id', $account_id)->delete();
                $wwLinkIds = WwLink::query()
                    ->whereIn('account_id', $account_id)
                    ->pluck('id')
                    ->toArray();

                if (empty($wwLinkIds)) {
                    return $this->response()->error('未查询到投放链接');
                }
                OcpxRecord::query()
                    ->whereIn('link_id', $wwLinkIds)
                    ->where('result', 2)
                    ->where('media_type', '腾讯广告')
                    ->delete();
            }else{ //只删除账户
                TencentAdAccount::query()->whereIn('account_id', $account_id)->delete();
            }
            DB::commit();
            return $this->response()->alert()->success('提示')->detail('处理完成');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->response()->alert()->error('提示')->detail($exception->getMessage());
        }
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        $this->html(
            <<<HTML
<div class="alert alert-info alert-dismissable">
            <h4><i class="fa fa-info"></i>&nbsp; 提示</h4>
         将会删除这些账户配置的投放链接下的回传失败的记录。
</div>
HTML
        );
        $this->textarea('account_id', '账户ID')
            ->placeholder('英文逗号隔开，不需要换行，例如：********,********')
            ->help('英文逗号隔开，不需要换行，例如：********,********')
            ->required();
        $this->radio('action_type','操作类型')
            ->options([0 => '账户和失败回传记录都删除', 1 => '只删除账户'])
            ->default(0)
            ->required();
    }

    /**
     * The data of the form.
     *
     * @return array
     */
    public function default()
    {
        return [

        ];
    }
}
