<?php

namespace App\Admin\Forms\Domains;

use Admin;
use App\Models\AdminDomain;
use App\Services\NotifySendService;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Widgets\Form;
use Illuminate\Support\Facades\DB;
use Throwable;

class SetDefaultDomainForm extends Form
{
    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function handle(array $input): JsonResponse
    {
        $setDefaultDomainId = $input['domain'];
        $adminUid = Admin::user()->id;
        try {
            DB::transaction(function () use ($setDefaultDomainId, $adminUid) {
                /** 更新所有域名状态为0 */
                AdminDomain::query()
                    ->where('admin_uid', $adminUid)
                    ->update(['status' => 0]);

                /** 更新选择的域名状态为1 */
                AdminDomain::query()
                    ->where('admin_uid', $adminUid)
                    ->where('id', $setDefaultDomainId)
                    ->update(['status' => 1]);
            });
        } catch (Throwable $e) {
            NotifySendService::sendWorkWeixinForError("批量修改默认域名Throwable错误".$e->getMessage());
            return $this->response()->alert()->error('提示')->detail("请求超时，请稍后再试")->refresh();
        }
        return $this->response()->alert()->success('提示')->detail("设置默认域名成功")->refresh();
    }

    /**
     * Build a form here.
     */
    public function form(): void
    {
        $domainList = AdminDomain::query()
            ->where('admin_uid', Admin::user()->id)
            ->get();
        $selectOptions = $domainList->pluck('domain', 'id')
            ->toArray();

        $this->select('domain', '域名')
            ->options($selectOptions)
            ->help('修改投放链接默认展示域名')
            ->required();
    }

}
