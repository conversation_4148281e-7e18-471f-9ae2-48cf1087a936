<?php

namespace App\Admin\Controllers;

use App\Models\AdminSubUserAuthCorp;
use App\Models\AdminUser;
use App\Models\CorpLabels;
use App\Models\TencentAdAccount;
use App\Models\WwCorpInfo;
use App\Models\WwTpl;
use App\Models\WwUser;
use App\Models\WwUsersGroup;
use App\Services\Tads\AdGroups\CustomAudiences;
use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ApiController
{
    /**
     * 异步获取企微标签
     * @param Request $request
     * @return mixed
     */
    public function asyncGetWwCorpLabel(Request $request)
    {
        $keyword = $request->get("q", '');
        $corpAuthId = $request->get("corp_auth_id");

        $query = CorpLabels::query()->select('id', 'corp_id', 'ww_corp_label_id', 'group_name', 'ww_corp_label_name');

        if (!empty($corpAuthId)) {
            $corpAuthInfo = AdminSubUserAuthCorp::query()
                ->select('id','corp_id')
//                ->where('admin_uid', Admin::user()->id)
                ->where('id',$corpAuthId)
                ->first();
            if(!$corpAuthInfo){
                return [];
            }
            $query->where('corp_id', $corpAuthInfo->corp_id);
        }
        if (!empty($keyword)) {
            $query->where('ww_corp_label_name', 'like', "%{$keyword}%");
        }
        $paginator = $query->orderBy('id','desc')->paginate(100);

        // 手动转换字段名
        $paginator->getCollection()->transform(function ($item) {

            return [
                'id' => $item->ww_corp_label_id,
                'text' => "「" . $item['group_name'] . "」" . $item->ww_corp_label_name
            ];
        });
        return $paginator;
    }

    /**
     * 获取分组列表
     * @param Request $request
     * @return Builder[]|Collection
     */
    public function getGroupList(Request $request)
    {

        $keyword = $request->get("q", 0);
        $adminUids = AdminUser::query()
            ->where('parent_id', $keyword)
            ->orWhere('id', $keyword)
            ->pluck('id')
            ->toArray();
        return WwUsersGroup::query()
            ->whereIn("admin_uid", $adminUids)
            ->orderByDesc("id")
            ->get(['id', DB::raw('title as text')]);

    }

    /**
     * 导出任务-获取企微列表
     * @param Request $request
     * @return Builder[]|Collection
     */
    public function getCorpList(Request $request)
    {

        $keyword = $request->get("q", 0);
        $adminUids = AdminUser::query()
            ->where('parent_id', $keyword)
            ->orWhere('id', $keyword)
            ->pluck('id')
            ->toArray();
        $corpIds = AdminSubUserAuthCorp::query()
            ->with("corpInfo")
            ->whereIn("admin_uid", $adminUids)
            ->orderByDesc("id")
            ->pluck("corp_id")
            ->unique();
        $corpList = WwCorpInfo::query()
            ->whereIn('id', $corpIds)
            ->get(['id', DB::raw('corp_name as text')]);
        return $corpList;

    }

    /**
     * 获取某个用户的屏蔽分组
     * @param Request $request
     * @return array|\Illuminate\Support\Collection
     */
    public function getShieldPolicieWwGroupList(Request $request)
    {
        $q = $request->get("q", 0);
        $groupList = WwUsersGroup::query()
            ->where('type',0)
            ->where('admin_uid',$q)
            ->get();
        if(!$groupList){
            return [];
        }
        $returnData = [];
        foreach ($groupList as $k => $group) {
            $returnData[] = [
                'id'   => $group['id'],
                'text' => "「" . $group['id'] . "」" . $group['title']
            ];
        }
        return  \Illuminate\Support\Collection::make($returnData);
    }


    /**
     * 获取某个用户的屏蔽页面
     * @param Request $request
     * @return array|\Illuminate\Support\Collection
     */
    public function getShieldPolicieAuditTpl(Request $request)
    {
        $q = $request->get("q", 0);
        $auditTplList = WwTpl::query()
            ->where('type',0)
            ->where('admin_uid',$q)
            ->get();
        if(!$auditTplList){
            return [];
        }
        $returnData = [];
        foreach ($auditTplList as $k => $auditTpl) {
            $returnData[] = [
                'id'   => $auditTpl['id'],
                'text' => "「" . $auditTpl['id'] . "」" . $auditTpl['name']
            ];
        }
        return  \Illuminate\Support\Collection::make($returnData);
    }

    /**
     * 根据用户ID获取腾讯广告账户
     * @param Request $request
     * @return Builder[]|Collection
     */
    public function getAdAccountList(Request $request)
    {

        $keyword = $request->get("q", 0);
        $adminUids = AdminUser::query()
            ->where('parent_id', $keyword)
            ->orWhere('id', $keyword)
            ->pluck('id')
            ->toArray();
        $groupList = TencentAdAccount::query()
            ->whereIn("admin_uid", $adminUids)
            ->orderByDesc("id")
            ->get(['account_id', DB::raw('account_id as text')]);

        return $groupList;

    }


    /**
     * 广告定向获取人群包
     *
     * @param Request $request
     * @return \Illuminate\Support\Collection
     * @throws Exception
     */
    public function getAudienceList(Request $request): \Illuminate\Support\Collection
    {
        $q = $request->get("q", 0);
        $accountIds = explode(",",$q);
        $response = CustomAudiences::get($accountIds[0]);
        $resp = json_decode($response, true);
        $returnData = [
            [
                'id'=>'none',
                'text' => '清空'
            ]
        ];
        if (isset($resp['data']['list'])) {
            foreach ($resp['data']['list'] as $k => $label) {
                $returnData[] = [
                    'id'=>$label['audience_id'],
                    'text' => $label['name']
                ];
            }
        }
        return Collection::make($returnData);
    }


    /**
     * 获取可被转移许可证的销售
     *
     * @param Request $request
     * @return Builder[]|Collection
     * @throws Exception
     */
    public function getAllowTransferWwUser(Request $request)
    {
        $keyword = $request->get("q", '');
        $wwUserId = $request->get("ww_user_id", 0);
        $wwUser = WwUser::query()->find($wwUserId);
        if (!$wwUser) {
            return [];
        }
        $list = WwUser::select('id', DB::raw('name as text'))
            ->when($keyword, function ($query) use ($keyword) {
                return $query->where(function ($subQuery) use ($keyword) {
                    $subQuery->where('name', 'like', "%{$keyword}%")
                             ->orWhere('id', 'like', "%{$keyword}%");
                });
            })
            ->where('id', '<>', $wwUserId)
            ->where('corp_id', $wwUser->corp_id)
            ->where('type', 1)
            // ->where('status', 1) // 只查询已激活的销售
            ->where('license_c_ex_time', '<=', now()->addDays(20)) // 许可证剩余时间小于等于20天
            ->orderByDesc('id')
            ->paginate();

        return $list;
    }
}
