<?php

	namespace App\Models;

	use Dcat\Admin\Traits\HasDateTimeFormatter;
    use Illuminate\Database\Eloquent\Model;
    use Illuminate\Database\Eloquent\SoftDeletes;

    /**
	 * @property mixed        $admin_uid
	 * @property mixed|string $product
	 * @property mixed|string $mdm
	 * @property mixed        $shield_policy_id
	 */
	class ShieldPolicyRule extends Model
	{
		use HasDateTimeFormatter;
		use SoftDeletes;

		protected $table = 'shield_policy_rules';

	}
