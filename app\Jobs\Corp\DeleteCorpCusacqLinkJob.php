<?php

namespace App\Jobs\Corp;

use App\Models\WwUser;
use App\Services\Corp\WwCorpApiService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DeleteCorpCusacqLinkJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $corpInfo;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($corpInfo)
    {
        $this->corpInfo = $corpInfo;
    }


    /**
     * Execute the job.
     *
     * @return mixed
     */
    public function handle()
    {
        $cursor = '';
        $linkId = [];
        while (1) {
            $resp = WwCorpApiService::list_link($this->corpInfo, $cursor);
            $linkId = array_merge($linkId, $resp['link_id_list']);
            if (!empty($resp['next_cursor'])) {
                $cursor = $resp['next_cursor'];
            } else {
                break;
            }
        }
        $wwUsers = WwUser::withTrashed()
            ->whereIn("cus_acq_link_id", $linkId)
            ->where("corp_id", $this->corpInfo->id)
            ->whereNotNull("deleted_at")
            ->get();
        if ($wwUsers->isNotEmpty()) {
            $count = count($wwUsers->toArray());
            foreach ($wwUsers as $wwUser) {
                $delResp = WwCorpApiService::delete_link($this->corpInfo, $wwUser); // 先删除旧的获客助手链接
                $wwUser->cus_acq_link_status_message = $delResp['errmsg'] ?? '删除失败';
                $wwUser->save();
                Log::info('企微ID：' .$this->corpInfo->id .  '，删除获客助手链接队列执行结果：' . json_encode($delResp));
            }
            Log::info('企微ID：' .$this->corpInfo->id .  '，删除获客助手链接总数：' . $count);
        }

        return true;
    }
}
