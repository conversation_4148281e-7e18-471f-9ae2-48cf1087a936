<?php

namespace App\Console\Commands\Tads\Account;

use App\Models\AdAccountDynamicCreative;
use App\Models\AdAccountPage;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MatchAdAccountPages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'MatchAdAccountPages';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        AdAccountDynamicCreative::query()
            ->select("creative_components", "id", "account_id")
            ->with("adAccountInfo:id,account_id,access_token")
            ->where('system_industry_id',"***********")
            ->whereNull("ln_url")
            ->chunkById(1000, function ($list) {
                foreach ($list as $item) {
                    $this->info($item->id);
                    $data = json_decode($item->creative_components,true);
                    if(!$data){
                        continue;
                    }
                    if(isset($data['main_jump_info'])){
                        foreach($data['main_jump_info'] as $jumpItem){
                            if($jumpItem['value']['page_type'] == 'PAGE_TYPE_WECHAT_CANVAS'){
                                $item->ln_url = "PAGE_TYPE_WECHAT_CANVAS";
                                $item->save();
                            }elseif($jumpItem['value']['page_type'] == 'PAGE_TYPE_OFFICIAL'){
                                $pageId = $jumpItem['value']['page_spec']['official_spec']['page_id'];
                                $pages = AdAccountPage::query()->where("page_id",$pageId)->first();
                                if($pages){
                                    $item->ln_url = $pages->page_url;
                                    $item->save();
                                }
                            }
                        }
                    }else{
                        $item->ln_url = "-1";
                        $item->save();
                    }
                }
            });
    }
}
