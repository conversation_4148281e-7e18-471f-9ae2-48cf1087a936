<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\AgainSetTagTask;
use App\Jobs\WwUserAddRecord\AgainSetTagPreJob;
use App\Models\AdminUser;
use App\Models\AgainSetTagTask as AgainSetTagTaskModel;
use App\Services\Tools\UtilsService;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\Request;

class AgainSetTagTaskController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new AgainSetTagTask(['adminInfo', 'corpInfo','groupInfo']), function (Grid $grid) {
            /** 对话框新增 */
            $grid->disableCreateButton();
            $grid->tools(function (Grid\Tools $tools) {
                $className = collect(Request::segments())->last();
                $tools->append(UtilsService::dialogForm('添加',Request::url().'/create',"create-{$className}"));
            });
            $grid->model()->orderBy('id', 'desc');
            $grid->column('id')->sortable();
            $grid->column('admin_uid', '用户ID');
            $grid->column("adminInfo.username", "用户");
            $grid->column("corp_id", "企微ID");
            $grid->column("corpInfo.corp_name", "企微");
            $grid->column("ww_group_id", "分组ID")->display(function ($ww_group_id) {
                if(!$ww_group_id){
                    return '无';
                }
                return $ww_group_id;
            });
            $grid->column("groupInfo.title", "分组");
            $grid->column('status', '状态')->using(AgainSetTagTaskModel::STATUS)->label([
                0 => 'default',
                1 => 'warning',
                2 => 'success',
                3 => 'warning',
                4 => 'danger',
            ]);
            $grid->column("add_label", "补打标签");
            $grid->column("del_label", "删除标签");
            $grid->column("err_desc", "备注");
            $grid->column('add_record_total', '总数');
            $grid->column("start_at", '进粉开始时间');
            $grid->column("end_at", '进粉结束时间');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) use ($grid) {
                $filter->panel('查询');
                $filter->expand();
                $adminUidListName = AdminUser::query()->where("parent_id", 0)->pluck("username", "id");
                $filter->equal('admin_uid', '用户')->select($adminUidListName)->loads(['corp_id','ww_group_id'], ['api/getCorpList','api/getGroupList'])->width(2);
                $filter->equal('corp_id', '企微')->select()->width(2);
                $filter->equal('ww_group_id', '分组')->select()->width(2);
                $filter->equal("status", '状态')->select(AgainSetTagTaskModel::STATUS)->width(2);
            });
            $grid->disableViewButton();
            $grid->disableBatchActions();
        });
    }


    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new AgainSetTagTask(), function (Form $form) {
            $form->display('id');
            $form->hidden('id');
//            $form->hidden('admin_uid');
//            $form->hidden('corp_id');
            $adminUsers = AdminUser::query()->where("parent_id", 0)->pluck("username", "id");
            $form->select('admin_uid', '用户')
                ->options($adminUsers)
                ->loads(['corp_id', 'ww_group_id'], ['api/getCorpList', 'api/getGroupList'])
                ->help('此处选择的是主账号'
                )->required();
            $form->select('corp_id', '企微')->required();
            $form->select('ww_group_id', '分组')->help('非必选，如果选择了分组，只会修改关联了该分组的进粉记录标签。');
            $form->textarea('add_record_id','进粉记录ID')->help('进粉记录ID，非必填，如果有少量进粉补打标签可填写对应进粉记录ID,回车换行。');
            $form->text('add_label', '补打的标签')->help('请向客户确认补打的标签是否在企微管理后台配置，非必填，“但是不能与删除的标签同时为空”。');
            $form->text('del_label', '删除的标签')->help('请向客户确认删除的标签是否在企微管理后台配置，非必填，“但是不能与补打的标签同时为空”。');
            $form->datetime('start_at', '进粉开始时间')->required();
            $form->datetime('end_at', '进粉结束时间')->required();
            $form->radio('status', '状态')->options(AgainSetTagTaskModel::STATUS)->default(0)->help('修改为【等待执行】状态，任务会重新执行');
            $form->disableViewButton();
            $form->disableViewCheck();
            $form->disableDeleteButton();
        })->saved(function (Form $form, $result) {
            if (!$form->input('add_label') && !$form->input('del_label')) {
                return $form->response()->alert()->error('提示')->detail('补打的标签和删除的标签不能同时为空');
            }
            if ($form->input('start_at') > $form->input('end_at')) {
                return $form->response()->alert()->error('提示')->detail('进粉开始时间不能大于进粉结束时间');
            }
            if ($form->isCreating()) {
                $id = $result;
            } else {
                $id = $form->getKey();
            }
            if (!$id) {
                return $form->response()->alert()->error('提示')->detail('任务创建失败，请稍后重试-1');
            }
            $task = AgainSetTagTaskModel::query()->where('id', $id)->first();
            if (!$task) {
                return $form->response()->alert()->error('提示')->detail('任务创建失败，请稍后重试-2');
            }
            if ($task->status == 0) {
                AgainSetTagPreJob::dispatch($task)->onQueue('again_set_tag_pre');
            }
        });
    }



}
