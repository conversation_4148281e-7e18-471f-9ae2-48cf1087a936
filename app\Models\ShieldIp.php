<?php

	namespace App\Models;

	use Dcat\Admin\Admin;
    use Dcat\Admin\Traits\HasDateTimeFormatter;
    use Illuminate\Database\Eloquent\Model;
    use Illuminate\Database\Eloquent\Relations\BelongsTo;
    use Illuminate\Database\Eloquent\SoftDeletes;
    use Illuminate\Support\Facades\Cache;

    /**
	 * @property mixed|string $ip
	 * @property mixed        $admin_uid
	 * @property mixed        $remark
	 */
	class ShieldIp extends Model
	{
		use HasDateTimeFormatter;
		use SoftDeletes;

		protected $table = 'shield_ips';

		public function adminInfo(): BelongsTo
		{
			return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
		}

        public static function getIpList(){
            /** @var AdminUser $adminUser */
            $adminUser = Admin::user();
            if(!$adminUser){
                return [];
            }
            $redisKey = 'ShieldIp_v2_'.$adminUser->id;
            $shieldIps = Cache::store('redis')->get($redisKey);
            if($shieldIps){
                return json_decode($shieldIps,true);
            }
        	$shieldIps = self::query()->whereIn("admin_uid",[$adminUser->id,$adminUser->parent_id])->pluck("ip")->toArray();
            Cache::store('redis')->set($redisKey, json_encode($shieldIps),30);
            return $shieldIps;
        }



        public static function create($adminUid,$ip,$remark): void
        {
            /** @var ShieldIp $ipData */
            $ipData = self::query()->where("admin_uid", $adminUid)->where("ip", $ip)->first();
            if ($ipData) {
                $ipData->updated_at = date("Y-m-d H:i:s", time());
                $ipData->save();
                return;
            }
            $obj = new ShieldIp();
            $obj->admin_uid = $adminUid;
            $obj->ip = $ip;
            $obj->remark = $remark;
            $obj->save();
        }
	}
