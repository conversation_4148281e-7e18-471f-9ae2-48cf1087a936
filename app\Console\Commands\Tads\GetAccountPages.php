<?php

    namespace App\Console\Commands\Tads;

    use App\Models\TencentAdAccount;
    use Illuminate\Console\Command;

    class GetAccountPages extends Command
    {
        /**
         * The name and signature of the console command.
         *
         * @var string
         */
        protected $signature = 'Tads:GetAccountPages';

        /**
         * The console command description.
         *
         * @var string
         */
        protected $description = 'Command description';

        /**
         * Execute the console command.
         *
         * @return int
         */
        public function handle()
        {
            $accountIds = [
//                ********,
                ********
            ];
            $adAccounts = TencentAdAccount::query()->whereIn("account_id", $accountIds)->get();

            foreach ($adAccounts as $adAccount) {
                $a = $this->getPages($adAccount);
                $b = $this->wechat_pages_get($adAccount);
                dd([
                    json_decode($a,true),
                    json_decode($b,true),
                ]);
            }
            return Command::SUCCESS;
        }

        public function getPages(AdAccount $account)
        {
            $interface = 'pages/get';
            $url = 'https://api.e.qq.com/v3.0/' . $interface;

            $common_parameters = array(
                'access_token' => $account->access_token,
                'timestamp' => time(),
                'nonce' => md5(uniqid('', true))
            );

            $parameters = array(
                'account_id' => $account->account_id,
                'filtering' => [
                    [
                        'field' => 'page_type',
                        'values' => ['PAGE_TYPE_OFFICIAL'],
                        'operator' => 'EQUALS',
                    ],
                    [
                        'field' => 'page_status',
                        'values' => ['NORMAL'],
                        'operator' => 'EQUALS',
                    ],
                ],
                'page' => 1,
                'page_size' => 100,
            );

            $parameters = array_merge($common_parameters, $parameters);

            foreach ($parameters as $key => $value) {
                if (!is_string($value)) {
                    $parameters[$key] = json_encode($value);
                }
            }

            $request_url = $url . '?' . http_build_query($parameters);

            $curl = curl_init();
            curl_setopt($curl, CURLOPT_URL, $request_url);
            curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 60);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
            $response = curl_exec($curl);
            if (curl_error($curl)) {
                $error_msg = curl_error($curl);
                $error_no = curl_errno($curl);
                curl_close($curl);
                throw new \Exception($error_msg, $error_no);
            }
            curl_close($curl);
            return $response;
        }

        function wechat_pages_get(AdAccount $account)
        {
            $interface = 'wechat_pages/get';
            $url = 'https://api.e.qq.com/v3.0/' . $interface;

            $common_parameters = array (
                'access_token' => $account->access_token,
                'timestamp' => time(),
                'nonce' => md5(uniqid('', true))
            );

            $parameters = array (
                'account_id' => $account->account_id,
                'filtering' =>[],
                'page' => 1,
                'page_size' => 100,
            );

            $parameters = array_merge($common_parameters, $parameters);

            foreach ($parameters as $key => $value) {
                if (!is_string($value)) {
                    $parameters[$key] = json_encode($value);
                }
            }

            $request_url = $url . '?' . http_build_query($parameters);

            $curl = curl_init();
            curl_setopt($curl, CURLOPT_URL, $request_url);
            curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 60);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
            $response = curl_exec($curl);
            if (curl_error($curl)) {
                $error_msg = curl_error($curl);
                $error_no = curl_errno($curl);
                curl_close($curl);
                throw new \Exception($error_msg, $error_no);
            }
            curl_close($curl);
            return $response;
        }
    }
