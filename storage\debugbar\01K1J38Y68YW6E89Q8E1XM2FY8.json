{"__meta": {"id": "01K1J38Y68YW6E89Q8E1XM2FY8", "datetime": "2025-08-01 13:56:57", "utime": **********.161507, "method": "GET", "uri": "/ztfz/sale", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754027813.331408, "end": **********.16152, "duration": 3.8301119804382324, "duration_str": "3.83s", "measures": [{"label": "Booting", "start": 1754027813.331408, "relative_start": 0, "end": **********.072105, "relative_end": **********.072105, "duration": 0.***************, "duration_str": "741ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.072121, "relative_start": 0.****************, "end": **********.161522, "relative_end": 1.9073486328125e-06, "duration": 3.****************, "duration_str": "3.09s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.106969, "relative_start": 0.****************, "end": **********.112555, "relative_end": **********.112555, "duration": 0.005585908889770508, "duration_str": "5.59ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: admin::grid.table", "start": **********.080073, "relative_start": 1.****************, "end": **********.080073, "relative_end": **********.080073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.table-toolbar", "start": **********.081123, "relative_start": 1.****************, "end": **********.081123, "relative_end": **********.081123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.batch-actions", "start": **********.081985, "relative_start": 1.****************, "end": **********.081985, "relative_end": **********.081985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.dropdown-actions", "start": **********.083998, "relative_start": 1.7525899410247803, "end": **********.083998, "relative_end": **********.083998, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.dropdown-actions", "start": **********.084692, "relative_start": 1.753283977508545, "end": **********.084692, "relative_end": **********.084692, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.dropdown-actions", "start": **********.085044, "relative_start": 1.7536358833312988, "end": **********.085044, "relative_end": **********.085044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.dropdown-actions", "start": **********.085377, "relative_start": 1.7539689540863037, "end": **********.085377, "relative_end": **********.085377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.dropdown-actions", "start": **********.085703, "relative_start": 1.7542948722839355, "end": **********.085703, "relative_end": **********.085703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.dropdown-actions", "start": **********.086028, "relative_start": 1.75462007522583, "end": **********.086028, "relative_end": **********.086028, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.dropdown-actions", "start": **********.086363, "relative_start": 1.7549550533294678, "end": **********.086363, "relative_end": **********.086363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.dropdown-actions", "start": **********.086683, "relative_start": 1.755275011062622, "end": **********.086683, "relative_end": **********.086683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.dropdown-actions", "start": **********.087001, "relative_start": 1.7555930614471436, "end": **********.087001, "relative_end": **********.087001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.dropdown-actions", "start": **********.087334, "relative_start": 1.7559258937835693, "end": **********.087334, "relative_end": **********.087334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.table-pagination", "start": **********.08794, "relative_start": 1.7565319538116455, "end": **********.08794, "relative_end": **********.08794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.pagination", "start": **********.089942, "relative_start": 1.7585339546203613, "end": **********.089942, "relative_end": **********.089942, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.dropdown", "start": **********.091919, "relative_start": 1.7605109214782715, "end": **********.091919, "relative_end": **********.091919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.switch", "start": **********.869427, "relative_start": 2.5380189418792725, "end": **********.869427, "relative_end": **********.869427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.switch", "start": **********.870277, "relative_start": 2.5388689041137695, "end": **********.870277, "relative_end": **********.870277, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.switch", "start": **********.870729, "relative_start": 2.539320945739746, "end": **********.870729, "relative_end": **********.870729, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.radio", "start": **********.872221, "relative_start": 2.5408129692077637, "end": **********.872221, "relative_end": **********.872221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.radio", "start": **********.872686, "relative_start": 2.5412778854370117, "end": **********.872686, "relative_end": **********.872686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.874257, "relative_start": 2.542849063873291, "end": **********.874257, "relative_end": **********.874257, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.radio", "start": **********.875342, "relative_start": 2.543933868408203, "end": **********.875342, "relative_end": **********.875342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.radio", "start": **********.875539, "relative_start": 2.54413104057312, "end": **********.875539, "relative_end": **********.875539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.876292, "relative_start": 2.544883966445923, "end": **********.876292, "relative_end": **********.876292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.radio", "start": **********.877131, "relative_start": 2.5457229614257812, "end": **********.877131, "relative_end": **********.877131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.radio", "start": **********.877315, "relative_start": 2.5459070205688477, "end": **********.877315, "relative_end": **********.877315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.displayer.editinline.template", "start": **********.878032, "relative_start": 2.546623945236206, "end": **********.878032, "relative_end": **********.878032, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.fixed-table", "start": **********.882587, "relative_start": 2.5511789321899414, "end": **********.882587, "relative_end": **********.882587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.table-toolbar", "start": **********.883424, "relative_start": 2.552016019821167, "end": **********.883424, "relative_end": **********.883424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.batch-actions", "start": **********.88393, "relative_start": 2.5525219440460205, "end": **********.88393, "relative_end": **********.88393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.button", "start": **********.885035, "relative_start": 2.5536270141601562, "end": **********.885035, "relative_end": **********.885035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.form", "start": **********.273781, "relative_start": 2.942373037338257, "end": **********.273781, "relative_end": **********.273781, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.fields", "start": **********.274762, "relative_start": 2.9433538913726807, "end": **********.274762, "relative_end": **********.274762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select", "start": **********.275704, "relative_start": 2.944295883178711, "end": **********.275704, "relative_end": **********.275704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.276596, "relative_start": 2.945188045501709, "end": **********.276596, "relative_end": **********.276596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.277412, "relative_start": 2.9460039138793945, "end": **********.277412, "relative_end": **********.277412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select-script", "start": **********.278166, "relative_start": 2.9467580318450928, "end": **********.278166, "relative_end": **********.278166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.278996, "relative_start": 2.9475879669189453, "end": **********.278996, "relative_end": **********.278996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.hidden", "start": **********.28035, "relative_start": 2.948941946029663, "end": **********.28035, "relative_end": **********.28035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.hidden", "start": **********.280989, "relative_start": 2.9495809078216553, "end": **********.280989, "relative_end": **********.280989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.form", "start": **********.823505, "relative_start": 3.4920969009399414, "end": **********.823505, "relative_end": **********.823505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.fields", "start": **********.824104, "relative_start": 3.4926960468292236, "end": **********.824104, "relative_end": **********.824104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select", "start": **********.824599, "relative_start": 3.4931910037994385, "end": **********.824599, "relative_end": **********.824599, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.825043, "relative_start": 3.4936349391937256, "end": **********.825043, "relative_end": **********.825043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.825416, "relative_start": 3.4940080642700195, "end": **********.825416, "relative_end": **********.825416, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select-script", "start": **********.825773, "relative_start": 3.4943649768829346, "end": **********.825773, "relative_end": **********.825773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.826142, "relative_start": 3.494734048843384, "end": **********.826142, "relative_end": **********.826142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.multipleselect", "start": **********.827167, "relative_start": 3.4957590103149414, "end": **********.827167, "relative_end": **********.827167, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.827774, "relative_start": 3.496366024017334, "end": **********.827774, "relative_end": **********.827774, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.828121, "relative_start": 3.4967129230499268, "end": **********.828121, "relative_end": **********.828121, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select-script", "start": **********.828464, "relative_start": 3.497056007385254, "end": **********.828464, "relative_end": **********.828464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.828825, "relative_start": 3.4974169731140137, "end": **********.828825, "relative_end": **********.828825, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select", "start": **********.829512, "relative_start": 3.4981038570404053, "end": **********.829512, "relative_end": **********.829512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.829936, "relative_start": 3.498528003692627, "end": **********.829936, "relative_end": **********.829936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.830289, "relative_start": 3.4988808631896973, "end": **********.830289, "relative_end": **********.830289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.select-script", "start": **********.830634, "relative_start": 3.4992260932922363, "end": **********.830634, "relative_end": **********.830634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.830982, "relative_start": 3.4995739459991455, "end": **********.830982, "relative_end": **********.830982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.file", "start": **********.831949, "relative_start": 3.5005409717559814, "end": **********.831949, "relative_end": **********.831949, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.832821, "relative_start": 3.501412868499756, "end": **********.832821, "relative_end": **********.832821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.833255, "relative_start": 3.5018470287323, "end": **********.833255, "relative_end": **********.833255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.keyvalue", "start": **********.834651, "relative_start": 3.5032429695129395, "end": **********.834651, "relative_end": **********.834651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.textarea", "start": **********.836213, "relative_start": 3.504805088043213, "end": **********.836213, "relative_end": **********.836213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.836821, "relative_start": 3.505413055419922, "end": **********.836821, "relative_end": **********.836821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.837181, "relative_start": 3.5057730674743652, "end": **********.837181, "relative_end": **********.837181, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.textarea", "start": **********.837695, "relative_start": 3.506286859512329, "end": **********.837695, "relative_end": **********.837695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.838081, "relative_start": 3.5066728591918945, "end": **********.838081, "relative_end": **********.838081, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.838427, "relative_start": 3.50701904296875, "end": **********.838427, "relative_end": **********.838427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.listbox", "start": **********.839268, "relative_start": 3.507859945297241, "end": **********.839268, "relative_end": **********.839268, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.839859, "relative_start": 3.508450984954834, "end": **********.839859, "relative_end": **********.839859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.840209, "relative_start": 3.508800983428955, "end": **********.840209, "relative_end": **********.840209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.radio", "start": **********.841061, "relative_start": 3.509653091430664, "end": **********.841061, "relative_end": **********.841061, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.radio", "start": **********.841246, "relative_start": 3.5098378658294678, "end": **********.841246, "relative_end": **********.841246, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.842191, "relative_start": 3.5107829570770264, "end": **********.842191, "relative_end": **********.842191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.842515, "relative_start": 3.5111069679260254, "end": **********.842515, "relative_end": **********.842515, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.radio", "start": **********.843075, "relative_start": 3.511667013168335, "end": **********.843075, "relative_end": **********.843075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.radio", "start": **********.843257, "relative_start": 3.5118489265441895, "end": **********.843257, "relative_end": **********.843257, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.844002, "relative_start": 3.512593984603882, "end": **********.844002, "relative_end": **********.844002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.844337, "relative_start": 3.5129289627075195, "end": **********.844337, "relative_end": **********.844337, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.845213, "relative_start": 3.5138049125671387, "end": **********.845213, "relative_end": **********.845213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.84578, "relative_start": 3.514371871948242, "end": **********.84578, "relative_end": **********.84578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.84611, "relative_start": 3.514702081680298, "end": **********.84611, "relative_end": **********.84611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.846628, "relative_start": 3.5152199268341064, "end": **********.846628, "relative_end": **********.846628, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.846993, "relative_start": 3.515584945678711, "end": **********.846993, "relative_end": **********.846993, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.847313, "relative_start": 3.5159049034118652, "end": **********.847313, "relative_end": **********.847313, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.number", "start": **********.848113, "relative_start": 3.51670503616333, "end": **********.848113, "relative_end": **********.848113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.848678, "relative_start": 3.517270088195801, "end": **********.848678, "relative_end": **********.848678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.849035, "relative_start": 3.517627000808716, "end": **********.849035, "relative_end": **********.849035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.number", "start": **********.849584, "relative_start": 3.5181760787963867, "end": **********.849584, "relative_end": **********.849584, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.849939, "relative_start": 3.518531084060669, "end": **********.849939, "relative_end": **********.849939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.850349, "relative_start": 3.5189409255981445, "end": **********.850349, "relative_end": **********.850349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.850947, "relative_start": 3.5195388793945312, "end": **********.850947, "relative_end": **********.850947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.851334, "relative_start": 3.519926071166992, "end": **********.851334, "relative_end": **********.851334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.851662, "relative_start": 3.520253896713257, "end": **********.851662, "relative_end": **********.851662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.85218, "relative_start": 3.5207719802856445, "end": **********.85218, "relative_end": **********.85218, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.852543, "relative_start": 3.521135091781616, "end": **********.852543, "relative_end": **********.852543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.852866, "relative_start": 3.5214579105377197, "end": **********.852866, "relative_end": **********.852866, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.checkbox", "start": **********.853953, "relative_start": 3.5225448608398438, "end": **********.853953, "relative_end": **********.853953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.checkbox", "start": **********.854409, "relative_start": 3.523000955581665, "end": **********.854409, "relative_end": **********.854409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.855547, "relative_start": 3.524138927459717, "end": **********.855547, "relative_end": **********.855547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.855981, "relative_start": 3.5245730876922607, "end": **********.855981, "relative_end": **********.855981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.856832, "relative_start": 3.525424003601074, "end": **********.856832, "relative_end": **********.856832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.857433, "relative_start": 3.5260250568389893, "end": **********.857433, "relative_end": **********.857433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.857838, "relative_start": 3.5264298915863037, "end": **********.857838, "relative_end": **********.857838, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.858418, "relative_start": 3.527009963989258, "end": **********.858418, "relative_end": **********.858418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.858802, "relative_start": 3.5273940563201904, "end": **********.858802, "relative_end": **********.858802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.859143, "relative_start": 3.5277349948883057, "end": **********.859143, "relative_end": **********.859143, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.input", "start": **********.859642, "relative_start": 3.5282340049743652, "end": **********.859642, "relative_end": **********.859642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.error", "start": **********.860015, "relative_start": 3.52860689163208, "end": **********.860015, "relative_end": **********.860015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.help-block", "start": **********.860341, "relative_start": 3.528933048248291, "end": **********.860341, "relative_end": **********.860341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.hidden", "start": **********.860834, "relative_start": 3.529425859451294, "end": **********.860834, "relative_end": **********.860834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::form.hidden", "start": **********.861281, "relative_start": 3.5298728942871094, "end": **********.861281, "relative_end": **********.861281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.column-selector", "start": **********.862159, "relative_start": 3.5307509899139404, "end": **********.862159, "relative_end": **********.862159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.checkbox", "start": **********.862345, "relative_start": 3.5309369564056396, "end": **********.862345, "relative_end": **********.862345, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.checkbox", "start": **********.864355, "relative_start": 3.532947063446045, "end": **********.864355, "relative_end": **********.864355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.container", "start": **********.867388, "relative_start": 3.535979986190796, "end": **********.867388, "relative_end": **********.867388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.869527, "relative_start": 3.538119077682495, "end": **********.869527, "relative_end": **********.869527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.870458, "relative_start": 3.5390498638153076, "end": **********.870458, "relative_end": **********.870458, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.871348, "relative_start": 3.5399398803710938, "end": **********.871348, "relative_end": **********.871348, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.872001, "relative_start": 3.540592908859253, "end": **********.872001, "relative_end": **********.872001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.872654, "relative_start": 3.541245937347412, "end": **********.872654, "relative_end": **********.872654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.873397, "relative_start": 3.5419890880584717, "end": **********.873397, "relative_end": **********.873397, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.873765, "relative_start": 3.5423569679260254, "end": **********.873765, "relative_end": **********.873765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.874357, "relative_start": 3.5429489612579346, "end": **********.874357, "relative_end": **********.874357, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.874718, "relative_start": 3.5433099269866943, "end": **********.874718, "relative_end": **********.874718, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.875263, "relative_start": 3.5438549518585205, "end": **********.875263, "relative_end": **********.875263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.875626, "relative_start": 3.544218063354492, "end": **********.875626, "relative_end": **********.875626, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.876172, "relative_start": 3.5447640419006348, "end": **********.876172, "relative_end": **********.876172, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.876551, "relative_start": 3.545142889022827, "end": **********.876551, "relative_end": **********.876551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.876978, "relative_start": 3.545569896697998, "end": **********.876978, "relative_end": **********.876978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.87767, "relative_start": 3.54626202583313, "end": **********.87767, "relative_end": **********.87767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.878071, "relative_start": 3.5466630458831787, "end": **********.878071, "relative_end": **********.878071, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.878512, "relative_start": 3.5471038818359375, "end": **********.878512, "relative_end": **********.878512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.87917, "relative_start": 3.547761917114258, "end": **********.87917, "relative_end": **********.87917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.879536, "relative_start": 3.5481278896331787, "end": **********.879536, "relative_end": **********.879536, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.880156, "relative_start": 3.548748016357422, "end": **********.880156, "relative_end": **********.880156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.880543, "relative_start": 3.5491349697113037, "end": **********.880543, "relative_end": **********.880543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.881015, "relative_start": 3.549607038497925, "end": **********.881015, "relative_end": **********.881015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.881683, "relative_start": 3.5502750873565674, "end": **********.881683, "relative_end": **********.881683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.882059, "relative_start": 3.5506510734558105, "end": **********.882059, "relative_end": **********.882059, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.882488, "relative_start": 3.5510799884796143, "end": **********.882488, "relative_end": **********.882488, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.883156, "relative_start": 3.551748037338257, "end": **********.883156, "relative_end": **********.883156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.883803, "relative_start": 3.5523948669433594, "end": **********.883803, "relative_end": **********.883803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.884577, "relative_start": 3.553169012069702, "end": **********.884577, "relative_end": **********.884577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.885771, "relative_start": 3.5543630123138428, "end": **********.885771, "relative_end": **********.885771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.886268, "relative_start": 3.5548598766326904, "end": **********.886268, "relative_end": **********.886268, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.886735, "relative_start": 3.5553269386291504, "end": **********.886735, "relative_end": **********.886735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.887386, "relative_start": 3.5559780597686768, "end": **********.887386, "relative_end": **********.887386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.text", "start": **********.887759, "relative_start": 3.5563509464263916, "end": **********.887759, "relative_end": **********.887759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.where", "start": **********.888365, "relative_start": 3.5569570064544678, "end": **********.888365, "relative_end": **********.888365, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::filter.select", "start": **********.888837, "relative_start": 3.557429075241089, "end": **********.888837, "relative_end": **********.888837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::scripts.select", "start": **********.889435, "relative_start": 3.5580270290374756, "end": **********.889435, "relative_end": **********.889435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.table-pagination", "start": **********.891119, "relative_start": 3.55971097946167, "end": **********.891119, "relative_end": **********.891119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::grid.pagination", "start": **********.891669, "relative_start": 3.5602610111236572, "end": **********.891669, "relative_end": **********.891669, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.dropdown", "start": **********.89226, "relative_start": 3.56085205078125, "end": **********.89226, "relative_end": **********.89226, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::widgets.tab", "start": **********.896787, "relative_start": 3.5653789043426514, "end": **********.896787, "relative_end": **********.896787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::layouts.content", "start": **********.898131, "relative_start": 3.566722869873047, "end": **********.898131, "relative_end": **********.898131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.breadcrumb", "start": **********.89912, "relative_start": 3.5677120685577393, "end": **********.89912, "relative_end": **********.89912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.alerts", "start": **********.900608, "relative_start": 3.569200038909912, "end": **********.900608, "relative_end": **********.900608, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.exception", "start": **********.901893, "relative_start": 3.5704848766326904, "end": **********.901893, "relative_end": **********.901893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.toastr", "start": **********.902995, "relative_start": 3.571587085723877, "end": **********.902995, "relative_end": **********.902995, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::layouts.page", "start": **********.904461, "relative_start": 3.5730528831481934, "end": **********.904461, "relative_end": **********.904461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::layouts.container", "start": **********.905843, "relative_start": 3.574434995651245, "end": **********.905843, "relative_end": **********.905843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.sidebar", "start": **********.906828, "relative_start": 3.5754199028015137, "end": **********.906828, "relative_end": **********.906828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.103025, "relative_start": 3.7716169357299805, "end": **********.103025, "relative_end": **********.103025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.104717, "relative_start": 3.7733089923858643, "end": **********.104717, "relative_end": **********.104717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.105388, "relative_start": 3.773979902267456, "end": **********.105388, "relative_end": **********.105388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.105884, "relative_start": 3.7744760513305664, "end": **********.105884, "relative_end": **********.105884, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.106355, "relative_start": 3.774946928024292, "end": **********.106355, "relative_end": **********.106355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.106817, "relative_start": 3.775408983230591, "end": **********.106817, "relative_end": **********.106817, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.107357, "relative_start": 3.775949001312256, "end": **********.107357, "relative_end": **********.107357, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.107783, "relative_start": 3.7763750553131104, "end": **********.107783, "relative_end": **********.107783, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.108374, "relative_start": 3.776966094970703, "end": **********.108374, "relative_end": **********.108374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.109022, "relative_start": 3.777613878250122, "end": **********.109022, "relative_end": **********.109022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.109525, "relative_start": 3.7781169414520264, "end": **********.109525, "relative_end": **********.109525, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.11005, "relative_start": 3.778641939163208, "end": **********.11005, "relative_end": **********.11005, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.110565, "relative_start": 3.7791569232940674, "end": **********.110565, "relative_end": **********.110565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.111066, "relative_start": 3.779658079147339, "end": **********.111066, "relative_end": **********.111066, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.111557, "relative_start": 3.780148983001709, "end": **********.111557, "relative_end": **********.111557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.11207, "relative_start": 3.7806620597839355, "end": **********.11207, "relative_end": **********.11207, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.112577, "relative_start": 3.7811689376831055, "end": **********.112577, "relative_end": **********.112577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.113087, "relative_start": 3.7816789150238037, "end": **********.113087, "relative_end": **********.113087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.113581, "relative_start": 3.782172918319702, "end": **********.113581, "relative_end": **********.113581, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.114083, "relative_start": 3.78267502784729, "end": **********.114083, "relative_end": **********.114083, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.114591, "relative_start": 3.7831828594207764, "end": **********.114591, "relative_end": **********.114591, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.115093, "relative_start": 3.7836849689483643, "end": **********.115093, "relative_end": **********.115093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.115586, "relative_start": 3.7841780185699463, "end": **********.115586, "relative_end": **********.115586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.116087, "relative_start": 3.7846789360046387, "end": **********.116087, "relative_end": **********.116087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.116592, "relative_start": 3.785183906555176, "end": **********.116592, "relative_end": **********.116592, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.117373, "relative_start": 3.7859649658203125, "end": **********.117373, "relative_end": **********.117373, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.118026, "relative_start": 3.7866179943084717, "end": **********.118026, "relative_end": **********.118026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.118574, "relative_start": 3.787165880203247, "end": **********.118574, "relative_end": **********.118574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.131617, "relative_start": 3.8002090454101562, "end": **********.131617, "relative_end": **********.131617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.132735, "relative_start": 3.8013269901275635, "end": **********.132735, "relative_end": **********.132735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.133404, "relative_start": 3.8019959926605225, "end": **********.133404, "relative_end": **********.133404, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.134024, "relative_start": 3.8026158809661865, "end": **********.134024, "relative_end": **********.134024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.134607, "relative_start": 3.803199052810669, "end": **********.134607, "relative_end": **********.134607, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.135704, "relative_start": 3.8042960166931152, "end": **********.135704, "relative_end": **********.135704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.136448, "relative_start": 3.805039882659912, "end": **********.136448, "relative_end": **********.136448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.137033, "relative_start": 3.8056249618530273, "end": **********.137033, "relative_end": **********.137033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.13769, "relative_start": 3.8062820434570312, "end": **********.13769, "relative_end": **********.13769, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.13853, "relative_start": 3.807121992111206, "end": **********.13853, "relative_end": **********.13853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.13914, "relative_start": 3.807731866836548, "end": **********.13914, "relative_end": **********.13914, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.139763, "relative_start": 3.8083550930023193, "end": **********.139763, "relative_end": **********.139763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.140342, "relative_start": 3.808933973312378, "end": **********.140342, "relative_end": **********.140342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.140889, "relative_start": 3.809480905532837, "end": **********.140889, "relative_end": **********.140889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.141419, "relative_start": 3.8100109100341797, "end": **********.141419, "relative_end": **********.141419, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.141946, "relative_start": 3.8105380535125732, "end": **********.141946, "relative_end": **********.141946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.142471, "relative_start": 3.811063051223755, "end": **********.142471, "relative_end": **********.142471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.142992, "relative_start": 3.811583995819092, "end": **********.142992, "relative_end": **********.142992, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.143524, "relative_start": 3.8121159076690674, "end": **********.143524, "relative_end": **********.143524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.143961, "relative_start": 3.8125529289245605, "end": **********.143961, "relative_end": **********.143961, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.144478, "relative_start": 3.813070058822632, "end": **********.144478, "relative_end": **********.144478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.144993, "relative_start": 3.813585042953491, "end": **********.144993, "relative_end": **********.144993, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.145496, "relative_start": 3.8140878677368164, "end": **********.145496, "relative_end": **********.145496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.146012, "relative_start": 3.8146040439605713, "end": **********.146012, "relative_end": **********.146012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.146535, "relative_start": 3.815126895904541, "end": **********.146535, "relative_end": **********.146535, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.147066, "relative_start": 3.8156580924987793, "end": **********.147066, "relative_end": **********.147066, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.147574, "relative_start": 3.8161659240722656, "end": **********.147574, "relative_end": **********.147574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.148086, "relative_start": 3.816678047180176, "end": **********.148086, "relative_end": **********.148086, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.148617, "relative_start": 3.817209005355835, "end": **********.148617, "relative_end": **********.148617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.14912, "relative_start": 3.8177120685577393, "end": **********.14912, "relative_end": **********.14912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.149616, "relative_start": 3.8182079792022705, "end": **********.149616, "relative_end": **********.149616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.150243, "relative_start": 3.8188350200653076, "end": **********.150243, "relative_end": **********.150243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.151353, "relative_start": 3.8199448585510254, "end": **********.151353, "relative_end": **********.151353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.152834, "relative_start": 3.8214259147644043, "end": **********.152834, "relative_end": **********.152834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.153628, "relative_start": 3.8222200870513916, "end": **********.153628, "relative_end": **********.153628, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.154176, "relative_start": 3.822767972946167, "end": **********.154176, "relative_end": **********.154176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.menu", "start": **********.154666, "relative_start": 3.8232579231262207, "end": **********.154666, "relative_end": **********.154666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.navbar", "start": **********.155517, "relative_start": 3.8241090774536133, "end": **********.155517, "relative_end": **********.155517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: announcements.index", "start": **********.156606, "relative_start": 3.82519793510437, "end": **********.156606, "relative_end": **********.156606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::partials.navbar-user-panel", "start": **********.157892, "relative_start": 3.826483964920044, "end": **********.157892, "relative_end": **********.157892, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 37930440, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.20", "PHP Version": "8.0.2", "Environment": "local", "Debug Mode": "Enabled", "URL": "sen.test", "Timezone": "PRC", "Locale": "zh_CN"}}, "views": {"count": 232, "nb_templates": 232, "templates": [{"name": "1x admin::grid.table", "param_count": null, "params": [], "start": **********.079986, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/table.blade.phpadmin::grid.table", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Ftable.blade.php&line=1", "ajax": false, "filename": "table.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.table"}, {"name": "2x admin::grid.table-toolbar", "param_count": null, "params": [], "start": **********.081052, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/table-toolbar.blade.phpadmin::grid.table-toolbar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Ftable-toolbar.blade.php&line=1", "ajax": false, "filename": "table-toolbar.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::grid.table-toolbar"}, {"name": "2x admin::grid.batch-actions", "param_count": null, "params": [], "start": **********.081915, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/batch-actions.blade.phpadmin::grid.batch-actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fbatch-actions.blade.php&line=1", "ajax": false, "filename": "batch-actions.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::grid.batch-actions"}, {"name": "10x admin::grid.dropdown-actions", "param_count": null, "params": [], "start": **********.083911, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/dropdown-actions.blade.phpadmin::grid.dropdown-actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdropdown-actions.blade.php&line=1", "ajax": false, "filename": "dropdown-actions.blade.php", "line": "?"}, "render_count": 10, "name_original": "admin::grid.dropdown-actions"}, {"name": "2x admin::grid.table-pagination", "param_count": null, "params": [], "start": **********.087873, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/table-pagination.blade.phpadmin::grid.table-pagination", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Ftable-pagination.blade.php&line=1", "ajax": false, "filename": "table-pagination.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::grid.table-pagination"}, {"name": "2x admin::grid.pagination", "param_count": null, "params": [], "start": **********.089861, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/pagination.blade.phpadmin::grid.pagination", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fpagination.blade.php&line=1", "ajax": false, "filename": "pagination.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::grid.pagination"}, {"name": "2x admin::widgets.dropdown", "param_count": null, "params": [], "start": **********.091842, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/dropdown.blade.phpadmin::widgets.dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fdropdown.blade.php&line=1", "ajax": false, "filename": "dropdown.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::widgets.dropdown"}, {"name": "3x admin::grid.displayer.switch", "param_count": null, "params": [], "start": **********.869353, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/switch.blade.phpadmin::grid.displayer.switch", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Fswitch.blade.php&line=1", "ajax": false, "filename": "switch.blade.php", "line": "?"}, "render_count": 3, "name_original": "admin::grid.displayer.switch"}, {"name": "3x admin::grid.displayer.editinline.radio", "param_count": null, "params": [], "start": **********.87215, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/editinline/radio.blade.phpadmin::grid.displayer.editinline.radio", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Feditinline%2Fradio.blade.php&line=1", "ajax": false, "filename": "radio.blade.php", "line": "?"}, "render_count": 3, "name_original": "admin::grid.displayer.editinline.radio"}, {"name": "5x admin::widgets.radio", "param_count": null, "params": [], "start": **********.87262, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/radio.blade.phpadmin::widgets.radio", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fradio.blade.php&line=1", "ajax": false, "filename": "radio.blade.php", "line": "?"}, "render_count": 5, "name_original": "admin::widgets.radio"}, {"name": "3x admin::grid.displayer.editinline.template", "param_count": null, "params": [], "start": **********.874185, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/displayer/editinline/template.blade.phpadmin::grid.displayer.editinline.template", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fdisplayer%2Feditinline%2Ftemplate.blade.php&line=1", "ajax": false, "filename": "template.blade.php", "line": "?"}, "render_count": 3, "name_original": "admin::grid.displayer.editinline.template"}, {"name": "1x admin::grid.fixed-table", "param_count": null, "params": [], "start": **********.882518, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/fixed-table.blade.phpadmin::grid.fixed-table", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Ffixed-table.blade.php&line=1", "ajax": false, "filename": "fixed-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.fixed-table"}, {"name": "1x admin::filter.button", "param_count": null, "params": [], "start": **********.884958, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/button.blade.phpadmin::filter.button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::filter.button"}, {"name": "2x admin::widgets.form", "param_count": null, "params": [], "start": **********.273708, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/form.blade.phpadmin::widgets.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::widgets.form"}, {"name": "2x admin::form.fields", "param_count": null, "params": [], "start": **********.274695, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/fields.blade.phpadmin::form.fields", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ffields.blade.php&line=1", "ajax": false, "filename": "fields.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::form.fields"}, {"name": "3x admin::form.select", "param_count": null, "params": [], "start": **********.275642, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/select.blade.phpadmin::form.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 3, "name_original": "admin::form.select"}, {"name": "20x admin::form.error", "param_count": null, "params": [], "start": **********.276529, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/error.blade.phpadmin::form.error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 20, "name_original": "admin::form.error"}, {"name": "20x admin::form.help-block", "param_count": null, "params": [], "start": **********.277349, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/help-block.blade.phpadmin::form.help-block", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fhelp-block.blade.php&line=1", "ajax": false, "filename": "help-block.blade.php", "line": "?"}, "render_count": 20, "name_original": "admin::form.help-block"}, {"name": "4x admin::form.select-script", "param_count": null, "params": [], "start": **********.278103, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/select-script.blade.phpadmin::form.select-script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fselect-script.blade.php&line=1", "ajax": false, "filename": "select-script.blade.php", "line": "?"}, "render_count": 4, "name_original": "admin::form.select-script"}, {"name": "12x admin::scripts.select", "param_count": null, "params": [], "start": **********.278932, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/scripts/select.blade.phpadmin::scripts.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fscripts%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 12, "name_original": "admin::scripts.select"}, {"name": "4x admin::form.hidden", "param_count": null, "params": [], "start": **********.280283, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/hidden.blade.phpadmin::form.hidden", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fhidden.blade.php&line=1", "ajax": false, "filename": "hidden.blade.php", "line": "?"}, "render_count": 4, "name_original": "admin::form.hidden"}, {"name": "1x admin::form.multipleselect", "param_count": null, "params": [], "start": **********.8271, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/multipleselect.blade.phpadmin::form.multipleselect", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fmultipleselect.blade.php&line=1", "ajax": false, "filename": "multipleselect.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.multipleselect"}, {"name": "1x admin::form.file", "param_count": null, "params": [], "start": **********.831885, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/file.blade.phpadmin::form.file", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ffile.blade.php&line=1", "ajax": false, "filename": "file.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.file"}, {"name": "1x admin::form.keyvalue", "param_count": null, "params": [], "start": **********.834559, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/keyvalue.blade.phpadmin::form.keyvalue", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fkeyvalue.blade.php&line=1", "ajax": false, "filename": "keyvalue.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.keyvalue"}, {"name": "2x admin::form.textarea", "param_count": null, "params": [], "start": **********.836143, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/textarea.blade.phpadmin::form.textarea", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Ftextarea.blade.php&line=1", "ajax": false, "filename": "textarea.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::form.textarea"}, {"name": "1x admin::form.listbox", "param_count": null, "params": [], "start": **********.839203, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/listbox.blade.phpadmin::form.listbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Flistbox.blade.php&line=1", "ajax": false, "filename": "listbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.listbox"}, {"name": "2x admin::form.radio", "param_count": null, "params": [], "start": **********.840995, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/radio.blade.phpadmin::form.radio", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fradio.blade.php&line=1", "ajax": false, "filename": "radio.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::form.radio"}, {"name": "7x admin::form.input", "param_count": null, "params": [], "start": **********.845139, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/input.blade.phpadmin::form.input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Finput.blade.php&line=1", "ajax": false, "filename": "input.blade.php", "line": "?"}, "render_count": 7, "name_original": "admin::form.input"}, {"name": "2x admin::form.number", "param_count": null, "params": [], "start": **********.848046, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/number.blade.phpadmin::form.number", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fnumber.blade.php&line=1", "ajax": false, "filename": "number.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::form.number"}, {"name": "1x admin::form.checkbox", "param_count": null, "params": [], "start": **********.853888, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/form/checkbox.blade.phpadmin::form.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fform%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::form.checkbox"}, {"name": "3x admin::widgets.checkbox", "param_count": null, "params": [], "start": **********.854347, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/checkbox.blade.phpadmin::widgets.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 3, "name_original": "admin::widgets.checkbox"}, {"name": "1x admin::grid.column-selector", "param_count": null, "params": [], "start": **********.862077, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/grid/column-selector.blade.phpadmin::grid.column-selector", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fgrid%2Fcolumn-selector.blade.php&line=1", "ajax": false, "filename": "column-selector.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::grid.column-selector"}, {"name": "1x admin::filter.container", "param_count": null, "params": [], "start": **********.867289, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/container.blade.phpadmin::filter.container", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fcontainer.blade.php&line=1", "ajax": false, "filename": "container.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::filter.container"}, {"name": "14x admin::filter.where", "param_count": null, "params": [], "start": **********.86945, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/where.blade.phpadmin::filter.where", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fwhere.blade.php&line=1", "ajax": false, "filename": "where.blade.php", "line": "?"}, "render_count": 14, "name_original": "admin::filter.where"}, {"name": "6x admin::filter.text", "param_count": null, "params": [], "start": **********.870376, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/text.blade.phpadmin::filter.text", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Ftext.blade.php&line=1", "ajax": false, "filename": "text.blade.php", "line": "?"}, "render_count": 6, "name_original": "admin::filter.text"}, {"name": "8x admin::filter.select", "param_count": null, "params": [], "start": **********.871933, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/filter/select.blade.phpadmin::filter.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Ffilter%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 8, "name_original": "admin::filter.select"}, {"name": "1x admin::widgets.tab", "param_count": null, "params": [], "start": **********.896714, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/widgets/tab.blade.phpadmin::widgets.tab", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fwidgets%2Ftab.blade.php&line=1", "ajax": false, "filename": "tab.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::widgets.tab"}, {"name": "1x admin::layouts.content", "param_count": null, "params": [], "start": **********.898063, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/layouts/content.blade.phpadmin::layouts.content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Flayouts%2Fcontent.blade.php&line=1", "ajax": false, "filename": "content.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::layouts.content"}, {"name": "1x admin::partials.breadcrumb", "param_count": null, "params": [], "start": **********.899051, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/breadcrumb.blade.phpadmin::partials.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.breadcrumb"}, {"name": "1x admin::partials.alerts", "param_count": null, "params": [], "start": **********.90052, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/alerts.blade.phpadmin::partials.alerts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Falerts.blade.php&line=1", "ajax": false, "filename": "alerts.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.alerts"}, {"name": "1x admin::partials.exception", "param_count": null, "params": [], "start": **********.9018, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/exception.blade.phpadmin::partials.exception", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fexception.blade.php&line=1", "ajax": false, "filename": "exception.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.exception"}, {"name": "1x admin::partials.toastr", "param_count": null, "params": [], "start": **********.902926, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/toastr.blade.phpadmin::partials.toastr", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Ftoastr.blade.php&line=1", "ajax": false, "filename": "toastr.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.toastr"}, {"name": "1x admin::layouts.page", "param_count": null, "params": [], "start": **********.904389, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/layouts/page.blade.phpadmin::layouts.page", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Flayouts%2Fpage.blade.php&line=1", "ajax": false, "filename": "page.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::layouts.page"}, {"name": "1x admin::layouts.container", "param_count": null, "params": [], "start": **********.905774, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\resources\\views/vendor/admin/layouts/container.blade.phpadmin::layouts.container", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fresources%2Fviews%2Fvendor%2Fadmin%2Flayouts%2Fcontainer.blade.php&line=1", "ajax": false, "filename": "container.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::layouts.container"}, {"name": "1x admin::partials.sidebar", "param_count": null, "params": [], "start": **********.906755, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/sidebar.blade.phpadmin::partials.sidebar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.sidebar"}, {"name": "65x admin::partials.menu", "param_count": null, "params": [], "start": **********.102948, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/menu.blade.phpadmin::partials.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 65, "name_original": "admin::partials.menu"}, {"name": "1x admin::partials.navbar", "param_count": null, "params": [], "start": **********.155434, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/navbar.blade.phpadmin::partials.navbar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fnavbar.blade.php&line=1", "ajax": false, "filename": "navbar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.navbar"}, {"name": "1x announcements.index", "param_count": null, "params": [], "start": **********.156512, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\resources\\views/announcements/index.blade.phpannouncements.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fresources%2Fviews%2Fannouncements%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "announcements.index"}, {"name": "1x admin::partials.navbar-user-panel", "param_count": null, "params": [], "start": **********.157809, "type": "blade", "hash": "bladeD:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src/../resources/views/partials/navbar-user-panel.blade.phpadmin::partials.navbar-user-panel", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fresources%2Fviews%2Fpartials%2Fnavbar-user-panel.blade.php&line=1", "ajax": false, "filename": "navbar-user-panel.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::partials.navbar-user-panel"}]}, "queries": {"count": 50, "nb_statements": 49, "nb_visible_statements": 50, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 2.4218200000000003, "accumulated_duration_str": "2.42s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Admin.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", "line": 145}, {"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 36}], "start": **********.153037, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "wind_rich", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `wr_admin_users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Admin.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", "line": 145}, {"index": 18, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.161812, "duration": 0.22579, "duration_str": "226ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wind_rich", "explain": null, "start_percent": 0, "width_percent": 9.323}, {"sql": "insert into `wr_operation_logs` (`admin_uid`, `user_name`, `path`, `method`, `ip`, `input`, `updated_at`, `created_at`) values (1, 'ZtFzSuperAdminPro', 'ztfz/sale', 'GET', '127.0.0.1', '[]', '2025-08-01 13:56:54', '2025-08-01 13:56:54')", "type": "query", "params": [], "bindings": [1, "ZtFzSuperAdminPro", "ztfz/sale", "GET", "127.0.0.1", "[]", "2025-08-01 13:56:54", "2025-08-01 13:56:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 51}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.3934238, "duration": 0.04887, "duration_str": "48.87ms", "memory": 0, "memory_str": null, "filename": "OpRecord.php:51", "source": {"index": 15, "namespace": null, "name": "app/Http/Middleware/OpRecord.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Http\\Middleware\\OpRecord.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FHttp%2FMiddleware%2FOpRecord.php&line=51", "ajax": false, "filename": "OpRecord.php", "line": "51"}, "connection": "wind_rich", "explain": null, "start_percent": 9.323, "width_percent": 2.018}, {"sql": "select * from `wr_announcements` where `wr_announcements`.`deleted_at` is null order by `id` desc limit 7", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Announcement.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\Announcement.php", "line": 24}, {"index": 15, "namespace": null, "name": "app/Admin/bootstrap.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\bootstrap.php", "line": 62}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Admin.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Admin.php", "line": 164}, {"index": 17, "namespace": null, "name": "app/Admin/bootstrap.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\bootstrap.php", "line": 80}, {"index": 19, "namespace": "middleware", "name": "admin.bootstrap", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\Bootstrap.php", "line": 14}], "start": **********.4497, "duration": 0.04457, "duration_str": "44.57ms", "memory": 0, "memory_str": null, "filename": "Announcement.php:24", "source": {"index": 14, "namespace": null, "name": "app/Models/Announcement.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\Announcement.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAnnouncement.php&line=24", "ajax": false, "filename": "Announcement.php", "line": "24"}, "connection": "wind_rich", "explain": null, "start_percent": 11.341, "width_percent": 1.84}, {"sql": "select `wr_admin_roles`.*, `wr_admin_role_users`.`user_id` as `pivot_user_id`, `wr_admin_role_users`.`role_id` as `pivot_role_id`, `wr_admin_role_users`.`created_at` as `pivot_created_at`, `wr_admin_role_users`.`updated_at` as `pivot_updated_at` from `wr_admin_roles` inner join `wr_admin_role_users` on `wr_admin_roles`.`id` = `wr_admin_role_users`.`role_id` where `wr_admin_role_users`.`user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/HasPermissions.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\HasPermissions.php", "line": 88}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/HasPermissions.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\HasPermissions.php", "line": 76}, {"index": 21, "namespace": "middleware", "name": "admin.permission", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\Permission.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 23, "namespace": "middleware", "name": "admin.bootstrap", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Http\\Middleware\\Bootstrap.php", "line": 19}], "start": **********.609608, "duration": 0.045200000000000004, "duration_str": "45.2ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:88", "source": {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/HasPermissions.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\HasPermissions.php", "line": 88}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FHasPermissions.php&line=88", "ajax": false, "filename": "HasPermissions.php", "line": "88"}, "connection": "wind_rich", "explain": null, "start_percent": 13.181, "width_percent": 1.866}, {"sql": "select * from `wr_ww_users` where `wr_ww_users`.`deleted_at` is not null limit 10", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Admin/Traits/RecycleBinTrait.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Traits\\RecycleBinTrait.php", "line": 38}, {"index": 15, "namespace": null, "name": "app/Admin/Traits/RecycleBinTrait.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Traits\\RecycleBinTrait.php", "line": 25}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 232}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasTools.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasTools.php", "line": 53}, {"index": 18, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 235}], "start": **********.691713, "duration": 0.046439999999999995, "duration_str": "46.44ms", "memory": 0, "memory_str": null, "filename": "RecycleBinTrait.php:38", "source": {"index": 14, "namespace": null, "name": "app/Admin/Traits/RecycleBinTrait.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Traits\\RecycleBinTrait.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FTraits%2FRecycleBinTrait.php&line=38", "ajax": false, "filename": "RecycleBinTrait.php", "line": "38"}, "connection": "wind_rich", "explain": null, "start_percent": 15.048, "width_percent": 1.918}, {"sql": "select count(*) as aggregate from `wr_ww_users` where `wr_ww_users`.`deleted_at` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.743695, "duration": 0.04714, "duration_str": "47.14ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 16.965, "width_percent": 1.946}, {"sql": "select * from `wr_ww_users` where `wr_ww_users`.`deleted_at` is not null limit 20 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.7936828, "duration": 0.04448, "duration_str": "44.48ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 18.912, "width_percent": 1.837}, {"sql": "select * from `wr_ww_corp_info` where `wr_ww_corp_info`.`id` in (3) and `wr_ww_corp_info`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.8422809, "duration": 0.04517, "duration_str": "45.17ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 20.748, "width_percent": 1.865}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` in (1, 4, 5) and `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.8901792, "duration": 0.04489, "duration_str": "44.89ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 22.614, "width_percent": 1.854}, {"sql": "select `wr_ww_users_groups`.*, `wr_ww_users_groups_rel`.`ww_user_id` as `pivot_ww_user_id`, `wr_ww_users_groups_rel`.`ww_group_id` as `pivot_ww_group_id`, `wr_ww_users_groups_rel`.`created_at` as `pivot_created_at`, `wr_ww_users_groups_rel`.`updated_at` as `pivot_updated_at` from `wr_ww_users_groups` inner join `wr_ww_users_groups_rel` on `wr_ww_users_groups`.`id` = `wr_ww_users_groups_rel`.`ww_group_id` where `wr_ww_users_groups_rel`.`ww_user_id` in (1, 2, 3, 4, 7, 8, 9, 10, 11, 12) and `wr_ww_users_groups`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.9384391, "duration": 0.045450000000000004, "duration_str": "45.45ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 24.467, "width_percent": 1.877}, {"sql": "select * from `wr_ww_user_qrcodes` where `is_used` = 0 and `wr_ww_user_qrcodes`.`ww_user_id` in (1, 2, 3, 4, 7, 8, 9, 10, 11, 12)", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.988516, "duration": 0.06892, "duration_str": "68.92ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 26.344, "width_percent": 2.846}, {"sql": "select * from `wr_admin_sub_user_auth_corps` where `wr_admin_sub_user_auth_corps`.`deleted_at` is null order by `id` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 461}, {"index": 18, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.09602, "duration": 0.04455, "duration_str": "44.55ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:117", "source": {"index": 14, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=117", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "117"}, "connection": "wind_rich", "explain": null, "start_percent": 29.19, "width_percent": 1.84}, {"sql": "select `id`, `corp_name` from `wr_ww_corp_info` where `wr_ww_corp_info`.`id` in (1, 3, 5, 6) and `wr_ww_corp_info`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, {"index": 20, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 461}, {"index": 23, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}], "start": **********.142984, "duration": 0.04707, "duration_str": "47.07ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:117", "source": {"index": 19, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=117", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "117"}, "connection": "wind_rich", "explain": null, "start_percent": 31.029, "width_percent": 1.944}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 461}, {"index": 27, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}], "start": **********.193377, "duration": 0.04442, "duration_str": "44.42ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 32.973, "width_percent": 1.834}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 461}, {"index": 27, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}], "start": **********.239737, "duration": 0.0444, "duration_str": "44.4ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 34.807, "width_percent": 1.833}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 9 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 461}, {"index": 27, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}], "start": **********.2863998, "duration": 0.04482, "duration_str": "44.82ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 36.64, "width_percent": 1.851}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 461}, {"index": 27, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}], "start": **********.3332748, "duration": 0.04924, "duration_str": "49.24ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 38.491, "width_percent": 2.033}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 461}, {"index": 27, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}], "start": **********.3846822, "duration": 0.04714, "duration_str": "47.14ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 40.524, "width_percent": 1.946}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 461}, {"index": 27, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 30, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}], "start": **********.434778, "duration": 0.0445, "duration_str": "44.5ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 42.471, "width_percent": 1.837}, {"sql": "select `title`, `id` from `wr_ww_users_groups` where `wr_ww_users_groups`.`deleted_at` is null order by `id` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 510}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 1008}], "start": **********.483922, "duration": 0.04462, "duration_str": "44.62ms", "memory": 0, "memory_str": null, "filename": "WwUserController.php:510", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 510}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwUserController.php&line=510", "ajax": false, "filename": "WwUserController.php", "line": "510"}, "connection": "wind_rich", "explain": null, "start_percent": 44.308, "width_percent": 1.842}, {"sql": "select `username`, `id` from `wr_admin_users` where `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 527}, {"index": 16, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 539}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Concerns/HasFilter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Concerns\\HasFilter.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 487}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid.php", "line": 1008}], "start": **********.5311942, "duration": 0.0475, "duration_str": "47.5ms", "memory": 0, "memory_str": null, "filename": "WwUserController.php:527", "source": {"index": 13, "namespace": null, "name": "app/Admin/Controllers/WwUserController.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Controllers\\WwUserController.php", "line": 527}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwUserController.php&line=527", "ajax": false, "filename": "WwUserController.php", "line": "527"}, "connection": "wind_rich", "explain": null, "start_percent": 46.15, "width_percent": 1.961}, {"sql": "select count(*) as aggregate from `wr_ww_users` where `type` = 1 and `wr_ww_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.581219, "duration": 0.04438, "duration_str": "44.38ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 48.112, "width_percent": 1.833}, {"sql": "select * from `wr_ww_users` where `type` = 1 and `wr_ww_users`.`deleted_at` is null order by `id` desc limit 20 offset 0", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.62762, "duration": 0.045200000000000004, "duration_str": "45.2ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 49.944, "width_percent": 1.866}, {"sql": "select * from `wr_ww_corp_info` where `wr_ww_corp_info`.`id` in (1, 3) and `wr_ww_corp_info`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.675019, "duration": 0.04448, "duration_str": "44.48ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 51.811, "width_percent": 1.837}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` in (1, 4) and `wr_admin_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.722219, "duration": 0.045219999999999996, "duration_str": "45.22ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 53.647, "width_percent": 1.867}, {"sql": "select `wr_ww_users_groups`.*, `wr_ww_users_groups_rel`.`ww_user_id` as `pivot_ww_user_id`, `wr_ww_users_groups_rel`.`ww_group_id` as `pivot_ww_group_id`, `wr_ww_users_groups_rel`.`created_at` as `pivot_created_at`, `wr_ww_users_groups_rel`.`updated_at` as `pivot_updated_at` from `wr_ww_users_groups` inner join `wr_ww_users_groups_rel` on `wr_ww_users_groups`.`id` = `wr_ww_users_groups_rel`.`ww_group_id` where `wr_ww_users_groups_rel`.`ww_user_id` in (5, 6, 13) and `wr_ww_users_groups`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.771308, "duration": 0.045259999999999995, "duration_str": "45.26ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 55.514, "width_percent": 1.869}, {"sql": "select * from `wr_ww_user_qrcodes` where `is_used` = 0 and `wr_ww_user_qrcodes`.`ww_user_id` in (5, 6, 13)", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, {"index": 24, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Repositories/EloquentRepository.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Repositories\\EloquentRepository.php", "line": 169}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 453}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 386}, {"index": 27, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Filter.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Filter.php", "line": 642}], "start": **********.8197181, "duration": 0.04476, "duration_str": "44.76ms", "memory": 0, "memory_str": null, "filename": "Model.php:684", "source": {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Grid/Model.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Grid\\Model.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FGrid%2FModel.php&line=684", "ajax": false, "filename": "Model.php", "line": "684"}, "connection": "wind_rich", "explain": null, "start_percent": 57.383, "width_percent": 1.848}, {"sql": "select * from `wr_admin_sub_user_auth_corps` where `wr_admin_sub_user_auth_corps`.`deleted_at` is null order by `id` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.891462, "duration": 0.04442, "duration_str": "44.42ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:117", "source": {"index": 14, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=117", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "117"}, "connection": "wind_rich", "explain": null, "start_percent": 59.231, "width_percent": 1.834}, {"sql": "select `id`, `corp_name` from `wr_ww_corp_info` where `wr_ww_corp_info`.`id` in (1, 3, 5, 6) and `wr_ww_corp_info`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, {"index": 20, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.939514, "duration": 0.04424, "duration_str": "44.24ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:117", "source": {"index": 19, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=117", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "117"}, "connection": "wind_rich", "explain": null, "start_percent": 61.066, "width_percent": 1.827}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.987406, "duration": 0.04431, "duration_str": "44.31ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 62.892, "width_percent": 1.83}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.034019, "duration": 0.04482, "duration_str": "44.82ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 64.722, "width_percent": 1.851}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 9 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.080847, "duration": 0.04424, "duration_str": "44.24ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 66.573, "width_percent": 1.827}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.1271, "duration": 0.04421, "duration_str": "44.21ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 68.399, "width_percent": 1.825}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.1734061, "duration": 0.04436, "duration_str": "44.36ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 70.225, "width_percent": 1.832}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/checkLicenseTimeForm.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\checkLicenseTimeForm.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.220093, "duration": 0.04451, "duration_str": "44.51ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 72.057, "width_percent": 1.838}, {"sql": "select * from `wr_admin_sub_user_auth_corps` where `wr_admin_sub_user_auth_corps`.`deleted_at` is null order by `id` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, {"index": 15, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.281508, "duration": 0.04439, "duration_str": "44.39ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:117", "source": {"index": 14, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=117", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "117"}, "connection": "wind_rich", "explain": null, "start_percent": 73.894, "width_percent": 1.833}, {"sql": "select `id`, `corp_name` from `wr_ww_corp_info` where `wr_ww_corp_info`.`id` in (1, 3, 5, 6) and `wr_ww_corp_info`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, {"index": 20, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 23, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.32867, "duration": 0.04393, "duration_str": "43.93ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:117", "source": {"index": 19, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=117", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "117"}, "connection": "wind_rich", "explain": null, "start_percent": 75.727, "width_percent": 1.814}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.374938, "duration": 0.044719999999999996, "duration_str": "44.72ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 77.541, "width_percent": 1.847}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.4217532, "duration": 0.04471, "duration_str": "44.71ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 79.388, "width_percent": 1.846}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 9 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.469578, "duration": 0.05178, "duration_str": "51.78ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 81.234, "width_percent": 2.138}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 4 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.523647, "duration": 0.04541, "duration_str": "45.41ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 83.372, "width_percent": 1.875}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.571142, "duration": 0.0442, "duration_str": "44.2ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 85.247, "width_percent": 1.825}, {"sql": "select * from `wr_admin_users` where `wr_admin_users`.`id` = 1 and `wr_admin_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, {"index": 23, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 128}, {"index": 24, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 208}, {"index": 25, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 26, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}], "start": **********.6176162, "duration": 0.04455, "duration_str": "44.55ms", "memory": 0, "memory_str": null, "filename": "AdminSubUserAuthCorp.php:121", "source": {"index": 20, "namespace": null, "name": "app/Models/AdminSubUserAuthCorp.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\AdminSubUserAuthCorp.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=121", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "121"}, "connection": "wind_rich", "explain": null, "start_percent": 87.072, "width_percent": 1.84}, {"sql": "select `id` from `wr_ww_users_groups` where `admin_uid` = 1 and `wr_ww_users_groups`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/WwUsersGroup.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\WwUsersGroup.php", "line": 155}, {"index": 14, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 234}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.678614, "duration": 0.044090000000000004, "duration_str": "44.09ms", "memory": 0, "memory_str": null, "filename": "WwUsersGroup.php:155", "source": {"index": 13, "namespace": null, "name": "app/Models/WwUsersGroup.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\WwUsersGroup.php", "line": 155}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwUsersGroup.php&line=155", "ajax": false, "filename": "WwUsersGroup.php", "line": "155"}, "connection": "wind_rich", "explain": null, "start_percent": 88.912, "width_percent": 1.821}, {"sql": "select `ww_user_group_ids` from `wr_admin_sub_users` where `admin_uid` = 1 and `wr_admin_sub_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/WwUsersGroup.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\WwUsersGroup.php", "line": 156}, {"index": 17, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 234}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.726425, "duration": 0.04441, "duration_str": "44.41ms", "memory": 0, "memory_str": null, "filename": "WwUsersGroup.php:156", "source": {"index": 16, "namespace": null, "name": "app/Models/WwUsersGroup.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\WwUsersGroup.php", "line": 156}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwUsersGroup.php&line=156", "ajax": false, "filename": "WwUsersGroup.php", "line": "156"}, "connection": "wind_rich", "explain": null, "start_percent": 90.732, "width_percent": 1.834}, {"sql": "select `title`, `id` from `wr_ww_users_groups` where `id` in (1, 2) and `wr_ww_users_groups`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/WwUsersGroup.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\WwUsersGroup.php", "line": 161}, {"index": 14, "namespace": null, "name": "app/Admin/Forms/WwUser/ImportWwUserFromUserId.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Admin\\Forms\\WwUser\\ImportWwUserFromUserId.php", "line": 234}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 832}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Widgets/Form.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Widgets\\Form.php", "line": 861}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Support/Helper.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Support\\Helper.php", "line": 100}], "start": **********.773158, "duration": 0.04424, "duration_str": "44.24ms", "memory": 0, "memory_str": null, "filename": "WwUsersGroup.php:161", "source": {"index": 13, "namespace": null, "name": "app/Models/WwUsersGroup.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\app\\Models\\WwUsersGroup.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwUsersGroup.php&line=161", "ajax": false, "filename": "WwUsersGroup.php", "line": "161"}, "connection": "wind_rich", "explain": null, "start_percent": 92.566, "width_percent": 1.827}, {"sql": "select * from `wr_admin_menu` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, {"index": 15, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 115}, {"index": 16, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/MenuCache.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\MenuCache.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 99}], "start": **********.908364, "duration": 0.04478, "duration_str": "44.78ms", "memory": 0, "memory_str": null, "filename": "ModelTree.php:123", "source": {"index": 14, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FModelTree.php&line=123", "ajax": false, "filename": "ModelTree.php", "line": "123"}, "connection": "wind_rich", "explain": null, "start_percent": 94.393, "width_percent": 1.849}, {"sql": "select `wr_admin_permissions`.*, `wr_admin_permission_menu`.`menu_id` as `pivot_menu_id`, `wr_admin_permission_menu`.`permission_id` as `pivot_permission_id`, `wr_admin_permission_menu`.`created_at` as `pivot_created_at`, `wr_admin_permission_menu`.`updated_at` as `pivot_updated_at` from `wr_admin_permissions` inner join `wr_admin_permission_menu` on `wr_admin_permissions`.`id` = `wr_admin_permission_menu`.`permission_id` where `wr_admin_permission_menu`.`menu_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 115}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 98}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/MenuCache.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\MenuCache.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 99}], "start": **********.959173, "duration": 0.04532, "duration_str": "45.32ms", "memory": 0, "memory_str": null, "filename": "ModelTree.php:123", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FModelTree.php&line=123", "ajax": false, "filename": "ModelTree.php", "line": "123"}, "connection": "wind_rich", "explain": null, "start_percent": 96.242, "width_percent": 1.871}, {"sql": "select `wr_admin_roles`.*, `wr_admin_role_menu`.`menu_id` as `pivot_menu_id`, `wr_admin_role_menu`.`role_id` as `pivot_role_id`, `wr_admin_role_menu`.`created_at` as `pivot_created_at`, `wr_admin_role_menu`.`updated_at` as `pivot_updated_at` from `wr_admin_roles` inner join `wr_admin_role_menu` on `wr_admin_roles`.`id` = `wr_admin_role_menu`.`role_id` where `wr_admin_role_menu`.`menu_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, {"index": 19, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 115}, {"index": 20, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 98}, {"index": 21, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/MenuCache.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\MenuCache.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Models/Menu.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Models\\Menu.php", "line": 99}], "start": **********.010912, "duration": 0.045700000000000005, "duration_str": "45.7ms", "memory": 0, "memory_str": null, "filename": "ModelTree.php:123", "source": {"index": 18, "namespace": null, "name": "vendor/dcat/laravel-admin/src/Traits/ModelTree.php", "file": "D:\\Documents\\PHP\\NewProject\\wind_rich\\vendor\\dcat\\laravel-admin\\src\\Traits\\ModelTree.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FTraits%2FModelTree.php&line=123", "ajax": false, "filename": "ModelTree.php", "line": "123"}, "connection": "wind_rich", "explain": null, "start_percent": 98.113, "width_percent": 1.887}]}, "models": {"data": {"App\\Models\\WwUserQrcode": {"value": 160, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwUserQrcode.php&line=1", "ajax": false, "filename": "WwUserQrcode.php", "line": "?"}}, "Dcat\\Admin\\Models\\Role": {"value": 156, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Dcat\\Admin\\Models\\Menu": {"value": 61, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FMenu.php&line=1", "ajax": false, "filename": "Menu.php", "line": "?"}}, "Dcat\\Admin\\Models\\Permission": {"value": 56, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\WwUser": {"value": 23, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwUser.php&line=1", "ajax": false, "filename": "WwUser.php", "line": "?"}}, "App\\Models\\AdminUser": {"value": 23, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminUser.php&line=1", "ajax": false, "filename": "AdminUser.php", "line": "?"}}, "App\\Models\\AdminSubUserAuthCorp": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAdminSubUserAuthCorp.php&line=1", "ajax": false, "filename": "AdminSubUserAuthCorp.php", "line": "?"}}, "App\\Models\\WwCorpInfo": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwCorpInfo.php&line=1", "ajax": false, "filename": "WwCorpInfo.php", "line": "?"}}, "App\\Models\\WwUsersGroup": {"value": 14, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FWwUsersGroup.php&line=1", "ajax": false, "filename": "WwUsersGroup.php", "line": "?"}}, "Dcat\\Admin\\Models\\Administrator": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fvendor%2Fdcat%2Flaravel-admin%2Fsrc%2FModels%2FAdministrator.php&line=1", "ajax": false, "filename": "Administrator.php", "line": "?"}}, "App\\Models\\Announcement": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FModels%2FAnnouncement.php&line=1", "ajax": false, "filename": "Announcement.php", "line": "?"}}}, "count": 528, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://sen.test/ztfz/sale", "action_name": "dcat.admin.sale.index", "controller_action": "App\\Admin\\Controllers\\WwUserController@index", "uri": "GET ztfz/sale", "controller": "App\\Admin\\Controllers\\WwUserController@index<a href=\"phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwUserController.php&line=72\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Admin\\Controllers", "prefix": "/ztfz", "file": "<a href=\"phpstorm://open?file=D%3A%2FDocuments%2FPHP%2FNewProject%2Fwind_rich%2Fapp%2FAdmin%2FControllers%2FWwUserController.php&line=72\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Admin/Controllers/WwUserController.php:72-108</a>", "middleware": "admin.app:admin, web, admin", "duration": "3.84s", "peak_memory": "4MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1321316788 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1321316788\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-949475742 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-949475742\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1097491433 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IllvVXovRndObTFWWDNjcCtKYjhVb3c9PSIsInZhbHVlIjoidUhKcFhXcnp6M1U5ZXR5L251M20zV3BSSkR4K3QvaENacnhCTll0Vjh1dVJ2L3pGL0grY3JMQUxBaGlWZUpWb0VDZ3hUakwyelhXU3pSUmRRYWhKSWVPbUFjSjB1VERBYmhQTXNRTXdzRVJIdDF4WDJtTWRndTBiK3ZCMUM1bjciLCJtYWMiOiIwM2FiNzBkN2JhMjY0YjExYjg1NzA1ZDNhZGZlMTVjM2Y5NDJmMGU4ZWJmNzAzMmU4OWU5NDQ0MWFmNzE4ZGU1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IklqbDV4MVVtcHdPbE5xUDFiYm9OVHc9PSIsInZhbHVlIjoiVnlZOHVTaGJqTmQyeHFPQWxqYVhRdTMzUktSMWIxQzdvb2Nta3AwU3BhRE9mSHlOT1NCbWFXZk5sK1kzSTZrSW9VcnoydVo4ZnlzUFlqN3NhbnRrMmRiMmJsZE1nMncyRUFwTDRtaU5nUStnOVRNNFpSV0VsNDc3R0dtc0xpd0MiLCJtYWMiOiI4MTM1OWYwNDMxN2U3NTVhM2Y4MzQzZTFhY2UzNzY3ZWVjZWJhZGFiMjBkYTA4NTE5YTZjMGUxYjI1MDVlMzA5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">zh-CN,zh;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">sen.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1097491433\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1905371375 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0esJyfQEGYXbNzItiQn8t75dzSvXZmiKqhcwIHBs</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Sc1mCMq4OSwvqqSk8mbaIttmOBzjSrhvaXfSjoYm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1905371375\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1409532996 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Aug 2025 05:56:56 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1409532996\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1311421389 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>prev</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://sen.test/ztfz/sale</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">admin.prev.url</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0esJyfQEGYXbNzItiQn8t75dzSvXZmiKqhcwIHBs</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://sen.test/ztfz/sale</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1311421389\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://sen.test/ztfz/sale", "action_name": "dcat.admin.sale.index", "controller_action": "App\\Admin\\Controllers\\WwUserController@index"}, "badge": null}}