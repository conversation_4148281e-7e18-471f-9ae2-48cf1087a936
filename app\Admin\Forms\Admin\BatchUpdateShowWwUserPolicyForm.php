<?php

namespace App\Admin\Forms\Admin;

use App\Models\AdminUser;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class BatchUpdateShowWwUserPolicyForm extends Form implements LazyRenderable
{
    use LazyWidget;
    // 处理请求
    public function handle(array $input)
    {
        // id转化为数组
        $id = explode(',', $input['id'] ?? null);
        if (!$id) {
            return $this->response()->alert()->error('提示')->detail('参数错误。');
        }

        $adminUsers = AdminUser::query()->find($id);
        if ($adminUsers->isEmpty()) {
            return $this->response()->alert()->error('提示')->detail('用户不存在。');
        }

        foreach ($adminUsers as $key => $adminUser) {
            $adminUser->show_ww_user_policy = $input['show_ww_user_policy'];
            $adminUser->save();
        }
        return $this->response()->alert()->success('提示')->detail('配置成功')->refresh();
    }

    public function form()
    {
        $this->select('show_ww_user_policy', '去重策略')->options(AdminUser::SHOW_WW_USER_POLICY)->required();
        $this->hidden('id')->value($this->payload['ids'] ?? '');
    }

    // 返回表单数据，如不需要可以删除此方法
    public function default()
    {
        return [
            'password' => '',
            'password_confirm' => '',
        ];
    }
}
