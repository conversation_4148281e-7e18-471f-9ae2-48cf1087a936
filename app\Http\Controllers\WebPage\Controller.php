<?php

	namespace App\Http\Controllers\WebPage;

	use Illuminate\Support\Facades\Cache;
	use Illuminate\Support\Facades\Storage;

	class Controller
	{

		public static function isWechat(): bool
		{
			if (str_contains($_SERVER['HTTP_USER_AGENT'] ?? '', 'MicroMessenger')) {
				if (str_contains($_SERVER['HTTP_USER_AGENT'] ?? '', 'wechatdevtools')) {
					return false;
				}
				if (str_contains($_SERVER['HTTP_USER_AGENT'] ?? '', 'WindowsWechat')) {
					return false;
				}
				return true;
			} else {
				return false;
			}
		}

		public static function isMobile(): bool
		{

			$_SERVER['ALL_HTTP'] = $_SERVER['ALL_HTTP'] ?? '';

			$mobile_browser = '0';

            if(isset($_SERVER['HTTP_USER_AGENT'])){
                if (preg_match('/(up.browser|up.link|mmp|symbian|smartphone|midp|wap|phone|iphone|ipad|ipod|android|xoom)/i', strtolower($_SERVER['HTTP_USER_AGENT']))) {
                    $mobile_browser++;
                }
            }


			if ((isset($_SERVER['HTTP_ACCEPT'])) and (str_contains(strtolower($_SERVER['HTTP_ACCEPT']), 'application/vnd.wap.xhtml+xml'))) {
				$mobile_browser++;
			}

			if (isset($_SERVER['HTTP_X_WAP_PROFILE'])) {
				$mobile_browser++;
			}

			if (isset($_SERVER['HTTP_PROFILE'])) {
				$mobile_browser++;
			}

            $mobile_ua = '';
            if(isset($_SERVER['HTTP_USER_AGENT'])){
                $mobile_ua = strtolower(substr($_SERVER['HTTP_USER_AGENT'], 0, 4));
            }


			$mobile_agents = array(

				'w3c ', 'acs-', 'alav', 'alca', 'amoi', 'audi', 'avan', 'benq', 'bird', 'blac',

				'blaz', 'brew', 'cell', 'cldc', 'cmd-', 'dang', 'doco', 'eric', 'hipt', 'inno',

				'ipaq', 'java', 'jigs', 'kddi', 'keji', 'leno', 'lg-c', 'lg-d', 'lg-g', 'lge-',

				'maui', 'maxo', 'midp', 'mits', 'mmef', 'mobi', 'mot-', 'moto', 'mwbp', 'nec-',

				'newt', 'noki', 'oper', 'palm', 'pana', 'pant', 'phil', 'play', 'port', 'prox',

				'qwap', 'sage', 'sams', 'sany', 'sch-', 'sec-', 'send', 'seri', 'sgh-', 'shar',

				'sie-', 'siem', 'smal', 'smar', 'sony', 'sph-', 'symb', 't-mo', 'teli', 'tim-',

				'tosh', 'tsm-', 'upg1', 'upsi', 'vk-v', 'voda', 'wap-', 'wapa', 'wapi', 'wapp',

				'wapr', 'webc', 'winw', 'winw', 'xda', 'xda-'

			);

			if (in_array($mobile_ua, $mobile_agents)) {
				$mobile_browser++;
			}

			if (str_contains(strtolower($_SERVER['ALL_HTTP']), 'operamini')) {
				$mobile_browser++;
			}

			// Pre-final check to reset everything if the user is on Windows

			if (str_contains(strtolower($_SERVER['HTTP_USER_AGENT'] ?? ''), 'windows')) {
				$mobile_browser = 0;
			}

			// But WP7 is also Windows, with a slightly different characteristic

			if (str_contains(strtolower($_SERVER['HTTP_USER_AGENT'] ?? ''), 'windows phone')) {
				$mobile_browser++;
			}
			if ($mobile_browser > 0) {
				return true;
			} else {
				return false;
			}

		}

		public static function isDenyIp($ip): bool
		{
			// 从文件读取IP段
			$wind_ip_txt = Cache::store('redis')->get('wr_ip_txt');
			if (empty($wind_ip_txt)) {
				$content = Storage::get("IP/ip.txt");;
				$ipPatterns = explode(PHP_EOL, $content);
				Cache::store('redis')->set('wr_ip_txt', json_encode($ipPatterns));
			} else {
				$ipPatterns = json_decode($wind_ip_txt, true);
			}

			foreach ($ipPatterns as $pattern) {
				// 这里我们使用了简单的通配符匹配。对于更复杂的网络位匹配，请使用合适的网络计算。
				$pattern = str_replace('.', '\.', $pattern);
				$pattern = str_replace('*', '\d+', $pattern); // 替换*为\d+以匹配1个或多个数字
				if (preg_match('/^' . $pattern . '$/', $ip)) {
					return true;
				}
			}
			return false;
		}
	}
