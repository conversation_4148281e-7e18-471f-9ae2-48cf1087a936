<?php

namespace App\Jobs\TencentAd;

use App\Models\TencentAdAccountMonitorCount;
use App\Models\WwLink;
use App\Models\WwUserAddRecord;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CountAdAccountHourlyReportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $reports;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($reports)
    {
        $this->reports = $reports;
    }


    /**
     * Execute the job.
     *
     * @return mixed
     */
    public function handle()
    {

        $reports = $this->reports;

        $where = [
            'admin_uid' => $reports['admin_uid'],
            'account_id' => $reports['account_id'],
            'date' => $reports['date']
        ];
        if ($reports['valid_click_count'] > 0) { //计算转化率 计算公式是：目标转化量/点击次数*100%
            $reports['conversions_rate'] = round(($reports['conversions_count'] / $reports['valid_click_count']) * 100, 2);
        }
        $actualAddCount = 0;
        $actualOpenCount = 0;
        $wwLinkIds = WwLink::query()
            ->select('id', 'account_id')
            ->where('admin_uid', $reports['admin_uid'])
            ->where('account_id', $reports['account_id'])
            ->pluck('id')
            ->toArray();
        if ($wwLinkIds) {
            $actualAddCount = WwUserAddRecord::query()
                ->where("admin_uid", $reports['admin_uid'])
                ->whereIn('ww_link_id', $wwLinkIds)
                ->where('page_type', 'page')
                ->where("date",$reports['date'])
                ->count();
            $reports['actual_add_count'] = $actualAddCount; //实际进粉数

            $actualOpenCount = WwUserAddRecord::query()
                ->where("admin_uid", $reports['admin_uid'])
                ->whereIn('ww_link_id', $wwLinkIds)
                ->where('customer_start_chat', 1)
                ->where('page_type', 'page')
                ->where("date",$reports['date'])
                ->count();
            $reports['actual_open_count'] = $actualOpenCount; //实际开口数
        }
        //实际进粉成本
        $adCost = round($reports['ad_cost'] / 100, 2);
        if ($actualAddCount > 0) {
            $addCountCost = round($adCost / $actualAddCount,2);
            $addCountCost = floor($addCountCost * 100) / 100;
            $reports['add_count_cost'] = $addCountCost;
        }
        //实际开口成本
        if ($actualOpenCount > 0) {
            $openCountCost = round($adCost / $actualOpenCount,2);
            $openCountCost = floor($openCountCost * 100) / 100;
            $reports['open_count_cost'] = $openCountCost;
        }

        //重新计算转化成本

        if($reports['conversions_count'] == 0){
            $reports['conversions_cost'] = 0;
        }
        if($reports['conversions_count'] > 0){
            $reports['conversions_cost'] = $reports['ad_cost'] / $reports['conversions_count'];
        }
        $checkCount = TencentAdAccountMonitorCount::query()->where($where)->first();
        if (!$checkCount) {
            $reports['created_at'] = date('Y-m-d H:i:s');
            $reports['updated_at'] = date('Y-m-d H:i:s');

            TencentAdAccountMonitorCount::query()->insert($reports);
        } else {
            unset($reports['admin_uid'], $reports['account_id'], $reports['date']);
            TencentAdAccountMonitorCount::query()->where('id',$checkCount->id)->update($reports);
        }
        return true;
    }
}
