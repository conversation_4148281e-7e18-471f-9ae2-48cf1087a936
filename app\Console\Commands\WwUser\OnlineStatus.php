<?php

namespace App\Console\Commands\WwUser;

use App\Jobs\AdminActionLogJob;
use App\Jobs\WwUser\WwUserAutoOnLineJob;
use App\Models\AdminActionLog;
use App\Models\WwUser;
use App\Models\WwUsersOnlineLogs;
use Dcat\Admin\Admin;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class OnlineStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'WwUser:OnlineStatus';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        WwUser::query()
            ->whereIn("auto_status_config", [1,2])
            ->chunkById(200, function ($wwUsersData) {
                /** @var WwUser $wwUsersDatum */
                foreach ($wwUsersData as $wwUsersDatum) {
                    if ($wwUsersDatum->auto_status_config == 1) { //按时间段
                        $this->upTime($wwUsersDatum);
                        $this->downTime($wwUsersDatum);
                        continue;
                    }
                    if ($wwUsersDatum->auto_status_config == 2) { //按粉丝量
                        $this->upTime($wwUsersDatum);
                        $this->downAddSuc($wwUsersDatum);
                    }
                }
            });

        return Command::SUCCESS;
    }

    /**
     * 自动上线
     * @param WwUser $wwUsersDatum
     * @return void
     */
    public function upTime(WwUser $wwUsersDatum): void
    {
        //这里判断一下，如果当前销售配置了上线时间，且当前是下线状态的话，才执行以下代码
        if($wwUsersDatum->up_time && $wwUsersDatum->online_status == 0){
            WwUserAutoOnLineJob::dispatch($wwUsersDatum)->onQueue('ww_user_auto_online');//调用销售自动上线处理队列
        }
    }

    /**
     * 自动下线
     * @param WwUser $wwUsersDatum
     * @return void
     */
    public function downTime(WwUser $wwUsersDatum): void
    {

        //这里判断一下，如果当前销售配置了下线时间，且当前是上线状态的话，才执行以下代码
        if($wwUsersDatum->down_time && $wwUsersDatum->online_status == 1){
            $todayTime = strtotime(date("Y-m-d ", time()) . $wwUsersDatum->down_time);

            if (time() >= $todayTime && (time() - $todayTime) < 240) {
                $wwUsersDatum->online_status = 0;
                $wwUsersDatum->save();
                //添加操作日志
                AdminActionLogJob::dispatch(
                    'time_auto_offline',
                    $wwUsersDatum->id,
                    AdminActionLog::ACTION_TYPE['销售'],
                    '「' . $wwUsersDatum->name . '」ID：「' . $wwUsersDatum->id . '」，自动下线成功',
                    '',
                    $wwUsersDatum->admin_uid,
                )->onQueue('admin_action_log_job');

                //添加销售下线记录
                $onlineStatus = 0;//下线
                $source = '系统-定时自动下线';
                WwUsersOnlineLogs::addOnLineStatusLogs($wwUsersDatum,$onlineStatus,$source);
            }
        }
    }


    /**
     * 按加粉量下线
     * @param WwUser $wwUsersDatum
     * @return void
     */
    public function downAddSuc(WwUser $wwUsersDatum): void
    {
        if ($wwUsersDatum->today_add_count >= $wwUsersDatum->down_add_count) {

            //判断一下当前销售状态是上线状态的话，才执行以下代码
            if($wwUsersDatum->online_status == 1){
                $wwUsersDatum->online_status = 0;
                $wwUsersDatum->save();

                //添加销售下线记录
                $onlineStatus = 0;//下线
                $source = '达到加粉量-系统自动下线';
                WwUsersOnlineLogs::addOnLineStatusLogs($wwUsersDatum,$onlineStatus,$source);

                //添加操作日志
                AdminActionLogJob::dispatch(
                    'time_auto_offline',
                    $wwUsersDatum->id,
                    AdminActionLog::ACTION_TYPE['销售'],
                    '达到加粉量「' . $wwUsersDatum->name . '」，自动下线成功',
                   '',
                    Admin::user()->id,
                )->onQueue('admin_action_log_job');
            }
        }
    }
}
