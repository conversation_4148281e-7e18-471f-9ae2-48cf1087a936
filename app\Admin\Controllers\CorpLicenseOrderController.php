<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Grid\Corp\CancelCorpLicenseOrder;
use App\Admin\Repositories\CorpLicenseOrders;
use App\Models\AdminSubUserAuthCorp;
use App\Models\AdminUser;
use App\Models\CorpLicenseActiveCode;
use App\Models\WwCorpInfo;
use App\Models\WwUser;
use App\Services\ProviderService;
use App\Services\Tools\UtilsService;
use App\Services\WxPayService;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Table;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Storage;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

/**
 * @property $ww_users_id
 * @property $external_contact_count
 * @property $is_prov_pay_success
 * @property $is_prov_pay_submit
 * @property $is_wx_pay
 * @property $prov_pay_jobid
 * @property $active_result
 * @property CorpLicenseActiveCode[] $activeCode
 */
class CorpLicenseOrderController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new CorpLicenseOrders(['adminInfo', 'corpInfo', 'activeCode']), function (Grid $grid) {
//            $grid->disableFilterButton();
//            $grid->disableActions();

            if(!AdminUser::isSystemOp()){
                $grid->disableViewButton();
                $grid->disableEditButton();
                $grid->disableDeleteButton();
                $grid->disableActions();
            }else{
                $grid->disableViewButton();
                $grid->disableEditButton();
                $grid->disableDeleteButton();

            }
            $grid->disableBatchActions();
//            $grid->disableCreateButton();
            $grid->disableCreateButton();

            $grid->tools(function (Grid\Tools $tools) {
                $tools->append(UtilsService::dialogForm('购买许可证',Request::url().'/create','create-CorpLicenseOrder'));
            });
            $grid->column('id')->sortable();
            $grid->model()->orderByDesc("id");
            if (!Admin::user()->isAdministrator() && !Admin::user()->isRole('wop')) {
                $grid->model()->where("admin_uid", Admin::user()->id);
            } else {
                $grid->column('admin_uid', '用户ID');
                $grid->column('adminInfo.username', '用户');
            }
            $grid->column('corp_id', '企微ID');
            $grid->column('corpInfo.corp_name', '企微');
            $grid->column('order_id')->copyable();
            $grid->column('days')->display(function ($days) {
                return $days . '天';
            });
            $grid->column('is_wx_pay')->using([
                -1 => '超时取消',
                0 => '否',
                1 => '已支付',
                2 => '管理员取消',
            ])->label([
                -1 => 'danger',
                0 => 'warning',
                1 => 'success',
                2 => 'default',
            ]);
            $grid->column('wx_pay_qrcode', '支付码')->image('', "80")->help('请于30分钟内完成支付，否则订单将超时取消。');
            $grid->column("active_result", '激活结果')->display(function () {
                if ($this->active_result) {
                    return '激活完成';
                }
                if (!$this->is_wx_pay) {
                    return '客户未支付';
                }
                if ($this->is_wx_pay == -1) {
                    return '超时未支付取消订单';
                }
                if ($this->is_wx_pay == 2) {
                    return '管理员取消';
                }
                if (!$this->is_prov_pay_submit) {
                    return '激活任务未提交';
                }
                if (!$this->is_prov_pay_success) {
                    return '等待企业微信支付';
                }
                return '等待企业微信激活';
            })->expand(function () {
                if ($this->active_result) {
                    $tableData = [];
                    $codeList = $this->activeCode;
                    foreach ($codeList as $item) {
                        $tableData[] = [
                            '激活时间' => $item->use_time,
                            '销售账号ID' => $item->resp_user_id,
                            '激活结果' => ($item->errcode == 0) ? "激活成功" : "未激活",
                        ];
                    }
                    $header = ['激活时间', '销售账号ID', '激活结果'];
                    return Table::make($header, $tableData);
                }
                $data = json_decode($this->prov_pay_jobid, true);
                if (!$data) {
                    $data = [];
                }
                $tableData = [];
                foreach ($data as $item) {
                    $tableData[$item['time']] = [
                        '状态' => ($item['status_string'] ?? "等待支付任务结果"),
                        '原因' => "企业微信" . ($item['pay_job_result']['errcode_string'] ?? "等待支付任务结果") . '-' . ($item['pay_job_result']['errcode'] ?? "0") . ",一般2分钟内完成，请耐心等待，如超过2分钟，请联系运营。"
                    ];
                }
                if (empty($tableData)) {
                    $status = '等待提交支付任务';
                    $reason = '系统正在等待提交支付任务，一般2分钟内完成，请耐心等待，如超过2分钟，请联系运营';
                    if ($this->is_wx_pay  == 0) {
                        $status = '客户未支付';
                        $reason = '请扫描二维码码完成支付，如已完成支付，但系统未更新，请联系运营';
                    } elseif ($this->is_wx_pay == -1) {
                        $status = '激活任务未提交';
                        $reason = '订单超时未支付，系统自动取消';
                    }elseif ($this->is_wx_pay == 2) {
                        $status = '激活任务未提交';
                        $reason = '管理员手动取消';
                    } elseif (!$this->is_prov_pay_submit) {
                        $status = '激活任务未提交';
                        $reason = '系统正在等待提交激活任务，一般2分钟内完成，请耐心等待，如超过2分钟，请联系运营';
                    } elseif (!$this->is_prov_pay_success) {
                        $status = '等待企业微信支付';
                        $reason = '等待企业微信侧确认支付结果，一般2分钟内完成，请耐心等待，如超过2分钟，请联系运营';
                    }


                    $tableData[date("Y-m-d H:i:s")] = [
                        '状态' => $status,
                        '原因' => $reason,
                    ];
                }
                ksort($tableData);
                return Table::make(['时间', '明细'], $tableData);
            });
            $grid->column('ww_users_id', '销售列表')->display(function () {
                return $this->external_contact_count . "个销售";
            })->expand(function () {
                return $this->ww_users_id;
            });
            $grid->column('created_at')->sortable();
            $grid->column('updated_at')->sortable();

            if(AdminUser::isSystemOp()){
                //未支付的订单显示取消订单按钮
                $grid->actions(function (Grid\Displayers\Actions $actions) {
                    $row = $actions->row;
                    if($row['is_wx_pay'] == 0){
                        $actions->prepend(new CancelCorpLicenseOrder());
                    }
                });
            }

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id')->width(2);
                $filter->expand();
                $filter->panel();
                $filter->equal('is_wx_pay','支付状态')->select([-1 => '超时取消', 0 => '未支付', 1 => '已支付'])->width(2);
                if(AdminUser::isSystemOp()){
                    $adminUsers = AdminUser::query()->pluck('username', 'id');
                    $filter->equal('admin_uid','用户')->select($adminUsers)->width(2);
                }
            });
        });
    }


    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {

        return Form::make(new CorpLicenseOrders(), function (Form $form) {


            $form->html(
                <<<HTML
<div class="alert alert-info alert-dismissable">
            <h4><i class="fa fa-info"></i>&nbsp; 提示</h4>
          请于30分钟内完成支付，否则订单将超时取消。
</div>
HTML
            );
            $form->display('id');

            $corpAuthRecord = AdminSubUserAuthCorp::getCorpAuthListBuyAdminUid(Admin::user()->id);
            $form->select('corp_id', '选择企微')
                ->options($corpAuthRecord)
                ->required();
            $form->textarea("ww_users_id", '销售账号ID')->placeholder("每行一个，回车换行，例如：\r\nuserId1\r\nuserId2")->required();
//            $form->text("tip", "提示信息")->prepend("")->readOnly()->disable()->default("销售账号ID格式说明")->help("一行一个，可以批量添加，账号ID获取方式【企微后台-通讯录-成员列表中的“账号列”】，如图<br/><img width='50%' src='https://wind.cdn.gotiger.cn/wwAsset/0/账户ID获取方式.png'>");
//            $form->number('months', '购买月份')->default(1)->help("购买的月数，每个月按照31天计算")->required();

            $form->hidden('admin_uid')->default(Admin::user()->id);
            $form->hidden('buyer_userid');
            $form->hidden('external_contact_count')->default(0);
            $form->hidden('days')->default(0);
            $form->saving(function (Form $form) {
//                dd(ProviderServer::cancelOrder('wpQwWRZQAAVdseOIzD4ad04LsItNrcaw','OI0000659105436805C2E570EA940N'));
                $corpInfo = WwCorpInfo::query()->find($form->input('corp_id'));
                if (!$corpInfo) {
                    return $form->response()->alert()->error('提示')->detail("选择的企微不存在，请重试");
                }
                //校验销售ID
                $realUserIds = [];
                $submitRealUserIds = explode(PHP_EOL, $form->input("ww_users_id"));
                foreach ($submitRealUserIds as $realUserId) {
                    $realUserIds[] = trim($realUserId);
                }
                //校验是否导入
                $wwUsers = WwUser::query()->where("corp_id", $form->input('corp_id'))->whereIn('user_id', $realUserIds)->get();
                if ($wwUsers->count() < count($realUserIds)) {
                    $dataWwUserIds = array_column($wwUsers->toArray(), 'user_id');
                    $exUserIds = array_diff($realUserIds, $dataWwUserIds);
                    return $form->validationErrorsResponse([
                        'ww_users_id' => "【" . implode(",", $exUserIds) . "】销售账号ID不存在，请在后台导入，或删除不存在的销售ID后重试"
                    ]);
                }
                //校验有效期
                $validUserIds = [];
                foreach ($wwUsers as $wwUser) {

                    //判断是不是已经存在激活的订单
                    $checkActiveCode = CorpLicenseActiveCode::query()
                        ->where('userid',$wwUser->user_id)
                        ->where('corp_id',$form->input('corp_id'))
                        ->where('errcode',0)
                        ->orderBy('id', 'desc')
                        ->first();
                    if($checkActiveCode){
                        //查询获取成员的激活详情接口
                        $activeInfoByApi = ProviderService::getActiveInfoByUser($wwUser->corpInfo->corp_id, $wwUser->user_id);
                        if (isset($activeInfoByApi['errcode']) && $activeInfoByApi['errcode'] == 0) {
                            if (isset($activeInfoByApi['active_info_list'])) {
                                foreach ($activeInfoByApi['active_info_list'] as $activeInfo) {
                                    //如果许可证过期时间>=20天
                                    if ($activeInfo['active_code'] == $checkActiveCode['active_code']) {
                                        $expireTime = $activeInfo['expire_time'] - time();
                                        $checkDays = $expireTime / 86400;
                                        if ($checkDays >= 20) {
                                            Log::info('购买企微许可证，查询获取成员的激活详情接口，许可证过期时间>=20天，销售ID：' . $wwUser->user_id);
                                            $validUserIds[] = $wwUser->user_id;
                                        }
                                    }
                                }
                            }
                        }
                    }

//
                    if ((strtotime($wwUser->license_c_ex_time) - time()) / 86400 >= 20) {
                        $validUserIds[] = $wwUser->user_id;
                    }
                }
                $validUserIds = array_filter($validUserIds, function ($value) {
                    return !empty($value);
                });
                if (!empty($validUserIds)) {
                    return $form->response()->alert()->error('提示')->detail("以下账号仍在有效期内，并且有效期时长大于20天，无法购买许可证，请剔除后重试。【" . implode(",", $validUserIds) . "】");
                }
                $form->input("ww_users_id", json_encode($realUserIds));
                $form->input('admin_uid', Admin::user()->id);
                $form->input('buyer_userid', 'XingXinZhong');
                $form->input('external_contact_count', count($realUserIds));
                $form->input("days", 31);
            });
            $form->saved(function (Form $form, $result) {
                // 判断是否是新增操作
                if ($form->isCreating()) {
                    // 自增ID
                    $newId = $result;
                    $orderInfo = \App\Models\CorpLicenseOrders::query()->find($newId);
//                    //关闭自动激活
                    ProviderService::setAutoActiveStatus($orderInfo->corpInfo->corp_id);
                    //获取数据，向企业微信下单
                    $resp = ProviderService::createNewOrder($orderInfo->corpInfo->corp_id, 'scryTiger', $orderInfo->external_contact_count, $orderInfo->days);
                    $orderInfo->create_order_resp = json_encode($resp);
                    if (isset($resp['order_id'])) {
                        $orderNoCount = Admin::user()->id;
                        $orderCountLen = strlen($orderNoCount);
                        for ($i = 0; $i < (4 - $orderCountLen); $i++) {
                            $orderNoCount = "0" . $orderNoCount;
                        }
                        $orderInfo->order_id = $resp['order_id'];
                        $orderInfo->wx_pay_order_no = "QWX-" . $orderNoCount . time() . rand(100, 999);
                        $orderInfo->save();
//                        //查询订单价格
                        $orderData = ProviderService::licenseGetOrder($orderInfo->order_id);
                        if (!isset($orderData['order']['price'])) {
                            ProviderService::cancelOrder($orderInfo->corpInfo->corp_id, $orderInfo->order_id);
                            $orderInfo->delete();
                            return $form->response()->alert()->error('提示')->detail('下单失败，请重试-2：' . ($orderData['errmsg'] ?? "错误原因展示"));
                        }
                        $orderInfo->ww_order_price = $orderData['order']['price'];
                    } else {
                        ProviderService::cancelOrder($orderInfo->corpInfo->corp_id, $orderInfo->order_id);
                        $orderInfo->delete();
                        if (isset($resp['errcode']) && $resp['errcode'] == '701032') {
                            return $form->response()->alert()->error('提示')->detail('下单失败，请重试-1：订单创建重复，请联系运营处理。');
                        }
                        return $form->response()->alert()->error('提示')->detail('下单失败，请重试-1：' . ($resp['errmsg'] ?? "错误原因展示"));
                    }
                    //整理数据，向用户提交支付
                    $payConfig = WxPayService::getConfig();
                    $app = WxPayService::getApp($payConfig);
                    $wxPayData = [
                        'appid' => $payConfig['app_id'],
                        'time_expire' => date('Y-m-d\TH:i:sP', time() + 600),
                        'mchid' => (string)$payConfig['mch_id'],
                        'out_trade_no' => $orderInfo->wx_pay_order_no,
                        'description' => '企业微信互通许可证',
                        'notify_url' => $payConfig['notify_url'], //回调地址
                        'amount' => [
                            'total' => $orderInfo->ww_order_price,//费用
                            'currency' => 'CNY',
                        ]
                    ];
//                    dd($wxPayData);
                    //调用扫码支付
                    try {
                        $response = $app->getClient()->postJson('v3/pay/transactions/native', $wxPayData);
                        if (!$response) {
                            ProviderService::cancelOrder($orderInfo->corpInfo->corp_id, $orderInfo->order_id);
                            $orderInfo->delete();
                            return $form->response()->alert()->error('下单失败，请重试-5：调起微信支付失败');
                        }
                    } catch (\Exception $exception) {
                        ProviderService::cancelOrder($orderInfo->corpInfo->corp_id, $orderInfo->order_id);
                        $orderInfo->delete();
                        return $form->response()->alert()->error('下单失败，请重试-4：调起微信支付失败:' . $exception->getMessage());
                    }
//                    dd($response->toArray(false));
                    $orderInfo->wx_pay_data = json_encode($wxPayData);
                    $orderInfo->save();
                    if (!isset($response['code_url'])) {
                        ProviderService::cancelOrder($orderInfo->corpInfo->corp_id, $orderInfo->order_id);
                        $orderInfo->delete();
                        return $form->response()->alert()->error('提示')->detail('下单失败，请重试-3：调起微信支付失败');
                    }
                    $qrcode_url = $response['code_url'];
                    $qrcodeTempPath = Storage::path('public/wxPayQrcode/');
                    $qrcodeFile = $orderInfo->order_id . '-' . time() . ".png";
                    ProviderService::createDir($qrcodeTempPath);
                    QrCode::format('png')
                        ->size(400)
                        //->backgroundColor(240, 187, 72, 25)
                        //->color(27, 43, 74)
                        ->encoding('UTF-8')
                        ->errorCorrection('H')
                        ->generate($qrcode_url, $qrcodeTempPath . $qrcodeFile);

                    $disk = Storage::disk('oss');
                    $disk->putFileAs('AI/wxPayQrcode/', $qrcodeTempPath . $qrcodeFile, $qrcodeFile);
                    Storage::delete('public/wxPayQrcode/' . $qrcodeFile);
                    $orderInfo->wx_pay_qrcode = env('CDN_URL') . '/AI/wxPayQrcode/' . $qrcodeFile;
                    $orderInfo->save();
                }
            });
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
