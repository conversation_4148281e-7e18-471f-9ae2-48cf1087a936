<?php

namespace App\Admin\Forms\WwLink;

use App\Jobs\AdminActionLogJob;
use App\Models\AdminActionLog;
use App\Models\AdminUser;
use App\Models\WwLink;
use App\Models\WwTpl;
use App\Services\Tools\LogService;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class WwLinkPageTplForm extends Form implements LazyRenderable
{
    use LazyWidget;

    // 处理请求
    public function handle(array $input)
    {
        LogService::inputLog('Tools','ww_link-链接列表 -配置投放页面', $input, Admin::user()->id, Admin::user()->username);
        // id转化为数组
        $id = explode(',', $input['id'] ?? null);
        if (!$id) {
            return $this->response()->error('参数错误');
        }

        $wwLik = WwLink::query()->find($id);
        if ($wwLik->isEmpty()) {
            return $this->response()->alert()->error('提示')->detail('请选择投放链接。');
        }

        foreach ($wwLik as $key => $value) {
            if (!AdminUser::isAdmin($value)) {
                return $this->response()->alert()->error('提示')->detail('异常操作，请刷新页面后重试。');
            }
            // if($value->tpl_id == -1){
            //     return $this->response()->error('ID：' . $value->id .'为外部链接，不支持批量修改');
            // }
            $value->tpl_id = $input['tpl_id'];
            $value->tpl_type = 1;
            $value->save();
            AdminActionLogJob::dispatch(
                'batch_set_ww_link_tpl',
                $value->id,
                AdminActionLog::ACTION_TYPE['链接'],
                '批量设置落地页，账户ID：「' . $value->account_id . '」，页面ID：' . $value->tpl_id ,
                getIp(),
                Admin::user()->id
            )->onQueue('admin_action_log_job');
        }
        return $this->response()->alert()->success('提示')->detail('配置成功')->refresh();
    }

    public function form()
    {
        $admin_uid = Admin::user()->id;
        //$tplList = WwTpl::query()->where('admin_uid', $admin_uid)->where('type',1)->pluck("name", "id");
        $tplList = WwTpl::getAdTplList();//这个方法会比上面的方法，多显示共享过来的页面
        $this->select('tpl_id', '投放页面')->options($tplList)->required();
        $this->hidden('id')->attribute('id', 'reset-password-id');
    }

    // 返回表单数据，如不需要可以删除此方法
    public function default()
    {
        return [
            'password' => '',
            'password_confirm' => '',
        ];
    }
}
