<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;


class AgainSetTagTask extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'again_set_tag_task';



    const STATUS = [
        0 => '等待执行',
        1 => '执行中',
        2 => '执行完成',
        3 => '执行失败',
    ];
    public function adminInfo(): BelongsTo
    {
        return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
    }

    public function corpInfo(): BelongsTo
    {
        return $this->BelongsTo(WwCorpInfo::class, 'corp_id', 'id');
    }

    public function groupInfo(): BelongsTo
    {
        return $this->BelongsTo(WwUsersGroup::class, 'ww_group_id', 'id');
    }


}
