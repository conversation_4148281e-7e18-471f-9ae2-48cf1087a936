<?php /** @noinspection PhpInconsistentReturnPointsInspection */

    namespace App\Admin\Controllers;


    use App\Admin\Actions\Grid\wwLink\BatchSetLinkUseDomain;
    use App\Admin\Actions\Grid\wwLink\BatchSetLinkWwUserGroup;
    use App\Admin\Actions\Grid\wwLink\BatchSetSwitch;
    use App\Admin\Actions\Grid\wwLink\BatchSetWwLinkPage;
    use App\Admin\Actions\Grid\wwLink\BatchSetWwLinkShieldPolicy;
    use App\Admin\Actions\Grid\wwLink\ConvRateBatchAction;
    use App\Admin\Actions\Grid\wwLink\FlushWwLinkTadsConvCount;
    use App\Admin\Actions\Grid\wwLink\WwLabelBatchAction;
    use App\Admin\Actions\Grid\wwLink\WwLinkBatchActions;
use App\Admin\Actions\Grid\wwLink\WwLinkBatchNeedShieldAction;
use App\Admin\Forms\WwLink\WwLinkConfigImportForm;
    use App\Admin\Renderable\WwLink\CopyLinkToAccount;
    use App\Admin\Renderable\WwLink\ViewRecord;
    use App\Admin\Repositories\WwLink;
    use App\Models\AdAction;
    use App\Models\AdminDomain;
    use App\Models\AdminSubUser;
    use App\Models\AdminUser;
    use App\Models\AdOppoAccount;
    use App\Models\AdVivoAccount;
    use App\Models\AdYoukuAccount;
    use App\Models\Area;
    use App\Models\ShieldPolicy;
    use App\Models\ShieldPolicyRule;
    use App\Models\TencentAdAccount;
    use App\Models\WwTpl;
    use App\Models\WwUsersGroup;
    use App\Services\Tools\UtilsService;
    use Dcat\Admin\Admin;
    use Dcat\Admin\Form;
    use Dcat\Admin\Grid;
    use Dcat\Admin\Http\Controllers\AdminController;
    use Dcat\Admin\Layout\Content;
    use Dcat\Admin\Show;
    use Dcat\Admin\Widgets\Alert;
    use Dcat\Admin\Widgets\Modal;
    use Dcat\Admin\Widgets\Tooltip;
    use App\Models\WwLink as WwLinkModel;
    use Illuminate\Support\Collection;
    use Illuminate\Support\Facades\Request as RequestFacade;

    /**
     * @property ShieldPolicy $shieldPolicy
     *
     * @property int $id
     * @property string $media_type
     * @property   int $tpl_type
     * @property $admin_uid
     * @property AdminUser $adminInfo
     * @property $input
     */
    class WwLinkController extends AdminController
    {


        public function index(Content $content): Content
        {
            Admin::style(
                <<<style
.custom-toolbar .btn{
    min-width: 170px;
}
style
            );
            /** 调整过滤器文本框宽度 */
            UtilsService::adjustFilterColumns();


            return $content
                ->title('投放链接')
                ->body($this->grid());
        }


        /**
         * Make a grid builder.
         *
         * @return Grid
         */
        protected function grid(): Grid
        {
            return Grid::make(new WwLink(['adAccountInfo', 'tplInfo', 'adminInfo', 'wwUserGroup', 'shieldPolicy', 'shieldPolicy.wwTpl']), function (Grid $grid) {
                /** 对话框新增按钮 */
                $grid->disableCreateButton();
                $grid->tools(function (Grid\Tools $tools) {
                    $className = collect(RequestFacade::segments())->last();
                    $tools->append(UtilsService::dialogForm('添加',RequestFacade::url().'/create',"create-{$className}"));
                });
                $grid->disableViewButton();
                $grid->showColumnSelector();
                $grid->fixColumns(0, -1);
                $grid->paginate(AdminUser::getPaginate());
                $grid->export()->rows(function ($rows) {
                    return $this->export($rows);
                });
                if (!AdminUser::isSystemOp()) {
                    /** @var AdminUser $adminUser */
                    $adminUser = Admin::user();

                    $grid->column('id','投放链接ID')->sortable();
                    if (Admin::user()->isRole('customer')) { //如果是“客户”的角色 也就是主账号
                        $grid->model()->whereIn("admin_uid", AdminSubUser::getAdminUids($adminUser->id));//如果是主账号，则获取所有子账号的id，包括自己
                        $grid->column('归属账号')->display(function () use ($adminUser) {
                            if ($adminUser->id == $this->admin_uid) { //如果是自己创建的则直接显示主账号
                                return '主账号';
                            } else {
                                $username = $this->adminInfo->username;
                                return str_replace($adminUser->username, '', $username);
                            }
                        });
                    } else { //如果是子账号，展示自己的创建的链接
                        $grid->model()->where("admin_uid", $adminUser->id)->orderByDesc("id");
                    }
                } else {
                    $grid->column("adminInfo.username", "客户")->filter();
                    $grid->column('id','投放链接ID')->sortable();
                }
                $grid->model()->orderByDesc("id");
                $grid->column('is_open')->switch();
                $grid->column('media_type');
                $grid->column('account_id');
                $grid->column('adAccountInfo.corporation_name', "账户主体");
                $grid->column('link_ad_id')->display(function ($value) {
                    $host = $this->use_domain ?? AdminDomain::getDomainHost();
                    return WwLinkModel::getLinkUrl($this->media_type,$host, $value);
                })->ClickCopy(0, "复制");
                $grid->column('remark')->editable();
                $grid->column('tplInfo.name', "投放页面")->display(function ($value) {
                    if ($this->tpl_type == 1) {
                        return $value;
                    } else {
                        return '外部链接';
                    }
                });
                $grid->column('wwUserGroup.title', '投放销售分组')->display(function ($value) {
                    if ($this->tpl_type == 1) {
                        return $value;
                    } else {
                        return '外部链接';
                    }
                });
                $grid->column("shieldPolicy.wwTpl.name", "屏蔽页面")->display(function ($value) {
                    return empty($value) ? "无" : $value;
                });
                $grid->column('shieldPolicy.name', '屏蔽规则')->display(function () {
                    $shieldPolicy = $this->shieldPolicy;
                    if (!$shieldPolicy) {
                        Tooltip::make('.block_area_help_message' . $this->id)->title('无屏蔽');
                        return '<div class="block_area_help_message' . $this->id . '">' . "策略" . '</div>';
                    }
                    if (!$shieldPolicy->wwUserGroup) {
                        Tooltip::make('.block_area_help_message' . $this->id)->title('策略：' . $shieldPolicy->name . "<br/>");
                        return '<div class="block_area_help_message' . $this->id . '" style="color:red">' . "接粉分组不存在" . '</div>';
                    }
                    $areaMergeName = '无';
                    $block_area = '屏蔽地区：';
                    if($shieldPolicy->block_area){
                        $blockAreaIds = json_decode($shieldPolicy->block_area);
                        $areaMergeName = Area::query()->whereIn('id', $blockAreaIds)->pluck('merge_name','id')->toArray();
                        if(!empty($areaMergeName)){
                            $areaMergeName = implode(',',$areaMergeName);
                        }else{
                            $areaMergeName = '无';
                        }
                    }

                    $data =
                        '策略：' . $shieldPolicy->name . "<br/>" .
                        '接粉销售：' . $shieldPolicy->wwUserGroup->title . "<br/>" .
                        '屏蔽页面：' . ((!empty($this->shieldPolicy->wwTpl)) ? $this->shieldPolicy->wwTpl->name : "不存在"). "<br/>" .
                        "$block_area" . $areaMergeName;
                    Tooltip::make('.block_area_help_message' . $this->id)->title($data);
                    return '<div class="block_area_help_message' . $this->id . '">' . $shieldPolicy->name . '</div>';
                });
                $grid->column('need_shield')
                    ->using([0 => '否', 1 => '是'])
                    ->dot([0 => 'warning', 1 => 'success']);

                $grid->column('conv_count');
                $grid->column('tads_count');
                $grid->column('ww_label', '企微标签')->textarea();
                $grid->column('访问记录')->display('访问记录')->modal(function ($modal) {
                    // 设置弹窗标题
                    $modal->title('访问记录 ');
                    $modal->xl();
                    return ViewRecord::make(['link_id' => $this->id]);
                });
                if(!Admin::user()->isRole("wop")){
                    $grid->column('复制')->display('复制')->label()->modal(function (Grid\Displayers\Modal $modal) {
                        // 标题
                        $modal->title('复制链接到其他账户');
                        // 自定义图标
                        $modal->icon('');//feather icon-check-circle
                        $modal->xl();
                        // 传递当前行字段值
                        $data = [
                            'ww_link_id' => $this->id
                        ];
                        return CopyLinkToAccount::make()->payload($data);
                    });
                }
                $grid->column('updated_at')->sortable();

                $grid->actions(function (Grid\Displayers\Actions $actions) {
                    $actions->prepend(new FlushWwLinkTadsConvCount());//刷新回传
                });

                //2025-06-03 修改样式开始
                $grid->tools(function (Grid\Tools $tools) use ($grid) {
                    if (!Admin::user()->isRole('wop')) {
//                        $tools->append(new BatchSetWwLinkPage());//批量设置投放页面
//                        $tools->append(new BatchSetLinkWwUserGroup());//批量设置投放销售分组页面
//                        $tools->append(new BatchSetWwLinkShieldPolicy());//批量设置屏蔽规则
//                        $tools->append(new BatchSetSwitch()); // 批量开关链接
//                        $tools->append(new WwLabelBatchAction());//批量配置企微标签
//                        $tools->append(new ConvRateBatchAction());//批量配置回传比例
//                        $tools->append(new BatchSetLinkUseDomain());//批量配置链接使用域名
                        $tools->append(new WwLinkBatchActions());//批量配置

                    } else {
                        $grid->disableCreateButton();
                        $grid->disableBatchDelete();
                        $grid->disableActions();
                    }
                    $grid->tools(function (Grid\Tools $tools) {
                        $tools->append(Modal::make()
                            ->lg()
                            ->title('批量导入')
                            ->button('<button class="btn btn-primary"><i class="fa fa-cloud-upload"></i>&nbsp;&nbsp;批量导入</button>')
                            ->body(WwLinkConfigImportForm::make()));
                    });
                });

                $grid->filter(function (Grid\Filter $filter) use ($grid) {
                    $filter->expand();
                    $filter->panel();

                    $filter->equal('id','投放链接ID')->width(2);
                    if (AdminUser::isSystemOp()) {
                        $filter->where('adAccountInfo.corporation_name', function ($query) {
                            $accountIds = TencentAdAccount::query()->where("corporation_name","Like", "%$this->input%")->pluck("account_id");
                            $query->whereIn("account_id", $accountIds);
                        }, "账户主体")->width(2);
                    } else {
                        $accountList = TencentAdAccount::query()->whereIn("admin_uid", AdminSubUser::getAdminUids(Admin::user()->id))->groupBy("corporation_name")->pluck('corporation_name', 'corporation_name');
                        $filter->where('adAccountInfo.corporation_name', function ($query) {
                            $accountIds = TencentAdAccount::query()->where("corporation_name", $this->input)->pluck("account_id");
                            $query->whereIn("account_id", $accountIds);
                        }, "账户主体")->width(2)->select($accountList);
                    }

                    //所有带「所属账户」的列表，主账户都增加对应筛选
                    if (Admin::user()->parent_id == 0) { //主账号
                        $adminIds = AdminSubUser::getALLAdminUids(Admin::user()->id);
                        $filter->equal("admin_uid", '操作账号')->select(AdminUser::query()->whereIn("id", $adminIds)->pluck("username", 'id')->toArray())->width(2);
                    }
                    $filter->where('account_id', function ($query) {
                        $keyWord = trim($this->input);
                        if (empty($keyWord)) {
                            return;
                        }
                        // 使用正则表达式分割多种分隔符：逗号、中文逗号、空格、制表符等
                        $keyWords = UtilsService::splitKeywords($keyWord);
                        $query->whereIn("account_id", $keyWords);
                    })->width(2);
                    $filter->like("remark")->width(2);

                    //投放链接这里加一个销售分组的筛选
                    $query =  WwUsersGroup::query()->whereIn('admin_uid',AdminSubUser::getAdminUids(Admin::user()->id))->where('type',1);

                    $authorizedGroupIds = AdminSubUser::query()->where('admin_uid', Admin::user()->id)->value('ww_user_group_ids');
                    if (!empty($authorizedGroupIds)) { //不为空说明是子账号
                        $decodedIds = json_decode($authorizedGroupIds);
                        if (is_array($decodedIds) && !empty($decodedIds)) {
                            if(in_array(-1, $decodedIds)) { //如果选择了全部
                               $parent_id = Admin::user()->parent_id;
                               if($parent_id){ //如果是子账号 则获取主账号能看到的全部分组
                                    $parentGroupIds = WwUsersGroup::query()->where('admin_uid',$parent_id)->where('type',1)->pluck('id')->toArray(); //获主账号的分组ID
                                    $selfGroupIds = $query->pluck('id')->toArray();//当前用户的分组ID
                                    $mergeGroupIds = array_merge($parentGroupIds, $selfGroupIds);//合并
                                   if($mergeGroupIds){
                                       $query->orWhereIn("id",$mergeGroupIds);
                                   }
                               }
                            }else{
                                $query->orWhereIn('id', $decodedIds)->where('type',1);
                            }
                        }
                    }else{
                        $query->whereIn('admin_uid', AdminSubUser::getAdminUids(Admin::user()->id));
                    }
                    $availableGroups = $query->pluck('title', 'id')->toArray();

                    $filter->equal('ww_user_group_id', '投放销售分组')->select($availableGroups)->width(2);
                    $filter->equal('tpl_type', '链接类型')->select([1 => '智投链接', 2 => '外部链接'])->width(2);
                    $filter->like('tpl_link', '外部链接')->width(2);
                    $filter->equal('media_type', '媒体类型')->select(WwLinkModel::MEDIA_TYPE)->width(2);
                });
            });
        }

        /**
         * Make a show builder.
         *
         * @param mixed $id
         *
         * @return Show
         */
        protected function detail(mixed $id): Show
        {
            return Show::make($id, new WwLink(), function (Show $show) {
                if (!AdminUser::isAdmin($show->model())) {
                    $show->field('N')->value("无数据");
                } else {
                    $show->field('id');
                    $show->field('account_id');
                }
            });
        }

        /**
         * Make a form builder.
         *
         * @return Form
         */
        protected function form(): Form
        {
            return Form::make(new WwLink(), function (Form $form) {
                if (!$form->isCreating() && !AdminUser::isAdmin($form->model())) {
                    $form->display('N')->value("无数据");
                } else {
                    $form->display('id');
                    $form->hidden('admin_uid');
//                    $form->hidden('media_type');
                    $form->text('remark');
                    $form->radio('need_shield')->options([0 => '关闭', 1 => '开启'])->when([1], function (Form $form) {
                        $shieldPolicy = [-1000001 => '自动匹配屏蔽规则（仅支持腾广告）'] + (ShieldPolicy::query()->whereIn("admin_uid", AdminSubUser::getALLAdminUids(Admin::user()->id))->pluck("name", "id")->toArray());
                        $form->select('shield_policy_id')->options($shieldPolicy)->default(-1000001);
                        $form->switch("is_open")->default(1);
//                            ->help("该开关打开时，正常流量进入投放页面，异常流量进入屏蔽规则内的屏蔽页面，该开关关闭时，所有流量进入屏蔽规则的屏蔽页面，如关闭屏蔽，则该开关无效");
                    })->default(1);
                    if ($form->isCreating()) {
//                        $alert = Alert::make("<h3 style='color: white'>上报类型左侧为选项列表，右侧为选择的内容</h3>")->info();
//                        $form->html($alert);
                        $form->radio('media_type')
                            ->options(WwLinkModel::MEDIA_TYPE)
                            ->when(['腾讯广告'], function (Form $form) {
                                $form->select('account_id')
                                    ->options(function ($keyword) { // 异步搜索回调
                                        return TencentAdAccount::query()
                                            ->where('admin_uid', Admin::user()->id)
                                            ->where('account_id', 'like', "{$keyword}%")
                                            ->pluck('account_id', 'account_id')
                                            ->take(5); // 限制返回数量
                                    })
                                    ->ajax(admin_route('api.tencent-ad-account')); // 启用异步搜索
                                $form->checkbox('add_succeeded_action_type')->options(AdAction::TENCENT_TYPE)->saveAsJson();
                                $form->checkbox('customer_start_chat_action_type')->options(AdAction::TENCENT_TYPE)->saveAsJson();
                                $form->checkbox('deep_action_type','深度回传行为')->options(AdAction::TENCENT_TYPE)->saveAsJson()->help('深度回传行为可在进粉记录列表点击【深度回传】按钮手动上报');
                            })->when(['巨量广告'], function (Form $form) {
                                $form->checkbox('add_succeeded_action_type')->options(AdAction::OE_TYPE)->saveAsJson();
                                $form->checkbox('customer_start_chat_action_type')->options(AdAction::OE_TYPE)->saveAsJson();
                            })->when(['快手广告'], function (Form $form) {
                                $form->checkbox('add_succeeded_action_type')->options(AdAction::KUAISHOU_TYPE)->saveAsJson();
                                $form->checkbox('customer_start_chat_action_type')->options(AdAction::KUAISHOU_TYPE)->saveAsJson();
//                                $form->checkbox('deep_action_type','深度回传行为')->options(AdAction::KUAISHOU_DEEP_TYPE)->saveAsJson()->help('深度回传行为可在进粉记录列表点击【深度回传】按钮手动上报');
                            })->when(['华为广告'], function (Form $form) {
                                $form->checkbox('add_succeeded_action_type')->options(AdAction::HUAWEI_TYPE)->saveAsJson();
                                $form->checkbox('customer_start_chat_action_type')->options(AdAction::HUAWEI_TYPE)->saveAsJson();
                            })->when(['爱奇艺广告'], function (Form $form) {
                                $form->checkbox('add_succeeded_action_type')->options(AdAction::IQIYI_TYPE)->saveAsJson();
                                $form->checkbox('customer_start_chat_action_type')->options(AdAction::IQIYI_TYPE)->saveAsJson();
                            })
                            ->when(['OPPO广告'], function (Form $form) {
//
                                //这里需要使用一个临时字段，因为这里如果使用account_id，数据还是上个下拉框的数据
                                $form->select('oppo_account_id','投放账户ID')
                                    ->options(function ($keyword) { // 异步搜索回调
                                        return AdOppoAccount::query()
                                            ->where('admin_uid', Admin::user()->id)
                                            ->where('owner_id', 'like', "{$keyword}%")
                                            ->pluck('owner_id', 'owner_id')
                                            ->take(5); // 限制返回数量
                                    })->ajax(admin_route('api.oppo-ad-account')); // 启用异步搜索
                                $form->checkbox('add_succeeded_action_type')->options(AdAction::OPPO_TYPE)->saveAsJson();
                                $form->checkbox('customer_start_chat_action_type')->options(AdAction::OPPO_TYPE)->saveAsJson();
                            })
                            ->when(['VIVO广告'], function (Form $form) {
                                //这里需要使用一个临时字段，因为这里如果使用account_id，数据还是上个下拉框的数据
                                $form->select('vivo_account_id','投放账户ID')
                                    ->options(function ($keyword) { // 异步搜索回调
                                        return AdVivoAccount::query()
                                            ->where('admin_uid', Admin::user()->id)
                                            ->where('account_id', 'like', "{$keyword}%")
                                            ->pluck('account_id', 'account_id')
                                            ->take(5); // 限制返回数量
                                    })
                                    ->ajax(admin_route('api.vivo-ad-account')); // 启用异步搜索
                                $form->checkbox('add_succeeded_action_type')->options(AdAction::VIVO_TYPE)->saveAsJson();
                                $form->checkbox('customer_start_chat_action_type')->options(AdAction::VIVO_TYPE)->saveAsJson();
                            })
                            ->when(['优酷广告'], function (Form $form) {
                                //这里需要使用一个临时字段，因为这里如果使用account_id，数据还是上个下拉框的数据
                                $form->select('youku_account_id', '投放账户ID')
                                    ->options(function ($keyword) {
                                        return AdYoukuAccount::query()
                                            ->where('admin_uid', Admin::user()->id)
                                            ->where('account_id', 'like', "{$keyword}%")
                                            ->pluck('account_id', 'account_id')
                                            ->take(5);
                                    })
                                    ->ajax(admin_route('api.youku-ad-account'));
                                $form->checkbox('add_succeeded_action_type')->options(AdAction::YOUKU_TYPE)->saveAsJson();
                                $form->checkbox('customer_start_chat_action_type')->options(AdAction::YOUKU_TYPE)->saveAsJson();
                            })
                            ->when(['喜马拉雅广告'], function (Form $form) {
                                $form->checkbox('add_succeeded_action_type')->options(AdAction::XIMALAYA_TYPE)->saveAsJson();
                                $form->checkbox('customer_start_chat_action_type')->options(AdAction::XIMALAYA_TYPE)->saveAsJson();
                            })
                            ->default("腾讯广告")->required();

                    } else if($form->isEditing()){
                        $form->display("media_type");
                        /** 获取媒体类型 */
                        $mediaType = $form->model()->media_type;

                        /** 通过媒体类型返回选项数组 */
                        $options = AdAction::getMediaType($mediaType);
                        /** 通过媒体类型返回媒体配置 */
                        $config = AdAction::getMediaTypeConfig($mediaType);

                        /** 如果配置accountId为true就显示该字段 */
                        if ($config['accountId']) {
                            $form->display('account_id');
                        }

                        /** 通用显示字段 */
                        $form->checkbox('add_succeeded_action_type')->options($options)->saveAsJson();
                        $form->checkbox('customer_start_chat_action_type')->options($options)->saveAsJson();

                        /** 如果配置deepAction为true就显示该字段 */
                        if ($config['deepAction']) {
                            $form->checkbox('deep_action_type','深度回传行为')
                                ->options($options)
                                ->saveAsJson()
                                ->help('深度回传行为可在进粉记录列表点击【深度回传】按钮手动上报');
                        }

                    }

                    $form->hidden('long_press_action_type');

                    $form->hidden('link_ad_id');

                    $tplList = WwTpl::getAdTplList();
                    $tplList[-1] = '使用其他系统';
                    $form->hidden("tpl_type");
                    $form->select('tpl_id')
                        ->options($tplList)
                        ->when(-1, function (Form $form) {
                            $form->url("tpl_link", "落地页链接")->help("这里填写您自己的落地页链接");
                        })
                        ->when(">", -1, function (Form $form) {
                            $form->select('ww_user_group_id')->options(WwUsersGroup::getMyAdGroupList());

                            $form->number('conv_count', '转化')->min(1)->default(1)->required();
                            $form->number('tads_count', '上报')->min(1)->default(1)->required()->help("建议使用默认配置，系统将根据此配置比例进行优化。例如：2转化1上报=50%回传，1转化1上报=100%回传。<br/>");
                        })
                        ->required();



                    $form->textarea("ww_label")->placeholder("每行一个，回车换行，请确认此处填写标签已经在企业微信后台标签库配置。")->help('每行一个，回车换行，请确认此处填写标签已经在企业微信后台标签库配置。');
                    $form->saving(function (Form $form) {

                        //判断是否只允许一个账户创建一条链接
                        if($form->isCreating()){
                            if (Admin::user()->repeat_ww_link == 0 && $form->input("account_id")) {
                                $where = [
                                    'account_id' => $form->input("account_id"),
                                    'admin_uid'  =>  Admin::user()->id
                                ];
                                if (WwLinkModel::query()->where($where)->exists()) {
                                    return $form->response()->alert()->error('提示')->detail('投放账户：' . $form->input("account_id") . '，已创建投放链接。');
                                }
                            }

                        }

                        $shield_policy_id = $form->input("shield_policy_id");
                        if ($shield_policy_id == -1000001 && $form->input("need_shield")) {
                            if ($form->isEditing()) {
                                /** @var WwLinkModel $model */
                                $model = $form->model();
                                if (empty($form->input("media_type"))) {
                                    $form->input("media_type", $model->media_type);
                                }
                                if (empty($form->input("account_id"))) {
                                    $form->input("account_id", $model->account_id);
                                }
                            }
                            if ($form->input("media_type") == '腾讯广告') {
                                if ($form->input("tpl_id") == -1) {
                                    return $form->response()->error("请选择屏蔽策略，外部链接无法使用自动选择")->withValidation([
                                        'shield_policy_id' => '请选择屏蔽策略，外部链接无法使用自动选择'
                                    ]);
                                }
                                //需要寻找匹配的屏蔽策略
                                /** @var TencentAdAccount $accountInfo */
                                $accountInfo = TencentAdAccount::query()
                                    ->where("account_id", $form->input("account_id"))
                                    ->select("corporation_name")
                                    ->first();
                                if (!$accountInfo) {
                                    return $form->response()->error("请选择有效的投放账户")->withValidation([
                                        'account_id' => '请选择有效的投放账户'
                                    ]);
                                }
                                /** @var WwTpl $wwTplInfo */
                                $wwTplInfo = WwTpl::query()->find($form->input("tpl_id"));
                                if (!$wwTplInfo) {
                                    return $form->response()->error("请选择有效的投放页面")->withValidation([
                                        'tpl_id' => '请选择有效的投放页面'
                                    ]);
                                }
                                /** @var ShieldPolicyRule $shieldPolicyRule */
                                $shieldPolicyRule = ShieldPolicyRule::query()
                                    ->where("mdm", $accountInfo->corporation_name)
                                    ->where("product", $wwTplInfo->product)
                                    ->whereIn("admin_uid", AdminSubUser::getALLAdminUids(Admin::user()->id))
                                    ->orderByDesc("id")
                                    ->first();
                                if ($shieldPolicyRule) {
                                    $form->input("shield_policy_id", $shieldPolicyRule->shield_policy_id);
                                } else {
                                    return $form->response()->error("无法匹配到可用的屏蔽规则，请您手动选择")->withValidation([
                                        'shield_policy_id' => '无法匹配到可用的屏蔽规则，请您手动选择'
                                    ]);
                                }
                            } else {
                                return $form->response()->error("抱歉，您选择的不是腾讯广告，无法自动匹配屏蔽规则")->withValidation([
                                    'shield_policy_id' => '抱歉，您选择的不是腾讯广告，无法自动匹配屏蔽规则'
                                ]);
                            }
                        }
                        if ($form->model()->admin_uid) {
                            $form->input("admin_uid", $form->model()->admin_uid);
                        } else {
                            $form->input("admin_uid", Admin::user()->id);
                        }
                        if ($form->input("need_shield") && !$form->input("shield_policy_id")) {
                            return $form->response()->error("请选择屏蔽规则")->withValidation([
                                'shield_policy_id' => '请选择屏蔽规则'
                            ]);
                        }
                        if ($form->input("media_type") == '腾讯广告' && !$form->input("account_id")) {
                            return $form->response()->error("请选择投放账户")->withValidation([
                                'account_id' => '请选择投放账户'
                            ]);
                        }
                        if ($form->input("media_type") == 'OPPO广告' && !$form->input("oppo_account_id")) {
                            return $form->response()->error("请选择投放账户")->withValidation([
                                'oppo_account_id' => '请选择投放账户'
                            ]);
                        }
                        if ($form->input("media_type") == 'VIVO广告' && !$form->input("vivo_account_id")) {
                            return $form->response()->error("请选择投放账户")->withValidation([
                                'vivo_account_id' => '请选择投放账户'
                            ]);
                        }
                        if ($form->input("media_type") == '优酷广告' && !$form->input("youku_account_id")) {
                            return $form->response()->error("请选择投放账户")->withValidation([
                                'youku_account_id' => '请选择投放账户'
                            ]);
                        }

                        //判断一下是不是oppo广告，如果是，在给account_id字段赋值后，需要删除临时字段
                         if ($form->input("media_type") == 'OPPO广告' && $form->input('oppo_account_id')) {
                             $form->account_id = 'op'. $form->input('oppo_account_id');
                         }

                        //判断一下是不是vivo广告，如果是，在给account_id字段赋值后，需要删除临时字段
                        if ($form->input("media_type") == 'VIVO广告' && $form->input('vivo_account_id')) {
                            $checkVivoSrcId = AdVivoAccount::query()->where('account_id',$form->input("vivo_account_id"))->value('src_id');
                            if(!$checkVivoSrcId){
                                return $form->response()->error('请配置事件源ID');
                            }
                            $form->account_id = 'vo'. $form->input('vivo_account_id');
                        }
                        //判断一下是不是优酷广告，如果是，在给account_id字段赋值后，需要删除临时字段
                        if ($form->input("media_type") == '优酷广告' && $form->input('youku_account_id')) {
                            $form->account_id = 'yk'. $form->input('youku_account_id');
                        }

                        $form->deleteInput(['oppo_account_id','vivo_account_id','youku_account_id']);//删除oppo/vivo/优酷广告账户的临时字段

                        if ($form->input("tpl_id")) {
                            if ($form->input("tpl_id") != -1) {
                                $form->input("tpl_type", 1);
                                if (!$form->input("ww_user_group_id")) {
                                    return $form->response()->error("请选择投放接粉销售分组")->withValidation([
                                        'ww_user_group_id' => '请选择投放接粉销售分组'
                                    ]);
                                }
                            } else {
                                $form->input("tpl_type", 2);
                                if (!$form->input("tpl_link")) {
                                    return $form->response()->error("请联系落地页地址")->withValidation([
                                        'tpl_link' => '请联系落地页地址'
                                    ]);
                                }
                            }
                        }
                    });
                    $form->saved(function (Form $form, $result) {
                        if ($form->isCreating()) {
                            $newId = $result;
                            /** @var WwLinkModel $lastData */
                            $lastData = WwLinkModel::query()->find($newId);
                            //生成AD_ID
                            if ($form->input("media_type") !== '腾讯广告' && $form->input("media_type") !== 'OPPO广告' && $form->input("media_type") !== 'VIVO广告' && $form->input("media_type") !== '优酷广告') {
                                $lastData->account_id = $lastData->id + ********;
                            }
                            if (!WwLinkModel::query()->where("link_ad_id", $lastData->account_id)->exists()) {
                                $lastData->link_ad_id = $lastData->account_id;
                            } else {
                                $lastData->link_ad_id = $lastData->account_id . "_" . WwLinkModel::withTrashed()->where("account_id", $lastData->account_id)->count();
                            }
                            $lastData->save();
                        }
                    });
//                    $form->display('created_at');
//                    $form->display('updated_at');
                    $form->disableViewButton();
                    $form->disableDeleteButton();
                    $form->disableViewCheck();
                    $form->disableEditingCheck();
                }
            });
        }

        public function destroy($id)
        {
            $data = WwLinkModel::query()->whereIn("id", explode(",", $id))->get();
            foreach ($data as $datum) {
                if (!AdminUser::isAdmin($datum)) {
                    return $this->form()->response()->error("无权限操作");
                }
            }
            return $this->form()->destroy($id);
        }

        /**
         * 导出
         * @param $rows
         * @return Collection
         */
        public function export($rows): Collection
        {
            $adminUser = Admin::user(); // 获取当前登录用户
            foreach ($rows as $index => &$row) {
                if ($adminUser->id == $row->admin_uid) {
                    $rows[$index]['归属账号'] = '主账号';
                } else {
                    $username  = $row->adminInfo ? $row->adminInfo->username : '';
                    $rows[$index]['归属账号'] = str_replace($adminUser->username, '', $username);
                }
                $rows[$index]['ID'] = $row->id;
                $rows[$index]['is_open'] = $row->is_open ? '开' : '关';
                $rows[$index]['media_type'] = $row->media_type;
                $rows[$index]['account_id'] = $row->account_id;
                $rows[$index]['账户主体'] =  $row->adAccountInfo->name ?? '';
                if ($row->use_domain){
                    $host = $row->use_domain;
                }else{
                    $host = AdminDomain::getDomainHost();
                }
                $linkUrl = WwLinkModel::getLinkUrl($row->media_type,$host, $row->account_id);
                $rows[$index]['link_ad_id'] = $linkUrl;
                $rows[$index]['remark'] =  $row->remark ?? '';
                $rows[$index]['tplInfo.name'] = $row->tplInfo->name ?? '';
                $rows[$index]['shieldPolicy.wwTpl.name'] = $row->shieldPolicy->wwTpl->name ?? '';//屏蔽页面
                $rows[$index]['shieldPolicy.name'] = $row->shieldPolicy->name ?? '';//屏蔽规则
                $rows[$index]['need_shield'] = $row->need_shield ? '是' : '否';
                $rows[$index]['conv_count'] = $row->conv_count;
                $rows[$index]['tads_count'] = $row->tads_count;
                $rows[$index]['ww_label'] = $row->ww_label ?? '';
                $rows[$index]['updated_at'] = $row->updated_at ?? '';
            }
            return $rows;
        }
    }

