<?php

namespace App\Admin\Forms\TencentAd;

use Admin;
use App\Jobs\TencentAd\TencentAdTrackClickConfigJob;
use App\Models\TencentAdAccount;
use App\Models\AdminSubUser;
use App\Models\AdminUser;
use App\Services\Tools\LogService;
use Dcat\Admin\Widgets\Form;

class AdTracClickConfigByTypeForm extends Form
{
    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        LogService::inputLog('Tools','腾讯广告监测链接配置-根据用户账号配置', $input, Admin::user()->id, Admin::user()->username);
        $secondCategoryType = $input['second_category_type'] ?? 'WEB';
        $accountList = [];
        switch ($input['type']) {
            case 0: //根据主账号配置
                if (!$input['master_admin_uid']) {
                    return $this->response()->alert()->error('提示')->detail('请选择用户');
                }
                $allAdminUids = AdminSubUser::getAdminUids($input['master_admin_uid']);
                $accountList = TencentAdAccount::query()->whereIn('admin_uid', $allAdminUids)->get();
                break;
            case 1: //选择主账号或者子账号配置
                if (!$input['admin_uid_one']) {
                    return $this->response()->alert()->error('提示')->detail('请选择用户');
                }
                $accountList = TencentAdAccount::query()->whereIn('admin_uid', $input['admin_uid_one'])->get();
                break;
            case 2: //选择主账号或者子账号+腾讯账户主体名称配置
                if (!$input['admin_uid_two']) {
                    return $this->response()->alert()->error('提示')->detail('请选择用户');
                }
                if (!$input['corporation_name_two']) {
                    return $this->response()->alert()->success('提示')->detail('请填写主体名称')->refresh();
                }
                $accountList = TencentAdAccount::query()
                    ->where('corporation_name', $input['corporation_name_two'])
                    ->whereIn('admin_uid', $input['admin_uid_two'])
                    ->get();
                break;
            case 3: //选择主账号+腾讯账户主体名称配置
                if (!$input['master_admin_uid_three']) {
                    return $this->response()->alert()->error('提示')->detail('请选择用户');
                }
                if (!$input['corporation_name_three']) {
                    return $this->response()->alert()->success('提示')->detail('请填写主体名称')->refresh();
                }
                $allAdminUids = AdminSubUser::getAdminUids($input['master_admin_uid_three']);
                $accountList = TencentAdAccount::query()->where('corporation_name', $input['corporation_name_three'])->whereIn('admin_uid', $allAdminUids)->get();
                break;
        }
        if ($accountList->isEmpty()) {
            return $this->response()->alert()->error('提示')->detail('未查询到账户数据');
        }
        foreach ($accountList as $accountInfo) {
            $feedbackName = 'ZTFZ-DN-智投方舟-' . $accountInfo->account_id;
            TencentAdTrackClickConfigJob::dispatch($accountInfo, $feedbackName,$secondCategoryType)->onQueue('tencent_ad_track_click_config');
        }
        return $this->response()->alert()->success('提示')->detail('已转入后台队列执行，稍后请在列表页查看')->refresh();
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        $type = [
            0 => '选择主账号配置',
            1 => '选择主账号或子账号配置',
            2 => '选择主账号或子账号+企业主体名称配置',
            3 => '选择主账号+企业主体名称配置',
        ];
        $adminUsers = AdminUser::query()
            ->whereNotIn('id', [1])
            ->pluck('username', 'id')
            ->toArray();
        $masterAdminUsers = AdminUser::query()
            ->whereNotIn('id', [1])
            ->where('parent_id', 0)
            ->pluck('username', 'id')
            ->toArray();
        $this->select('type', '选择配置方式')->options($type)
            ->when(0, function (Form $form) use ($masterAdminUsers) {
                $this->select('master_admin_uid', '主账号')->options($masterAdminUsers)->help('根据主账号配置，会将所选主账号以及所属子账号下的全部腾讯账户都配置监测链接。');
            })
            ->when(1, function (Form $form) use ($adminUsers) {

                $this->multipleSelect('admin_uid_one', '选择用户（可多选）')->options($adminUsers)->help('根据选择主账号或子账号配置，会将所选账号下的全部腾讯账户都配置监测链接。');
            })
            ->when(2, function (Form $form) use ($adminUsers) {
                $adminUsers = AdminUser::query()
                    ->whereNotIn('id', [1])
                    ->pluck('username', 'id')
                    ->toArray();
                $this->multipleSelect('admin_uid_two', '选择用户（可多选）')->options($adminUsers)->help('选择主账号或者子账号+腾讯账户主体名称配置，会将所选账户下的全部腾讯账户都配置监测链接。');
                $this->text('corporation_name_two', '企业主体名称')->help('根据账户企业名称批量创建，会将该企业下的全部账户都配置监测链接。');
            })
            ->when(3, function (Form $form) use ($masterAdminUsers) {
                $this->select('master_admin_uid_three', '主账号')->options($masterAdminUsers)->help('根据主账号+企业主体名称创建，会将所选主账号以及所属子账号下的全部腾讯账户都配置监测链接。');
                $this->text('corporation_name_three', '企业主体名称')->help('根据账户企业名称批量创建，会将该企业下的全部账户都配置监测链接。');
            })
            ->required()->default(0);
        $this->select('second_category_type','营销载体类型')->help('默认为WEB类型，如客户有其他类型需求，则选择对应的营销载体类型')->options(\App\Models\TenCentAdTrackClickConfig::CATEGORY_TYPE)->required()->default('WEB');


    }

    /**
     * The data of the form.
     *
     * @return array
     */
    public function default()
    {
        return [

        ];
    }
}
