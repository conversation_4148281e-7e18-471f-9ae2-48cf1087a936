<?php

    namespace App\Jobs;

    use App\Services\Ocpx\OcpxSubmitService;
    use Illuminate\Bus\Queueable;
    use Illuminate\Contracts\Queue\ShouldBeUnique;
    use Illuminate\Contracts\Queue\ShouldQueue;
    use Illuminate\Foundation\Bus\Dispatchable;
    use Illuminate\Queue\InteractsWithQueue;
    use Illuminate\Queue\SerializesModels;
    use Illuminate\Support\Facades\Log;

    /**
     * OCPX的上报队列
     */
    class OcpxSubmitJob implements ShouldQueue
    {
        use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

        protected $addRecord;

        protected $action;

        /**
         * Create a new job instance.
         *
         * @return void
         */
        public function __construct($addRecord,$action)
        {
            //
            $this->addRecord = $addRecord;
            $this->action = $action;
        }

        /**
         * Execute the job.
         *
         * @return mixed
         */
        public function handle()
        {
            //Log::info("添加外部联系人-OCPX上报队列开始执行");
            $isRate = 1;//默认添加行为是1 计算比例
            if($this->action != 'add_external_contact'){ //如果不是添加行为。则是0
                $isRate = 0;
            }
            $ocpxObj = new OcpxSubmitService();
            $ocpxObj->up($this->addRecord, $this->action,$isRate);
        }

    }
