<?php

namespace App\Console\Commands;

use App\Models\AdminDomain;
use App\Services\AlibabaCloudService;
use App\Services\CacheService;
use App\Services\NotifySendService;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;

class SenTest extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:sen';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $domains = [
            'baidu.com',
            'www.baidu.com'
        ];
        $input = "baidu.com";

        // 测试域名匹配功能
        $result = AdminDomain::isDomainMatched($input, $domains);
        $this->info("域名 '{$input}' 匹配结果: " . ($result ? '匹配' : '不匹配') . ' 结果：' . ($result ? 'true' : 'false'));

        return 0;
    }


}
