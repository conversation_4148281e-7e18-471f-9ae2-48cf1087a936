<?php

namespace App\Models;

use Dcat\Admin\Admin;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class WwUsersOnlineLogs extends Model
{
    use HasDateTimeFormatter;

    protected $table = 'ww_users_online_logs';


    public function wwUserInfo(): BelongsTo
    {
        return $this->BelongsTo(WwUser::class, 'ww_user_id', 'id');
    }

    public function adminInfo(): BelongsTo
    {
        return $this->BelongsTo(AdminUser::class, 'admin_uid', 'id');
    }

    public function corpInfo(): BelongsTo
    {
        return $this->BelongsTo(WwCorpInfo::class, 'corp_id', 'id');
    }

    /**
     * 新增销售上下线记录
     * @param $wwUser
     * @param $onlineStatus
     * @param $source
     * @return mixed
     */
    public static function addOnLineStatusLogs($wwUser, $onlineStatus, $source ,$adminUid = 0)
    {
        $date = date("Y-m-d H:i:s", time());
        if(!$adminUid || $adminUid == 0) {
            $adminUid = $wwUser->admin_uid;
        }
        self::query()->insert([
            'admin_uid' => $adminUid,
            'ww_user_id' => $wwUser->id,
            'corp_id' => $wwUser->corp_id,
            'online_status' => $onlineStatus,
            'source' => $source,
            'created_at' => $date,
            'updated_at' => $date,
        ]);
        return true;
    }
}
