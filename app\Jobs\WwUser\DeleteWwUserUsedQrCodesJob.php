<?php

namespace App\Jobs\WwUser;

use App\Models\WwUserQrcode;
use App\Models\WwUserQrCodesDelete;
use App\Services\Corp\WwCorpApiService;
use App\Services\NotifySendService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;


class DeleteWwUserUsedQrCodesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $data;

    protected $wwUser;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data, $wwUser)
    {

        $this->data = $data;
        $this->wwUser = $wwUser;


    }


    /**
     * 防止任务重叠。 expireAfter 设置锁的最大持有时间（防止任务崩溃导致死锁）
     *
     * @return array
     */
    public function middleware()
    {
        $qrcodeId = $this->data['id'];
        if (empty($qrcodeId)) {
            return [];
        }
        return [(new WithoutOverlapping($qrcodeId))->releaseAfter(10)->expireAfter(15)];
    }


    /**
     * Execute the job.
     *
     * @return mixed
     */
    public function handle()
    {
        //
        //Log::info("删除销售已使用二维码队列开始执行");

        $wwUser = $this->wwUser;
        $configId = $this->data['config_id'];
        $id = $this->data['id'];

        if ($wwUser->corpInfo) {
            $deleteRes = WwCorpApiService::del_contact_way($wwUser->corpInfo, $configId);
            Log::info('删除企业二维码结果：' . json_encode($deleteRes));
        } else {
            $deleteRes = [
                'errcode' => 0
            ];
            $this->data['room_base_name'] = '501';
        }
        if (isset($deleteRes['errcode']) && ($deleteRes['errcode'] == 0 || $deleteRes['errcode'] == 41044)) {
            WwUserQrcode::query()->where('id', $id)->delete();//删除已使用的二维码
//            Log::info('删除销售二维码队列-成功删除');
            try {
                WwUserQrCodesDelete::query()->insert($this->data);
            } catch (\Exception $e) {
                Log::error("[定时删除销售二维码队列失败-转移到冷库]：" . $e->getMessage());
                NotifySendService::sendWorkWeixinForError($e->getMessage());
            }
        } else {
            $message = '[定时删除销售二维码队列失败]：销售ID：' . $wwUser->id . '，config_id：' . $configId . '，errcode：' . $deleteRes['errcode'] ?? '' . '，errmsg：' . $deleteRes['errmsg'] ?? '';
            NotifySendService::sendWorkWeixinForError($message);
        }
        return true;
    }
}
